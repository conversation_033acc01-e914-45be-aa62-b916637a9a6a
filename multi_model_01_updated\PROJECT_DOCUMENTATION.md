# 多模型集成机器学习平台 - 项目文档

本文档是多模型集成机器学习平台的综合指南，旨在为用户和开发者提供全面的功能说明、使用方法和技术细节。

---

## 📖 目录

1.  [**快速开始**](#-快速开始)
    -   [安装](#安装)
    -   [启动GUI](#启动gui)
    -   [命令行基本用法](#命令行基本用法)
2.  [**核心功能概览**](#-核心功能概览)
    -   [数据预处理](#数据预处理)
    -   [模型训练与评估](#模型训练与评估)
    -   [超参数调优](#超参数调优)
    -   [智能最佳模型选择](#智能最佳模型选择)
    -   [集成学习](#集成学习)
    -   [数据探索](#数据探索)
    -   [GPU加速](#gpu加速)
3.  [**GUI使用指南**](#-gui使用指南)
    -   [界面布局](#界面布局)
    -   [基本操作流程](#基本操作流程)
    -   [数据探索功能](#数据探索功能)
    -   [智能模型选择功能](#智能模型选择功能)
4.  [**高级功能详解**](#-高级功能详解)
    -   [融合模型智能选择策略](#融合模型智能选择策略)
    -   [完整分析流程](#完整分析流程)
    -   [多数据源集成](#多数据源集成)
5.  [**技术细节与配置**](#-技术细节与配置)
    -   [数据格式要求](#数据格式要求)
    -   [输出目录结构](#输出目录结构)
    -   [GPU配置](#gpu配置)
6.  [**故障排除**](#-故障排除)

---

## 🚀 快速开始

### 安装

1.  克隆仓库:
    ```bash
    git clone https://github.com/yourusername/multi_model_ensemble.git
    cd multi_model_ensemble
    ```

2.  安装依赖:
    ```bash
    pip install -r requirements.txt
    ```

### 启动GUI

```bash
# 确保在项目根目录下
python gui_main.py
```

### 命令行基本用法

```bash
# 训练所有模型
python code/main.py --model All --mode train

# 训练特定模型
python code/main.py --model RandomForest --mode train

# 可视化特定模型
python code/main.py --model XGBoost --mode plot

# 一键运行完整分析流程
python code/binary_classification_pipeline.py --data your_data.csv --strategy balanced
```

---

## 核心功能概览

### 数据预处理
-   自动检测目标变量。
-   支持多种数据缩放方法（标准化、最小最大等）。
-   集成多种特征选择方法。

### 模型训练与评估
-   支持10种常用机器学习模型（决策树、随机森林、XGBoost、LightGBM、CatBoost、逻辑回归、SVM、KNN、朴素贝叶斯、神经网络）。
-   提供全面的性能评估指标（准确率、精确率、召回率、F1、AUC等）。

### 超参数调优
-   使用Optuna框架进行高效的贝叶斯优化。
-   为每个模型预设了优化的超参数搜索空间。

### 智能最佳模型选择
-   基于综合性能指标自动从多个训练好的模型中推荐最佳模型。
-   提供多种选择策略：
    -   **performance**: 性能优先。
    -   **robustness**: 稳健性优先。
    -   **balanced**: 平衡性能与稳健性。
    -   **interpretability**: 优先选择易于解释的模型。

### 集成学习
-   支持多种集成方法：投票（Voting）、堆叠（Stacking）、加权平均等。
-   **智能基模型选择**：自动选择性能和多样性最佳的模型组合进行集成。
-   支持多数据源集成。

### 数据探索
-   **分组概率分析**：将连续变量分箱，计算每组的目标事件概率，并提供置信区间。
-   **相关性分析**：生成专业的相关性热力图。
-   **学术级图表**：输出的图表符合学术论文发表标准。

### GPU加速
-   自动检测并使用可用的GPU资源（NVIDIA）来加速模型训练。
-   支持XGBoost和CatBoost的GPU加速。
-   在GPU不可用时，自动回退到CPU模式。

---

## GUI使用指南

### 界面布局
-   **菜单栏/工具栏**: 提供所有核心功能的快捷入口。
-   **左侧导航**: 按模块化的方式组织功能。
-   **中央工作区**: 主要操作区域，通过选项卡切换不同功能。
-   **右侧面板**: 显示配置项和实时日志。
-   **状态栏**: 显示当前状态和进度。

### 基本操作流程
1.  **加载数据**: 在“数据管理”选项卡中加载CSV文件。
2.  **模型训练**:
    -   在“模型训练”选项卡中选择一个或多个模型。
    -   配置训练参数（如测试集比例、随机种子）。
    -   （可选）启用超参数调优。
    -   点击“开始训练”。
3.  **结果可视化**:
    -   训练完成后，在“结果可视化”选项卡中选择模型和图表类型（ROC、混淆矩阵等）进行分析。
    -   点击“模型比较”可查看多个模型间的性能对比。

### 数据探索功能
1.  切换到“数据探索”选项卡。
2.  加载数据或使用当前已加载的数据。
3.  选择目标变量和需要分析的连续变量。
4.  配置分箱参数（分组数量和方法）。
5.  点击“开始分析”，完成后可在输出目录查看图表和报告。

### 智能模型选择功能
1.  切换到“集成学习”选项卡。
2.  在“智能模型选择”区域配置选择策略和目标模型数量。
3.  点击“智能模型选择”按钮，系统会自动评估并勾选最优的模型组合。
4.  之后可直接点击“开始单数据源集成”来训练融合模型。

---

## 高级功能详解

### 融合模型智能选择策略
该功能旨在解决“选择哪些基模型进行集成”的核心问题。
-   **性能与多样性平衡**: 不仅选择性能好的模型，也考虑模型之间的差异性（多样性），因为差异大的模型组合通常能带来更好的集成效果。
-   **选择策略**:
    -   `Performance`: 只看性能。
    -   `Diversity`: 只看多样性。
    -   `Balanced`: 平衡二者（推荐）。
-   **动态权重**: 系统还会根据每个被选中模型的贡献度，计算出推荐的融合权重。

### 完整分析流程
这是一个一键式功能，将所有步骤串联起来，实现最大程度的自动化。
1.  **触发**: 在GUI点击“🚀 完整分析”按钮，或在命令行运行`binary_classification_pipeline.py`。
2.  **执行过程**:
    -   数据预处理
    -   （可选）超参数调优
    -   训练所有选定模型
    -   自动选择最佳模型
    -   生成所有可视化图表和SHAP分析
    -   生成最终的HTML性能报告和Markdown摘要。
3.  **适用场景**: 快速获得对数据集的全面分析和基准性能。

### 多数据源集成
-   **使用场景**: 当你的不同模型是基于不同数据源训练的时（例如，来自不同医院的数据）。
-   **配置**: 需要提供一个JSON配置文件，映射每个模型使用的数据文件路径。
-   **融合策略**:
    -   `unified`: 使用统一的测试集评估所有模型。
    -   `original`: 每个模型使用其对应来源的测试集评估。
    -   `combined`: 将所有数据源合并后进行评估。

---

##  기술细节与配置

### 数据格式要求
-   支持CSV格式。
-   目标变量列应命名为`label`或`target`，或放置在数据集的最后一列。
-   特征列应为数值型。

### 输出目录结构
-   `output/`: 存放所有图表和报告。
-   `cache/`: 存放模型训练的中间结果，用于缓存。
-   `models/`: （可选）保存的最终模型文件。
-   `ensemble/`: 存放集成学习的结果。
-   `logs/`: 存放运行日志。

### GPU配置
-   **配置文件**: `code/config.py`中的`GPU_CONFIG`部分。
-   **自动检测**: 默认开启，项目启动时会自动检查环境。
-   **监控**: 在训练时，可以通过`nvidia-smi`命令来实时监控GPU的使用情况。

---

## 故障排除

-   **GUI无法启动**: 检查Python环境和依赖是否正确安装。
-   **模型训练失败**: 检查数据格式是否符合要求，特别是目标列是否为二分类。
-   **内存不足**: 对于大数据集，可以尝试减少同时训练的模型数量，或禁用超参数调优。
-   **SHAP分析失败**: SHAP对某些模型或大数据集可能不稳定，可尝试禁用或对数据进行采样。

如遇问题，请优先查看`logs/`目录下的日志文件和GUI右侧面板的日志输出。

---

## SHAP可视化指南（GUI）

本节精简自原《SHAP_GUI_使用说明》，保留核心操作要点：

### 功能概述
- 为单模型训练结果提供完整的 SHAP 可解释性图：摘要图、依赖图、力图、决策图、瀑布图
- 图表样式与命令行版本保持一致（英文图例与坐标，避免中文方块）

### 使用步骤
1. 在“模型训练”完成后，切换到“结果可视化”页面
2. 在“选择模型”中选择已训练的模型
3. 在“图表类型”选择“SHAP分析”
4. 点击“📈 单模型可视化”，弹出 SHAP 分析窗口

### SHAP窗口与交互
- 图表类型：摘要图 / 依赖图 / 力图 / 决策图 / 瀑布图
- 多图类型（依赖图、力图、瀑布图）支持翻页：上一页/下一页/页码显示
- 操作按钮：刷新、保存当前图、保存所有图（支持 PNG/PDF 与合并PDF报告）

### 图表解读要点
- 摘要图：特征按重要性排序，颜色代表特征值大小
- 依赖图：X为特征值，Y为SHAP值，观察单特征与贡献关系
- 力图/瀑布图：单样本贡献，红色正向、蓝色负向；基准值 E[f(X)] → 预测值 f(x)
- 决策图：样本决策路径（前50个样本）

### 技术特性与稳定性
- 自动选择合适解释器（树模型优先 TreeExplainer，回退 KernelExplainer）
- 大数据自动采样、结果缓存、异常处理完善
- 保存图支持 PNG/PDF、单个或合并成PDF报告（含封面、时间、模型信息）

### 注意事项
- 安装 shap 与 Pillow 库
- 树模型支持度最佳；大数据集计算时间与内存占用较高

