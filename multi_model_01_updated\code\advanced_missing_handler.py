#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级缺失值处理器
提供智能缺失值检测、分析和多种处理策略
"""

import pandas as pd
import numpy as np
from sklearn.impute import SimpleImputer, KNNImputer, IterativeImputer
from sklearn.experimental import enable_iterative_imputer
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.model_selection import cross_val_score, KFold
from sklearn.metrics import mean_squared_error, accuracy_score
from typing import Dict, List, Tuple, Optional, Union, Any
import warnings

# 尝试导入logger
try:
    from logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class MissingValueAnalyzer:
    """
    缺失值分析器
    分析缺失值模式、类型和影响
    """
    
    def __init__(self):
        self.analysis_results = {}
        
    def analyze_missing_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析缺失值模式
        
        Args:
            df: 输入数据框
            
        Returns:
            缺失值分析结果
        """
        analysis = {
            'total_missing': df.isnull().sum().sum(),
            'missing_percentage': (df.isnull().sum() / len(df) * 100).to_dict(),
            'columns_with_missing': df.columns[df.isnull().any()].tolist(),
            'complete_cases': len(df.dropna()),
            'missing_combinations': {}
        }
        
        # 分析缺失值组合模式
        missing_patterns = df.isnull()
        pattern_counts = missing_patterns.value_counts()
        analysis['missing_patterns'] = {
            str(pattern): count for pattern, count in pattern_counts.head(10).items()
        }
        
        # 缺失值相关性分析
        if len(analysis['columns_with_missing']) > 1:
            missing_corr = missing_patterns[analysis['columns_with_missing']].corr()
            analysis['missing_correlation'] = missing_corr.to_dict()
        
        # 缺失值分类
        analysis['missing_categories'] = self._categorize_missing_columns(df)
        
        self.analysis_results = analysis
        return analysis
    
    def _categorize_missing_columns(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """
        将缺失值列按照缺失程度分类
        
        Args:
            df: 输入数据框
            
        Returns:
            分类结果字典
        """
        categories = {
            'low_missing': [],      # <5%
            'medium_missing': [],   # 5-20%
            'high_missing': [],     # 20-50%
            'extreme_missing': []   # >50%
        }
        
        for col in df.columns:
            missing_pct = (df[col].isnull().sum() / len(df)) * 100
            
            if missing_pct == 0:
                continue
            elif missing_pct < 5:
                categories['low_missing'].append(col)
            elif missing_pct < 20:
                categories['medium_missing'].append(col)
            elif missing_pct < 50:
                categories['high_missing'].append(col)
            else:
                categories['extreme_missing'].append(col)
        
        return categories
    
    def recommend_strategy(self, df: pd.DataFrame) -> Dict[str, str]:
        """
        为每列推荐缺失值处理策略
        
        Args:
            df: 输入数据框
            
        Returns:
            每列的推荐策略
        """
        if not self.analysis_results:
            self.analyze_missing_patterns(df)
        
        recommendations = {}
        categories = self.analysis_results['missing_categories']
        
        for col in df.columns:
            if df[col].isnull().sum() == 0:
                continue
            
            missing_pct = (df[col].isnull().sum() / len(df)) * 100
            dtype = df[col].dtype
            
            # 基于缺失程度和数据类型推荐策略
            if missing_pct > 70:
                recommendations[col] = 'drop'  # 缺失过多，建议删除
            elif missing_pct > 30:
                if pd.api.types.is_numeric_dtype(dtype):
                    recommendations[col] = 'median'
                else:
                    recommendations[col] = 'mode'
            elif missing_pct > 10:
                if pd.api.types.is_numeric_dtype(dtype):
                    recommendations[col] = 'iterative'  # 迭代填充
                else:
                    recommendations[col] = 'knn'
            else:
                if pd.api.types.is_numeric_dtype(dtype):
                    recommendations[col] = 'mean'
                else:
                    recommendations[col] = 'mode'
        
        return recommendations


class AdvancedMissingHandler:
    """
    高级缺失值处理器
    提供多种智能缺失值处理策略
    """
    
    def __init__(self, random_state: int = 42):
        self.random_state = random_state
        self.imputers = {}
        self.strategy_performance = {}
        self.fitted = False
        
    def _create_simple_imputer(self, strategy: str, dtype: type) -> SimpleImputer:
        """
        创建简单填充器
        
        Args:
            strategy: 填充策略
            dtype: 数据类型
            
        Returns:
            SimpleImputer实例
        """
        if pd.api.types.is_numeric_dtype(dtype):
            valid_strategies = ['mean', 'median', 'most_frequent', 'constant']
        else:
            valid_strategies = ['most_frequent', 'constant']
        
        if strategy not in valid_strategies:
            strategy = 'most_frequent'
        
        return SimpleImputer(strategy=strategy)
    
    def _create_knn_imputer(self, n_neighbors: int = 5) -> KNNImputer:
        """
        创建KNN填充器
        
        Args:
            n_neighbors: 邻居数量
            
        Returns:
            KNNImputer实例
        """
        return KNNImputer(n_neighbors=n_neighbors)
    
    def _create_iterative_imputer(self, dtype: type) -> IterativeImputer:
        """
        创建迭代填充器
        
        Args:
            dtype: 数据类型
            
        Returns:
            IterativeImputer实例
        """
        if pd.api.types.is_numeric_dtype(dtype):
            estimator = RandomForestRegressor(n_estimators=10, random_state=self.random_state)
        else:
            estimator = RandomForestClassifier(n_estimators=10, random_state=self.random_state)
        
        return IterativeImputer(
            estimator=estimator,
            random_state=self.random_state,
            max_iter=10
        )
    
    def evaluate_strategy(
        self, 
        X: pd.DataFrame, 
        y: Optional[np.ndarray] = None,
        strategies: Optional[List[str]] = None,
        cv_folds: int = 3
    ) -> Dict[str, float]:
        """
        评估不同缺失值处理策略的效果
        
        Args:
            X: 特征数据
            y: 目标变量（可选）
            strategies: 要评估的策略列表
            cv_folds: 交叉验证折数
            
        Returns:
            策略评估结果
        """
        if strategies is None:
            strategies = ['mean', 'median', 'mode', 'knn', 'iterative']
        
        results = {}
        
        # 创建人工缺失值用于评估
        X_with_missing = X.copy()
        np.random.seed(self.random_state)
        
        # 随机引入5%的缺失值
        for col in X.columns:
            if pd.api.types.is_numeric_dtype(X[col]):
                mask = np.random.random(len(X)) < 0.05
                X_with_missing.loc[mask, col] = np.nan
        
        for strategy in strategies:
            try:
                # 应用策略
                X_filled = self._apply_strategy(X_with_missing, strategy)
                
                if y is not None:
                    # 使用机器学习模型评估
                    from sklearn.ensemble import RandomForestClassifier
                    from sklearn.model_selection import cross_val_score
                    
                    model = RandomForestClassifier(n_estimators=50, random_state=self.random_state)
                    scores = cross_val_score(model, X_filled, y, cv=cv_folds, scoring='accuracy')
                    results[strategy] = scores.mean()
                else:
                    # 使用重构误差评估
                    mse = mean_squared_error(
                        X.select_dtypes(include=[np.number]).fillna(0),
                        X_filled.select_dtypes(include=[np.number]).fillna(0)
                    )
                    results[strategy] = -mse  # 负值，越大越好
                    
            except Exception as e:
                logger.warning(f"策略 {strategy} 评估失败: {e}")
                results[strategy] = float('-inf')
        
        self.strategy_performance = results
        logger.info(f"策略评估完成: {results}")
        return results
    
    def _apply_strategy(self, X: pd.DataFrame, strategy: str) -> pd.DataFrame:
        """
        应用单个缺失值处理策略
        
        Args:
            X: 输入数据
            strategy: 处理策略
            
        Returns:
            处理后的数据
        """
        X_filled = X.copy()
        
        if strategy == 'drop':
            return X_filled.dropna()
        
        elif strategy in ['mean', 'median', 'mode', 'most_frequent']:
            for col in X.columns:
                if X[col].isnull().any():
                    if strategy == 'mode' or strategy == 'most_frequent':
                        imputer = SimpleImputer(strategy='most_frequent')
                    else:
                        if pd.api.types.is_numeric_dtype(X[col]):
                            imputer = SimpleImputer(strategy=strategy)
                        else:
                            imputer = SimpleImputer(strategy='most_frequent')
                    
                    X_filled[col] = imputer.fit_transform(X_filled[[col]]).ravel()
        
        elif strategy == 'knn':
            # 只对数值列使用KNN
            numeric_cols = X.select_dtypes(include=[np.number]).columns
            categorical_cols = X.select_dtypes(exclude=[np.number]).columns
            
            if len(numeric_cols) > 0:
                knn_imputer = KNNImputer(n_neighbors=5)
                X_filled[numeric_cols] = knn_imputer.fit_transform(X[numeric_cols])
            
            # 分类列使用众数填充
            if len(categorical_cols) > 0:
                for col in categorical_cols:
                    if X[col].isnull().any():
                        mode_imputer = SimpleImputer(strategy='most_frequent')
                        X_filled[col] = mode_imputer.fit_transform(X_filled[[col]]).ravel()
        
        elif strategy == 'iterative':
            # 使用迭代填充
            try:
                iterative_imputer = IterativeImputer(random_state=self.random_state)
                X_filled_values = iterative_imputer.fit_transform(X_filled)
                X_filled = pd.DataFrame(X_filled_values, columns=X.columns, index=X.index)
            except Exception as e:
                logger.warning(f"迭代填充失败，回退到中位数填充: {e}")
                return self._apply_strategy(X, 'median')
        
        return X_filled
    
    def fit_transform(
        self, 
        X: pd.DataFrame, 
        y: Optional[np.ndarray] = None,
        strategy: Union[str, Dict[str, str]] = 'auto'
    ) -> pd.DataFrame:
        """
        拟合并转换数据
        
        Args:
            X: 输入特征数据
            y: 目标变量（可选）
            strategy: 处理策略，可以是字符串或每列的策略字典
            
        Returns:
            处理后的数据
        """
        if X.isnull().sum().sum() == 0:
            logger.info("数据中无缺失值，无需处理")
            self.fitted = True
            return X.copy()
        
        logger.info(f"开始处理缺失值，策略: {strategy}")
        
        # 分析缺失值模式
        analyzer = MissingValueAnalyzer()
        analysis = analyzer.analyze_missing_patterns(X)
        
        logger.info(f"缺失值分析完成:")
        logger.info(f"  总缺失值: {analysis['total_missing']}")
        logger.info(f"  有缺失值的列: {len(analysis['columns_with_missing'])}")
        
        if strategy == 'auto':
            # 自动选择策略
            if y is not None:
                # 有监督情况，评估策略效果
                performance = self.evaluate_strategy(X, y)
                best_strategy = max(performance.keys(), key=lambda k: performance[k])
                logger.info(f"自动选择最佳策略: {best_strategy}")
                strategy = best_strategy
            else:
                # 无监督情况，使用推荐策略
                recommendations = analyzer.recommend_strategy(X)
                strategy = recommendations
        
        # 应用策略
        if isinstance(strategy, str):
            # 统一策略
            X_filled = self._apply_strategy(X, strategy)
        else:
            # 每列不同策略
            X_filled = X.copy()
            for col, col_strategy in strategy.items():
                if col in X.columns and X[col].isnull().any():
                    X_col_filled = self._apply_strategy(X[[col]], col_strategy)
                    X_filled[col] = X_col_filled[col]
        
        self.fitted = True
        
        # 验证处理结果
        remaining_missing = X_filled.isnull().sum().sum()
        if remaining_missing > 0:
            logger.warning(f"处理后仍有 {remaining_missing} 个缺失值")
            # 最后兜底处理
            for col in X_filled.columns:
                if X_filled[col].isnull().any():
                    if pd.api.types.is_numeric_dtype(X_filled[col]):
                        X_filled[col].fillna(X_filled[col].median(), inplace=True)
                    else:
                        X_filled[col].fillna(X_filled[col].mode().iloc[0] if not X_filled[col].mode().empty else 'unknown', inplace=True)
        
        logger.info("缺失值处理完成")
        return X_filled
    
    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        转换新数据
        
        Args:
            X: 新数据
            
        Returns:
            处理后的数据
        """
        if not self.fitted:
            raise ValueError("处理器尚未拟合，请先调用fit_transform")
        
        # 对新数据应用已保存的填充器
        X_filled = X.copy()
        
        for col, imputer in self.imputers.items():
            if col in X.columns and X[col].isnull().any():
                X_filled[col] = imputer.transform(X_filled[[col]]).ravel()
        
        return X_filled
    
    def get_missing_report(self, X_original: pd.DataFrame, X_filled: pd.DataFrame) -> Dict[str, Any]:
        """
        生成缺失值处理报告
        
        Args:
            X_original: 原始数据
            X_filled: 处理后数据
            
        Returns:
            处理报告
        """
        report = {
            'original_missing': X_original.isnull().sum().to_dict(),
            'filled_missing': X_filled.isnull().sum().to_dict(),
            'missing_reduction': {},
            'strategy_performance': self.strategy_performance,
            'processing_summary': {
                'total_original_missing': X_original.isnull().sum().sum(),
                'total_filled_missing': X_filled.isnull().sum().sum(),
                'columns_processed': len([col for col in X_original.columns if X_original[col].isnull().any()]),
                'success_rate': 1 - (X_filled.isnull().sum().sum() / max(X_original.isnull().sum().sum(), 1))
            }
        }
        
        # 计算每列的缺失值减少情况
        for col in X_original.columns:
            original_missing = X_original[col].isnull().sum()
            filled_missing = X_filled[col].isnull().sum()
            if original_missing > 0:
                reduction = (original_missing - filled_missing) / original_missing
                report['missing_reduction'][col] = reduction
        
        return report


def handle_missing_values_smart(
    X: pd.DataFrame,
    y: Optional[np.ndarray] = None,
    strategy: str = 'auto',
    evaluate_strategies: bool = True
) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    智能缺失值处理的便捷接口
    
    Args:
        X: 输入特征数据
        y: 目标变量（可选）
        strategy: 处理策略
        evaluate_strategies: 是否评估策略效果
        
    Returns:
        tuple: (处理后的数据, 处理报告)
    """
    handler = AdvancedMissingHandler()
    
    X_original = X.copy()
    X_filled = handler.fit_transform(X, y, strategy)
    
    report = handler.get_missing_report(X_original, X_filled)
    
    return X_filled, report
