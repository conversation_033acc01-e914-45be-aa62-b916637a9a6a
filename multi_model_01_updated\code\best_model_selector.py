#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最佳模型自动选择器
实现智能的模型选择逻辑，为二分类任务推荐最优模型
"""

import numpy as np
import pandas as pd
import json
from pathlib import Path
from joblib import load
from sklearn.metrics import accuracy_score, roc_auc_score, f1_score, precision_score, recall_score
from sklearn.metrics import matthews_corrcoef, cohen_kappa_score, average_precision_score

from config import CACHE_PATH, MODEL_DISPLAY_NAMES
from logger import get_logger

logger = get_logger(__name__)

class BestModelSelector:
    """
    最佳模型自动选择器
    """
    
    def __init__(self, strategy='balanced'):
        """
        初始化选择器
        
        Args:
            strategy: 选择策略 ('performance', 'robustness', 'balanced', 'interpretability')
        """
        self.strategy = strategy
        self.model_results = {}
        self.performance_metrics = {}
        
        # 不同策略的权重配置
        self.strategy_weights = {
            'performance': {
                'auc_roc': 0.3,
                'f1_score': 0.25,
                'accuracy': 0.2,
                'precision': 0.15,
                'mcc': 0.1
            },
            'robustness': {
                'mcc': 0.3,
                'balanced_accuracy': 0.25,
                'kappa': 0.2,
                'f1_score': 0.15,
                'auc_roc': 0.1
            },
            'balanced': {
                'auc_roc': 0.25,
                'f1_score': 0.2,
                'mcc': 0.2,
                'accuracy': 0.15,
                'precision': 0.1,
                'recall': 0.1
            },
            'interpretability': {
                'f1_score': 0.3,
                'accuracy': 0.25,
                'interpretability_score': 0.25,
                'auc_roc': 0.2
            }
        }
        
        # 模型可解释性评分（基于模型类型）
        self.interpretability_scores = {
            'DecisionTree': 1.0,
            'RandomForest': 0.8,
            'Logistic': 0.9,
            'NaiveBayes': 0.7,
            'KNN': 0.6,
            'SVM': 0.4,
            'XGBoost': 0.7,
            'LightGBM': 0.7,
            'CatBoost': 0.7,
            'NeuralNet': 0.3
        }
    
    def load_model_results(self, selected_models=None):
        """
        加载模型训练结果
        
        Args:
            selected_models: 指定要加载的模型列表，None表示加载所有
        """
        logger.info("加载模型训练结果...")
        
        if selected_models is None:
            # 自动发现所有可用的模型结果
            selected_models = []
            for model_name in MODEL_DISPLAY_NAMES.keys():
                cache_file = CACHE_PATH / f"{model_name}_results.joblib"
                if cache_file.exists():
                    selected_models.append(model_name)
        
        self.model_results = {}
        for model_name in selected_models:
            cache_file = CACHE_PATH / f"{model_name}_results.joblib"
            if cache_file.exists():
                try:
                    result = load(cache_file)
                    self.model_results[model_name] = result
                    logger.info(f"成功加载模型 {model_name} 的结果")
                except Exception as e:
                    logger.warning(f"加载模型 {model_name} 结果失败: {e}")
            else:
                logger.warning(f"模型 {model_name} 的缓存文件不存在: {cache_file}")
        
        logger.info(f"共加载了 {len(self.model_results)} 个模型的结果")
        return len(self.model_results) > 0
    
    def calculate_comprehensive_metrics(self):
        """
        计算所有模型的综合性能指标
        """
        logger.info("计算综合性能指标...")
        
        self.performance_metrics = {}
        
        for model_name, result in self.model_results.items():
            try:
                y_true = result['y_true']
                y_pred = result['y_pred']
                y_pred_proba = result.get('y_pred_proba', None)
                
                # 基础指标
                metrics = {
                    'accuracy': accuracy_score(y_true, y_pred),
                    'precision': precision_score(y_true, y_pred, zero_division=0),
                    'recall': recall_score(y_true, y_pred, zero_division=0),
                    'f1_score': f1_score(y_true, y_pred, zero_division=0),
                    'mcc': matthews_corrcoef(y_true, y_pred),
                    'kappa': cohen_kappa_score(y_true, y_pred)
                }
                
                # 概率相关指标
                if y_pred_proba is not None and len(y_pred_proba) == len(y_true):
                    metrics['auc_roc'] = roc_auc_score(y_true, y_pred_proba)
                    metrics['auc_pr'] = average_precision_score(y_true, y_pred_proba)
                else:
                    metrics['auc_roc'] = 0.5
                    metrics['auc_pr'] = 0.5
                
                # 混淆矩阵相关指标
                from sklearn.metrics import confusion_matrix
                tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
                
                metrics['specificity'] = tn / (tn + fp) if (tn + fp) > 0 else 0
                metrics['sensitivity'] = tp / (tp + fn) if (tp + fn) > 0 else 0
                metrics['balanced_accuracy'] = (metrics['sensitivity'] + metrics['specificity']) / 2
                
                # 可解释性评分
                metrics['interpretability_score'] = self.interpretability_scores.get(model_name, 0.5)
                
                self.performance_metrics[model_name] = metrics
                
            except Exception as e:
                logger.error(f"计算模型 {model_name} 指标失败: {e}")
        
        logger.info(f"成功计算了 {len(self.performance_metrics)} 个模型的性能指标")
    
    def calculate_composite_scores(self):
        """
        根据选择策略计算综合得分
        """
        logger.info(f"使用 {self.strategy} 策略计算综合得分...")
        
        weights = self.strategy_weights.get(self.strategy, self.strategy_weights['balanced'])
        
        composite_scores = {}
        for model_name, metrics in self.performance_metrics.items():
            score = 0
            weight_sum = 0
            
            for metric, weight in weights.items():
                if metric in metrics:
                    score += metrics[metric] * weight
                    weight_sum += weight
            
            # 归一化得分
            if weight_sum > 0:
                composite_scores[model_name] = score / weight_sum
            else:
                composite_scores[model_name] = 0
        
        return composite_scores
    
    def select_best_model(self, top_k=3):
        """
        选择最佳模型
        
        Args:
            top_k: 返回前k个最佳模型
            
        Returns:
            dict: 最佳模型选择结果
        """
        if not self.model_results:
            logger.error("没有可用的模型结果，请先加载模型结果")
            return None
        
        # 计算性能指标
        self.calculate_comprehensive_metrics()
        
        # 计算综合得分
        composite_scores = self.calculate_composite_scores()
        
        # 排序选择最佳模型
        sorted_models = sorted(composite_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 构建结果
        result = {
            'strategy': self.strategy,
            'best_model': sorted_models[0][0],
            'best_score': sorted_models[0][1],
            'top_models': sorted_models[:top_k],
            'all_scores': composite_scores,
            'detailed_metrics': self.performance_metrics,
            'selection_reasoning': self._generate_selection_reasoning(sorted_models[0])
        }
        
        logger.info(f"最佳模型选择完成，推荐模型: {result['best_model']} (得分: {result['best_score']:.4f})")
        
        return result
    
    def _generate_selection_reasoning(self, best_model_info):
        """
        生成模型选择的理由说明
        
        Args:
            best_model_info: (模型名称, 得分) 元组
            
        Returns:
            str: 选择理由
        """
        model_name, score = best_model_info
        metrics = self.performance_metrics[model_name]
        display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
        
        reasoning = f"推荐 {display_name} 作为最佳模型的理由：\n\n"
        
        # 根据策略生成不同的理由
        if self.strategy == 'performance':
            reasoning += f"1. 在性能优先策略下，该模型获得了最高的综合得分 {score:.4f}\n"
            reasoning += f"2. AUC-ROC: {metrics['auc_roc']:.4f}，表现出色的分类能力\n"
            reasoning += f"3. F1分数: {metrics['f1_score']:.4f}，在精确率和召回率之间取得良好平衡\n"
            
        elif self.strategy == 'robustness':
            reasoning += f"1. 在稳健性优先策略下，该模型获得了最高的综合得分 {score:.4f}\n"
            reasoning += f"2. 马修斯相关系数(MCC): {metrics['mcc']:.4f}，显示出良好的稳健性\n"
            reasoning += f"3. 平衡准确率: {metrics['balanced_accuracy']:.4f}，在不平衡数据上表现稳定\n"
            
        elif self.strategy == 'interpretability':
            reasoning += f"1. 在可解释性优先策略下，该模型获得了最高的综合得分 {score:.4f}\n"
            reasoning += f"2. 可解释性评分: {metrics['interpretability_score']:.2f}，模型决策过程相对透明\n"
            reasoning += f"3. 在保持可解释性的同时，F1分数达到 {metrics['f1_score']:.4f}\n"
            
        else:  # balanced
            reasoning += f"1. 在平衡策略下，该模型获得了最高的综合得分 {score:.4f}\n"
            reasoning += f"2. 在多个关键指标上表现均衡：\n"
            reasoning += f"   - AUC-ROC: {metrics['auc_roc']:.4f}\n"
            reasoning += f"   - F1分数: {metrics['f1_score']:.4f}\n"
            reasoning += f"   - MCC: {metrics['mcc']:.4f}\n"
        
        reasoning += f"\n4. 该模型在二分类任务中展现出了优秀的综合性能"
        
        return reasoning
    
    def save_selection_result(self, result, output_path=None):
        """
        保存模型选择结果
        
        Args:
            result: 选择结果
            output_path: 输出路径
        """
        if output_path is None:
            output_path = CACHE_PATH / "best_model_selection.json"
        
        # 转换numpy类型为Python原生类型
        def convert_numpy(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            return obj
        
        # 递归转换所有numpy类型
        def recursive_convert(obj):
            if isinstance(obj, dict):
                return {k: recursive_convert(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [recursive_convert(v) for v in obj]
            elif isinstance(obj, tuple):
                return tuple(recursive_convert(v) for v in obj)
            else:
                return convert_numpy(obj)
        
        result_serializable = recursive_convert(result)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result_serializable, f, ensure_ascii=False, indent=2)
        
        logger.info(f"模型选择结果已保存到: {output_path}")


def select_best_model_for_binary_classification(data_path=None, strategy='balanced', top_k=3):
    """
    为二分类任务自动选择最佳模型的便捷函数
    
    Args:
        data_path: 数据路径（用于日志记录）
        strategy: 选择策略
        top_k: 返回前k个最佳模型
        
    Returns:
        dict: 最佳模型选择结果
    """
    logger.info("开始自动选择最佳模型...")
    
    selector = BestModelSelector(strategy=strategy)
    
    # 加载模型结果
    if not selector.load_model_results():
        logger.error("没有找到可用的模型训练结果，请先训练模型")
        return None
    
    # 选择最佳模型
    result = selector.select_best_model(top_k=top_k)
    
    if result:
        # 保存结果
        selector.save_selection_result(result)
        
        # 输出推荐信息
        logger.info("=" * 60)
        logger.info("最佳模型推荐结果")
        logger.info("=" * 60)
        logger.info(f"推荐策略: {strategy}")
        logger.info(f"最佳模型: {MODEL_DISPLAY_NAMES.get(result['best_model'], result['best_model'])}")
        logger.info(f"综合得分: {result['best_score']:.4f}")
        logger.info("\n前三名模型:")
        for i, (model, score) in enumerate(result['top_models'], 1):
            display_name = MODEL_DISPLAY_NAMES.get(model, model)
            logger.info(f"  {i}. {display_name}: {score:.4f}")
        
        logger.info("\n" + result['selection_reasoning'])
        logger.info("=" * 60)
    
    return result


if __name__ == "__main__":
    # 测试最佳模型选择
    result = select_best_model_for_binary_classification(strategy='balanced')
    if result:
        print(f"推荐的最佳模型: {result['best_model']}")
        print(f"综合得分: {result['best_score']:.4f}")
    else:
        print("请先训练模型，然后再运行最佳模型选择")
