#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
替代全局变量使用，提供更优雅的配置和状态管理
"""

import threading
from typing import Any, Dict, Optional, Union
from pathlib import Path
import json
import pickle
from datetime import datetime

# 尝试导入logger
try:
    from logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class ConfigManager:
    """
    线程安全的配置管理器
    管理预处理器状态、模型配置等信息
    """
    
    def __init__(self):
        self._lock = threading.RLock()
        self._config = {}
        self._preprocessors = {}
        self._session_info = {}
        
    def set_config(self, key: str, value: Any) -> None:
        """
        设置配置项
        
        Args:
            key: 配置键
            value: 配置值
        """
        with self._lock:
            self._config[key] = value
            logger.debug(f"设置配置: {key} = {type(value).__name__}")
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        with self._lock:
            return self._config.get(key, default)
    
    def update_config(self, config_dict: Dict[str, Any]) -> None:
        """
        批量更新配置
        
        Args:
            config_dict: 配置字典
        """
        with self._lock:
            self._config.update(config_dict)
            logger.debug(f"批量更新配置: {list(config_dict.keys())}")
    
    def clear_config(self) -> None:
        """清空配置"""
        with self._lock:
            self._config.clear()
            logger.debug("清空所有配置")
    
    def register_preprocessor(self, name: str, preprocessor: Any) -> None:
        """
        注册预处理器
        
        Args:
            name: 预处理器名称
            preprocessor: 预处理器实例
        """
        with self._lock:
            self._preprocessors[name] = {
                'instance': preprocessor,
                'timestamp': datetime.now(),
                'type': type(preprocessor).__name__
            }
            logger.info(f"注册预处理器: {name} ({type(preprocessor).__name__})")
    
    def get_preprocessor(self, name: str) -> Optional[Any]:
        """
        获取预处理器
        
        Args:
            name: 预处理器名称
            
        Returns:
            预处理器实例或None
        """
        with self._lock:
            preprocessor_info = self._preprocessors.get(name)
            if preprocessor_info:
                return preprocessor_info['instance']
            return None
    
    def get_current_scaler(self) -> Optional[Any]:
        """
        获取当前使用的scaler（向后兼容接口）
        
        Returns:
            当前scaler或None
        """
        return self.get_preprocessor('current_scaler')
    
    def set_current_scaler(self, scaler: Any) -> None:
        """
        设置当前scaler
        
        Args:
            scaler: scaler实例
        """
        self.register_preprocessor('current_scaler', scaler)
    
    def list_preprocessors(self) -> Dict[str, Dict[str, Any]]:
        """
        列出所有预处理器
        
        Returns:
            预处理器信息字典
        """
        with self._lock:
            result = {}
            for name, info in self._preprocessors.items():
                result[name] = {
                    'type': info['type'],
                    'timestamp': info['timestamp'].isoformat(),
                    'available': info['instance'] is not None
                }
            return result
    
    def remove_preprocessor(self, name: str) -> bool:
        """
        移除预处理器
        
        Args:
            name: 预处理器名称
            
        Returns:
            是否成功移除
        """
        with self._lock:
            if name in self._preprocessors:
                del self._preprocessors[name]
                logger.info(f"移除预处理器: {name}")
                return True
            return False
    
    def set_session_info(self, key: str, value: Any) -> None:
        """
        设置会话信息
        
        Args:
            key: 会话信息键
            value: 会话信息值
        """
        with self._lock:
            self._session_info[key] = value
            logger.debug(f"设置会话信息: {key}")
    
    def get_session_info(self, key: str, default: Any = None) -> Any:
        """
        获取会话信息
        
        Args:
            key: 会话信息键
            default: 默认值
            
        Returns:
            会话信息值
        """
        with self._lock:
            return self._session_info.get(key, default)
    
    def save_config_to_file(self, file_path: Union[str, Path]) -> bool:
        """
        保存配置到文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否保存成功
        """
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with self._lock:
                # 只保存可序列化的配置项
                serializable_config = {}
                for key, value in self._config.items():
                    try:
                        json.dumps(value)  # 测试是否可序列化
                        serializable_config[key] = value
                    except (TypeError, ValueError):
                        logger.warning(f"配置项 {key} 不可序列化，跳过")
                
                # 保存预处理器信息（不包含实例）
                preprocessor_info = {}
                for name, info in self._preprocessors.items():
                    preprocessor_info[name] = {
                        'type': info['type'],
                        'timestamp': info['timestamp'].isoformat()
                    }
                
                save_data = {
                    'config': serializable_config,
                    'preprocessors': preprocessor_info,
                    'session_info': self._session_info,
                    'save_timestamp': datetime.now().isoformat()
                }
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, indent=2, ensure_ascii=False)
                
                logger.info(f"配置已保存到: {file_path}")
                return True
                
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            return False
    
    def load_config_from_file(self, file_path: Union[str, Path]) -> bool:
        """
        从文件加载配置
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否加载成功
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.warning(f"配置文件不存在: {file_path}")
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                load_data = json.load(f)
            
            with self._lock:
                # 加载配置
                if 'config' in load_data:
                    self._config.update(load_data['config'])
                
                # 加载会话信息
                if 'session_info' in load_data:
                    self._session_info.update(load_data['session_info'])
                
                logger.info(f"配置已从文件加载: {file_path}")
                return True
                
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return False
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """
        获取内存使用情况
        
        Returns:
            内存使用信息
        """
        import sys
        
        with self._lock:
            memory_info = {
                'config_items': len(self._config),
                'preprocessors': len(self._preprocessors),
                'session_items': len(self._session_info),
                'config_size_bytes': sys.getsizeof(self._config),
                'preprocessors_size_bytes': sys.getsizeof(self._preprocessors),
                'session_size_bytes': sys.getsizeof(self._session_info)
            }
            
            # 估算总内存使用
            total_size = (memory_info['config_size_bytes'] + 
                         memory_info['preprocessors_size_bytes'] + 
                         memory_info['session_size_bytes'])
            memory_info['total_size_mb'] = total_size / 1024 / 1024
            
            return memory_info
    
    def cleanup(self) -> None:
        """清理资源"""
        with self._lock:
            self._config.clear()
            self._preprocessors.clear()
            self._session_info.clear()
            logger.info("配置管理器资源已清理")
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取配置管理器状态
        
        Returns:
            状态信息字典
        """
        with self._lock:
            return {
                'config_items': len(self._config),
                'preprocessors': len(self._preprocessors),
                'session_items': len(self._session_info),
                'memory_usage': self.get_memory_usage(),
                'last_update': datetime.now().isoformat()
            }


class ContextManager:
    """
    上下文管理器
    提供临时配置和自动清理功能
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.original_config = {}
        
    def __enter__(self):
        # 保存当前配置
        with self.config_manager._lock:
            self.original_config = self.config_manager._config.copy()
        return self.config_manager
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 恢复原始配置
        with self.config_manager._lock:
            self.config_manager._config = self.original_config


# 全局配置管理器实例
global_config_manager = ConfigManager()


def get_config_manager() -> ConfigManager:
    """
    获取全局配置管理器实例
    
    Returns:
        配置管理器实例
    """
    return global_config_manager


def temporary_config() -> ContextManager:
    """
    创建临时配置上下文
    
    Returns:
        上下文管理器
    """
    return ContextManager(global_config_manager)


# 向后兼容的函数
def get_current_scaler():
    """
    获取当前使用的scaler（向后兼容接口）
    
    Returns:
        scaler实例或None
    """
    return global_config_manager.get_current_scaler()


def set_current_scaler(scaler):
    """
    设置当前scaler（向后兼容接口）
    
    Args:
        scaler: scaler实例
    """
    global_config_manager.set_current_scaler(scaler)


# 配置常量
class ConfigKeys:
    """配置键常量"""
    
    # 数据预处理相关
    SCALING_METHOD = 'scaling_method'
    TEST_SIZE = 'test_size'
    RANDOM_STATE = 'random_state'
    HANDLE_MISSING = 'handle_missing'
    MISSING_STRATEGY = 'missing_strategy'
    
    # 模型训练相关
    MODEL_TYPE = 'model_type'
    HYPERPARAMETER_TUNING = 'hyperparameter_tuning'
    CV_FOLDS = 'cv_folds'
    
    # 集成学习相关
    ENSEMBLE_METHOD = 'ensemble_method'
    ENSEMBLE_DATA_STRATEGY = 'ensemble_data_strategy'
    FEATURE_SELECTION = 'feature_selection'
    
    # 系统相关
    OUTPUT_PATH = 'output_path'
    CACHE_PATH = 'cache_path'
    LOG_LEVEL = 'log_level'
    N_JOBS = 'n_jobs'
    USE_GPU = 'use_gpu'


# 默认配置
DEFAULT_CONFIG = {
    ConfigKeys.SCALING_METHOD: 'standard',
    ConfigKeys.TEST_SIZE: 0.2,
    ConfigKeys.RANDOM_STATE: 42,
    ConfigKeys.HANDLE_MISSING: 'simple',
    ConfigKeys.MISSING_STRATEGY: 'median',
    ConfigKeys.CV_FOLDS: 5,
    ConfigKeys.ENSEMBLE_METHOD: 'voting',
    ConfigKeys.ENSEMBLE_DATA_STRATEGY: 'unified',
    ConfigKeys.FEATURE_SELECTION: True,
    ConfigKeys.N_JOBS: -1,
    ConfigKeys.USE_GPU: False
}


def initialize_default_config():
    """初始化默认配置"""
    global_config_manager.update_config(DEFAULT_CONFIG)
    logger.info("默认配置已初始化")


def save_current_config(file_path: Union[str, Path] = "config.json"):
    """
    保存当前配置到文件
    
    Args:
        file_path: 保存路径
    """
    global_config_manager.save_config_to_file(file_path)


def load_config(file_path: Union[str, Path] = "config.json"):
    """
    从文件加载配置
    
    Args:
        file_path: 配置文件路径
    """
    global_config_manager.load_config_from_file(file_path)


# 初始化默认配置
initialize_default_config()
