#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的融合模型选择器
实现基于性能和多样性的智能基模型选择策略
"""

import numpy as np
import pandas as pd
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, precision_score, recall_score
from sklearn.model_selection import cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, f_classif
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
import xgboost as xgb
import lightgbm as lgb
from catboost import CatBoostClassifier
from itertools import combinations
import warnings
warnings.filterwarnings('ignore')

from logger import get_logger
from model_training import MODEL_TRAINERS
from improved_diversity_analyzer import ImprovedDiversityAnalyzer
from model_performance_report import bootstrap_roc_auc_ci
from quantified_diversity_evaluator import QuantifiedDiversityEvaluator

logger = get_logger(__name__)

class EnhancedEnsembleSelector:
    """
    增强的融合模型选择器
    基于性能和多样性的智能基模型选择
    """
    
    def __init__(self, correlation_threshold=0.3, min_performance_threshold=0.6,
                 use_improved_diversity=True):
        """
        初始化选择器

        Args:
            correlation_threshold: 相关性阈值，低于此值认为模型具有良好多样性
            min_performance_threshold: 最低性能阈值，低于此值的模型将被排除
            use_improved_diversity: 是否使用改进的多样性分析
        """
        self.correlation_threshold = correlation_threshold
        self.min_performance_threshold = min_performance_threshold
        self.use_improved_diversity = use_improved_diversity
        self.model_results = {}
        self.diversity_matrix = None
        self.performance_scores = {}

        # 初始化改进的多样性分析器
        if use_improved_diversity:
            self.diversity_analyzer = ImprovedDiversityAnalyzer(
                correlation_threshold=correlation_threshold,
                adaptive_threshold=True
            )

        # 初始化量化多样性评估器
        self.quantified_evaluator = QuantifiedDiversityEvaluator()

        # 模型类型分类（用于多样性分析）
        self.model_types = {
            'linear': ['Logistic', 'SVM'],
            'tree': ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost'],
            'probabilistic': ['NaiveBayes'],
            'instance': ['KNN'],
            'neural': ['NeuralNet']
        }
    
    def evaluate_base_models(self, X_train, y_train, X_test, y_test, model_names=None):
        """
        评估基模型性能
        
        Args:
            X_train, y_train: 训练数据
            X_test, y_test: 测试数据
            model_names: 要评估的模型名称列表
            
        Returns:
            dict: 模型评估结果
        """
        if model_names is None:
            model_names = list(MODEL_TRAINERS.keys())
        
        logger.info(f"开始评估 {len(model_names)} 个基模型...")

        # 保存测试标签供后续使用
        self.current_y_test = y_test

        results = {}
        predictions = {}
        
        for model_name in model_names:
            try:
                logger.info(f"  评估模型: {model_name}")
                
                # 训练模型
                trainer = MODEL_TRAINERS[model_name]
                model = trainer.train_and_evaluate(X_train, y_train, X_test, y_test)
                
                # 获取预测结果
                y_pred = model.predict(X_test)
                y_pred_proba = None
                
                if hasattr(model, 'predict_proba'):
                    try:
                        y_pred_proba = model.predict_proba(X_test)[:, 1]
                    except:
                        pass
                elif hasattr(model, 'decision_function'):
                    try:
                        y_pred_proba = model.decision_function(X_test)
                    except:
                        pass
                
                # 计算性能指标
                metrics = {
                    'accuracy': accuracy_score(y_test, y_pred),
                    'f1_score': f1_score(y_test, y_pred, average='weighted'),
                    'precision': precision_score(y_test, y_pred, average='weighted', zero_division=0),
                    'recall': recall_score(y_test, y_pred, average='weighted', zero_division=0)
                }
                
                if y_pred_proba is not None:
                    try:
                        # 计算ROC AUC及其95%置信区间
                        auc_roc, ci_lower, ci_upper = bootstrap_roc_auc_ci(y_test, y_pred_proba)
                        metrics['auc_roc'] = auc_roc
                        if ci_lower is not None and ci_upper is not None:
                            metrics['auc_roc_ci'] = f"({ci_lower:.3f}, {ci_upper:.3f})"
                            metrics['auc_roc_ci_lower'] = ci_lower
                            metrics['auc_roc_ci_upper'] = ci_upper
                        else:
                            metrics['auc_roc_ci'] = "N/A"
                            metrics['auc_roc_ci_lower'] = None
                            metrics['auc_roc_ci_upper'] = None
                    except:
                        metrics['auc_roc'] = 0.5
                        metrics['auc_roc_ci'] = "N/A"
                        metrics['auc_roc_ci_lower'] = None
                        metrics['auc_roc_ci_upper'] = None
                else:
                    metrics['auc_roc'] = 0.5
                    metrics['auc_roc_ci'] = "N/A"
                    metrics['auc_roc_ci_lower'] = None
                    metrics['auc_roc_ci_upper'] = None
                
                # 计算综合性能得分
                performance_score = (
                    metrics['accuracy'] * 0.25 +
                    metrics['f1_score'] * 0.25 +
                    metrics['auc_roc'] * 0.25 +
                    metrics['precision'] * 0.125 +
                    metrics['recall'] * 0.125
                )
                
                results[model_name] = {
                    'model': model,
                    'metrics': metrics,
                    'performance_score': performance_score,
                    'predictions': y_pred,
                    'probabilities': y_pred_proba
                }
                
                predictions[model_name] = y_pred
                self.performance_scores[model_name] = performance_score
                
                logger.info(f"    {model_name} - 性能得分: {performance_score:.4f}")
                
            except Exception as e:
                logger.error(f"评估模型 {model_name} 失败: {e}")
                continue
        
        self.model_results = results

        # 使用改进的多样性分析或传统方法
        if self.use_improved_diversity:
            self._calculate_improved_diversity_matrix(results, X_test, y_test)
        else:
            self._calculate_diversity_matrix(predictions)

        # 进行量化多样性评估
        self._perform_quantified_diversity_evaluation(predictions, y_test)

        return results
    
    def _calculate_diversity_matrix(self, predictions):
        """
        计算模型多样性矩阵（基于预测结果相关性）
        
        Args:
            predictions: 模型预测结果字典
        """
        model_names = list(predictions.keys())
        n_models = len(model_names)
        
        if n_models < 2:
            self.diversity_matrix = np.array([[1.0]])
            return
        
        # 构建预测矩阵
        pred_matrix = np.array([predictions[name] for name in model_names])
        
        # 计算相关性矩阵
        correlation_matrix = np.corrcoef(pred_matrix)
        
        # 多样性 = 1 - 相关性
        self.diversity_matrix = 1 - np.abs(correlation_matrix)
        
        logger.info("模型多样性矩阵计算完成")
        
        # 打印多样性信息
        for i, name1 in enumerate(model_names):
            for j, name2 in enumerate(model_names):
                if i < j:
                    diversity = self.diversity_matrix[i, j]
                    correlation = 1 - diversity
                    logger.info(f"  {name1} vs {name2}: 相关性={correlation:.3f}, 多样性={diversity:.3f}")

    def _calculate_improved_diversity_matrix(self, model_results, X_test, y_test):
        """
        使用改进的多样性分析计算多样性矩阵

        Args:
            model_results: 模型结果字典
            X_test: 测试特征
            y_test: 测试标签
        """
        if not self.use_improved_diversity or not hasattr(self, 'diversity_analyzer'):
            logger.warning("改进的多样性分析器未初始化，回退到传统方法")
            predictions = {name: result['predictions'] for name, result in model_results.items()}
            self._calculate_diversity_matrix(predictions)
            return

        # 提取模型对象
        models = {name: result['model'] for name, result in model_results.items()}

        # 进行综合多样性分析
        diversity_analysis = self.diversity_analyzer.comprehensive_diversity_analysis(
            models, X_test, y_test
        )

        # 更新多样性矩阵
        pred_diversity = diversity_analysis['prediction_diversity']
        self.diversity_matrix = pred_diversity['diversity_matrix']

        # 更新相关性阈值
        self.correlation_threshold = diversity_analysis['adjusted_threshold']

        # 存储详细分析结果
        self.detailed_diversity_analysis = diversity_analysis

        logger.info("使用改进的多样性分析完成")
        logger.info(f"数据复杂度: {diversity_analysis['data_complexity']:.3f}")
        logger.info(f"调整后相关性阈值: {diversity_analysis['adjusted_threshold']:.3f}")
        logger.info(f"综合多样性得分: {diversity_analysis['comprehensive_score']:.3f}")

        # 打印详细的多样性信息
        model_names = pred_diversity['model_names']
        correlation_matrix = pred_diversity['correlation_matrix']

        for i, name1 in enumerate(model_names):
            for j, name2 in enumerate(model_names):
                if i < j:
                    diversity = self.diversity_matrix[i, j]
                    correlation = correlation_matrix[i, j]
                    logger.info(f"  {name1} vs {name2}: 相关性={correlation:.3f}, 多样性={diversity:.3f}")

        # 显示特征重要性多样性（如果可用）
        importance_diversity = diversity_analysis['importance_diversity']
        if importance_diversity['avg_diversity'] > 0:
            logger.info(f"特征重要性多样性: {importance_diversity['avg_diversity']:.3f}")

        # 显示算法类型多样性
        type_diversity = diversity_analysis['algorithm_type_diversity']
        logger.info(f"算法类型多样性: {type_diversity['type_diversity']:.3f}")
        logger.info(f"算法类型分布: {type_diversity['type_counts']}")

    def _perform_quantified_diversity_evaluation(self, predictions, y_test):
        """
        执行量化多样性评估

        Args:
            predictions: 模型预测结果字典
            y_test: 测试标签
        """
        try:
            logger.info("开始量化多样性评估...")

            # 计算量化多样性指标
            quantified_results = self.quantified_evaluator.calculate_pairwise_diversity(
                y_test, predictions
            )

            # 存储量化评估结果
            self.quantified_diversity_results = quantified_results

            if quantified_results:
                overall_stats = quantified_results['overall_statistics']
                logger.info(f"量化多样性评估完成:")
                logger.info(f"  Q统计量平均多样性: {overall_stats['mean_diversity']:.3f}")
                logger.info(f"  多样性变异系数: {overall_stats['std_diversity']/overall_stats['mean_diversity']:.3f}")

                # 计算熵多样性
                entropy_diversity = self.quantified_evaluator.calculate_entropy_measure(predictions)
                logger.info(f"  熵多样性: {entropy_diversity:.3f}")

                # 存储熵多样性结果
                self.entropy_diversity = entropy_diversity

        except Exception as e:
            logger.error(f"量化多样性评估失败: {e}")
            self.quantified_diversity_results = None
    
    def calculate_type_diversity(self, model_names):
        """
        计算基于算法类型的多样性得分
        
        Args:
            model_names: 模型名称列表
            
        Returns:
            float: 类型多样性得分
        """
        type_counts = {}
        
        for model_name in model_names:
            for model_type, models in self.model_types.items():
                if model_name in models:
                    type_counts[model_type] = type_counts.get(model_type, 0) + 1
                    break
        
        # 计算类型多样性（不同类型数量 / 总模型数量）
        n_types = len(type_counts)
        n_models = len(model_names)
        
        if n_models <= 1:
            return 0.0
        
        # 基础多样性得分
        base_diversity = n_types / min(n_models, len(self.model_types))
        
        # 平衡性惩罚（避免某一类型模型过多）
        balance_penalty = 0
        for count in type_counts.values():
            if count > n_models // 2:  # 如果某类型超过一半
                balance_penalty += (count - n_models // 2) * 0.1
        
        diversity_score = max(0, base_diversity - balance_penalty)

        return diversity_score

    def select_optimal_ensemble(self, target_size=3, strategy='balanced', use_quantified=True):
        """
        选择最优的基模型组合

        Args:
            target_size: 目标集成模型数量
            strategy: 选择策略 ('performance', 'diversity', 'balanced', 'quantified')
            use_quantified: 是否使用量化多样性评估

        Returns:
            dict: 最优模型组合结果
        """
        if not self.model_results:
            logger.error("请先运行 evaluate_base_models")
            return None

        model_names = list(self.model_results.keys())

        # 过滤低性能模型
        qualified_models = []
        for name in model_names:
            if self.performance_scores[name] >= self.min_performance_threshold:
                qualified_models.append(name)

        if len(qualified_models) < target_size:
            logger.warning(f"合格模型数量({len(qualified_models)})少于目标数量({target_size})")
            target_size = len(qualified_models)

        if target_size < 2:
            logger.error("至少需要2个模型进行集成")
            return None

        logger.info(f"从 {len(qualified_models)} 个合格模型中选择 {target_size} 个进行集成")

        # 如果使用量化评估策略
        if strategy == 'quantified' and use_quantified and hasattr(self, 'quantified_diversity_results'):
            return self._select_with_quantified_evaluation(qualified_models, target_size)

        best_combination = None
        best_score = -1

        # 遍历所有可能的组合
        for combination in combinations(qualified_models, target_size):
            score = self._evaluate_combination(combination, strategy)

            if score > best_score:
                best_score = score
                best_combination = combination

        # 计算最优组合的详细信息
        result = self._analyze_combination(best_combination, strategy)
        result['selection_strategy'] = strategy
        result['target_size'] = target_size

        # 添加量化多样性分析（如果可用）
        if use_quantified and hasattr(self, 'quantified_diversity_results'):
            result['quantified_analysis'] = self._add_quantified_analysis(best_combination)

        logger.info(f"最优组合: {list(best_combination)}")
        logger.info(f"综合得分: {best_score:.4f}")

        return result

    def _evaluate_combination(self, combination, strategy):
        """
        评估模型组合的综合得分

        Args:
            combination: 模型组合
            strategy: 评估策略

        Returns:
            float: 综合得分
        """
        # 性能得分（平均性能）
        performance_scores = [self.performance_scores[name] for name in combination]
        avg_performance = np.mean(performance_scores)

        # 多样性得分
        diversity_score = self._calculate_combination_diversity(combination)

        # 类型多样性得分
        type_diversity = self.calculate_type_diversity(combination)

        # 根据策略计算综合得分
        if strategy == 'performance':
            final_score = avg_performance
        elif strategy == 'diversity':
            final_score = 0.5 * diversity_score + 0.5 * type_diversity
        else:  # balanced
            final_score = (
                0.5 * avg_performance +
                0.3 * diversity_score +
                0.2 * type_diversity
            )

        return final_score

    def _calculate_combination_diversity(self, combination):
        """
        计算组合的多样性得分

        Args:
            combination: 模型组合

        Returns:
            float: 多样性得分
        """
        if self.diversity_matrix is None or len(combination) < 2:
            return 0.0

        model_names = list(self.model_results.keys())
        indices = [model_names.index(name) for name in combination]

        # 计算组合内模型间的平均多样性
        diversities = []
        for i in range(len(indices)):
            for j in range(i + 1, len(indices)):
                diversities.append(self.diversity_matrix[indices[i], indices[j]])

        return np.mean(diversities) if diversities else 0.0

    def _analyze_combination(self, combination, strategy):
        """
        分析最优组合的详细信息

        Args:
            combination: 最优模型组合
            strategy: 选择策略

        Returns:
            dict: 详细分析结果
        """
        result = {
            'selected_models': list(combination),
            'model_details': {},
            'combination_metrics': {},
            'diversity_analysis': {},
            'recommendations': []
        }

        # 模型详细信息
        for model_name in combination:
            model_info = self.model_results[model_name]
            result['model_details'][model_name] = {
                'performance_score': model_info['performance_score'],
                'metrics': model_info['metrics']
            }

        # 组合指标
        performance_scores = [self.performance_scores[name] for name in combination]
        result['combination_metrics'] = {
            'avg_performance': np.mean(performance_scores),
            'min_performance': np.min(performance_scores),
            'max_performance': np.max(performance_scores),
            'performance_std': np.std(performance_scores),
            'diversity_score': self._calculate_combination_diversity(combination),
            'type_diversity': self.calculate_type_diversity(combination)
        }

        # 多样性分析
        model_names = list(self.model_results.keys())
        for i, name1 in enumerate(combination):
            for j, name2 in enumerate(combination):
                if i < j:
                    idx1 = model_names.index(name1)
                    idx2 = model_names.index(name2)
                    correlation = 1 - self.diversity_matrix[idx1, idx2]

                    result['diversity_analysis'][f'{name1}_vs_{name2}'] = {
                        'correlation': correlation,
                        'diversity': self.diversity_matrix[idx1, idx2],
                        'is_diverse': correlation < self.correlation_threshold
                    }

        # 生成建议
        result['recommendations'] = self._generate_recommendations(combination, result)

        return result

    def calculate_optimal_weights(self, combination, method='performance_diversity'):
        """
        计算最优融合权重

        Args:
            combination: 模型组合
            method: 权重计算方法 ('equal', 'performance', 'diversity', 'performance_diversity')

        Returns:
            dict: 模型权重
        """
        n_models = len(combination)
        weights = {}

        if method == 'equal':
            # 等权重
            for model_name in combination:
                weights[model_name] = 1.0 / n_models

        elif method == 'performance':
            # 基于性能的权重
            performance_scores = [self.performance_scores[name] for name in combination]
            total_performance = sum(performance_scores)

            for i, model_name in enumerate(combination):
                weights[model_name] = performance_scores[i] / total_performance

        elif method == 'diversity':
            # 基于多样性的权重（多样性高的模型权重大）
            diversity_scores = []
            model_names = list(self.model_results.keys())

            for model_name in combination:
                idx = model_names.index(model_name)
                # 计算该模型与其他模型的平均多样性
                other_indices = [model_names.index(other) for other in combination if other != model_name]
                if other_indices:
                    avg_diversity = np.mean([self.diversity_matrix[idx, other_idx] for other_idx in other_indices])
                else:
                    avg_diversity = 1.0
                diversity_scores.append(avg_diversity)

            total_diversity = sum(diversity_scores)
            for i, model_name in enumerate(combination):
                weights[model_name] = diversity_scores[i] / total_diversity if total_diversity > 0 else 1.0 / n_models

        else:  # performance_diversity
            # 性能和多样性的平衡权重
            performance_weights = self.calculate_optimal_weights(combination, 'performance')
            diversity_weights = self.calculate_optimal_weights(combination, 'diversity')

            for model_name in combination:
                weights[model_name] = 0.7 * performance_weights[model_name] + 0.3 * diversity_weights[model_name]

        # 归一化权重
        total_weight = sum(weights.values())
        if total_weight > 0:
            for model_name in weights:
                weights[model_name] /= total_weight

        return weights

    def create_enhanced_training_data(self, X_train, y_train, n_variants=3,
                                    feature_subset_ratio=0.8, sample_ratio=0.8):
        """
        创建增强的训练数据变体以提高模型多样性

        Args:
            X_train, y_train: 原始训练数据
            n_variants: 创建的数据变体数量
            feature_subset_ratio: 特征子集比例
            sample_ratio: 样本子集比例

        Returns:
            list: 数据变体列表
        """
        variants = []
        n_samples, n_features = X_train.shape

        for i in range(n_variants):
            # Bootstrap采样
            sample_indices = np.random.choice(n_samples,
                                            size=int(n_samples * sample_ratio),
                                            replace=True)

            # 特征子集选择
            feature_indices = np.random.choice(n_features,
                                             size=int(n_features * feature_subset_ratio),
                                             replace=False)

            X_variant = X_train[sample_indices][:, feature_indices]
            y_variant = y_train[sample_indices]

            variants.append({
                'X': X_variant,
                'y': y_variant,
                'sample_indices': sample_indices,
                'feature_indices': feature_indices,
                'variant_id': i
            })

        logger.info(f"创建了 {n_variants} 个数据变体用于增强模型多样性")
        return variants

    def _generate_recommendations(self, combination, analysis_result):
        """
        生成模型组合建议

        Args:
            combination: 模型组合
            analysis_result: 分析结果

        Returns:
            list: 建议列表
        """
        recommendations = []

        # 性能建议
        metrics = analysis_result['combination_metrics']
        if metrics['performance_std'] > 0.1:
            recommendations.append("模型性能差异较大，建议使用基于性能的加权融合")

        # 多样性建议
        if metrics['diversity_score'] < 0.3:
            recommendations.append("模型多样性较低，建议考虑添加不同类型的算法")

        # 相关性建议
        high_correlation_pairs = []
        for pair_name, pair_info in analysis_result['diversity_analysis'].items():
            if not pair_info['is_diverse']:
                high_correlation_pairs.append(pair_name)

        if high_correlation_pairs:
            recommendations.append(f"以下模型对相关性过高: {', '.join(high_correlation_pairs)}")

        # 类型多样性建议
        if metrics['type_diversity'] < 0.5:
            recommendations.append("建议增加不同类型的算法以提高多样性")

        # 融合策略建议
        if metrics['avg_performance'] > 0.8 and metrics['diversity_score'] > 0.4:
            recommendations.append("推荐使用加权平均融合策略")
        elif metrics['diversity_score'] > 0.6:
            recommendations.append("推荐使用投票法融合策略")
        else:
            recommendations.append("推荐使用堆叠法融合策略")

        return recommendations

    def generate_selection_report(self, result):
        """
        生成模型选择报告

        Args:
            result: 选择结果

        Returns:
            str: 格式化的报告
        """
        if not result:
            return "无可用结果"

        report = []
        report.append("=" * 60)
        report.append("融合模型基模型选择报告")
        report.append("=" * 60)

        # 基本信息
        report.append(f"\n选择策略: {result['selection_strategy']}")
        report.append(f"目标模型数量: {result['target_size']}")
        report.append(f"选中模型: {', '.join(result['selected_models'])}")

        # 组合指标
        metrics = result['combination_metrics']
        report.append(f"\n组合性能指标:")
        report.append(f"  平均性能: {metrics['avg_performance']:.4f}")
        report.append(f"  性能标准差: {metrics['performance_std']:.4f}")
        report.append(f"  多样性得分: {metrics['diversity_score']:.4f}")
        report.append(f"  类型多样性: {metrics['type_diversity']:.4f}")

        # 模型详情
        report.append(f"\n各模型详细信息:")
        for model_name, details in result['model_details'].items():
            report.append(f"  {model_name}:")
            report.append(f"    性能得分: {details['performance_score']:.4f}")
            report.append(f"    准确率: {details['metrics']['accuracy']:.4f}")
            report.append(f"    F1分数: {details['metrics']['f1_score']:.4f}")
            report.append(f"    AUC: {details['metrics']['auc_roc']:.4f}")

        # 多样性分析
        report.append(f"\n模型间多样性分析:")
        for pair_name, pair_info in result['diversity_analysis'].items():
            status = "✓" if pair_info['is_diverse'] else "✗"
            report.append(f"  {pair_name}: 相关性={pair_info['correlation']:.3f} {status}")

        # 建议
        if result['recommendations']:
            report.append(f"\n优化建议:")
            for i, rec in enumerate(result['recommendations'], 1):
                report.append(f"  {i}. {rec}")

        return "\n".join(report)

    def _select_with_quantified_evaluation(self, qualified_models, target_size):
        """
        使用量化多样性评估选择最优组合

        Args:
            qualified_models: 合格的模型列表
            target_size: 目标模型数量

        Returns:
            dict: 最优组合结果
        """
        logger.info("使用量化多样性评估进行模型选择...")

        # 获取预测结果
        predictions = {name: self.model_results[name]['predictions'] for name in qualified_models}

        # 尝试从类属性中获取测试标签
        y_test = getattr(self, 'current_y_test', None)

        # 如果没有，尝试从模型结果中获取
        if y_test is None:
            for result in self.model_results.values():
                if 'y_test' in result:
                    y_test = result['y_test']
                    break

        if y_test is None:
            logger.error("无法获取测试标签，回退到传统方法")
            # 回退到传统的balanced策略，避免递归
            best_combination = None
            best_score = -1

            # 遍历所有可能的组合
            from itertools import combinations
            for combination in combinations(qualified_models, target_size):
                score = self._evaluate_combination(combination, 'balanced')

                if score > best_score:
                    best_score = score
                    best_combination = combination

            # 计算最优组合的详细信息
            result = self._analyze_combination(best_combination, 'balanced')
            result['selection_strategy'] = 'balanced_fallback'
            result['target_size'] = target_size

            logger.info(f"回退方法选择的最优组合: {list(best_combination)}")
            return result

        # 生成所有可能的组合
        from itertools import combinations
        all_combinations = list(combinations(qualified_models, target_size))

        # 使用量化评估器排序组合
        ranked_combinations = self.quantified_evaluator.rank_model_combinations(
            y_test, predictions, self.performance_scores, all_combinations,
            diversity_weight=0.5  # 平衡性能和多样性
        )

        if not ranked_combinations:
            logger.error("量化评估失败，回退到传统方法")
            # 使用相同的回退逻辑
            best_combination = None
            best_score = -1

            from itertools import combinations
            for combination in combinations(qualified_models, target_size):
                score = self._evaluate_combination(combination, 'balanced')

                if score > best_score:
                    best_score = score
                    best_combination = combination

            result = self._analyze_combination(best_combination, 'balanced')
            result['selection_strategy'] = 'quantified_fallback'
            result['target_size'] = target_size

            logger.info(f"量化评估回退方法选择的最优组合: {list(best_combination)}")
            return result

        # 选择最优组合
        best_evaluation = ranked_combinations[0]
        best_combination = best_evaluation['combination']

        # 构建结果
        result = {
            'selected_models': list(best_combination),
            'selection_strategy': 'quantified',
            'target_size': target_size,
            'quantified_evaluation': best_evaluation,
            'all_evaluations': ranked_combinations[:5],  # 保存前5个组合
            'model_details': {},
            'combination_metrics': {},
            'diversity_analysis': {},
            'recommendations': []
        }

        # 添加模型详细信息
        for model_name in best_combination:
            model_info = self.model_results[model_name]
            result['model_details'][model_name] = {
                'performance_score': model_info['performance_score'],
                'metrics': model_info['metrics']
            }

        # 添加组合指标
        result['combination_metrics'] = {
            'avg_performance': best_evaluation['performance_score'],
            'performance_std': best_evaluation.get('performance_std', 0.0),
            'quantified_diversity_score': best_evaluation['diversity_score'],
            'comprehensive_score': best_evaluation['comprehensive_score'],
            'diversity_level': best_evaluation['diversity_level'],
            'diversity_description': best_evaluation['diversity_description'],
            'diversity_score': best_evaluation['diversity_score'],  # 保持兼容性
            'type_diversity': self.calculate_type_diversity(best_combination)
        }

        # 添加多样性分析
        result['diversity_analysis'] = self._generate_diversity_analysis_for_quantified(best_combination)

        # 生成建议
        result['recommendations'] = self._generate_quantified_recommendations(best_evaluation)

        # 导出多样性指标详细表格
        try:
            exported_files = self.quantified_evaluator.export_diversity_tables(
                y_test, predictions, self.performance_scores, all_combinations,
                diversity_weight=0.5, export_dir=None  # 使用默认导出目录
            )
            result['exported_files'] = exported_files
            logger.info("多样性指标详细表格导出成功")
        except Exception as e:
            logger.warning(f"多样性指标表格导出失败: {e}")
            result['exported_files'] = None

        logger.info(f"量化评估选择完成:")
        logger.info(f"  最优组合: {list(best_combination)}")
        logger.info(f"  性能得分: {best_evaluation['performance_score']:.4f}")
        logger.info(f"  多样性得分: {best_evaluation['diversity_score']:.4f}")
        logger.info(f"  综合得分: {best_evaluation['comprehensive_score']:.4f}")
        logger.info(f"  多样性等级: {best_evaluation['diversity_description']}")

        return result

    def export_quantified_diversity_analysis(self, qualified_models, target_size, export_dir=None):
        """
        专门用于导出quantified策略的多样性分析表格

        Args:
            qualified_models: 合格的模型列表
            target_size: 目标模型数量
            export_dir: 自定义导出目录路径

        Returns:
            dict: 导出文件信息
        """
        logger.info("开始导出quantified策略多样性分析表格...")

        # 获取预测结果
        predictions = {name: self.model_results[name]['predictions'] for name in qualified_models}

        # 获取测试标签
        y_test = getattr(self, 'current_y_test', None)
        if y_test is None:
            for result in self.model_results.values():
                if 'y_test' in result:
                    y_test = result['y_test']
                    break

        if y_test is None:
            logger.error("无法获取测试标签，无法导出多样性分析")
            return None

        # 生成所有可能的组合
        from itertools import combinations
        all_combinations = list(combinations(qualified_models, target_size))

        # 导出多样性指标详细表格
        try:
            exported_files = self.quantified_evaluator.export_diversity_tables(
                y_test, predictions, self.performance_scores, all_combinations,
                diversity_weight=0.5, export_dir=export_dir
            )

            logger.info("quantified策略多样性分析表格导出完成")
            return exported_files

        except Exception as e:
            logger.error(f"多样性分析表格导出失败: {e}")
            return None

    def _generate_diversity_analysis_for_quantified(self, combination):
        """
        为量化策略生成多样性分析

        Args:
            combination: 模型组合

        Returns:
            dict: 多样性分析结果
        """
        diversity_analysis = {}

        # 获取模型名称列表
        model_names = list(self.model_results.keys())

        # 计算组合内模型对的多样性分析
        for i, name1 in enumerate(combination):
            for j, name2 in enumerate(combination):
                if i < j:
                    # 获取模型在列表中的索引
                    try:
                        idx1 = model_names.index(name1)
                        idx2 = model_names.index(name2)

                        # 计算相关性和多样性
                        if hasattr(self, 'diversity_matrix') and self.diversity_matrix is not None:
                            diversity = self.diversity_matrix[idx1, idx2]
                            correlation = 1 - diversity
                        else:
                            # 如果没有多样性矩阵，使用默认值
                            correlation = 0.5
                            diversity = 0.5

                        diversity_analysis[f'{name1}_vs_{name2}'] = {
                            'correlation': correlation,
                            'diversity': diversity,
                            'is_diverse': correlation < self.correlation_threshold
                        }

                    except (ValueError, IndexError, AttributeError) as e:
                        # 如果出现错误，使用默认值
                        diversity_analysis[f'{name1}_vs_{name2}'] = {
                            'correlation': 0.5,
                            'diversity': 0.5,
                            'is_diverse': True
                        }

        return diversity_analysis

    def _add_quantified_analysis(self, combination):
        """
        为选定的组合添加量化多样性分析

        Args:
            combination: 模型组合

        Returns:
            dict: 量化分析结果
        """
        if not hasattr(self, 'quantified_diversity_results'):
            return {}

        # 提取组合相关的量化结果
        quantified_analysis = {
            'q_statistics': {},
            'disagreement_measures': {},
            'double_fault_measures': {},
            'correlation_coefficients': {},
            'comprehensive_diversities': {}
        }

        # 获取组合内模型对的量化指标
        for i, model1 in enumerate(combination):
            for j, model2 in enumerate(combination):
                if i < j:
                    pair_name = f"{model1}_vs_{model2}"

                    if pair_name in self.quantified_diversity_results['diversity_summary']:
                        summary = self.quantified_diversity_results['diversity_summary'][pair_name]

                        quantified_analysis['q_statistics'][pair_name] = summary['q_statistic']
                        quantified_analysis['disagreement_measures'][pair_name] = summary['disagreement']
                        quantified_analysis['double_fault_measures'][pair_name] = summary['double_fault']
                        quantified_analysis['correlation_coefficients'][pair_name] = summary['correlation']
                        quantified_analysis['comprehensive_diversities'][pair_name] = summary['comprehensive_diversity']

        # 计算组合的量化统计
        if quantified_analysis['comprehensive_diversities']:
            diversities = list(quantified_analysis['comprehensive_diversities'].values())
            quantified_analysis['combination_stats'] = {
                'mean_diversity': np.mean(diversities),
                'min_diversity': np.min(diversities),
                'max_diversity': np.max(diversities),
                'std_diversity': np.std(diversities)
            }

        return quantified_analysis

    def _generate_quantified_recommendations(self, evaluation):
        """
        基于量化评估结果生成建议

        Args:
            evaluation: 量化评估结果

        Returns:
            list: 建议列表
        """
        recommendations = []

        diversity_score = evaluation['diversity_score']
        performance_score = evaluation['performance_score']

        # 基于多样性等级的建议
        if evaluation['diversity_level'] == 'excellent':
            recommendations.append("多样性优秀，推荐使用简单的投票法或平均法融合")
        elif evaluation['diversity_level'] == 'good':
            recommendations.append("多样性良好，推荐使用加权平均融合策略")
        elif evaluation['diversity_level'] == 'moderate':
            recommendations.append("多样性中等，建议使用堆叠法或更复杂的融合策略")
        else:
            recommendations.append("多样性较差，建议重新选择模型或增加数据扰动")

        # 基于性能-多样性平衡的建议
        if performance_score > 0.8 and diversity_score > 0.6:
            recommendations.append("性能和多样性都很好，这是理想的模型组合")
        elif performance_score > 0.8 and diversity_score < 0.4:
            recommendations.append("性能高但多样性低，考虑牺牲部分性能换取更好的多样性")
        elif performance_score < 0.6 and diversity_score > 0.6:
            recommendations.append("多样性好但性能偏低，建议提高基模型质量")

        # 基于量化指标的具体建议
        detailed_diversity = evaluation.get('detailed_diversity', {})
        if 'detailed_results' in detailed_diversity:
            diversity_summary = detailed_diversity['detailed_results'].get('diversity_summary', {})

            # 检查是否有高相关性的模型对
            high_correlation_pairs = []
            for pair_name, summary in diversity_summary.items():
                if abs(summary['correlation']) > 0.8:
                    high_correlation_pairs.append(pair_name)

            if high_correlation_pairs:
                recommendations.append(f"发现高相关性模型对: {', '.join(high_correlation_pairs)}")
                recommendations.append("建议考虑替换其中一个模型以提高多样性")

        return recommendations
