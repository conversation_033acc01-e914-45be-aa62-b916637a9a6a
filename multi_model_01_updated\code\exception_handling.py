#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强异常处理机制
提供统一的异常处理、错误恢复和日志记录功能
"""

import functools
import traceback
import sys
import logging
from typing import Callable, Any, Optional, Dict, Union
from contextlib import contextmanager
import warnings

# 尝试导入项目logger
try:
    from logger import get_logger
    default_logger = get_logger(__name__)
except ImportError:
    default_logger = logging.getLogger(__name__)


class ModelTrainingError(Exception):
    """模型训练相关错误"""
    pass


class DataProcessingError(Exception):
    """数据处理相关错误"""
    pass


class EnsembleError(Exception):
    """集成学习相关错误"""
    pass


class ValidationError(Exception):
    """验证相关错误"""
    pass


class ExceptionHandler:
    """
    统一异常处理器
    提供异常捕获、记录、恢复策略
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or default_logger
        self.error_count = 0
        self.max_retries = 3
        self.recovery_strategies = {}
        
    def register_recovery_strategy(self, exception_type: type, strategy: Callable):
        """
        注册异常恢复策略
        
        Args:
            exception_type: 异常类型
            strategy: 恢复策略函数
        """
        self.recovery_strategies[exception_type] = strategy
        
    def handle_exception(
        self, 
        exception: Exception, 
        context: str = "", 
        raise_on_failure: bool = True,
        return_on_failure: Any = None
    ) -> Any:
        """
        处理异常
        
        Args:
            exception: 发生的异常
            context: 异常上下文描述
            raise_on_failure: 是否在失败时重新抛出异常
            return_on_failure: 失败时返回的默认值
            
        Returns:
            恢复策略的结果或默认值
        """
        self.error_count += 1
        exception_type = type(exception)
        
        # 记录异常
        self.logger.error(f"异常发生在 {context}: {str(exception)}")
        self.logger.debug(f"异常详情: {traceback.format_exc()}")
        
        # 尝试恢复策略
        if exception_type in self.recovery_strategies:
            try:
                self.logger.info(f"尝试使用恢复策略处理 {exception_type.__name__}")
                result = self.recovery_strategies[exception_type](exception)
                self.logger.info("异常恢复成功")
                return result
            except Exception as recovery_error:
                self.logger.error(f"恢复策略失败: {recovery_error}")
        
        # 如果没有恢复策略或恢复失败
        if raise_on_failure:
            raise exception
        else:
            self.logger.warning(f"返回默认值: {return_on_failure}")
            return return_on_failure
    
    def reset_error_count(self):
        """重置错误计数"""
        self.error_count = 0


# 全局异常处理器实例
global_exception_handler = ExceptionHandler()


def safe_execute(
    logger: Optional[logging.Logger] = None,
    context: str = "",
    raise_on_failure: bool = False,
    return_on_failure: Any = None,
    max_retries: int = 1
):
    """
    安全执行装饰器
    
    Args:
        logger: 日志记录器
        context: 执行上下文
        raise_on_failure: 是否在失败时抛出异常
        return_on_failure: 失败时的返回值
        max_retries: 最大重试次数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            _logger = logger or default_logger
            
            for attempt in range(max_retries + 1):
                try:
                    result = func(*args, **kwargs)
                    if attempt > 0:
                        _logger.info(f"{func.__name__} 在第{attempt + 1}次尝试后成功")
                    return result
                    
                except Exception as e:
                    if attempt < max_retries:
                        _logger.warning(f"{func.__name__} 第{attempt + 1}次尝试失败: {e}, 重试中...")
                        continue
                    else:
                        _logger.error(f"{func.__name__} 在{max_retries + 1}次尝试后仍然失败")
                        return global_exception_handler.handle_exception(
                            e, 
                            context=context or func.__name__,
                            raise_on_failure=raise_on_failure,
                            return_on_failure=return_on_failure
                        )
        return wrapper
    return decorator


@contextmanager
def error_context(
    context_name: str,
    logger: Optional[logging.Logger] = None,
    suppress_errors: bool = False,
    cleanup_func: Optional[Callable] = None
):
    """
    错误上下文管理器
    
    Args:
        context_name: 上下文名称
        logger: 日志记录器
        suppress_errors: 是否抑制错误
        cleanup_func: 清理函数
    """
    _logger = logger or default_logger
    _logger.debug(f"进入上下文: {context_name}")
    
    try:
        yield
        _logger.debug(f"上下文 {context_name} 执行成功")
    except Exception as e:
        _logger.error(f"上下文 {context_name} 发生错误: {e}")
        if cleanup_func:
            try:
                cleanup_func()
                _logger.info("清理函数执行成功")
            except Exception as cleanup_error:
                _logger.error(f"清理函数执行失败: {cleanup_error}")
        
        if not suppress_errors:
            raise
    finally:
        _logger.debug(f"退出上下文: {context_name}")


class ModelTrainingRecovery:
    """模型训练失败恢复策略"""
    
    @staticmethod
    def default_model_recovery(exception: Exception) -> Any:
        """
        默认模型恢复策略
        返回一个简单的基线模型
        """
        from sklearn.dummy import DummyClassifier
        
        default_logger.info("使用基线模型作为恢复策略")
        return DummyClassifier(strategy='most_frequent')
    
    @staticmethod
    def parameter_adjustment_recovery(exception: Exception) -> Dict[str, Any]:
        """
        参数调整恢复策略
        返回更保守的参数设置
        """
        conservative_params = {
            'n_estimators': 50,  # 减少估计器数量
            'max_depth': 3,      # 限制树深度
            'learning_rate': 0.1, # 保守的学习率
            'min_samples_split': 20,  # 增加分割所需的最小样本数
            'min_samples_leaf': 10    # 增加叶节点最小样本数
        }
        
        default_logger.info(f"使用保守参数恢复: {conservative_params}")
        return conservative_params


class DataProcessingRecovery:
    """数据处理失败恢复策略"""
    
    @staticmethod
    def missing_value_recovery(exception: Exception) -> str:
        """缺失值处理恢复策略"""
        default_logger.info("使用中位数填充作为缺失值处理恢复策略")
        return 'median'
    
    @staticmethod
    def scaling_recovery(exception: Exception) -> str:
        """特征缩放恢复策略"""
        default_logger.info("使用标准化作为特征缩放恢复策略")
        return 'standard'
    
    @staticmethod
    def feature_selection_recovery(exception: Exception) -> Dict[str, Any]:
        """特征选择恢复策略"""
        recovery_params = {
            'method': 'simple',
            'k': 10,
            'strategy': 'variance'
        }
        default_logger.info(f"使用简化特征选择恢复: {recovery_params}")
        return recovery_params


class EnsembleRecovery:
    """集成学习失败恢复策略"""
    
    @staticmethod
    def voting_recovery(exception: Exception) -> str:
        """投票集成恢复策略"""
        default_logger.info("降级到硬投票集成")
        return 'hard'
    
    @staticmethod
    def stacking_recovery(exception: Exception) -> Any:
        """堆叠集成恢复策略"""
        from sklearn.linear_model import LogisticRegression
        
        default_logger.info("使用简单逻辑回归作为元学习器")
        return LogisticRegression(random_state=42, max_iter=1000)
    
    @staticmethod
    def dimension_mismatch_recovery(exception: Exception) -> float:
        """维度不匹配恢复策略"""
        default_logger.info("使用默认预测值0.5进行维度对齐")
        return 0.5


def register_default_recovery_strategies():
    """注册默认的恢复策略"""
    
    # 模型训练相关
    global_exception_handler.register_recovery_strategy(
        ModelTrainingError, 
        ModelTrainingRecovery.default_model_recovery
    )
    
    # 数据处理相关
    global_exception_handler.register_recovery_strategy(
        DataProcessingError,
        DataProcessingRecovery.missing_value_recovery
    )
    
    # 集成学习相关
    global_exception_handler.register_recovery_strategy(
        EnsembleError,
        EnsembleRecovery.voting_recovery
    )
    
    # 通用异常
    global_exception_handler.register_recovery_strategy(
        ValueError,
        lambda e: None
    )
    
    global_exception_handler.register_recovery_strategy(
        RuntimeError,
        lambda e: None
    )


def create_robust_function(
    original_func: Callable,
    recovery_value: Any = None,
    max_retries: int = 2,
    context: str = ""
) -> Callable:
    """
    创建健壮版本的函数
    
    Args:
        original_func: 原始函数
        recovery_value: 恢复值
        max_retries: 最大重试次数
        context: 上下文描述
        
    Returns:
        健壮版本的函数
    """
    
    @functools.wraps(original_func)
    def robust_wrapper(*args, **kwargs):
        for attempt in range(max_retries + 1):
            try:
                return original_func(*args, **kwargs)
            except Exception as e:
                if attempt < max_retries:
                    default_logger.warning(f"{original_func.__name__} 第{attempt + 1}次尝试失败，重试中...")
                    continue
                else:
                    default_logger.error(f"{original_func.__name__} 最终失败: {e}")
                    if recovery_value is not None:
                        default_logger.info(f"返回恢复值: {recovery_value}")
                        return recovery_value
                    else:
                        raise e
    
    return robust_wrapper


def log_performance_warning(func_name: str, execution_time: float, threshold: float = 10.0):
    """
    记录性能警告
    
    Args:
        func_name: 函数名
        execution_time: 执行时间
        threshold: 警告阈值（秒）
    """
    if execution_time > threshold:
        default_logger.warning(f"函数 {func_name} 执行时间较长: {execution_time:.2f}秒")


def with_timeout(timeout_seconds: int = 300):
    """
    超时装饰器
    
    Args:
        timeout_seconds: 超时秒数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import signal
            
            def timeout_handler(signum, frame):
                raise TimeoutError(f"函数 {func.__name__} 执行超时 ({timeout_seconds}秒)")
            
            # 只在Unix系统上使用signal
            if hasattr(signal, 'SIGALRM'):
                old_handler = signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(timeout_seconds)
                
                try:
                    result = func(*args, **kwargs)
                    signal.alarm(0)  # 取消超时
                    return result
                except Exception as e:
                    signal.alarm(0)  # 确保取消超时
                    raise e
                finally:
                    signal.signal(signal.SIGALRM, old_handler)
            else:
                # Windows系统上的简化版本
                default_logger.warning("超时功能在Windows上不可用，正常执行函数")
                return func(*args, **kwargs)
                
        return wrapper
    return decorator


# 初始化默认恢复策略
register_default_recovery_strategies()


# 导出的工具函数
__all__ = [
    'ExceptionHandler',
    'safe_execute',
    'error_context',
    'create_robust_function',
    'with_timeout',
    'ModelTrainingError',
    'DataProcessingError',
    'EnsembleError',
    'ValidationError',
    'global_exception_handler'
]
