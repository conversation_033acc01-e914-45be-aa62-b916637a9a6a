#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单数据源集成学习模块
实现传统的集成学习方法，包括投票法、装袋法、提升法、堆叠法等
支持单一数据源的多模型集成
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from pathlib import Path
from joblib import dump, load
from datetime import datetime
from sklearn.base import BaseEstimator, ClassifierMixin, clone
from sklearn.model_selection import cross_val_score, StratifiedKFold, train_test_split
import numpy.random as np_random
from sklearn.ensemble import (
    VotingClassifier, BaggingClassifier, AdaBoostClassifier, 
    GradientBoostingClassifier, RandomForestClassifier, ExtraTreesClassifier
)
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, confusion_matrix, classification_report,
    roc_curve, auc, matthews_corrcoef, balanced_accuracy_score,
    average_precision_score, brier_score_loss
)
from model_performance_report import bootstrap_roc_auc_ci

# 导入项目模块
from config import OUTPUT_PATH, CACHE_PATH, ENSEMBLE_PATH, RANDOM_SEED
from model_training import MODEL_TRAINERS
from data_preprocessing import load_and_preprocess_data
from logger import get_logger

# 尝试导入会话管理功能
try:
    from session_utils import get_current_session, save_to_session
except ImportError:
    # 如果无法导入会话管理，提供空函数
    def get_current_session():
        return None

    def save_to_session(data, data_type, name, **kwargs):
        return None

warnings.filterwarnings('ignore')
logger = get_logger(__name__)



def _cache_ensemble_result(name, model, X_test, y_test, y_pred, y_score, metrics, method, additional_info=None):
    """
    缓存集成模型结果，与单模型结构保持一致

    Args:
        name: 集成模型名称
        model: 训练好的集成模型
        X_test, y_test, y_pred: 测试数据和预测结果
        y_score: 概率预测或决策函数值
        metrics: 性能指标字典
        method: 集成方法名称
        additional_info: 额外信息字典
    """
    try:
        # 构建统一的结果对象（与单模型结构一致）
        result = {
            'model': model,
            'y_true': y_test,
            'y_pred': y_pred,
            'y_score': y_score,  # 统一命名：y_score表示用于阈值无关指标的连续分数
            'X_test': X_test,
            'feature_names': _get_safe_feature_names(X_test, X_test.shape[1]),
            'metrics': metrics,
            # 集成学习特有信息
            'model_type': 'ensemble',
            'ensemble_method': method,
            'timestamp': datetime.now().isoformat()
        }

        # 添加额外信息
        if additional_info:
            result.update(additional_info)

        # 优先使用会话管理系统保存
        current_session = get_current_session()
        if current_session:
            try:
                session_path = save_to_session(
                    result, 'model', name,
                    model_type='ensemble',
                    additional_data={
                        'ensemble_method': method,
                        'performance_metrics': metrics,
                        'data_shape': X_test.shape if hasattr(X_test, 'shape') else None,
                        'n_classes': len(set(y_test))
                    }
                )

                if session_path:
                    logger.info(f"集成模型 {name} 已保存到会话: {session_path}")

                    # 同时保存到传统缓存以保持兼容性
                    cache_file = CACHE_PATH / f"Ensemble_{name}_results.joblib"
                    dump(result, cache_file)
                    logger.info(f"集成模型 {name} 同时保存到缓存: {cache_file}")

                    return
            except Exception as e:
                logger.warning(f"保存到会话失败，使用传统方式: {e}")

        # 传统缓存方式作为备选
        cache_file = CACHE_PATH / f"Ensemble_{name}_results.joblib"
        dump(result, cache_file)
        logger.info(f"集成模型 {name} 的结果已缓存到: {cache_file}")

        # 保存特征名称
        try:
            feature_names_file = CACHE_PATH / f"Ensemble_{name}_feature_names.joblib"
            dump(result['feature_names'], feature_names_file)
            logger.info(f"集成模型 {name} 特征名称已缓存到: {feature_names_file}")
        except Exception as e:
            logger.warning(f"保存特征名称失败: {e}")

    except Exception as e:
        logger.error(f"缓存集成模型 {name} 结果失败: {e}")

def _get_safe_feature_names(X_data, expected_length):
    """
    安全地获取特征名称，避免字体问题

    Args:
        X_data: 输入数据（可能是DataFrame或numpy数组）
        expected_length: 期望的特征数量

    Returns:
        list: 安全的特征名称列表（英文）
    """
    try:
        reserved_targets = {'label', 'target', 'class', 'y', 'outcome', 'result'}
        # 如果是DataFrame，尝试使用列名
        if hasattr(X_data, 'columns'):
            original_names = X_data.columns.tolist()
            if len(original_names) == expected_length:
                # 将中文特征名转换为安全的英文名称，并屏蔽目标列名称
                safe_names = []
                for i, name in enumerate(original_names):
                    name_str = str(name)
                    # 如果特征名包含中文或特殊字符，使用英文替代
                    if any(ord(char) > 127 for char in name_str):
                        safe_names.append(f'Feature_{i}')
                    else:
                        # 保留英文和数字特征名，但确保安全
                        safe_name = name_str.replace(' ', '_').replace('-', '_').lower()
                        if safe_name in reserved_targets:
                            safe_names.append(f'Feature_{i}')
                        else:
                            safe_names.append(safe_name)
                return safe_names

        # 尝试从缓存加载特征名称
        try:
            # 查找可能的特征名称缓存文件
            cache_files = list(CACHE_PATH.glob("*_feature_names.joblib"))
            if cache_files:
                from joblib import load
                cached_names = load(cache_files[0])  # 使用第一个找到的缓存
                if len(cached_names) == expected_length:
                    # 同样处理缓存的特征名
                    safe_names = []
                    for i, name in enumerate(cached_names):
                        if any(ord(char) > 127 for char in str(name)):
                            safe_names.append(f'Feature_{i}')
                        else:
                            name_str = str(name).lower()
                            safe_name = name_str.replace(' ', '_').replace('-', '_')
                            if safe_name in reserved_targets:
                                safe_names.append(f'Feature_{i}')
                            else:
                                safe_names.append(safe_name)
                    return safe_names
        except Exception:
            pass

        # 如果都失败了，使用默认的英文特征名
        return [f'Feature_{i}' for i in range(expected_length)]

    except Exception as e:
        logger.warning(f"获取特征名称失败: {e}，使用默认名称")
        return [f'Feature_{i}' for i in range(expected_length)]

# SHAP 相关导入和可用性检查
try:
    import shap
    SHAP_AVAILABLE = True
    logger.info("SHAP库已成功导入，可解释性分析功能已启用")
except ImportError:
    SHAP_AVAILABLE = False
    logger.warning("SHAP库未安装，可解释性分析功能将被禁用")


def _tune_voting_weights(base_models, X_train, y_train, budget=30, scoring='f1_weighted', cv=5, n_jobs=None):
    """
    优化Voting集成的权重

    Args:
        base_models: 基础模型字典
        X_train, y_train: 训练数据
        budget: 搜索预算
        scoring: 评估指标
        cv: 交叉验证折数
        n_jobs: 并行作业数

    Returns:
        best_weights: 最优权重
        best_score: 最优分数
    """
    try:
        from sklearn.model_selection import cross_val_score, StratifiedKFold

        model_list = list(base_models.values())
        n_models = len(model_list)

        if n_models < 2:
            return None, 0.0

        best_weights = None
        best_score = -np.inf

        # 使用Dirichlet分布生成权重
        for _ in range(budget):
            # 生成随机权重
            alpha = np.ones(n_models)  # 均匀Dirichlet
            weights = np_random.dirichlet(alpha)

            # 创建VotingClassifier
            estimators = [(name, model) for name, model in base_models.items()]
            voting_clf = VotingClassifier(
                estimators=estimators,
                voting='soft',
                weights=weights
            )

            # 交叉验证评估
            cv_scores = cross_val_score(
                voting_clf, X_train, y_train,
                cv=StratifiedKFold(n_splits=cv, shuffle=True, random_state=42),
                scoring=scoring,
                n_jobs=n_jobs
            )

            mean_score = cv_scores.mean()
            if mean_score > best_score:
                best_score = mean_score
                best_weights = weights.copy()

        logger.info(f"Voting权重优化完成: 最优CV {scoring}={best_score:.4f}")
        return best_weights, best_score

    except Exception as e:
        logger.warning(f"Voting权重优化失败: {e}")
        return None, 0.0


def _tune_bagging_params(base_models, X_train, y_train, budget=30, scoring='f1_weighted', cv=5, n_jobs=None):
    """
    优化Bagging集成的参数

    Args:
        base_models: 基础模型字典
        X_train, y_train: 训练数据
        budget: 搜索预算
        scoring: 评估指标
        cv: 交叉验证折数
        n_jobs: 并行作业数

    Returns:
        best_params: 最优参数
        best_score: 最优分数
    """
    try:
        from sklearn.model_selection import cross_val_score, StratifiedKFold

        # 参数搜索空间
        param_space = {
            'n_estimators': [30, 50, 100, 150],
            'max_samples': [0.6, 0.8, 1.0],
            'max_features': [0.5, 0.7, 1.0],
            'bootstrap': [True, False]
        }

        best_params = None
        best_score = -np.inf

        # 随机搜索
        for _ in range(budget):
            # 随机选择参数
            params = {
                key: np_random.choice(values)
                for key, values in param_space.items()
            }

            # 使用第一个基础模型作为基学习器
            base_estimator = list(base_models.values())[0]

            # 创建BaggingClassifier，兼容不同版本
            try:
                bagging_clf = BaggingClassifier(
                    estimator=base_estimator,
                    **params,
                    random_state=42
                )
            except TypeError:
                bagging_clf = BaggingClassifier(
                    base_estimator=base_estimator,
                    **params,
                    random_state=42
                )

            # 交叉验证评估
            cv_scores = cross_val_score(
                bagging_clf, X_train, y_train,
                cv=StratifiedKFold(n_splits=cv, shuffle=True, random_state=42),
                scoring=scoring,
                n_jobs=n_jobs
            )

            mean_score = cv_scores.mean()
            if mean_score > best_score:
                best_score = mean_score
                best_params = params.copy()

        logger.info(f"Bagging参数优化完成: 最优CV {scoring}={best_score:.4f}")
        return best_params, best_score

    except Exception as e:
        logger.warning(f"Bagging参数优化失败: {e}")
        return None, 0.0


def _tune_adaboost_params(X_train, y_train, budget=30, scoring='f1_weighted', cv=5, n_jobs=None):
    """
    优化AdaBoost集成的参数

    Args:
        X_train, y_train: 训练数据
        budget: 搜索预算
        scoring: 评估指标
        cv: 交叉验证折数

    Returns:
        best_params: 最优参数
        best_score: 最优分数
    """
    try:
        from sklearn.model_selection import cross_val_score, StratifiedKFold

        # 参数搜索空间
        param_space = {
            'n_estimators': [50, 100, 150, 200],
            'learning_rate': [0.01, 0.1, 0.3, 0.5, 1.0]
        }

        best_params = None
        best_score = -np.inf

        # 随机搜索
        for _ in range(budget):
            # 随机选择参数
            params = {
                key: np_random.choice(values)
                for key, values in param_space.items()
            }

            # 使用弱学习器作为基学习器
            base_estimator = DecisionTreeClassifier(max_depth=1, random_state=42)

            # 创建AdaBoostClassifier，兼容不同版本
            try:
                ada_clf = AdaBoostClassifier(
                    estimator=base_estimator,
                    **params,
                    random_state=42
                )
            except TypeError:
                ada_clf = AdaBoostClassifier(
                    base_estimator=base_estimator,
                    **params,
                    random_state=42
                )

            # 交叉验证评估
            cv_scores = cross_val_score(
                ada_clf, X_train, y_train,
                cv=StratifiedKFold(n_splits=cv, shuffle=True, random_state=42),
                scoring=scoring,
                n_jobs=n_jobs
            )

            mean_score = cv_scores.mean()
            if mean_score > best_score:
                best_score = mean_score
                best_params = params.copy()

        logger.info(f"AdaBoost参数优化完成: 最优CV {scoring}={best_score:.4f}")
        return best_params, best_score

    except Exception as e:
        logger.warning(f"AdaBoost参数优化失败: {e}")
        return None, 0.0


def _tune_stacking_params(base_models, X_train, y_train, budget=30, scoring='f1_weighted', cv=5, n_jobs=None):
    """
    优化Stacking集成的元学习器参数

    Args:
        base_models: 基础模型字典
        X_train, y_train: 训练数据
        budget: 搜索预算
        scoring: 评估指标
        cv: 交叉验证折数

    Returns:
        best_params: 最优参数
        best_score: 最优分数
    """
    try:
        from sklearn.model_selection import cross_val_score, StratifiedKFold

        # 参数搜索空间
        param_space = {
            'C': [0.01, 0.1, 1.0, 10.0],
            'penalty': ['l2'],
            'class_weight': [None, 'balanced'],
            'passthrough': [True, False]
        }

        best_params = None
        best_score = -np.inf

        # 随机搜索
        for _ in range(budget):
            # 随机选择参数
            C = np_random.choice(param_space['C'])
            penalty = np_random.choice(param_space['penalty'])
            class_weight = np_random.choice(param_space['class_weight'])
            passthrough = np_random.choice(param_space['passthrough'])

            # 创建元学习器
            meta_learner = LogisticRegression(
                C=C, penalty=penalty, class_weight=class_weight,
                random_state=42, max_iter=1000
            )

            # 创建StackingClassifier
            estimators = [(name, model) for name, model in base_models.items()]
            stacking_clf = StackingClassifier(
                estimators=estimators,
                final_estimator=meta_learner,
                passthrough=passthrough,
                cv=5
            )

            # 交叉验证评估
            cv_scores = cross_val_score(
                stacking_clf, X_train, y_train,
                cv=StratifiedKFold(n_splits=cv, shuffle=True, random_state=42),
                scoring=scoring,
                n_jobs=n_jobs
            )

            mean_score = cv_scores.mean()
            if mean_score > best_score:
                best_score = mean_score
                best_params = {
                    'C': C, 'penalty': penalty, 'class_weight': class_weight,
                    'passthrough': passthrough
                }

        logger.info(f"Stacking参数优化完成: 最优CV {scoring}={best_score:.4f}")
        return best_params, best_score

    except Exception as e:
        logger.warning(f"Stacking参数优化失败: {e}")
        return None, 0.0


class EnsembleClassifier(BaseEstimator, ClassifierMixin):
    """
    集成分类器
    支持多种集成策略：硬投票、软投票、加权投票、Bagging、Boosting、Stacking等
    """
    
    def __init__(self, base_models=None, ensemble_method='voting',
                 voting='soft', weights=None, meta_classifier=None,
                 n_estimators=10, random_state=None, cv=5,
                 max_samples=1.0, max_features=1.0, bootstrap=True,
                 learning_rate=1.0):
        """
        初始化集成分类器

        Args:
            base_models: 基础模型列表或字典 {model_name: model_instance}
            ensemble_method: 集成方法 ('voting', 'bagging', 'boosting', 'stacking')
            voting: 投票方式 ('hard', 'soft') - 仅用于voting方法
            weights: 模型权重 - 仅用于voting方法
            meta_classifier: 元分类器 - 仅用于stacking方法
            n_estimators: 基础估计器数量 - 用于bagging和boosting
            random_state: 随机种子
            cv: 交叉验证折数 - 用于stacking
            max_samples: Bagging采样比例
            max_features: Bagging特征采样比例
            bootstrap: Bagging是否有放回采样
            learning_rate: AdaBoost学习率
        """
        self.base_models = base_models or {}
        self.ensemble_method = ensemble_method
        self.voting = voting
        self.weights = weights
        self.meta_classifier = meta_classifier or LogisticRegression(random_state=random_state)
        self.n_estimators = n_estimators
        self.random_state = random_state or RANDOM_SEED
        self.cv = cv
        # Bagging参数
        self.max_samples = max_samples
        self.max_features = max_features
        self.bootstrap = bootstrap
        # AdaBoost参数
        self.learning_rate = learning_rate
        
        self.ensemble_model = None
        self.trained_models = {}
        self.feature_importances_ = None
        
    def _prepare_base_models(self):
        """准备基础模型"""
        if isinstance(self.base_models, dict):
            return [(name, model) for name, model in self.base_models.items()]
        elif isinstance(self.base_models, list):
            if all(isinstance(item, tuple) and len(item) == 2 for item in self.base_models):
                return self.base_models
            else:
                return [(f'model_{i}', model) for i, model in enumerate(self.base_models)]
        else:
            raise ValueError("base_models必须是字典或列表格式")
    
    def fit(self, X, y):
        """训练集成模型"""
        logger.info(f"开始训练集成模型，方法: {self.ensemble_method}")
        
        if self.ensemble_method == 'voting':
            self._fit_voting(X, y)
        elif self.ensemble_method == 'bagging':
            self._fit_bagging(X, y)
        elif self.ensemble_method == 'boosting':
            self._fit_boosting(X, y)
        elif self.ensemble_method == 'stacking':
            self._fit_stacking(X, y)
        else:
            raise ValueError(f"不支持的集成方法: {self.ensemble_method}")
        
        return self
    
    def _fit_voting(self, X, y):
        """训练投票集成模型"""
        estimators = self._prepare_base_models()
        
        self.ensemble_model = VotingClassifier(
            estimators=estimators,
            voting=self.voting,
            weights=self.weights
        )
        
        self.ensemble_model.fit(X, y)
        self.trained_models = dict(estimators)
        
        # 计算特征重要性（如果可能）
        self._compute_feature_importances()
    
    def _fit_bagging(self, X, y):
        """训练Bagging集成模型"""
        # 使用第一个基础模型作为基础估计器
        if self.base_models:
            base_estimator = list(self.base_models.values())[0]
        else:
            base_estimator = DecisionTreeClassifier(random_state=self.random_state)
        
        # 兼容不同版本的scikit-learn：新版本使用estimator，旧版本使用base_estimator
        try:
            self.ensemble_model = BaggingClassifier(
                estimator=base_estimator,
                n_estimators=self.n_estimators,
                max_samples=self.max_samples,
                max_features=self.max_features,
                bootstrap=self.bootstrap,
                random_state=self.random_state
            )
        except TypeError:
            # 回退到旧版本的参数名
            self.ensemble_model = BaggingClassifier(
                base_estimator=base_estimator,
                n_estimators=self.n_estimators,
                max_samples=self.max_samples,
                max_features=self.max_features,
                bootstrap=self.bootstrap,
                random_state=self.random_state
            )
        
        self.ensemble_model.fit(X, y)
        
        # 保存基础估计器
        self.trained_models = {
            f'bagging_estimator_{i}': estimator 
            for i, estimator in enumerate(self.ensemble_model.estimators_)
        }
        
        self._compute_feature_importances()
    
    def _fit_boosting(self, X, y):
        """训练Boosting集成模型"""
        # AdaBoost应始终使用弱学习器作为基础估计器，避免复杂模型导致的性能和兼容性问题
        base_estimator = DecisionTreeClassifier(max_depth=1, random_state=self.random_state)
        
        # 兼容不同版本的scikit-learn：新版本使用estimator，旧版本使用base_estimator
        try:
            self.ensemble_model = AdaBoostClassifier(
                estimator=base_estimator,
                n_estimators=self.n_estimators,
                learning_rate=self.learning_rate,
                random_state=self.random_state
            )
        except TypeError:
            # 回退到旧版本的参数名
            self.ensemble_model = AdaBoostClassifier(
                base_estimator=base_estimator,
                n_estimators=self.n_estimators,
                learning_rate=self.learning_rate,
                random_state=self.random_state
            )
        
        self.ensemble_model.fit(X, y)
        
        # 保存基础估计器和权重
        self.trained_models = {
            f'boosting_estimator_{i}': estimator 
            for i, estimator in enumerate(self.ensemble_model.estimators_)
        }
        
        self._compute_feature_importances()
    
    def _fit_stacking(self, X, y):
        """训练Stacking集成模型"""
        from sklearn.ensemble import StackingClassifier
        
        estimators = self._prepare_base_models()
        
        self.ensemble_model = StackingClassifier(
            estimators=estimators,
            final_estimator=self.meta_classifier,
            cv=self.cv,
            stack_method='predict_proba' if self.voting == 'soft' else 'predict'
        )
        
        self.ensemble_model.fit(X, y)
        self.trained_models = dict(estimators)
        self.trained_models['meta_classifier'] = self.ensemble_model.final_estimator_
        
        self._compute_feature_importances()
    
    def _compute_feature_importances(self):
        """计算特征重要性"""
        try:
            if hasattr(self.ensemble_model, 'feature_importances_'):
                self.feature_importances_ = self.ensemble_model.feature_importances_
            elif self.ensemble_method == 'voting':
                # 对于投票方法，计算平均特征重要性
                importances = []
                for name, model in self.trained_models.items():
                    if hasattr(model, 'feature_importances_'):
                        importances.append(model.feature_importances_)
                
                if importances:
                    self.feature_importances_ = np.mean(importances, axis=0)
        except Exception as e:
            logger.warning(f"无法计算特征重要性: {e}")
            self.feature_importances_ = None
    
    def predict(self, X):
        """预测"""
        if self.ensemble_model is None:
            raise ValueError("模型尚未训练，请先调用fit方法")
        return self.ensemble_model.predict(X)
    
    def predict_proba(self, X):
        """预测概率"""
        if self.ensemble_model is None:
            raise ValueError("模型尚未训练，请先调用fit方法")
        return self.ensemble_model.predict_proba(X)
    
    def score(self, X, y):
        """计算准确率"""
        return self.ensemble_model.score(X, y)


def create_base_models_from_names(model_names, X_train, y_train, X_test, y_test, use_gpu=True, n_jobs=-1):
    """
    根据模型名称创建并训练基础模型

    Args:
        model_names: 模型名称列表
        X_train, y_train: 训练数据
        X_test, y_test: 测试数据
        use_gpu: 是否为XGBoost启用GPU加速
        n_jobs: 并行作业数

    Returns:
        dict: 训练好的模型字典
    """
    base_models = {}

    for model_name in model_names:
        if model_name in MODEL_TRAINERS:
            logger.info(f"训练基础模型: {model_name}")
            try:
                trainer = MODEL_TRAINERS[model_name]
                model = trainer.train_and_evaluate(X_train, y_train, X_test, y_test,
                                                 use_gpu=use_gpu, n_jobs=n_jobs)
                base_models[model_name] = model
                logger.info(f"  {model_name} 训练完成")
            except Exception as e:
                logger.error(f"训练模型 {model_name} 失败: {e}")
        else:
            logger.warning(f"未知的模型名称: {model_name}")
    
    return base_models


def evaluate_ensemble_model(ensemble_model, X_test, y_test, model_name="Ensemble"):
    """
    评估集成模型性能 - 增强版，与单模型指标保持一致

    Args:
        ensemble_model: 训练好的集成模型
        X_test, y_test: 测试数据
        model_name: 模型名称

    Returns:
        dict: 性能指标字典，包含与单模型一致的指标
    """
    # 预测
    y_pred = ensemble_model.predict(X_test)

    # 获取用于AUC/PRC的连续分数：
    # - soft voting: 使用predict_proba
    # - hard voting: 使用基础模型的概率平均，若无概率则使用正类投票比例
    y_score = None

    def _safe_sigmoid(x):
        try:
            return 1.0 / (1.0 + np.exp(-x))
        except Exception:
            return x

    try:
        voting_mode = getattr(ensemble_model, 'voting', None)
        if voting_mode == 'hard':
            # 从已训练的基础模型聚合分数
            base_models = []
            try:
                if hasattr(ensemble_model, 'trained_models') and ensemble_model.trained_models:
                    base_models = list(ensemble_model.trained_models.values())
            except Exception:
                base_models = []

            scores = []
            for m in base_models:
                s = None
                try:
                    if hasattr(m, 'predict_proba'):
                        proba = m.predict_proba(X_test)
                        if proba is not None:
                            s = proba[:, 1] if proba.ndim == 2 and proba.shape[1] > 1 else proba.ravel()
                    if s is None and hasattr(m, 'decision_function'):
                        df = m.decision_function(X_test)
                        # 将决策函数映射到(0,1)，优先使用sigmoid，若失败则min-max
                        try:
                            s_tmp = _safe_sigmoid(df)
                            s = np.asarray(s_tmp, dtype=float)
                        except Exception:
                            df = np.asarray(df, dtype=float)
                            mn, mx = np.min(df), np.max(df)
                            s = (df - mn) / (mx - mn + 1e-12)
                    if s is None and hasattr(m, 'predict'):
                        pred = m.predict(X_test)
                        s = np.asarray(pred, dtype=float)
                    if s is not None:
                        scores.append(np.asarray(s, dtype=float))
                except Exception:
                    continue

            if scores:
                # 对每个样本取平均作为连续分数
                y_score = np.mean(np.vstack(scores), axis=0)
            else:
                # 最后兜底：直接使用Ensemble的硬投票比例（需要可访问到其基础估计器）
                try:
                    hard_preds = []
                    if hasattr(ensemble_model, 'trained_models') and ensemble_model.trained_models:
                        for m in ensemble_model.trained_models.values():
                            try:
                                hard_preds.append(np.asarray(m.predict(X_test), dtype=float))
                            except Exception:
                                pass
                    if hard_preds:
                        y_score = np.mean(np.vstack(hard_preds), axis=0)
                except Exception:
                    y_score = None
        else:
            # soft 或未知：尝试直接拿predict_proba/decision_function
            if hasattr(ensemble_model, 'predict_proba'):
                proba_result = ensemble_model.predict_proba(X_test)
                if proba_result is not None:
                    if proba_result.ndim == 2 and proba_result.shape[1] > 1:
                        y_score = proba_result[:, 1]
                    else:
                        y_score = proba_result.ravel()
            if y_score is None and hasattr(ensemble_model, 'decision_function'):
                y_score = ensemble_model.decision_function(X_test)
    except Exception as e:
        logger.warning(f"无法获取用于AUC/PRC的连续分数: {e}")
        y_score = None

    # 计算基础指标
    metrics = {
        'model_name': model_name,
        'accuracy': accuracy_score(y_test, y_pred),
        'precision': precision_score(y_test, y_pred, average='weighted', zero_division=0),
        'recall': recall_score(y_test, y_pred, average='weighted', zero_division=0),
        'f1_score': f1_score(y_test, y_pred, average='weighted', zero_division=0),
        'balanced_accuracy': balanced_accuracy_score(y_test, y_pred),
        'mcc': matthews_corrcoef(y_test, y_pred)
    }

    # 计算依赖于概率/分数的指标
    if y_score is not None:
        try:
            # 计算ROC AUC及其95%置信区间
            auc_roc, ci_lower, ci_upper = bootstrap_roc_auc_ci(y_test, y_score)
            metrics['auc'] = auc_roc
            if ci_lower is not None and ci_upper is not None:
                metrics['auc_ci'] = f"({ci_lower:.3f}, {ci_upper:.3f})"
                metrics['auc_ci_lower'] = ci_lower
                metrics['auc_ci_upper'] = ci_upper
            else:
                metrics['auc_ci'] = "N/A"
                metrics['auc_ci_lower'] = None
                metrics['auc_ci_upper'] = None
        except Exception as e:
            logger.warning(f"无法计算ROC AUC: {e}")
            metrics['auc'] = 0.0
            metrics['auc_ci'] = "N/A"
            metrics['auc_ci_lower'] = None
            metrics['auc_ci_upper'] = None

        try:
            metrics['auc_pr'] = average_precision_score(y_test, y_score)
        except Exception as e:
            logger.warning(f"无法计算PR AUC: {e}")
            metrics['auc_pr'] = 0.0

        try:
            # 对于概率预测，计算Brier分数
            if hasattr(ensemble_model, 'predict_proba') and y_score is not None:
                # 确保y_score是概率值（0-1之间）
                if np.all((y_score >= 0) & (y_score <= 1)):
                    metrics['brier_score'] = brier_score_loss(y_test, y_score)
                else:
                    metrics['brier_score'] = None
            else:
                metrics['brier_score'] = None
        except Exception as e:
            logger.warning(f"无法计算Brier分数: {e}")
            metrics['brier_score'] = None
    else:
        metrics['auc'] = 0.0
        metrics['auc_ci'] = "N/A"
        metrics['auc_ci_lower'] = None
        metrics['auc_ci_upper'] = None
        metrics['auc_pr'] = 0.0
        metrics['brier_score'] = None

    return metrics


def run_ensemble_pipeline(X_train, y_train, X_test, y_test, model_names,
                         ensemble_methods=None, save_results=True,
                         output_dir=None, enable_shap=True,
                         tune_ensemble=False, tune_budget=30, scoring='f1_weighted',
                         use_gpu=True, n_jobs=-1):
    """
    运行集成学习管道

    Args:
        X_train, y_train: 训练数据
        X_test, y_test: 测试数据
        model_names: 要使用的基础模型名称列表
        ensemble_methods: 集成方法列表，默认为['voting', 'bagging', 'boosting', 'stacking']
        save_results: 是否保存结果
        output_dir: 输出目录
        enable_shap: 是否启用SHAP分析
        tune_ensemble: 是否启用集成层超参数调优
        tune_budget: 调参预算（随机搜索次数）
        scoring: 调参评估指标
        use_gpu: 是否为XGBoost启用GPU加速
        n_jobs: 并行作业数（-1表示使用所有CPU核心）

    Returns:
        dict: 集成结果字典
    """
    if ensemble_methods is None:
        ensemble_methods = ['voting', 'bagging', 'boosting', 'stacking']

    if output_dir is None:
        output_dir = ENSEMBLE_PATH
    else:
        output_dir = Path(output_dir)

    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info("=" * 60)
    logger.info("开始运行集成学习管道")
    logger.info("=" * 60)
    logger.info(f"基础模型: {model_names}")
    logger.info(f"集成方法: {ensemble_methods}")

    # 1. 创建并训练基础模型
    logger.info("步骤1: 训练基础模型")
    base_models = create_base_models_from_names(model_names, X_train, y_train, X_test, y_test, use_gpu, n_jobs)

    if not base_models:
        logger.error("没有成功训练任何基础模型")
        return None

    logger.info(f"成功训练了 {len(base_models)} 个基础模型")

    # 2. 运行不同的集成方法
    ensemble_results = {}

    for method in ensemble_methods:
        logger.info(f"步骤2: 运行集成方法 - {method}")

        try:
            # 创建集成分类器
            if method == 'voting':
                # 尝试软投票和硬投票
                for voting_type in ['soft', 'hard']:
                    ensemble_name = f"{method}_{voting_type}"
                    logger.info(f"  训练 {ensemble_name} 集成模型")

                    # 超参数调优：优化权重（仅对软投票）
                    weights = None
                    if tune_ensemble and voting_type == 'soft':
                        logger.info(f"    正在优化 {ensemble_name} 的权重...")
                        best_weights, best_score = _tune_voting_weights(
                            base_models, X_train, y_train,
                            budget=tune_budget, scoring=scoring, n_jobs=n_jobs
                        )
                        if best_weights is not None:
                            weights = best_weights
                            logger.info(f"    使用优化权重: {weights}")

                    ensemble = EnsembleClassifier(
                        base_models=base_models,
                        ensemble_method='voting',
                        voting=voting_type,
                        weights=weights,
                        random_state=RANDOM_SEED
                    )

                    ensemble.fit(X_train, y_train)
                    metrics = evaluate_ensemble_model(ensemble, X_test, y_test, ensemble_name)

                    # 获取预测结果用于缓存
                    y_pred = ensemble.predict(X_test)
                    y_score = None
                    try:
                        if voting_type == 'soft' and hasattr(ensemble, 'predict_proba'):
                            proba_result = ensemble.predict_proba(X_test)
                            y_score = proba_result[:, 1] if proba_result.shape[1] > 1 else proba_result.flatten()
                        elif voting_type == 'hard':
                            # 为硬投票构造连续分数，便于AUC/PRC与后续分析
                            scores = []
                            try:
                                for m in ensemble.trained_models.values():
                                    s = None
                                    if hasattr(m, 'predict_proba'):
                                        proba = m.predict_proba(X_test)
                                        if proba is not None:
                                            s = proba[:, 1] if proba.ndim == 2 and proba.shape[1] > 1 else proba.ravel()
                                    if s is None and hasattr(m, 'decision_function'):
                                        df = m.decision_function(X_test)
                                        # 使用sigmoid将决策函数映射到(0,1)
                                        try:
                                            s = 1.0 / (1.0 + np.exp(-df))
                                        except Exception:
                                            df = np.asarray(df, dtype=float)
                                            mn, mx = np.min(df), np.max(df)
                                            s = (df - mn) / (mx - mn + 1e-12)
                                    if s is None and hasattr(m, 'predict'):
                                        pred = m.predict(X_test)
                                        s = np.asarray(pred, dtype=float)
                                    if s is not None:
                                        scores.append(np.asarray(s, dtype=float))
                            except Exception:
                                scores = []
                            if scores:
                                y_score = np.mean(np.vstack(scores), axis=0)
                    except Exception:
                        pass

                    # 缓存集成模型结果
                    _cache_ensemble_result(
                        name=ensemble_name,
                        model=ensemble,
                        X_test=X_test,
                        y_test=y_test,
                        y_pred=y_pred,
                        y_score=y_score,
                        metrics=metrics,
                        method=method,
                        additional_info={
                            'voting_type': voting_type,
                            'base_model_names': list(base_models.keys())
                        }
                    )

                    ensemble_results[ensemble_name] = {
                        'model': ensemble,
                        'metrics': metrics,
                        'method': method,
                        'voting': voting_type
                    }

                    logger.info(f"    {ensemble_name} - 准确率: {metrics['accuracy']:.4f}, F1: {metrics['f1_score']:.4f}")

            else:
                # 超参数调优
                ensemble_params = {'random_state': RANDOM_SEED}

                if tune_ensemble:
                    logger.info(f"    正在优化 {method} 的参数...")

                    if method == 'bagging':
                        best_params, best_score = _tune_bagging_params(
                            base_models, X_train, y_train,
                            budget=tune_budget, scoring=scoring, n_jobs=n_jobs
                        )
                        if best_params is not None:
                            ensemble_params.update(best_params)
                            logger.info(f"    使用优化参数: {best_params}")

                    elif method == 'boosting':
                        best_params, best_score = _tune_adaboost_params(
                            X_train, y_train,
                            budget=tune_budget, scoring=scoring, n_jobs=n_jobs
                        )
                        if best_params is not None:
                            ensemble_params.update(best_params)
                            logger.info(f"    使用优化参数: {best_params}")

                    elif method == 'stacking':
                        best_params, best_score = _tune_stacking_params(
                            base_models, X_train, y_train,
                            budget=tune_budget, scoring=scoring, n_jobs=n_jobs
                        )
                        if best_params is not None:
                            # 为stacking创建优化的元学习器
                            meta_classifier = LogisticRegression(
                                C=best_params['C'],
                                penalty=best_params['penalty'],
                                class_weight=best_params['class_weight'],
                                random_state=RANDOM_SEED,
                                max_iter=1000
                            )
                            ensemble_params['meta_classifier'] = meta_classifier
                            # passthrough参数需要在EnsembleClassifier中支持
                            logger.info(f"    使用优化参数: {best_params}")

                ensemble = EnsembleClassifier(
                    base_models=base_models,
                    ensemble_method=method,
                    **ensemble_params
                )

                ensemble.fit(X_train, y_train)
                metrics = evaluate_ensemble_model(ensemble, X_test, y_test, method)

                # 获取预测结果用于缓存
                y_pred = ensemble.predict(X_test)
                y_score = None
                try:
                    if hasattr(ensemble, 'predict_proba'):
                        proba_result = ensemble.predict_proba(X_test)
                        y_score = proba_result[:, 1] if proba_result.shape[1] > 1 else proba_result.flatten()
                    elif hasattr(ensemble, 'decision_function'):
                        y_score = ensemble.decision_function(X_test)
                except Exception:
                    pass

                # 缓存集成模型结果
                _cache_ensemble_result(
                    name=method,
                    model=ensemble,
                    X_test=X_test,
                    y_test=y_test,
                    y_pred=y_pred,
                    y_score=y_score,
                    metrics=metrics,
                    method=method,
                    additional_info={
                        'base_model_names': list(base_models.keys())
                    }
                )

                ensemble_results[method] = {
                    'model': ensemble,
                    'metrics': metrics,
                    'method': method
                }

                logger.info(f"  {method} - 准确率: {metrics['accuracy']:.4f}, F1: {metrics['f1_score']:.4f}")

        except Exception as e:
            logger.error(f"集成方法 {method} 失败: {e}")

    # 3. 找出最佳集成模型
    if ensemble_results:
        best_model_name = max(ensemble_results.keys(),
                            key=lambda x: ensemble_results[x]['metrics']['f1_score'])
        best_model_info = ensemble_results[best_model_name]

        logger.info("=" * 60)
        logger.info("集成学习结果总结")
        logger.info("=" * 60)
        logger.info(f"最佳集成模型: {best_model_name}")
        logger.info(f"最佳F1分数: {best_model_info['metrics']['f1_score']:.4f}")
        logger.info(f"最佳准确率: {best_model_info['metrics']['accuracy']:.4f}")

        # 显示所有模型的性能对比
        logger.info("\n所有集成模型性能对比:")
        for name, result in ensemble_results.items():
            metrics = result['metrics']
            logger.info(f"  {name:15} - 准确率: {metrics['accuracy']:.4f}, "
                       f"精确率: {metrics['precision']:.4f}, "
                       f"召回率: {metrics['recall']:.4f}, "
                       f"F1: {metrics['f1_score']:.4f}, "
                       f"AUC: {metrics['auc']:.4f}")

    # 4. 保存结果
    if save_results and ensemble_results:
        logger.info("步骤3: 保存集成学习结果")

        # 保存模型
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 准备保存的数据
        save_data = {
            'ensemble_results': ensemble_results,
            'base_models': base_models,
            'best_model': best_model_name if ensemble_results else None,
            'timestamp': timestamp,
            'model_names': model_names,
            'ensemble_methods': ensemble_methods
        }

        # 优先使用会话管理系统保存
        current_session = get_current_session()
        if current_session:
            try:
                session_path = save_to_session(
                    save_data, 'model', f"ensemble_{timestamp}",
                    model_type='ensemble',
                    additional_data={
                        'ensemble_methods': ensemble_methods,
                        'base_model_names': model_names,
                        'best_ensemble_method': best_model_name
                    }
                )

                if session_path:
                    logger.info(f"集成学习结果已保存到会话: {session_path}")

                    # 同时保存到传统路径以保持兼容性
                    results_file = output_dir / f"ensemble_results_{timestamp}.joblib"
                    dump(save_data, results_file)

                    # 保存性能报告到会话
                    report_path = current_session.get_path('reports', f"ensemble_report_{timestamp}.html")
                    save_ensemble_report(ensemble_results, report_path)

                    return ensemble_results
            except Exception as e:
                logger.warning(f"保存到会话失败，使用传统方式: {e}")

        # 传统保存方式作为备选
        results_file = output_dir / f"ensemble_results_{timestamp}.joblib"
        dump(save_data, results_file)
        logger.info(f"集成学习结果已保存到: {results_file}")

        # 保存性能报告
        save_ensemble_report(ensemble_results, output_dir / f"ensemble_report_{timestamp}.html")

    # 5. SHAP分析（如果启用）
    if enable_shap and SHAP_AVAILABLE and ensemble_results:
        logger.info("步骤4: 生成SHAP可解释性分析")
        try:
            generate_ensemble_shap_analysis(
                ensemble_results, X_test, y_test,
                output_dir / "shap_analysis"
            )
        except Exception as e:
            logger.warning(f"SHAP分析失败: {e}")

    return ensemble_results


def save_ensemble_report(ensemble_results, output_file):
    """
    保存集成学习HTML报告 - 使用安全的字体配置

    Args:
        ensemble_results: 集成结果字典
        output_file: 输出文件路径
    """
    try:
        # 使用安全的文本报告替代HTML报告，避免字体问题
        from safe_visualization import safe_create_summary_report

        output_path = Path(output_file).parent
        safe_create_summary_report(ensemble_results, output_path)

        logger.info(f"Ensemble report saved to: {output_path}")

    except Exception as e:
        logger.warning(f"Failed to save ensemble report: {e}")
        # 降级处理：生成简单的文本报告
        try:
            _save_simple_text_report(ensemble_results, output_file)
        except:
            pass

def _save_simple_text_report(ensemble_results, output_file):
    """生成简单的文本报告"""
    output_file = Path(output_file).with_suffix('.txt')

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("ENSEMBLE LEARNING PERFORMANCE REPORT\n")
        f.write("=" * 50 + "\n\n")

        if not ensemble_results:
            f.write("No ensemble results available.\n")
            return

        # 找出最佳模型
        best_model = max(ensemble_results.keys(),
                        key=lambda x: ensemble_results[x]['metrics'].get('f1_score', 0))
        best_metrics = ensemble_results[best_model]['metrics']

        f.write(f"BEST ENSEMBLE MODEL: {best_model}\n")
        f.write(f"Best F1 Score: {best_metrics.get('f1_score', 0):.4f}\n")
        f.write(f"Best Accuracy: {best_metrics.get('accuracy', 0):.4f}\n\n")

        f.write("ALL ENSEMBLE MODELS PERFORMANCE:\n")
        f.write("-" * 50 + "\n")

        for i, (name, result) in enumerate(ensemble_results.items(), 1):
            metrics = result['metrics']
            f.write(f"{i}. {name}:\n")
            f.write(f"   Accuracy:  {metrics.get('accuracy', 0):.4f}\n")
            f.write(f"   Precision: {metrics.get('precision', 0):.4f}\n")
            f.write(f"   Recall:    {metrics.get('recall', 0):.4f}\n")
            f.write(f"   F1 Score:  {metrics.get('f1_score', 0):.4f}\n")
            f.write(f"   AUC:       {metrics.get('auc', 0):.4f}\n\n")


def generate_ensemble_shap_analysis(ensemble_results, X_test, y_test, output_dir):
    """
    生成集成模型的SHAP可解释性分析

    Args:
        ensemble_results: 集成结果字典
        X_test, y_test: 测试数据
        output_dir: 输出目录
    """
    if not SHAP_AVAILABLE:
        logger.warning("SHAP库不可用，跳过可解释性分析")
        return

    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # 为每个集成模型生成SHAP分析
    for name, result in ensemble_results.items():
        try:
            logger.info(f"为 {name} 生成SHAP分析")

            model = result['model']
            model_output_dir = output_dir / name
            model_output_dir.mkdir(parents=True, exist_ok=True)

            # 使用增强的SHAP可视化
            try:
                from enhanced_shap_visualization import create_complete_shap_analysis

                # 获取真实的特征名称
                real_feature_names = _get_safe_feature_names(X_test, X_test.shape[1])

                # 创建完整的SHAP分析
                shap_results = create_complete_shap_analysis(
                    model=model.ensemble_model,
                    X_data=X_test,
                    feature_names=real_feature_names,
                    model_name=name,
                    output_dir=model_output_dir,
                    max_samples=100
                )

                if shap_results:
                    logger.info(f"  {name} 完整SHAP分析完成")
                    logger.info(f"    生成的图表类型: {list(shap_results.keys())}")

                    # 记录生成的文件
                    for plot_type, paths in shap_results.items():
                        if isinstance(paths, list):
                            logger.info(f"    {plot_type}: {len(paths)} 个文件")
                        elif isinstance(paths, dict):
                            logger.info(f"    {plot_type}: {list(paths.keys())}")
                        elif paths:
                            logger.info(f"    {plot_type}: 1 个文件")
                else:
                    logger.warning(f"  {name} SHAP分析未生成任何图表")

            except ImportError:
                logger.warning("增强SHAP可视化模块不可用，使用基础SHAP分析")
                # 回退到基础SHAP分析
                _create_basic_shap_analysis(model.ensemble_model, X_test, name, model_output_dir)
            except Exception as e:
                logger.warning(f"增强SHAP分析失败: {e}，尝试基础分析")
                _create_basic_shap_analysis(model.ensemble_model, X_test, name, model_output_dir)

            logger.info(f"  {name} SHAP分析完成")

        except Exception as e:
            logger.warning(f"为 {name} 生成SHAP分析失败: {e}")

def _create_basic_shap_analysis(model, X_test, name, output_dir):
    """创建基础的SHAP分析（回退方案）"""
    try:
        # 创建SHAP解释器
        if hasattr(model, 'predict_proba'):
            explainer = shap.Explainer(model.predict_proba, X_test[:50])
        else:
            explainer = shap.Explainer(model.predict, X_test[:50])

        # 计算SHAP值
        shap_values = explainer(X_test[:50])

        # 获取特征名称
        real_feature_names = _get_safe_feature_names(X_test, X_test.shape[1])

        # 生成基础摘要图
        plt.figure(figsize=(10, 6))
        shap.summary_plot(shap_values, X_test[:50], feature_names=real_feature_names, show=False)
        plt.title(f'{name} - SHAP Summary Plot')
        plt.tight_layout()
        plt.savefig(output_dir / 'shap_summary.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 生成基础特征重要性图
        if hasattr(shap_values, 'values'):
            feature_importance = np.abs(shap_values.values).mean(0)
            if len(feature_importance.shape) > 1:
                feature_importance = feature_importance[:, 1]

            plt.figure(figsize=(10, 6))
            sorted_idx = np.argsort(feature_importance)[-10:]

            plt.barh(range(len(sorted_idx)), feature_importance[sorted_idx])
            plt.yticks(range(len(sorted_idx)), [real_feature_names[i] for i in sorted_idx])
            plt.xlabel('SHAP Feature Importance')
            plt.title(f'{name} - Top 10 Feature Importance')
            plt.tight_layout()
            plt.savefig(output_dir / 'feature_importance.png', dpi=300, bbox_inches='tight')
            plt.close()

        logger.info(f"  {name} 基础SHAP分析完成")

    except Exception as e:
        logger.warning(f"基础SHAP分析也失败: {e}")

def load_ensemble_results(results_file):
    """
    加载集成学习结果

    Args:
        results_file: 结果文件路径

    Returns:
        dict: 集成结果字典
    """
    try:
        results = load(results_file)
        logger.info(f"成功加载集成学习结果: {results_file}")
        return results
    except Exception as e:
        logger.error(f"加载集成学习结果失败: {e}")
        return None
