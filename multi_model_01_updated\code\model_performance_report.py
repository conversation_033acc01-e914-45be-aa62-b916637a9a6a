#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型性能自动比较功能
提供详细的性能指标计算和综合比较报告
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 设置非交互式后端，避免GUI警告
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from joblib import load
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, confusion_matrix, classification_report,
    average_precision_score, matthews_corrcoef, cohen_kappa_score
)
from datetime import datetime
import json
from scipy import stats
from config import OUTPUT_PATH, CACHE_PATH, PROJECT_ROOT

# 配置字典
CONFIG = {
    'output_path': OUTPUT_PATH,
    'cache_path': CACHE_PATH,
    'report_path': PROJECT_ROOT / 'reports',
    'dpi': 150,
    'figsize': (12, 8)
}

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False
sns.set_theme(style='whitegrid')

# 设置英文字体和样式
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
sns.set_theme(style='whitegrid')

# 中文术语到英文的映射
TERM_MAPPING = {
    '模型性能指标热力图': 'Model Performance Metrics Heatmap',
    '模型名称': 'Model Name',
    '性能指标': 'Performance Metrics',
    '性能指标值': 'Performance Metric Value',
    '模型性能雷达图': 'Model Performance Radar Chart',
    '模型综合性能排名': 'Model Performance Ranking',
    '模型名称': 'Model Name',
    '综合性能得分': 'Composite Performance Score',
    '准确率': 'Accuracy',
    '精确率': 'Precision',
    '召回率': 'Recall',
    'F1分数': 'F1 Score',
    '特异性': 'Specificity',
    '敏感性': 'Sensitivity',
    '阴性预测值': 'NPV',
    '阳性预测值': 'PPV'
}

def translate_term(term):
    """将中文术语翻译为英文"""
    return TERM_MAPPING.get(term, term)

# 模型列表
MODEL_NAMES = ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
               'Logistic', 'SVM', 'NeuralNet', 'NaiveBayes', 'KNN']

def bootstrap_roc_auc_ci(y_true, y_score, confidence_level=0.95, n_bootstrap=1000, random_state=42):
    """
    使用Bootstrap方法计算ROC AUC的置信区间

    Args:
        y_true: 真实标签
        y_score: 预测概率或决策函数值
        confidence_level: 置信水平，默认0.95
        n_bootstrap: Bootstrap重采样次数，默认1000
        random_state: 随机种子

    Returns:
        tuple: (auc, ci_lower, ci_upper)
    """
    if y_score is None or len(y_score) != len(y_true):
        return None, None, None

    try:
        # 计算原始AUC
        original_auc = roc_auc_score(y_true, y_score)

        # Bootstrap重采样
        np.random.seed(random_state)
        bootstrap_aucs = []

        n_samples = len(y_true)
        for _ in range(n_bootstrap):
            # 有放回抽样
            indices = np.random.choice(n_samples, size=n_samples, replace=True)
            y_true_boot = np.array(y_true)[indices]
            y_score_boot = np.array(y_score)[indices]

            # 确保bootstrap样本中包含两个类别
            if len(np.unique(y_true_boot)) > 1:
                try:
                    boot_auc = roc_auc_score(y_true_boot, y_score_boot)
                    bootstrap_aucs.append(boot_auc)
                except:
                    continue

        if len(bootstrap_aucs) < 10:  # 如果有效的bootstrap样本太少
            return original_auc, None, None

        # 计算置信区间
        alpha = 1 - confidence_level
        ci_lower = np.percentile(bootstrap_aucs, 100 * alpha / 2)
        ci_upper = np.percentile(bootstrap_aucs, 100 * (1 - alpha / 2))

        return original_auc, ci_lower, ci_upper

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.warning(f"Bootstrap置信区间计算失败: {e}")
        return roc_auc_score(y_true, y_score) if y_score is not None else None, None, None

def calculate_comprehensive_metrics(y_true, y_pred, y_score=None):
    """
    计算综合性能指标

    Args:
        y_true: 真实标签
        y_pred: 预测标签
        y_score: 连续分数（predict_proba[:,1] 或 decision_function）

    Returns:
        dict: 包含所有性能指标的字典
    """
    metrics = {}

    # 基础分类指标
    metrics['accuracy'] = accuracy_score(y_true, y_pred)
    metrics['precision'] = precision_score(y_true, y_pred, zero_division=0)
    metrics['recall'] = recall_score(y_true, y_pred, zero_division=0)
    metrics['f1_score'] = f1_score(y_true, y_pred, zero_division=0)

    # 混淆矩阵相关指标
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    metrics['specificity'] = tn / (tn + fp) if (tn + fp) > 0 else 0
    metrics['sensitivity'] = tp / (tp + fn) if (tp + fn) > 0 else 0
    metrics['npv'] = tn / (tn + fn) if (tn + fn) > 0 else 0  # 阴性预测值
    metrics['ppv'] = tp / (tp + fp) if (tp + fp) > 0 else 0  # 阳性预测值

    # 概率相关指标（需连续分数）
    if y_score is not None and len(y_score) == len(y_true):
        # 计算ROC AUC及其95%置信区间
        auc_roc, ci_lower, ci_upper = bootstrap_roc_auc_ci(y_true, y_score)
        metrics['auc_roc'] = auc_roc
        if ci_lower is not None and ci_upper is not None:
            metrics['auc_roc_ci'] = f"({ci_lower:.3f}, {ci_upper:.3f})"
            metrics['auc_roc_ci_lower'] = ci_lower
            metrics['auc_roc_ci_upper'] = ci_upper
        else:
            metrics['auc_roc_ci'] = "N/A"
            metrics['auc_roc_ci_lower'] = None
            metrics['auc_roc_ci_upper'] = None

        metrics['auc_pr'] = average_precision_score(y_true, y_score)
    else:
        metrics['auc_roc'] = None
        metrics['auc_roc_ci'] = "N/A"
        metrics['auc_roc_ci_lower'] = None
        metrics['auc_roc_ci_upper'] = None
        metrics['auc_pr'] = None

    # 其他高级指标
    metrics['mcc'] = matthews_corrcoef(y_true, y_pred)  # 马修斯相关系数
    metrics['kappa'] = cohen_kappa_score(y_true, y_pred)  # Cohen's Kappa

    # 平衡准确率
    metrics['balanced_accuracy'] = (metrics['sensitivity'] + metrics['specificity']) / 2

    return metrics

def load_model_results(selected_models=None):
    """
    加载所有模型的缓存结果
    
    Args:
        selected_models: 可选，要加载的模型列表。如果为None，则加载所有模型。
    
    Returns:
        dict: 包含所有模型结果的字典
    """
    results = {}
    
    # 确定要加载的模型列表
    models_to_load = selected_models if selected_models is not None else MODEL_NAMES
    
    for model_name in models_to_load:
        if model_name not in MODEL_NAMES:
            print(f"Warning: Model {model_name} is not in the predefined MODEL_NAMES list")
            continue
            
        cache_file = CONFIG['cache_path'] / f"{model_name}_results.joblib"
        if cache_file.exists():
            try:
                results[model_name] = load(cache_file)
                print(f"Successfully loaded cached results for model {model_name}")
            except Exception as e:
                print(f"Failed to load model {model_name}: {e}")
        else:
            print(f"Cache file for model {model_name} does not exist: {cache_file}")
    
    return results

def generate_performance_dataframe(results):
    """
    生成性能指标数据框

    Args:
        results: 模型结果字典

    Returns:
        pd.DataFrame: 包含所有模型性能指标的数据框
    """
    performance_data = []

    for model_name, data in results.items():
        y_true = data['y_true']
        y_pred = data['y_pred']
        y_score = data.get('y_score', None)

        # 计算综合指标
        metrics = calculate_comprehensive_metrics(y_true, y_pred, y_score)
        metrics['model_name'] = model_name
        performance_data.append(metrics)

    df = pd.DataFrame(performance_data)
    df = df.set_index('model_name')

    # 确保数值列都是float类型，处理None值，但保留字符串格式的置信区间
    for col in df.columns:
        if col != 'model_name' and not col.endswith('_ci'):
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0.0)

    return df

def create_performance_heatmap(df, save_path):
    """
    创建性能指标热力图
    
    Args:
        df: 性能指标数据框
        save_path: 保存路径
    """
    plt.figure(figsize=(14, 10))
    
    # 选择主要指标进行可视化
    main_metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'specificity', 
                   'auc_roc', 'auc_pr', 'mcc', 'kappa', 'balanced_accuracy']
    
    # 确保所有指标都存在
    available_metrics = [metric for metric in main_metrics if metric in df.columns]
    
    # 创建热力图
    sns.heatmap(df[available_metrics].T, annot=True, cmap='RdYlBu_r', 
                center=0.5, fmt='.3f', cbar_kws={'label': translate_term('性能指标值')})
    
    plt.title(translate_term('模型性能指标热力图'), fontsize=16, fontweight='bold', pad=20)
    plt.xlabel(translate_term('模型名称'), fontsize=12)
    plt.ylabel(translate_term('性能指标'), fontsize=12)
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    
    # 保存图表
    save_path.mkdir(parents=True, exist_ok=True)
    plt.savefig(save_path / 'performance_heatmap.png', dpi=CONFIG['dpi'], bbox_inches='tight')
    plt.close()
    print(f"Performance heatmap saved to: {save_path / 'performance_heatmap.png'}")

def create_radar_comparison(df, save_path):
    """
    创建多模型雷达图比较
    
    Args:
        df: 性能指标数据框
        save_path: 保存路径
    """
    # 选择关键指标
    key_metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'specificity', 'auc_roc']
    available_metrics = [metric for metric in key_metrics if metric in df.columns]
    
    if len(available_metrics) < 3:
        print("Insufficient metrics available, skipping radar chart generation")
        return
    
    # 设置雷达图参数
    N = len(available_metrics)
    angles = np.linspace(0, 2 * np.pi, N, endpoint=False).tolist()
    angles += angles[:1]
    
    # 创建子图 - 使用3x4布局以容纳所有10个模型
    rows = 3
    cols = 4
    fig, axes = plt.subplots(rows, cols, figsize=(24, 18), subplot_kw=dict(projection='polar'))
    axes = axes.flatten()
    
    colors = sns.color_palette('tab10', len(df))
    
    for idx, (model_name, row) in enumerate(df.iterrows()):
        if idx >= rows * cols:  # 最多显示12个模型
            print(f"Warning: Only showing first {rows * cols} models in radar chart")
            break
            
        ax = axes[idx]
        values = [row[metric] for metric in available_metrics]
        values += values[:1]  # 闭合雷达图
        
        ax.plot(angles, values, 'o-', linewidth=2, label=model_name, color=colors[idx])
        ax.fill(angles, values, alpha=0.25, color=colors[idx])
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(available_metrics)
        ax.set_ylim(0, 1)
        ax.set_title(f'{model_name} Performance Radar', size=12, fontweight='bold')
        ax.grid(True)
    
    # 隐藏多余的子图
    for idx in range(len(df), rows * cols):
        axes[idx].set_visible(False)
    
    plt.tight_layout()
    save_path.mkdir(parents=True, exist_ok=True)
    plt.savefig(save_path / 'radar_comparison.png', dpi=CONFIG['dpi'], bbox_inches='tight')
    plt.close()
    print(f"Radar comparison chart saved to: {save_path / 'radar_comparison.png'}")

def calculate_strategy_scores(df):
    """
    计算所有策略的综合得分

    Args:
        df: 性能指标数据框

    Returns:
        dict: 包含所有策略得分的字典
    """
    # 与best_model_selector保持一致的策略权重配置
    strategy_weights = {
        'performance': {
            'auc_roc': 0.3,
            'f1_score': 0.25,
            'accuracy': 0.2,
            'precision': 0.15,
            'mcc': 0.1
        },
        'robustness': {
            'mcc': 0.3,
            'balanced_accuracy': 0.25,
            'kappa': 0.2,
            'f1_score': 0.15,
            'auc_roc': 0.1
        },
        'balanced': {
            'auc_roc': 0.25,
            'f1_score': 0.2,
            'mcc': 0.2,
            'accuracy': 0.15,
            'precision': 0.1,
            'recall': 0.1
        },
        'interpretability': {
            'f1_score': 0.3,
            'accuracy': 0.25,
            'interpretability_score': 0.25,
            'auc_roc': 0.2
        }
    }

    strategy_scores = {}

    for strategy, weights in strategy_weights.items():
        df_copy = df.copy()
        df_copy[f'{strategy}_score'] = 0.0

        # 计算加权得分，处理缺失指标
        for model_idx in df_copy.index:
            score = 0
            weight_sum = 0

            for metric, weight in weights.items():
                if metric in df_copy.columns and not pd.isna(df_copy.loc[model_idx, metric]):
                    score += df_copy.loc[model_idx, metric] * weight
                    weight_sum += weight
                elif metric == 'interpretability_score':
                    # 可解释性得分：简单模型得分更高
                    interpretability_scores = {
                        'DecisionTree': 0.9, 'RandomForest': 0.7, 'Logistic': 0.8,
                        'NaiveBayes': 0.8, 'KNN': 0.6, 'SVM': 0.5,
                        'XGBoost': 0.4, 'LightGBM': 0.4, 'CatBoost': 0.4,
                        'NeuralNet': 0.3
                    }
                    model_name = model_idx
                    interp_score = interpretability_scores.get(model_name, 0.5)
                    score += interp_score * weight
                    weight_sum += weight

            # 归一化得分
            if weight_sum > 0:
                df_copy.loc[model_idx, f'{strategy}_score'] = score / weight_sum
            else:
                df_copy.loc[model_idx, f'{strategy}_score'] = 0

        # 按策略得分排序
        df_sorted = df_copy.sort_values(f'{strategy}_score', ascending=False)
        strategy_scores[strategy] = df_sorted

    return strategy_scores, strategy_weights

def create_ranking_chart(df, save_path):
    """
    创建模型排名图表

    Args:
        df: 性能指标数据框
        save_path: 保存路径
    """
    # 计算所有策略得分
    strategy_scores, strategy_weights = calculate_strategy_scores(df)

    # 使用balanced策略作为默认排序
    df_sorted = strategy_scores['balanced']
    
    # 创建排名图表
    plt.figure(figsize=(12, 8))

    # 条形图
    bars = plt.bar(range(len(df_sorted)), df_sorted['balanced_score'],
                   color=sns.color_palette('viridis', len(df_sorted)))

    # 添加数值标签
    for i, (idx, row) in enumerate(df_sorted.iterrows()):
        plt.text(i, row['balanced_score'] + 0.01, f'{row["balanced_score"]:.3f}',
                ha='center', va='bottom', fontweight='bold')

    plt.xlabel(translate_term('模型名称'), fontsize=12)
    plt.ylabel(translate_term('综合性能得分'), fontsize=12)
    plt.title(translate_term('模型综合性能排名 (平衡策略)'), fontsize=16, fontweight='bold', pad=20)
    plt.xticks(range(len(df_sorted)), df_sorted.index, rotation=45)
    plt.ylim(0, 1)
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()

    save_path.mkdir(parents=True, exist_ok=True)
    plt.savefig(save_path / 'model_ranking.png', dpi=CONFIG['dpi'], bbox_inches='tight')
    plt.close()
    print(f"Model ranking chart saved to: {save_path / 'model_ranking.png'}")

    return df_sorted, strategy_scores, strategy_weights

def generate_html_report(df, df_sorted, save_path, strategy_scores=None, strategy_weights=None):
    """
    生成HTML格式的详细报告

    Args:
        df: 性能指标数据框
        df_sorted: 按综合得分排序的数据框
        save_path: 保存路径
        strategy_scores: 所有策略的得分结果
        strategy_weights: 所有策略的权重配置
    """
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>模型性能比较报告</title>
        <style>
            body {{
                font-family: 'Arial', sans-serif;
                margin: 40px;
                background-color: #f5f5f5;
            }}
            .container {{
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            h1 {{
                color: #2c3e50;
                text-align: center;
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
            }}
            h2 {{
                color: #34495e;
                margin-top: 30px;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 12px;
                text-align: center;
            }}
            th {{
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }}
            tr:nth-child(even) {{
                background-color: #f2f2f2;
            }}
            .best-score {{
                background-color: #2ecc71 !important;
                color: white;
                font-weight: bold;
            }}
            .summary {{
                background-color: #ecf0f1;
                padding: 20px;
                border-radius: 5px;
                margin: 20px 0;
            }}
            .metric-description {{
                font-size: 0.9em;
                color: #7f8c8d;
                margin-top: 10px;
            }}
            .strategy-explanation {{
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                border: 1px solid #dee2e6;
            }}
            .strategy-explanation h3 {{
                color: #495057;
                margin-top: 20px;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 2px solid #e9ecef;
            }}
            .strategy-explanation ul {{
                margin: 10px 0;
                padding-left: 20px;
            }}
            .strategy-explanation li {{
                margin: 5px 0;
                line-height: 1.4;
            }}
            .strategy-explanation p {{
                margin: 10px 0;
                line-height: 1.5;
            }}
            .strategy-explanation em {{
                color: #6c757d;
                font-size: 0.9em;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>模型性能比较报告</h1>
            <p style="text-align: center; color: #7f8c8d;">生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <div class="summary">
                <h2>📊 执行摘要</h2>
                <p><strong>最佳模型 (平衡策略):</strong> {df_sorted.index[0]} (综合得分: {df_sorted.iloc[0]['balanced_score']:.3f})</p>
                <p><strong>比较模型数量:</strong> {len(df)}</p>
                <p><strong>评估指标数量:</strong> {len(df.columns)}</p>
                <p><strong>评估策略数量:</strong> 4 (性能优先、稳健性优先、平衡策略、可解释性优先)</p>
            </div>

            <h2>📋 评分策略说明</h2>
            <div class="strategy-explanation">
                <h3>🎯 性能优先策略 (Performance)</h3>
                <p>注重模型的整体预测性能，适用于对准确性要求极高的场景。</p>
                <ul>
                    <li>AUC-ROC: 30% - 衡量分类器整体区分能力</li>
                    <li>F1-Score: 25% - 平衡精确率和召回率</li>
                    <li>准确率: 20% - 整体预测正确率</li>
                    <li>精确率: 15% - 预测为正例的准确性</li>
                    <li>MCC: 10% - 马修斯相关系数，适合不平衡数据</li>
                </ul>

                <h3>🛡️ 稳健性优先策略 (Robustness)</h3>
                <p>注重模型的稳定性和可靠性，适用于数据质量不稳定或不平衡的场景。</p>
                <ul>
                    <li>MCC: 30% - 马修斯相关系数，对不平衡数据敏感</li>
                    <li>平衡准确率: 25% - 考虑类别平衡的准确率</li>
                    <li>Kappa系数: 20% - 衡量分类一致性</li>
                    <li>F1-Score: 15% - 综合性能指标</li>
                    <li>AUC-ROC: 10% - 基础区分能力</li>
                </ul>

                <h3>⚖️ 平衡策略 (Balanced)</h3>
                <p>综合考虑各项指标，适用于大多数应用场景的通用选择。</p>
                <ul>
                    <li>AUC-ROC: 25% - 整体区分能力</li>
                    <li>F1-Score: 20% - 综合性能</li>
                    <li>MCC: 20% - 稳健性指标</li>
                    <li>准确率: 15% - 基础性能</li>
                    <li>精确率: 10% - 预测准确性</li>
                    <li>召回率: 10% - 覆盖完整性</li>
                </ul>

                <h3>🔍 可解释性优先策略 (Interpretability)</h3>
                <p>在保证性能的同时，优先选择易于理解和解释的模型。</p>
                <ul>
                    <li>F1-Score: 30% - 基础性能保证</li>
                    <li>准确率: 25% - 整体表现</li>
                    <li>可解释性得分: 25% - 模型复杂度和可理解性</li>
                    <li>AUC-ROC: 20% - 区分能力</li>
                </ul>
                <p><em>可解释性得分：决策树(0.9) > 逻辑回归/朴素贝叶斯(0.8) > 随机森林(0.7) > KNN(0.6) > SVM(0.5) > 集成模型(0.4) > 神经网络(0.3)</em></p>
            </div>

            <h2>🏆 模型排名 (平衡策略)</h2>
            <table>
                <tr>
                    <th>排名</th>
                    <th>模型名称</th>
                    <th>综合得分</th>
                    <th>准确率</th>
                    <th>精确率</th>
                    <th>召回率</th>
                    <th>F1分数</th>
                    <th>AUC-ROC</th>
                </tr>
    """

    # 添加排名表格
    for rank, (model_name, row) in enumerate(df_sorted.iterrows(), 1):
        html_content += f"""
                <tr>
                    <td>{rank}</td>
                    <td><strong>{model_name}</strong></td>
                    <td class="{'best-score' if rank == 1 else ''}">{row['balanced_score']:.3f}</td>
                    <td>{row.get('accuracy', 0):.3f}</td>
                    <td>{row.get('precision', 0):.3f}</td>
                    <td>{row.get('recall', 0):.3f}</td>
                    <td>{row.get('f1_score', 0):.3f}</td>
                    <td>{row.get('auc_roc', 0):.3f}</td>
                </tr>
        """
    
    html_content += """
            </table>
    """

    # 添加所有策略的排名表
    if strategy_scores:
        strategy_names = {
            'performance': '🎯 性能优先策略排名',
            'robustness': '🛡️ 稳健性优先策略排名',
            'balanced': '⚖️ 平衡策略排名',
            'interpretability': '🔍 可解释性优先策略排名'
        }

        for strategy, title in strategy_names.items():
            if strategy in strategy_scores:
                strategy_df = strategy_scores[strategy]
                html_content += f"""
            <h2>{title}</h2>
            <table>
                <tr>
                    <th>排名</th>
                    <th>模型名称</th>
                    <th>策略得分</th>
                    <th>准确率</th>
                    <th>F1分数</th>
                    <th>AUC-ROC</th>
                    <th>MCC</th>
                </tr>
                """

                for rank, (model_name, row) in enumerate(strategy_df.iterrows(), 1):
                    score_col = f'{strategy}_score'
                    html_content += f"""
                <tr>
                    <td>{rank}</td>
                    <td><strong>{model_name}</strong></td>
                    <td class="{'best-score' if rank == 1 else ''}">{row[score_col]:.3f}</td>
                    <td>{row.get('accuracy', 0):.3f}</td>
                    <td>{row.get('f1_score', 0):.3f}</td>
                    <td>{row.get('auc_roc', 0):.3f}</td>
                    <td>{row.get('mcc', 0):.3f}</td>
                </tr>
                    """

                html_content += """
            </table>
                """

    html_content += """
            <h2>📈 详细性能指标</h2>
            <table>
                <tr>
                    <th>模型</th>
    """
    
    # 英文列名到中文的映射
    column_mapping = {
        'accuracy': '准确率',
        'precision': '精确率', 
        'recall': '召回率',
        'f1_score': 'F1分数',
        'specificity': '特异性',
        'sensitivity': '敏感性',
        'npv': '阴性预测值',
        'ppv': '阳性预测值',
        'auc_roc': 'AUC-ROC',
        'auc_pr': 'AUC-PR',
        'mcc': 'MCC',
        'kappa': 'Kappa',
        'balanced_accuracy': '平衡准确率'
    }
    
    # 添加指标列标题
    for col in df.columns:
        if col != 'composite_score':
            chinese_name = column_mapping.get(col, col)
            html_content += f"<th>{chinese_name}</th>"
    
    html_content += "</tr>"
    
    # 添加详细指标数据
    for model_name, row in df.iterrows():
        html_content += f"<tr><td><strong>{model_name}</strong></td>"
        for col in df.columns:
            # 跳过策略得分列
            if not col.endswith('_score'):
                value = row[col]
                # 高亮最佳值（只对数值列进行比较）
                if isinstance(value, (int, float)) and not col.endswith('_ci'):
                    is_best = value == df[col].max()
                    cell_class = 'best-score' if is_best else ''
                    html_content += f'<td class="{cell_class}">{value:.3f}</td>'
                else:
                    # 对于字符串格式的置信区间，直接显示
                    html_content += f'<td>{value}</td>'
        html_content += "</tr>"
    
    html_content += f"""
            </table>
            
            <div class="metric-description">
                <h2>📝 指标说明</h2>
                <ul>
                    <li><strong>准确率(Accuracy):</strong> 正确预测实例的比例</li>
                    <li><strong>精确率(Precision):</strong> 预测为正例中实际为正例的比例</li>
                    <li><strong>召回率(Recall):</strong> 实际正例中被正确预测的比例</li>
                    <li><strong>F1分数(F1 Score):</strong> 精确率和召回率的调和平均数</li>
                    <li><strong>特异性(Specificity):</strong> 实际负例中被正确预测的比例</li>
                    <li><strong>AUC-ROC:</strong> ROC曲线下面积，衡量分类器性能</li>
                    <li><strong>AUC-PR:</strong> 精确率-召回率曲线下面积</li>
                    <li><strong>MCC:</strong> 马修斯相关系数，平衡的分类性能度量</li>
                    <li><strong>Kappa:</strong> 科恩卡帕系数，考虑随机一致性</li>
                    <li><strong>平衡准确率(Balanced Accuracy):</strong> 敏感性和特异性的平均值</li>
                </ul>
            </div>
            
            <div class="summary">
                <h2>💡 推荐建议</h2>
                <p>基于<strong>平衡策略</strong>的综合性能评估，我们推荐使用 <strong>{df_sorted.index[0]}</strong> 模型进行后续任务。</p>
                <p>不同策略下的最佳模型可能不同，请根据您的具体需求选择合适的评估策略：</p>
                <ul>
                    <li><strong>性能优先</strong>：追求最高预测准确性</li>
                    <li><strong>稳健性优先</strong>：数据不平衡或质量不稳定时</li>
                    <li><strong>平衡策略</strong>：大多数应用场景的通用选择</li>
                    <li><strong>可解释性优先</strong>：需要理解模型决策过程时</li>
                </ul>
                <p>详细的策略权重配置和各策略排名请参考上方表格。</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    # 保存HTML报告
    save_path.mkdir(parents=True, exist_ok=True)
    with open(save_path / 'performance_report.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"HTML report saved to: {save_path / 'performance_report.html'}")

def save_performance_json(df, df_sorted, save_path):
    """
    保存性能数据为JSON格式

    Args:
        df: 性能指标数据框
        df_sorted: 按综合得分排序的数据框
        save_path: 保存路径
    """
    performance_data = {
        'generation_time': datetime.now().isoformat(),
        'best_model': df_sorted.index[0],
        'best_score': float(df_sorted.iloc[0]['balanced_score']),
        'model_count': len(df),
        'detailed_metrics': df.to_dict('index'),
        'ranking': [(model, float(score)) for model, score in
                   zip(df_sorted.index, df_sorted['balanced_score'])]
    }

    save_path.mkdir(parents=True, exist_ok=True)
    with open(save_path / 'performance_data.json', 'w', encoding='utf-8') as f:
        json.dump(performance_data, f, ensure_ascii=False, indent=2)

    print(f"Performance data JSON saved to: {save_path / 'performance_data.json'}")

def save_performance_excel(df, df_sorted, save_path):
    """
    保存性能数据为Excel格式，包含多个工作表

    Args:
        df: 性能指标数据框
        df_sorted: 按综合得分排序的数据框
        save_path: 保存路径
    """
    try:
        import openpyxl
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        from openpyxl.utils.dataframe import dataframe_to_rows

        save_path.mkdir(parents=True, exist_ok=True)
        excel_path = save_path / 'model_performance_comparison.xlsx'

        # 创建工作簿
        wb = openpyxl.Workbook()

        # 删除默认工作表
        wb.remove(wb.active)

        # 中文列名映射
        column_mapping = {
            'accuracy': '准确率',
            'precision': '精确率',
            'recall': '召回率',
            'f1_score': 'F1分数',
            'specificity': '特异性',
            'sensitivity': '敏感性',
            'npv': '阴性预测值',
            'ppv': '阳性预测值',
            'auc_roc': 'AUC-ROC',
            'auc_pr': 'AUC-PR',
            'mcc': 'MCC',
            'kappa': 'Kappa',
            'balanced_accuracy': '平衡准确率',
            'composite_score': '综合得分'
        }

        # 1. 模型排名工作表
        ws_ranking = wb.create_sheet("模型排名")

        # 准备排名数据
        ranking_data = df_sorted.copy()
        ranking_data.insert(0, '排名', range(1, len(ranking_data) + 1))
        ranking_data.index.name = '模型名称'

        # 重命名列
        ranking_columns = ['排名'] + [column_mapping.get(col, col) for col in df_sorted.columns]

        # 写入标题行
        ws_ranking.append(['模型名称'] + ranking_columns)

        # 写入数据
        for idx, (model_name, row) in enumerate(ranking_data.iterrows()):
            row_data = [model_name, idx + 1]  # 模型名称和排名
            for col in df_sorted.columns:
                value = row[col]
                if isinstance(value, float):
                    row_data.append(round(value, 4))
                else:
                    row_data.append(value)
            ws_ranking.append(row_data)

        # 2. 详细指标工作表
        ws_details = wb.create_sheet("详细指标")

        # 准备详细数据
        details_data = df.copy()
        details_data.index.name = '模型名称'

        # 写入标题行
        detail_columns = [column_mapping.get(col, col) for col in df.columns]
        ws_details.append(['模型名称'] + detail_columns)

        # 写入数据
        for model_name, row in details_data.iterrows():
            row_data = [model_name]
            for col in df.columns:
                value = row[col]
                if isinstance(value, float):
                    row_data.append(round(value, 4))
                else:
                    row_data.append(value)
            ws_details.append(row_data)

        # 3. 摘要信息工作表
        ws_summary = wb.create_sheet("摘要信息")

        summary_info = [
            ['报告生成时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['最佳模型', df_sorted.index[0]],
            ['最佳模型综合得分', round(df_sorted.iloc[0]['balanced_score'], 4)],
            ['比较模型数量', len(df)],
            ['评估指标数量', len([col for col in df.columns if not col.endswith('_score')])],
            ['', ''],
            ['指标说明', ''],
            ['准确率(Accuracy)', '正确预测实例的比例'],
            ['精确率(Precision)', '预测为正例中实际为正例的比例'],
            ['召回率(Recall)', '实际正例中被正确预测的比例'],
            ['F1分数(F1 Score)', '精确率和召回率的调和平均数'],
            ['特异性(Specificity)', '实际负例中被正确预测的比例'],
            ['AUC-ROC', 'ROC曲线下面积，衡量分类器性能'],
            ['AUC-PR', '精确率-召回率曲线下面积'],
            ['MCC', '马修斯相关系数，平衡的分类性能度量'],
            ['Kappa', '科恩卡帕系数，考虑随机一致性'],
            ['平衡准确率', '敏感性和特异性的平均值']
        ]

        for row in summary_info:
            ws_summary.append(row)

        # 应用样式
        def apply_table_style(worksheet, start_row=1):
            # 标题行样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")

            # 数据行样式
            data_alignment = Alignment(horizontal="center", vertical="center")
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 应用标题行样式
            for cell in worksheet[start_row]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                cell.border = border

            # 应用数据行样式
            for row in worksheet.iter_rows(min_row=start_row + 1):
                for cell in row:
                    cell.alignment = data_alignment
                    cell.border = border

            # 自动调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 20)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        # 应用样式到各工作表
        apply_table_style(ws_ranking)
        apply_table_style(ws_details)

        # 摘要工作表特殊样式
        for row in ws_summary.iter_rows():
            for cell in row:
                if cell.row == 1 or cell.value in ['指标说明']:
                    cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal="left", vertical="center")

        # 保存Excel文件
        wb.save(excel_path)
        print(f"Excel performance report saved to: {excel_path}")

    except ImportError:
        print("Warning: openpyxl not installed, skipping Excel export")
        print("Install with: pip install openpyxl")
    except Exception as e:
        print(f"Error saving Excel report: {e}")

def generate_comprehensive_report(selected_models=None):
    """
    生成综合性能比较报告
    主函数，协调所有报告生成功能

    Args:
        selected_models: 可选，要包含在报告中的模型列表。如果为None，则包含所有可用模型。
    """
    print("Starting model performance comparison report generation...")

    # 创建报告目录
    report_path = CONFIG['report_path']
    report_path.mkdir(parents=True, exist_ok=True)
    
    # 加载模型结果
    results = load_model_results(selected_models)
    
    if not results:
        print("No model results available, cannot generate report!")
        return
    
    print(f"Successfully loaded results for {len(results)} models")
    
    # 生成性能数据框
    df = generate_performance_dataframe(results)
    print("Performance metrics calculation completed")
    
    # 创建可视化图表
    create_performance_heatmap(df, report_path)
    create_radar_comparison(df, report_path)
    df_sorted, strategy_scores, strategy_weights = create_ranking_chart(df, report_path)

    # 生成报告文件
    generate_html_report(df, df_sorted, report_path, strategy_scores, strategy_weights)
    save_performance_json(df, df_sorted, report_path)
    save_performance_excel(df, df_sorted, report_path)

    # 保存CSV格式的详细数据
    df.to_csv(report_path / 'performance_metrics.csv', encoding='utf-8-sig')
    print(f"Performance metrics CSV saved to: {report_path / 'performance_metrics.csv'}")

    print(f"\n📊 Model performance comparison report generation completed!")
    print(f"📁 Report files location: {report_path}")
    print(f"🏆 Best model (Balanced Strategy): {df_sorted.index[0]} (Score: {df_sorted.iloc[0]['balanced_score']:.3f})")
    print(f"📄 View detailed report: {report_path / 'performance_report.html'}")
    print(f"📊 Excel report: {report_path / 'model_performance_comparison.xlsx'}")
    print(f"📋 Report includes 4 evaluation strategies with detailed weight configurations")

if __name__ == "__main__":
    generate_comprehensive_report()