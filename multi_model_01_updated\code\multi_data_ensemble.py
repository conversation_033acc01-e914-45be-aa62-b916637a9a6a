#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多数据源模型融合功能
支持基模型使用不同数据源进行训练，融合模型可选择使用统一数据或原始数据
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import json
from pathlib import Path
from joblib import dump, load
from sklearn.model_selection import cross_val_score, StratifiedKFold, train_test_split
from sklearn.ensemble import VotingClassifier, StackingClassifier, RandomForestClassifier
from sklearn.feature_selection import SelectKBest, f_classif
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, confusion_matrix, classification_report,
    roc_curve, auc
)
from sklearn.base import BaseEstimator, ClassifierMixin
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# SHAP 相关导入和可用性检查
try:
    import shap
    SHAP_AVAILABLE = True
    print("SHAP库已成功导入，可解释性分析功能已启用")
except ImportError:
    SHAP_AVAILABLE = False
    print("警告：SHAP库未安装，可解释性分析功能将被禁用")
    print("请运行 'pip install shap' 来安装SHAP库")

# 导入数据预处理和模型训练函数
from data_preprocessing import load_and_preprocess_data, EnsembleFeatureSelector, load_and_clean_data
from model_training import (
    train_decision_tree, train_random_forest, train_xgboost, 
    train_lightgbm, train_catboost, train_logistic, 
    train_svm, train_knn, train_naive_bayes, train_neural_net
)
from config import OUTPUT_PATH, CACHE_PATH, ENSEMBLE_PATH, PROJECT_ROOT

# 尝试导入绘图工具
try:
    from plot_utils import get_font_properties, save_plot, translate_term
except ImportError:
    # 如果无法导入，提供基本的绘图支持
    def get_font_properties():
        return None
    
    def save_plot(fig, model_name, plot_type, file_name=None, close_fig=True):
        try:
            # 确保目标目录存在
            model_dir = OUTPUT_PATH / model_name
            model_dir.mkdir(parents=True, exist_ok=True)
            
            # 指定完整的保存路径
            if file_name is None:
                import time
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                file_name = f"{model_name}_{plot_type}_{timestamp}.png"
            
            filepath = model_dir / file_name
            
            # 保存图表
            fig.savefig(filepath, dpi=150, bbox_inches='tight')
            if close_fig:
                plt.close(fig)
            print(f"图表已保存至: {filepath}")
            return str(filepath)
        except Exception as e:
            print(f"保存图表失败: {e}")
            if close_fig:
                plt.close(fig)
            return None
    
    def translate_term(term):
        return term

# 使用统一配置
from config import get_model_functions, PLOT_CONFIG, MULTI_DATA_CACHE_PATH

# 配置字典
CONFIG = {
    'output_path': OUTPUT_PATH,
    'output_dir': OUTPUT_PATH,  # 添加output_dir键
    'cache_path': CACHE_PATH,
    'multi_data_cache_path': MULTI_DATA_CACHE_PATH,
    'ensemble_path': ENSEMBLE_PATH,
    'dpi': PLOT_CONFIG['dpi'],
    'figsize': PLOT_CONFIG['figsize']
}

# 获取模型训练函数
MODEL_FUNCTIONS = get_model_functions()

# 设置绘图风格
sns.set_theme(style='whitegrid')

# 新增：统一的SHAP解释器获取函数
def get_shap_explainer(model, X_background, X_sample=None):
    """
    获取适合模型类型的SHAP解释器
    
    Args:
        model: 已训练的模型
        X_background: 背景数据集
        X_sample: 样本数据集(可选)
        
    Returns:
        tuple: (explainer, shap_values, explainer_type)
    """
    if not SHAP_AVAILABLE:
        return None, None, None
        
    explainer = None
    shap_values = None
    explainer_type = None
    
    # 首先尝试TreeExplainer（适用于树模型）
    try:
        if hasattr(model, 'estimators_') or 'Tree' in str(type(model)) or hasattr(model, 'tree_'):
            explainer = shap.TreeExplainer(model)
            explainer_type = 'TreeExplainer'
            if X_sample is not None:
                shap_values = explainer.shap_values(X_sample)
    except Exception as e:
        print(f"TreeExplainer初始化失败: {e}")
    
    # 如果TreeExplainer失败，使用KernelExplainer
    if explainer is None:
        try:
            # 创建一个包装函数，确保输入维度正确
            def model_predict(X):
                try:
                    return model.predict_proba(X)
                except Exception:
                    try:
                        preds = model.predict(X)
                        return np.column_stack((1-preds, preds))  # 转换为二分类概率格式
                    except Exception:
                        return np.zeros((X.shape[0], 2))
            
            explainer = shap.KernelExplainer(model_predict, X_background)
            explainer_type = 'KernelExplainer'
            if X_sample is not None:
                shap_values = explainer.shap_values(X_sample)
        except Exception as e:
            print(f"KernelExplainer初始化失败: {e}")
            return None, None, None
    
    return explainer, shap_values, explainer_type

# 新增：统一的SHAP图表生成函数
def generate_shap_plots(shap_values, X_sample, feature_names, output_dir, model_name, prefix=""):
    """
    生成SHAP分析图表
    
    Args:
        shap_values: SHAP值
        X_sample: 样本数据
        feature_names: 特征名称
        output_dir: 输出目录
        model_name: 模型名称
        prefix: 文件名前缀
    
    Returns:
        dict: 生成的图表路径和重要特征
    """
    if not SHAP_AVAILABLE or shap_values is None:
        return {}
    
    result = {
        'plots': {},
        'top_features': []
    }
    
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 处理不同维度的shap_values
    if isinstance(shap_values, list) and len(shap_values) > 1:
        # 二分类情况，使用正类的SHAP值
        shap_values_plot = shap_values[1]
    else:
        shap_values_plot = shap_values
    
    # 确保X_sample是DataFrame
    if not isinstance(X_sample, pd.DataFrame):
        X_sample = pd.DataFrame(X_sample, columns=feature_names)
    
    try:
        # 1. 生成SHAP摘要图
        plt.figure(figsize=(12, 8))
        shap.summary_plot(shap_values_plot, X_sample, feature_names=feature_names, show=False, max_display=15)
        plt.title(f'{model_name} - SHAP Summary Plot', fontsize=14, pad=20)
        plt.tight_layout()
        summary_path = output_dir / f'{prefix}{model_name}_shap_summary.png'
        plt.savefig(summary_path, dpi=300, bbox_inches='tight')
        plt.close()
        result['plots']['summary'] = str(summary_path)
        
        # 2. 生成特征重要性条形图
        plt.figure(figsize=(10, 8))
        feature_importance = np.abs(shap_values_plot).mean(0)
        if len(feature_importance.shape) > 1:
            feature_importance = feature_importance.mean(1)
        
        # 确保长度匹配
        if len(feature_importance) != len(feature_names):
            min_len = min(len(feature_importance), len(feature_names))
            feature_importance = feature_importance[:min_len]
            feature_names_plot = feature_names[:min_len]
        else:
            feature_names_plot = feature_names
            
        # 创建特征重要性DataFrame
        importance_df = pd.DataFrame({
            'feature': feature_names_plot,
            'importance': feature_importance
        }).sort_values('importance', ascending=False)
        
        # 绘制条形图
        plt.barh(importance_df['feature'][:15], importance_df['importance'][:15])
        plt.xlabel('Mean |SHAP Value|')
        plt.title(f'{model_name} - Feature Importance', fontsize=14, pad=20)
        plt.tight_layout()
        importance_path = output_dir / f'{prefix}{model_name}_feature_importance.png'
        plt.savefig(importance_path, dpi=300, bbox_inches='tight')
        plt.close()
        result['plots']['importance'] = str(importance_path)
        
        # 获取重要特征列表
        result['top_features'] = importance_df['feature'][:5].tolist()
        
        # 3. 为重要特征生成依赖图
        for feature in result['top_features']:
            try:
                # 提取特征索引和值
                feature_idx = list(X_sample.columns).index(feature)
                feature_values = X_sample[feature].values
                
                # 确保shap_values是二维的
                if isinstance(shap_values_plot, list):
                    shap_values_feature = shap_values_plot[0][:, feature_idx]
                elif shap_values_plot.ndim > 2:
                    shap_values_feature = shap_values_plot[:, 0, feature_idx]
                else:
                    shap_values_feature = shap_values_plot[:, feature_idx]
                
                # 创建散点图
                plt.figure(figsize=(10, 6))
                plt.scatter(feature_values, shap_values_feature, alpha=0.7, s=40)
                plt.xlabel(feature, fontsize=13)
                plt.ylabel('SHAP值 (对模型输出的影响)', fontsize=13)
                plt.title(f'{model_name} - 依赖图: {feature}', fontsize=14, pad=20)
                plt.axhline(y=0, color='gray', linestyle='-', alpha=0.3)
                
                # 添加趋势线
                try:
                    from scipy import stats
                    slope, intercept, r_value, p_value, std_err = stats.linregress(feature_values, shap_values_feature)
                    x_line = np.array([min(feature_values), max(feature_values)])
                    y_line = slope * x_line + intercept
                    plt.plot(x_line, y_line, color='red', linestyle='--', alpha=0.7, 
                            label=f'趋势线 (r={r_value:.2f})')
                    plt.legend()
                except Exception:
                    pass  # 忽略趋势线错误
                
                plt.tight_layout()
                
                # 清理文件名中的非法字符
                safe_feature_name = "".join([c for c in feature if c.isalnum() or c in ('_', '-')]).rstrip()
                dep_path = output_dir / f'{prefix}{model_name}_dependence_{safe_feature_name}.png'
                plt.savefig(dep_path, dpi=300, bbox_inches='tight')
                plt.close()
                
                if 'dependence' not in result['plots']:
                    result['plots']['dependence'] = {}
                result['plots']['dependence'][feature] = str(dep_path)
                
            except Exception as e:
                print(f"为特征 {feature} 生成依赖图失败: {e}")
    
    except Exception as e:
        print(f"生成SHAP图表失败: {e}")
    
    return result

def generate_multi_data_shap_analysis(ensemble_model, model_datasets, output_dir, feature_names=None):
    """
    为多数据源集成模型生成SHAP可解释性分析
    
    Args:
        ensemble_model: 训练好的集成模型
        model_datasets: 模型数据集字典
        output_dir: 输出目录
        feature_names: 特征名称列表
    """
    if not SHAP_AVAILABLE:
        print("SHAP库不可用，跳过可解释性分析")
        return
    
    print("\n开始生成多数据源集成模型SHAP可解释性分析...")
    
    # 创建SHAP输出目录
    shap_dir = Path(output_dir) / 'shap_analysis'
    shap_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 获取预测特征而不是原始特征
        X_pred, feature_names_pred = _create_prediction_features(model_datasets, ensemble_model)
        
        if X_pred is None or X_pred.shape[0] == 0:
            print("无法创建预测特征，跳过SHAP分析")
            return
        
        # 使用提供的特征名称或默认名称
        if feature_names is None:
            feature_names = feature_names_pred
        
        # 转换为DataFrame格式（如果不是的话）
        if not isinstance(X_pred, pd.DataFrame):
            X_pred = pd.DataFrame(X_pred, columns=feature_names)
        
        # 采样数据用于分析
        background_data = X_pred.sample(min(50, len(X_pred)), random_state=42)
        sample_data = X_pred.sample(min(100, len(X_pred)), random_state=42)
        
        # 获取SHAP解释器
        explainer, shap_values, explainer_type = get_shap_explainer(ensemble_model, background_data, sample_data)
        
        if explainer is None:
            print("无法创建SHAP解释器，跳过SHAP分析")
            return
            
        print(f"使用{explainer_type}成功获取SHAP值")
        
        # 生成SHAP图表
        shap_results = generate_shap_plots(
            shap_values, sample_data, feature_names, 
            shap_dir, "multi_data_ensemble"
        )
        
        print(f"\n多数据源集成模型SHAP分析完成，结果保存在: {shap_dir}")
        return shap_results
        
    except Exception as e:
        print(f"生成多数据源集成模型SHAP分析时出错: {e}")
        return None

def generate_multi_data_shap_report(ensemble_results, output_dir, base_model_shap_results=None):
    """
    生成多数据源集成模型SHAP综合报告
    
    Args:
        ensemble_results: 集成结果列表
        output_dir: 输出目录
        base_model_shap_results: 基础模型SHAP分析结果
    
    Returns:
        str: 报告文件路径
    """
    if not SHAP_AVAILABLE:
        print("SHAP库不可用，跳过可解释性分析报告生成")
        return None
    
    print("\n开始生成多数据源集成模型综合SHAP报告...")
    
    # 创建报告输出目录
    report_dir = Path(output_dir) / 'shap_reports'
    report_dir.mkdir(parents=True, exist_ok=True)
    
    # 提取成功的模型结果
    successful_models = [
        result for result in ensemble_results
        if result['status'] == 'success'
    ]
    
    if not successful_models:
        print("没有成功训练的模型，无法生成报告")
        return None
    
    # 创建HTML报告
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>多数据源集成模型SHAP可解释性分析报告</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8f9fa;
            }
            .container {
                background-color: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            h1, h2, h3, h4 {
                color: #2c3e50;
                margin-top: 1.5em;
            }
            h1 {
                text-align: center;
                color: #3498db;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
                margin-bottom: 30px;
            }
            .model-section {
                margin-bottom: 40px;
                padding: 20px;
                background-color: #f1f8ff;
                border-left: 5px solid #3498db;
                border-radius: 4px;
            }
            .base-model-section {
                margin-bottom: 30px;
                padding: 15px;
                background-color: #f0f7f4;
                border-left: 5px solid #2ecc71;
                border-radius: 4px;
            }
            .image-container {
                margin: 20px 0;
                text-align: center;
            }
            .image-container img {
                max-width: 100%;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .image-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }
            .summary {
                background-color: #f0f7f4;
                padding: 20px;
                border-radius: 4px;
                margin: 20px 0;
            }
            .file-link {
                display: inline-block;
                margin: 10px 0;
                padding: 8px 15px;
                background-color: #3498db;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                transition: background-color 0.3s;
            }
            .file-link:hover {
                background-color: #2980b9;
            }
            .timestamp {
                margin-top: 40px;
                text-align: center;
                font-size: 0.9em;
                color: #7f8c8d;
            }
            ul li {
                margin-bottom: 8px;
            }
            .highlight {
                background-color: #ffffcc;
                padding: 2px 5px;
                border-radius: 3px;
            }
            .feature-table {
                width: 100%;
                border-collapse: collapse;
                margin: 15px 0;
            }
            .feature-table th, .feature-table td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            .feature-table th {
                background-color: #f2f2f2;
            }
            .feature-table tr:nth-child(even) {
                background-color: #f9f9f9;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔍 多数据源集成模型SHAP可解释性分析报告</h1>
            
            <div class="summary">
                <h2>📋 报告概述</h2>
                <p>本报告展示了多数据源集成模型的SHAP可解释性分析结果，帮助理解模型的预测机制和特征重要性。</p>
                <p><strong>特别说明：</strong> 本报告包含两部分分析：</p>
                <ul>
                    <li><span class="highlight">基础模型分析</span>：展示各个基础模型在各自原始数据上的SHAP分析，反映原始特征的重要性</li>
                    <li><span class="highlight">集成模型分析</span>：基于预测特征的分析，反映各个基础模型对最终预测的贡献</li>
                </ul>
            </div>
    """
    
    # 添加基础模型SHAP分析结果（如果有）
    if base_model_shap_results and len(base_model_shap_results) > 0:
        html_content += """
            <h2>🧩 基础模型SHAP分析</h2>
            <p>以下展示各个基础模型在其原始数据上的SHAP分析结果，帮助理解各个模型的内部工作机制和特征重要性。</p>
        """
        
        for model_name, shap_result in base_model_shap_results.items():
            if not shap_result:
                continue
                
            shap_dir = shap_result.get('shap_dir')
            top_features = shap_result.get('top_features', [])
            
            # 获取相对路径
            shap_dir_rel = Path(shap_dir).relative_to(Path(output_dir)) if shap_dir else ""
            
            html_content += f"""
            <div class="base-model-section">
                <h3>📊 {model_name} 模型</h3>
                
                <h4>🔍 SHAP可解释性图表</h4>
                <div class="image-container">
                    <p><strong>SHAP摘要图:</strong></p>
                    <img src="{shap_dir_rel}/{model_name}_shap_summary.png" alt="{model_name} SHAP Summary Plot">
                </div>
                
                <div class="image-container">
                    <p><strong>特征重要性图:</strong></p>
                    <img src="{shap_dir_rel}/{model_name}_feature_importance.png" alt="{model_name} Feature Importance">
                </div>
            """
            
            if top_features:
                html_content += """
                <h4>📈 重要特征依赖图</h4>
                <p>以下展示最重要的几个特征与SHAP值的关系：</p>
                <div class="image-grid">
                """
                
                for feature in top_features:
                    safe_feature = feature.replace('/', '_').replace('\\', '_').replace(':', '_')
                    html_content += f"""
                    <div class="image-container">
                        <p><strong>{feature}:</strong></p>
                        <img src="{shap_dir_rel}/{model_name}_dependence_{safe_feature}.png" alt="{model_name} {feature} Dependence">
                    </div>
                    """
                
                html_content += """
                </div>
                """
            
            html_content += f"""
                <h4>📁 相关文件</h4>
                <a href="{shap_dir_rel}" class="file-link">📂 查看所有SHAP分析文件</a>
            </div>
            """
    
    # 添加集成模型SHAP分析结果
    html_content += """
            <h2>🎯 集成模型SHAP分析结果</h2>
            <p>以下展示集成模型的SHAP分析结果，反映各个基础模型对最终预测的贡献。</p>
    """
    
    for result in successful_models:
        strategy = result['strategy']
        method = result['method']
        model_name = f"{strategy}_{method}"
        
        html_content += f"""
            <div class="model-section">
                <h3>📈 {strategy.upper()} + {method.upper()} 集成模型</h3>
                <p><strong>数据策略:</strong> {strategy}</p>
                <p><strong>集成方法:</strong> {method}</p>
                
                <h4>🔍 SHAP可解释性图表</h4>
                <div class="image-container">
                    <p><strong>SHAP摘要图:</strong></p>
                    <img src="shap_analysis/multi_data_ensemble_shap_summary.png" alt="SHAP Summary Plot">
                </div>
                
                <div class="image-container">
                    <p><strong>特征重要性图:</strong></p>
                    <img src="shap_analysis/multi_data_ensemble_feature_importance.png" alt="Feature Importance">
                </div>
                
                <div class="image-container">
                    <p><strong>依赖图:</strong></p>
                    <img src="shap_analysis/multi_data_ensemble_dependence.png" alt="Dependence Plot">
                </div>
                
                <h4>📁 相关文件</h4>
                <a href="shap_analysis/" class="file-link">📂 查看所有SHAP分析文件</a>
            </div>
        """

    # 添加SHAP方法说明
    html_content += """
            <h2>📚 SHAP方法说明</h2>
            <div class="summary">
                <h3>🔬 SHAP值解释</h3>
                <ul>
                    <li><strong>SHAP摘要图:</strong> 显示每个特征对模型预测的影响程度和方向</li>
                    <li><strong>特征重要性图:</strong> 按照平均绝对SHAP值排序显示特征重要性</li>
                    <li><strong>依赖图:</strong> 显示特征值与其SHAP值之间的关系，反映特征如何影响预测</li>
                </ul>
                
                <h3>🎨 图表解读指南</h3>
                <ul>
                    <li><strong>颜色含义:</strong> 红色表示正向影响（增加预测概率），蓝色表示负向影响（降低预测概率）</li>
                    <li><strong>位置含义:</strong> 横轴表示SHAP值大小，纵轴表示特征名称</li>
                    <li><strong>点的分布:</strong> 每个点代表一个样本，点的密集程度反映该特征值的分布</li>
                </ul>
            </div>
            
            <h2>🔧 技术实现</h2>
            <div class="summary">
                <h3>⚙️ 解释器选择</h3>
                <p>系统根据模型类型自动选择最适合的SHAP解释器：</p>
                <ul>
                    <li><strong>TreeExplainer:</strong> 用于树模型（随机森林、XGBoost等），计算速度快且精确</li>
                    <li><strong>KernelExplainer:</strong> 通用解释器，适用于任何机器学习模型，包括我们的元学习器</li>
                </ul>
                
                <h3>📊 分析范围</h3>
                <ul>
                    <li><strong>基础模型分析:</strong> 使用原始特征，展示各个特征对各模型的重要性</li>
                    <li><strong>集成模型分析:</strong> 使用预测特征，展示各个基础模型对最终预测的贡献</li>
                    <li><strong>样本数量:</strong> 使用部分测试数据进行分析，确保计算效率</li>
                </ul>
                
                <h3>🔄 多层次可解释性</h3>
                <p>本系统提供了多层次的可解释性分析：</p>
                <ul>
                    <li>第一层：理解各个基础模型如何利用原始特征进行预测</li>
                    <li>第二层：理解集成模型如何利用各个基础模型的预测结果</li>
                    <li>这种多层次分析提供了从原始特征到最终预测的完整解释链</li>
                </ul>
            </div>
    """
    
    # 添加页脚
    html_content += f"""
            <div class="timestamp">
                <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>多数据源集成学习系统 - SHAP可解释性分析模块</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    # 保存HTML报告
    report_path = report_dir / 'multi_data_ensemble_shap_report.html'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"\n多数据源集成模型SHAP综合报告已生成: {report_path}")
    return report_path

def generate_base_model_shap_analysis(model, X_train, X_test, y_test, feature_names, output_dir, model_name):
    """
    为基础模型生成SHAP可解释性分析
    
    Args:
        model: 训练好的模型
        X_train: 训练特征数据
        X_test: 测试特征数据
        y_test: 测试标签
        feature_names: 特征名称列表
        output_dir: 输出目录
        model_name: 模型名称
    """
    if not SHAP_AVAILABLE:
        print(f"SHAP库不可用，跳过 {model_name} 的可解释性分析")
        return
    
    print(f"\n开始生成 {model_name} 模型的SHAP可解释性分析...")
    
    # 创建SHAP输出目录
    base_model_dir = Path(output_dir) / 'base_models_shap' / model_name
    base_model_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 确保feature_names有效
        if feature_names is None or len(feature_names) != X_test.shape[1]:
            if hasattr(X_test, 'columns'):
                feature_names = X_test.columns.tolist()
            else:
                feature_names = [f'feature_{i}' for i in range(X_test.shape[1])]
        
        print(f"使用特征名称: {feature_names[:5]}... (共{len(feature_names)}个)")
        
        # 将测试数据和训练数据转换为DataFrame
        X_test_df = pd.DataFrame(X_test, columns=feature_names)
        X_train_df = pd.DataFrame(X_train, columns=feature_names)

        # 使用一部分测试数据进行SHAP分析
        sample_size = min(100, X_test_df.shape[0])
        X_sample = X_test_df.sample(n=sample_size, random_state=42)
        
        print(f"使用样本数据形状: {X_sample.shape}")
        
        # 尝试不同的解释器
        explainer = None
        shap_values = None
        
        # 首先尝试TreeExplainer（适用于树模型）
        try:
            if hasattr(model, 'estimators_') or 'Tree' in str(type(model)) or hasattr(model, 'tree_'):
                explainer = shap.TreeExplainer(model)
                explainer_type = 'TreeExplainer'
                if X_sample is not None:
                    shap_values = explainer.shap_values(X_sample)
        except Exception as e:
            print(f"TreeExplainer初始化失败: {e}")
        
        # 如果TreeExplainer失败，使用KernelExplainer
        if explainer is None:
            try:
                # 创建一个包装函数，确保输入维度正确
                def model_predict(X):
                    try:
                        return model.predict_proba(X)
                    except Exception:
                        try:
                            preds = model.predict(X)
                            return np.column_stack((1-preds, preds))  # 转换为二分类概率格式
                        except Exception:
                            return np.zeros((X.shape[0], 2))
                
                explainer = shap.KernelExplainer(model_predict, X_train_df.sample(n=min(50, len(X_train_df)), random_state=42))
                explainer_type = 'KernelExplainer'
                if X_sample is not None:
                    shap_values = explainer.shap_values(X_sample)
            except Exception as e:
                print(f"KernelExplainer初始化失败: {e}")
                return None, None, None
        
        # 处理不同维度的shap_values
        if isinstance(shap_values, list) and len(shap_values) > 1:
            # 二分类情况，使用正类的SHAP值
            shap_values_plot = shap_values[1]
            print(f"使用正类(索引1)的SHAP值，形状: {shap_values_plot.shape}")
        else:
            shap_values_plot = shap_values
            print(f"直接使用shap_values，形状: {np.array(shap_values_plot).shape if isinstance(shap_values_plot, list) else shap_values_plot.shape}")
        
        # 1. 生成SHAP摘要图
        try:
            print(f"为 {model_name} 生成SHAP摘要图...")
            
            # 确保我们只为摘要图使用正类（class 1）的SHAP值
            shap_values_for_summary = None
            if isinstance(shap_values, list) and len(shap_values) > 1:
                shap_values_for_summary = shap_values[1] # 使用正类
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:
                shap_values_for_summary = shap_values[:, :, 1] # 使用正类
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 2:
                shap_values_for_summary = shap_values # 已经是我们需要的格式
            
            if shap_values_for_summary is None:
                print(f"警告：无法为模型 '{model_name}' 提取用于摘要图的SHAP值。跳过。")
                return None # or continue if in a loop

            shap.summary_plot(shap_values_for_summary, X_test, feature_names=feature_names, show=False)
            
            plt.title(f'{model_name} Model - SHAP Summary Plot', fontsize=16, pad=20)
            plt.gcf().set_size_inches(12, 8) 
            plt.tight_layout(pad=1.5)
            
            summary_path = base_model_dir / f'{model_name}_shap_summary.png'
            plt.savefig(summary_path, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"{model_name} SHAP摘要图已保存: {summary_path}")
        except Exception as e:
            print(f"生成 {model_name} SHAP摘要图失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 2. 生成特征重要性条形图
        try:
            plt.figure(figsize=(10, 8))
            
            # 计算特征重要性 - 确保是一维数组
            feature_importance = np.abs(shap_values_plot).mean(0)
            # 如果是多维数组，取平均值转为一维
            if len(feature_importance.shape) > 1:
                feature_importance = feature_importance.mean(1)
            
            # 确保长度匹配
            if len(feature_importance) != len(feature_names):
                feature_importance = feature_importance[:len(feature_names)]
            
            # 打印形状以便调试
            print(f"{model_name} 特征重要性形状: {feature_importance.shape}, 特征名称数量: {len(feature_names)}")
            
            # 创建特征重要性DataFrame
            importance_df = pd.DataFrame({
                'feature': feature_names,
                'importance': feature_importance
            })
            
            # 按重要性排序
            importance_df = importance_df.sort_values('importance', ascending=False)
            
            # 绘制条形图
            plt.barh(importance_df['feature'][:15], importance_df['importance'][:15])
            plt.xlabel('Mean |SHAP Value|')
            plt.title(f'{model_name} Model - Feature Importance', fontsize=14, pad=20)
            plt.tight_layout()
            importance_path = base_model_dir / f'{model_name}_feature_importance.png'
            plt.savefig(importance_path, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"{model_name} 特征重要性图已保存: {importance_path}")
        except Exception as e:
            print(f"生成 {model_name} 特征重要性图失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 3. 为重要特征生成依赖图
        try:
            # 获取前5个重要特征
            top_features = importance_df['feature'][:5].tolist()
            
            for feature in top_features:
                try:
                    plt.figure(figsize=(10, 6))
                    
                    # 获取特征索引
                    feature_idx = list(X_sample.columns).index(feature)
                    
                    # 获取特征值
                    feature_values = X_sample[feature].values
                    
                    # 获取SHAP值
                    # SHAP对于二分类模型输出一个列表包含两个数组，或者一个(样本数, 特征数, 2)的数组
                    # 我们关心的是对正类(通常是索引1)的预测的影响
                    shap_values_for_class_1 = None
                    if isinstance(shap_values_plot, list) and len(shap_values_plot) > 1:
                        shap_values_for_class_1 = shap_values_plot[1]
                    elif isinstance(shap_values_plot, np.ndarray) and shap_values_plot.ndim == 3:
                        shap_values_for_class_1 = shap_values_plot[:, :, 1]
                    elif isinstance(shap_values_plot, np.ndarray) and shap_values_plot.ndim == 2:
                        # 如果已经是二维的，假设它就是我们想要的
                        shap_values_for_class_1 = shap_values_plot
                    
                    if shap_values_for_class_1 is None:
                        print(f"警告：无法为特征 '{feature}' 确定用于绘图的SHAP值。跳过依赖图。")
                        continue

                    shap_values_feature = shap_values_for_class_1[:, feature_idx]
                    
                    # 确保两个数组长度相同
                    if len(feature_values) != len(shap_values_feature):
                        print(f"错误：特征 '{feature}' 的值数组和SHAP值数组长度不匹配。特征: {len(feature_values)}, SHAP: {len(shap_values_feature)}。跳过依赖图。")
                        continue
                        
                    # 绘制散点图
                    plt.scatter(feature_values, shap_values_feature, alpha=0.7, s=40)
                    plt.xlabel(feature, fontsize=13)
                    plt.ylabel('SHAP值 (对模型输出的影响)', fontsize=13)
                    plt.title(f'{model_name} - 依赖图: {feature}', fontsize=14, pad=20)
                    
                    # 添加水平参考线
                    plt.axhline(y=0, color='gray', linestyle='-', alpha=0.3)
                    
                    # 添加趋势线
                    try:
                        from scipy import stats
                        slope, intercept, r_value, p_value, std_err = stats.linregress(feature_values, shap_values_feature)
                        x_line = np.array([min(feature_values), max(feature_values)])
                        y_line = slope * x_line + intercept
                        plt.plot(x_line, y_line, color='red', linestyle='--', alpha=0.7, 
                                label=f'趋势线 (r={r_value:.2f})')
                        plt.legend()
                    except Exception as e:
                        print(f"无法添加趋势线: {e}")
                    
                    plt.tight_layout()
                    
                    # 清理文件名中的非法字符
                    safe_feature_name = "".join([c for c in feature if c.isalnum() or c in ('_', '-')]).rstrip()
                    
                    # 保存图片
                    dep_path = base_model_dir / f'{model_name}_dependence_{safe_feature_name}.png'
                    plt.savefig(dep_path, dpi=300, bbox_inches='tight')
                    plt.close()
                    print(f"{model_name} 特征 {feature} 依赖图已保存: {dep_path}")
                except Exception as e:
                    print(f"生成 {model_name} 特征 {feature} 依赖图失败: {e}")
                    import traceback
                    traceback.print_exc()
        
        except Exception as e:
            print(f"生成 {model_name} 依赖图失败: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"\n{model_name} 模型SHAP分析完成，结果保存在: {base_model_dir}")
        
        # 返回分析结果路径和重要特征
        return {
            'model_name': model_name,
            'shap_dir': base_model_dir,
            'top_features': top_features,
            'feature_importance': importance_df.to_dict('records')
        }
        
    except Exception as e:
        print(f"生成 {model_name} SHAP分析时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

# 创建一个新的函数来获取模型预测结果
def get_model_predictions(model, X, positive_class=True):
    """
    获取模型预测，统一处理各种预测情况
    
    Args:
        model: 训练好的模型
        X: 输入特征
        positive_class: 是否返回正类预测概率
    
    Returns:
        numpy.ndarray: 模型预测结果
    """
    try:
        if hasattr(model, 'predict_proba'):
            y_pred_proba = model.predict_proba(X)
            # 只保留正类的概率
            if y_pred_proba.shape[1] > 1 and positive_class:
                return y_pred_proba[:, 1]
            else:
                return y_pred_proba.flatten()
        else:
            # 如果模型不支持概率预测，使用硬预测
            return model.predict(X)
    except Exception as e:
        print(f"预测失败: {e}")
        return np.full(X.shape[0], 0.5)

# 新增：加载并预处理数据的统一函数
def load_and_process_data(data_path, test_size=0.2, random_state=42):
    """
    加载并预处理数据的统一函数
    
    Args:
        data_path: 数据路径
        test_size: 测试集比例
        random_state: 随机种子
    
    Returns:
        dict: 包含X_train, X_test, y_train, y_test的字典
    """
    try:
        # 加载并清理数据
        df, target_col = load_and_clean_data(data_path)
        
        # 划分数据
        X = df.drop(columns=[target_col])
        y = df[target_col]
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )
        
        return {
            'X_train': X_train, 
            'X_test': X_test, 
            'y_train': y_train, 
            'y_test': y_test,
            'data_path': data_path,
            'feature_names': X.columns.tolist()
        }
    except Exception as e:
        print(f"加载和预处理数据失败: {data_path}, 错误: {e}")
        return None

# 优化集成分类器基类
class BaseEnsembleClassifier(BaseEstimator, ClassifierMixin):
    """集成分类器的基类，提供共同的功能"""
    
    def __init__(self, models):
        self.models = models
        self.model_names = [name for name, _ in models]
    
    def fit(self, X, y=None):
        # 集成模型已经训练好基础模型，不需要额外训练
        return self
    
    def _get_predictions(self, X, method='predict'):
        """获取每个模型的预测结果"""
        if not isinstance(X, dict):
            raise ValueError("X应该是一个字典，键是模型名称，值是对应的输入数据")
            
        predictions = []
        for model_name, model in self.models:
            if model_name in X:
                try:
                    # 使用指定的方法进行预测
                    if method == 'predict_proba':
                        if hasattr(model, 'predict_proba'):
                            pred = model.predict_proba(X[model_name])
                        else:
                            # 如果模型不支持概率预测，使用硬预测然后转换为概率
                            preds = model.predict(X[model_name])
                            pred = np.column_stack((1-preds, preds))
                    else:  # 默认使用predict
                        pred = model.predict(X[model_name])
                        
                    predictions.append(pred)
                except Exception as e:
                    print(f"模型 {model_name} 预测失败: {e}")
                    # 根据方法创建默认预测
                    if method == 'predict_proba':
                        n_samples = X[model_name].shape[0]
                        predictions.append(np.full((n_samples, 2), 0.5))
                    else:
                        predictions.append(np.zeros(X[model_name].shape[0]))
        
        if not predictions:
            # 如果所有模型都失败，返回一个默认预测
            first_key = next(iter(X))
            n_samples = X[first_key].shape[0]
            if method == 'predict_proba':
                return np.full((n_samples, 2), 0.5)
            else:
                return np.zeros(n_samples)
                
        return predictions

class CustomVotingClassifier(BaseEnsembleClassifier):
    """
    自定义投票分类器，支持对不同数据源的预测进行投票
    """
    
    def __init__(self, models, voting='soft'):
        super().__init__(models)
        self.voting = voting

    def predict(self, X):
        """使用投票方式预测类别"""
        predictions = self._get_predictions(X, method='predict')
        
        # 将预测结果转置，以便按样本进行投票
        predictions = np.asarray(predictions).T
        
        # 对每个样本进行多数投票
        return np.apply_along_axis(lambda x: np.argmax(np.bincount(x.astype(int))), 
                                  axis=1, arr=predictions)

    def predict_proba(self, X):
        """预测类别概率"""
        probas = self._get_predictions(X, method='predict_proba')
        
        # 计算平均概率
        return np.mean(probas, axis=0)

class CustomWeightedClassifier(BaseEnsembleClassifier):
    """
    自定义加权分类器，支持为不同数据源的预测分配权重
    """
    
    def __init__(self, models, weights=None):
        super().__init__(models)
        self.weights = weights
        
        # 如果没有提供权重，默认使用均等权重
        if self.weights is None:
            self.weights = np.ones(len(models)) / len(models)

    def predict_proba(self, X):
        """加权预测类别概率"""
        probas = self._get_predictions(X, method='predict_proba')
        
        # 应用权重
        weighted_probas = []
        for prob, weight in zip(probas, self.weights):
            weighted_probas.append(prob * weight)
        
        # 计算加权平均
        return np.sum(weighted_probas, axis=0)
    
    def predict(self, X):
        """预测类别"""
        probas = self.predict_proba(X)
        return np.argmax(probas, axis=1) if probas.ndim > 1 else (probas > 0.5).astype(int)

class MultiDataEnsemble(BaseEstimator, ClassifierMixin):
    """
    多数据源集成分类器
    支持基模型使用不同数据源训练，融合时可选择数据策略
    """
    
    def __init__(self, model_data_mapping, ensemble_method='voting', 
                 ensemble_data_strategy='unified', meta_classifier=None,
                 feature_selection=True, feature_selection_method='weighted', k=None):
        """
        初始化多数据源集成分类器
        
        Args:
            model_data_mapping: 模型与数据源的映射字典 {model_name: data_path}
            ensemble_method: 集成方法 ('voting', 'stacking', 'weighted')
            ensemble_data_strategy: 融合数据策略 ('unified', 'original', 'combined')
                - 'unified': 使用统一的测试数据进行融合
                - 'original': 每个模型使用其原始训练数据对应的测试数据
                - 'combined': 合并所有数据源的测试数据
            meta_classifier: 元分类器（用于stacking）
            feature_selection: 是否进行特征筛选
            feature_selection_method: 特征筛选方法 ('combined', 'union', 'intersection', 'weighted', 'meta_model')
            k: 选择的特征数量，如果为None则自动确定
        """
        self.model_data_mapping = model_data_mapping
        self.ensemble_method = ensemble_method
        self.ensemble_data_strategy = ensemble_data_strategy
        self.meta_classifier = meta_classifier or LogisticRegression()
        self.feature_selection = feature_selection
        self.feature_selection_method = feature_selection_method
        self.k = k
        
        # 存储训练好的模型和数据
        self.trained_models = {}
        self.model_datasets = {}
        self.ensemble_model = None
        self.selected_features = None
        self.feature_selector = None
        self.base_models = {}
        
    def _select_prediction_features(self, X_pred, y, feature_names, k=None):
        """
        对模型预测结果组成的特征矩阵进行特征选择
        """
        if not self.feature_selection or X_pred.shape[1] <= 1:
            # 创建一个默认的选择器，选择所有特征
            selector = EnsembleFeatureSelector(np.arange(X_pred.shape[1]), feature_names)
            return X_pred, feature_names, selector

        print(f"开始对预测特征进行筛选，方法: {self.feature_selection_method}")

        if k is None:
            k = max(int(X_pred.shape[1] * 0.75), 1)  # 默认选择75%的预测特征
        k = min(k, X_pred.shape[1])
        
        # 根据选择的集成方法来决定特征筛选策略
        if self.ensemble_method == 'stacking' and self.meta_classifier is not None:
            # 对于stacking，使用元分类器自身的特征选择能力
            from sklearn.feature_selection import SelectFromModel
            
            # 确保元分类器已训练
            try:
                self.meta_classifier.fit(X_pred, y)
            except Exception as e:
                 print(f"训练元分类器失败: {e}, 使用默认随机森林")
                 self.meta_classifier = RandomForestClassifier()
                 self.meta_classifier.fit(X_pred, y)

            selector_model = SelectFromModel(self.meta_classifier, max_features=k, prefit=True, threshold=-np.inf)
            selected_indices = selector_model.get_support(indices=True)
            
        else:
            # 对于voting或weighted等方法，使用更通用的方法，如基于F值的单变量选择
            from sklearn.feature_selection import SelectKBest, f_classif
            
            selector_model = SelectKBest(f_classif, k=k)
            selector_model.fit(X_pred, y)
            selected_indices = selector_model.get_support(indices=True)

        selected_feature_names = [feature_names[i] for i in selected_indices]
        selector = EnsembleFeatureSelector(selected_indices, feature_names)
        
        print(f"预测特征筛选完成, 从{len(feature_names)}个特征中选择了{len(selected_feature_names)}个")
        print(f"选择的预测特征: {selected_feature_names}")
        
        return selector.transform(X_pred), selected_feature_names, selector

    def load_and_prepare_data(self):
        """
        加载和预处理所有数据源
        
        Returns:
            dict: 包含所有数据源的字典
        """
        print("开始加载多数据源...")
        datasets = {}
        
        for model_name, data_path in self.model_data_mapping.items():
            print(f"加载 {model_name} 的数据: {data_path}")
            dataset = load_and_process_data(data_path)
            if dataset:
                datasets[model_name] = dataset
                print(f"成功加载 {model_name} 数据，训练集大小: {dataset['X_train'].shape}")
        
        self.model_datasets = datasets
        return datasets
    
    def train_base_models(self, use_cache=True, enable_shap=True):
        """
        使用不同数据源训练基础模型
        
        Args:
            use_cache: 是否使用缓存
            enable_shap: 是否为基础模型生成SHAP分析
        """
        print("开始训练基础模型...")
        
        # 创建多数据缓存目录
        CONFIG['multi_data_cache_path'].mkdir(parents=True, exist_ok=True)
        
        # 存储每个基础模型的SHAP分析结果
        base_model_shap_results = {}
        
        for model_name, dataset in self.model_datasets.items():
            # 创建缓存文件路径
            cache_file = CONFIG['multi_data_cache_path'] / f"{model_name}_multi_data_results.joblib"
            
            # 检查缓存
            if use_cache and cache_file.exists():
                try:
                    cached_data = load(cache_file)
                    self.trained_models[model_name] = cached_data['model']
                    print(f"从缓存加载模型: {model_name}")
                    
                    # 加载SHAP结果
                    if enable_shap and SHAP_AVAILABLE and 'shap_results' in cached_data:
                        print(f"从缓存加载 {model_name} 的SHAP分析结果")
                        base_model_shap_results[model_name] = cached_data['shap_results']
                    # 如果需要SHAP分析但缓存中没有，则生成
                    elif enable_shap and SHAP_AVAILABLE:
                        print(f"为缓存模型 {model_name} 生成SHAP分析...")
                        shap_results = self._generate_base_model_shap(
                            model_name, cached_data['model'], dataset
                        )
                        if shap_results:
                            cached_data['shap_results'] = shap_results
                            dump(cached_data, cache_file)
                            base_model_shap_results[model_name] = shap_results
                    
                    continue
                except Exception as e:
                    print(f"加载缓存失败: {e}，重新训练模型")
            
            # 训练模型
            # 处理特殊情况：NaiveBayes1和NaiveBayes2使用NaiveBayes的训练函数
            base_model_name = model_name
            if model_name.startswith('NaiveBayes'):
                base_model_name = 'NaiveBayes'
                
            if base_model_name in MODEL_FUNCTIONS:
                print(f"训练模型: {model_name} (使用 {base_model_name} 训练函数)")
                try:
                    train_func = MODEL_FUNCTIONS[base_model_name]
                    model = train_func(
                        dataset['X_train'], dataset['y_train'],
                        dataset['X_test'], dataset['y_test']
                    )
                    
                    self.trained_models[model_name] = model
                    
                    # 缓存结果
                    cache_data = {
                        'model': model,
                        'y_true': dataset['y_test'],
                        'y_pred': model.predict(dataset['X_test']),
                        'y_pred_proba': get_model_predictions(model, dataset['X_test']),
                        'X_test': dataset['X_test'],
                        'data_path': dataset['data_path']
                    }
                    
                    # 如果启用SHAP分析，生成SHAP分析
                    if enable_shap and SHAP_AVAILABLE:
                        print(f"为模型 {model_name} 生成SHAP分析...")
                        shap_results = self._generate_base_model_shap(model_name, model, dataset)
                        if shap_results:
                            cache_data['shap_results'] = shap_results
                            base_model_shap_results[model_name] = shap_results
                    
                    dump(cache_data, cache_file)
                    print(f"模型 {model_name} 训练完成并缓存")
                    
                except Exception as e:
                    from exception_handling import ModelTrainingError, global_exception_handler
                    print(f"训练模型 {model_name} 失败: {e}")
                    
                    # 使用异常处理器尝试恢复
                    try:
                        recovery_model = global_exception_handler.handle_exception(
                            ModelTrainingError(f"模型 {model_name} 训练失败: {e}"),
                            context=f"训练模型 {model_name}",
                            raise_on_failure=False,
                            return_on_failure=None
                        )
                        
                        if recovery_model is not None:
                            # 尝试用恢复的模型进行训练
                            recovery_model.fit(dataset['X_train'], dataset['y_train'])
                            self.trained_models[model_name] = recovery_model
                            print(f"模型 {model_name} 使用恢复策略训练成功")
                            
                            # 缓存恢复模型的结果
                            cache_data = {
                                'model': recovery_model,
                                'y_true': dataset['y_test'],
                                'y_pred': recovery_model.predict(dataset['X_test']),
                                'y_pred_proba': get_model_predictions(recovery_model, dataset['X_test']),
                                'X_test': dataset['X_test'],
                                'data_path': dataset['data_path'],
                                'recovery_used': True
                            }
                            dump(cache_data, cache_file)
                        else:
                            print(f"模型 {model_name} 恢复失败，跳过该模型")
                    except Exception as recovery_error:
                        print(f"模型 {model_name} 恢复策略也失败: {recovery_error}")
            else:
                print(f"未知的模型类型: {model_name} (基础类型: {base_model_name})")
        
        # 存储基础模型SHAP结果
        self.base_model_shap_results = base_model_shap_results
        return base_model_shap_results
    
    def _generate_base_model_shap(self, model_name, model, dataset):
        """生成基础模型的SHAP分析结果"""
        if not SHAP_AVAILABLE:
            return None
            
        # 获取数据和特征名称
        X_train = dataset['X_train']
        X_test = dataset['X_test']
        y_test = dataset['y_test']
        feature_names = dataset.get('feature_names', None)
        
        if feature_names is None and hasattr(X_train, 'columns'):
            feature_names = X_train.columns.tolist()
        elif feature_names is None:
            feature_names = [f'Feature_{i}' for i in range(X_train.shape[1])]
        
        # 创建SHAP输出目录
        output_dir = CONFIG['output_dir'] / 'base_models_shap'
        
        # 采样数据用于分析
        if hasattr(X_train, 'sample'):
            background_data = X_train.sample(min(50, len(X_train)), random_state=42)
            sample_data = X_test.sample(min(100, len(X_test)), random_state=42)
        else:
            # 如果不是DataFrame，随机选择样本
            indices = np.random.choice(len(X_train), min(50, len(X_train)), replace=False)
            background_data = X_train[indices]
            indices = np.random.choice(len(X_test), min(100, len(X_test)), replace=False)
            sample_data = X_test[indices]
        
        # 获取SHAP解释器和值
        explainer, shap_values, explainer_type = get_shap_explainer(model, background_data, sample_data)
        
        if explainer is None:
            return None
            
        # 生成SHAP图表
        return generate_shap_plots(
            shap_values, sample_data, feature_names, 
            output_dir / model_name, model_name
        )

    def prepare_ensemble_data(self, target_data_path=None, dataset_type='test'):
        """
        根据策略准备集成训练数据
        
        Args:
            target_data_path: 目标数据路径（用于unified策略）
            
        Returns:
            tuple: (X_train, y_train, X_test, y_test)
        """
        print(f"准备集成数据，策略: {self.ensemble_data_strategy}")
        
        if self.ensemble_data_strategy == 'unified':
            # 使用统一的数据源
            if target_data_path:
                return load_and_process_data(target_data_path)
            else:
                # 使用第一个数据源作为统一数据
                first_dataset = list(self.model_datasets.values())[0]
                return first_dataset
        
        elif self.ensemble_data_strategy == 'combined':
            # 合并所有数据源
            all_X_train, all_y_train = [], []
            all_X_test, all_y_test = [], []
            
            for dataset in self.model_datasets.values():
                all_X_train.append(dataset['X_train'])
                all_y_train.append(dataset['y_train'])
                all_X_test.append(dataset['X_test'])
                all_y_test.append(dataset['y_test'])
            
            # 合并数据 (假设所有数据集都是DataFrame或都是numpy数组)
            try:
                if all(isinstance(X, pd.DataFrame) for X in all_X_train):
                    X_train_combined = pd.concat(all_X_train, axis=0)
                    X_test_combined = pd.concat(all_X_test, axis=0)
                else:
                    X_train_combined = np.vstack(all_X_train)
                    X_test_combined = np.vstack(all_X_test)
                
                y_train_combined = np.hstack(all_y_train)
                y_test_combined = np.hstack(all_y_test)
                
                print(f"合并后数据大小 - 训练集: {X_train_combined.shape}, 测试集: {X_test_combined.shape}")
                
                return {
                    'X_train': X_train_combined, 
                    'X_test': X_test_combined,
                    'y_train': y_train_combined, 
                    'y_test': y_test_combined
                }
            except Exception as e:
                print(f"合并数据失败: {e}")
                # 失败时返回第一个数据集
                return list(self.model_datasets.values())[0]
        
        elif self.ensemble_data_strategy == 'original':
            # 使用原始数据策略，返回第一个数据集作为默认
            # 实际预测时会使用各自的原始数据
            return list(self.model_datasets.values())[0]
        
        else:
            raise ValueError(f"未知的集成数据策略: {self.ensemble_data_strategy}")
    
    def _create_model_predictions(self, dataset_type='train'):
        """
        获取每个模型在其对应数据集上的预测结果，用于模型融合
        
        Args:
            dataset_type: 'train' 或 'test'，指定使用训练集还是测试集
            
        Returns:
            tuple: (X_pred, y_true) 其中X_pred是模型预测结果组成的特征矩阵，y_true是真实标签
        """
        if not self.trained_models or not self.model_datasets:
            raise ValueError("模型或数据集未加载")
        
        # 存储每个模型的预测结果和对应的真实标签
        all_predictions = {}
        all_labels = {}
        
        # 对每个模型获取预测结果
        for model_name, model in self.trained_models.items():
            # 获取对应的数据集
            if model_name not in self.model_datasets:
                print(f"警告: 模型 {model_name} 没有对应的数据集")
                continue
                
            dataset = self.model_datasets[model_name]
            X = dataset[f'X_{dataset_type}']
            y = dataset[f'y_{dataset_type}']
            
            try:
                # 获取预测概率
                predictions = get_model_predictions(model, X).reshape(-1, 1)
                
                # 存储预测结果和标签
                all_predictions[model_name] = predictions
                all_labels[model_name] = y
                
                print(f"模型 {model_name} 生成了 {len(predictions)} 个预测结果")
            except Exception as e:
                print(f"获取模型 {model_name} 的预测结果失败: {e}")
                # 为失败的模型创建默认预测，避免维度不匹配
                if all_labels:
                    # 使用已有标签的长度作为参考
                    reference_length = len(list(all_labels.values())[0])
                else:
                    # 如果没有参考，尝试从数据集获取
                    try:
                        reference_length = len(dataset[f'y_{dataset_type}'])
                    except:
                        reference_length = 100  # 默认长度
                
                # 创建默认预测（0.5表示不确定）
                default_predictions = np.full((reference_length, 1), 0.5)
                all_predictions[model_name] = default_predictions
                
                # 使用数据集中的标签
                try:
                    all_labels[model_name] = dataset[f'y_{dataset_type}']
                except:
                    # 如果获取标签也失败，创建默认标签
                    all_labels[model_name] = np.zeros(reference_length)
                
                print(f"为模型 {model_name} 创建了默认预测结果")
        
        if not all_predictions:
            raise ValueError("没有成功获取任何模型的预测结果")
        
        # 验证和对齐预测结果
        model_names = list(all_predictions.keys())
        predictions_lengths = [len(all_predictions[name]) for name in model_names]
        labels_lengths = [len(all_labels[name]) for name in model_names]
        
        print(f"模型预测长度: {dict(zip(model_names, predictions_lengths))}")
        print(f"标签长度: {dict(zip(model_names, labels_lengths))}")
        
        # 根据数据策略合并预测结果
        if self.ensemble_data_strategy == 'unified' or self.ensemble_data_strategy == 'combined':
            # 对于unified和combined策略，使用第一个数据集的标签
            first_model = model_names[0]
            target_length = len(all_labels[first_model])
            y_true = all_labels[first_model]
            
            # 创建特征矩阵，每个模型的预测作为一个特征
            X_pred = np.zeros((target_length, len(all_predictions)))
            
            for i, model_name in enumerate(model_names):
                predictions = all_predictions[model_name].flatten()
                
                if len(predictions) == target_length:
                    X_pred[:, i] = predictions
                elif len(predictions) > target_length:
                    # 如果预测长度过长，截取前面部分
                    print(f"警告: 模型 {model_name} 预测长度({len(predictions)})超过目标长度({target_length})，截取前{target_length}个")
                    X_pred[:, i] = predictions[:target_length]
                else:
                    # 如果预测长度不足，用最后一个值填充或使用0.5
                    print(f"警告: 模型 {model_name} 预测长度({len(predictions)})小于目标长度({target_length})")
                    X_pred[:len(predictions), i] = predictions
                    if len(predictions) > 0:
                        # 用最后一个预测值填充剩余部分
                        X_pred[len(predictions):, i] = predictions[-1]
                    else:
                        # 如果没有有效预测，使用0.5
                        X_pred[:, i] = 0.5
            
        elif self.ensemble_data_strategy == 'original':
            # 对于original策略，需要确保每个样本只使用一次
            # 计算总样本数
            total_samples = sum(len(y) for y in all_labels.values())
            
            # 创建特征矩阵
            X_pred = np.full((total_samples, len(all_predictions)), 0.5)  # 用0.5初始化而不是NaN
            all_y = []
            
            # 填充特征矩阵
            start_idx = 0
            for i, model_name in enumerate(model_names):
                predictions = all_predictions[model_name].flatten()
                labels = all_labels[model_name]
                n_samples = len(labels)
                
                # 确保预测和标签长度一致
                if len(predictions) != n_samples:
                    if len(predictions) > n_samples:
                        predictions = predictions[:n_samples]
                    else:
                        # 扩展预测到标签长度
                        extended_predictions = np.full(n_samples, 0.5)
                        extended_predictions[:len(predictions)] = predictions
                        predictions = extended_predictions
                
                # 在对应位置填充预测值
                end_idx = start_idx + n_samples
                X_pred[start_idx:end_idx, i] = predictions
                
                # 收集标签
                all_y.extend(labels)
                
                # 更新起始索引
                start_idx = end_idx
            
            y_true = np.array(all_y)
        
        else:
            raise ValueError(f"未知的集成数据策略: {self.ensemble_data_strategy}")
        
        print(f"创建的预测特征矩阵形状: {X_pred.shape}")
        print(f"标签向量长度: {len(y_true)}")
        
        # 最终验证
        if X_pred.shape[0] != len(y_true):
            raise ValueError(f"预测特征矩阵行数({X_pred.shape[0]})与标签数量({len(y_true)})不匹配")
        
        return X_pred, y_true

    def fit(self, target_data_path=None, test_size=0.2, random_state=42):
        """
        训练多数据源集成模型
        """
        # 1. 加载和预处理所有数据源
        print("加载和预处理所有数据源...")
        self.load_and_prepare_data()
        
        if not self.model_datasets:
            raise ValueError("没有成功加载任何数据集")
        
        # 2. 训练基础模型
        print("训练基础模型...")
        self.train_base_models()
        
        # 3. 创建集成模型
        print(f"创建集成模型，方法: {self.ensemble_method}")
        if self.ensemble_method == 'voting':
            self.ensemble_model = CustomVotingClassifier(models=list(self.trained_models.items()))
        elif self.ensemble_method == 'weighted':
            self.ensemble_model = CustomWeightedClassifier(models=list(self.trained_models.items()))
        elif self.ensemble_method == 'stacking':
            # 为stacking方法，需要额外训练元分类器
            print("训练stacking元分类器...")
            # 获取每个模型在训练集上的预测作为元特征
            X_meta, y_meta = self._create_model_predictions(dataset_type='train')
            
            # 如果进行特征选择，应用特征选择
            if self.feature_selection:
                X_meta, selected_features, self.feature_selector = self._select_prediction_features(
                    X_meta, y_meta, 
                    [f"{name}_pred" for name in self.trained_models.keys()],
                    self.k
                )
            
            # 训练元分类器
            self.meta_classifier.fit(X_meta, y_meta)
            self.ensemble_model = self.meta_classifier
        else:
            raise ValueError(f"未知的集成方法: {self.ensemble_method}")
        
        return self
    
    def _evaluate_performance(self):
        """
        使用原始测试数据评估集成模型性能。
        它会调用self.predict和self.predict_proba来确保端到端的评估。
        """
        print("\n=== 集成模型性能评估 ===")
        try:
            # 创建一个字典，包含每个模型对应的原始测试数据
            test_data_dict = {name: data['X_test'] for name, data in self.model_datasets.items()}
            # 获取一个基准的y_test用于计算指标
            y_test_benchmark = next(iter(self.model_datasets.values()))['y_test']

            y_pred = self.predict(test_data_dict)
            y_pred_proba = self.predict_proba(test_data_dict)

            # 计算常用分类指标
            metrics = {
                'accuracy': accuracy_score(y_test_benchmark, y_pred),
                'precision': precision_score(y_test_benchmark, y_pred, zero_division=0),
                'recall': recall_score(y_test_benchmark, y_pred, zero_division=0),
                'f1': f1_score(y_test_benchmark, y_pred, zero_division=0),
                'roc_auc': roc_auc_score(y_test_benchmark, y_pred_proba)
            }
            
            # 打印指标
            for name, value in metrics.items():
                print(f"{name.capitalize()}: {value:.4f}")
            
            return metrics
        except Exception as e:
            print(f"评估集成模型失败: {e}")
            return None

    def predict(self, X_dict):
        """
        预测类别标签
        
        Args:
            X_dict: 字典，键为模型名称，值为该模型的测试数据
            
        Returns:
            numpy.ndarray: 预测的类别标签
        """
        return self.ensemble_model.predict(X_dict)

    def predict_proba(self, X_dict):
        """
        预测类别概率
        
        Args:
            X_dict: 字典，键为模型名称，值为该模型的测试数据
            
        Returns:
            numpy.ndarray: 预测的类别概率
        """
        return self.ensemble_model.predict_proba(X_dict)
    
    def save_model(self, save_path):
        """
        保存训练好的多数据源集成模型
        
        Args:
            save_path: 保存路径
        """
        # 确保父目录存在
        save_path = Path(save_path)
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 尝试保存模型，如果失败则使用时间戳创建唯一文件名
        try:
            # 直接保存到指定路径
            dump(self, save_path)
            print(f"多数据源集成模型已保存至: {save_path}")
        except PermissionError:
            # 如果权限错误，尝试使用时间戳创建唯一文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            new_path = save_path.parent / f"multi_data_ensemble_{timestamp}.joblib"
            try:
                dump(self, new_path)
                print(f"由于权限问题，模型已保存至替代路径: {new_path}")
                save_path = new_path  # 更新路径以便后续使用
            except Exception as e:
                print(f"保存模型失败: {e}")
                raise
        
        # 保存配置信息
        try:
            config_file = save_path.parent / "ensemble_config.json"
            import json
            config = {
                'model_data_mapping': {k: str(v) for k, v in self.model_data_mapping.items()},
                'ensemble_method': self.ensemble_method,
                'ensemble_data_strategy': self.ensemble_data_strategy,
                'trained_models': list(self.trained_models.keys()),
                'save_time': datetime.now().isoformat(),
                'model_path': str(save_path)  # 添加实际保存的模型路径
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print(f"集成配置已保存至: {config_file}")
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            # 继续执行，不抛出异常

    def get_raw_test_data_for_evaluation(self, target_data_path):
        """
        获取用于评估的原始测试数据
        """
        # 对于所有策略，我们都基于模型内部存储的数据集来获取测试数据
        # 'unified' 和 'original' 最终都依赖于 self.model_datasets
        try:
            # 以第一个数据集的测试集为基准
            first_dataset_key = next(iter(self.model_datasets))
            X_test = self.model_datasets[first_dataset_key]['X_test']
            y_test = self.model_datasets[first_dataset_key]['y_test']
            return X_test, y_test
        except (StopIteration, KeyError):
            return None, None

def run_multi_data_ensemble_pipeline(model_data_mapping, ensemble_methods=None, 
                                    ensemble_data_strategies=None, target_data_path=None,
                                    feature_names=None, enable_shap=True,
                                    feature_selection=True, feature_selection_method='weighted', k=None):
    """
    运行多数据源集成学习管道
    
    Args:
        model_data_mapping: 模型与数据源的映射字典
        ensemble_methods: 集成方法列表
        ensemble_data_strategies: 数据策略列表
        target_data_path: 目标数据路径（可选）
        feature_names: 特征名称列表（可选）
        enable_shap: 是否启用SHAP分析
        feature_selection: 是否进行特征筛选
        feature_selection_method: 特征筛选方法
        k: 选择的特征数量
        
    Returns:
        list: 集成结果列表
    """
    if ensemble_methods is None:
        ensemble_methods = ['voting', 'stacking', 'weighted']
    
    if ensemble_data_strategies is None:
        ensemble_data_strategies = ['unified', 'original', 'combined']
    
    all_results = [] # 初始化列表以收集所有结果

    for strategy in ensemble_data_strategies:
        for method in ensemble_methods:
            # 实例化并训练模型
            ensemble = MultiDataEnsemble(
                    model_data_mapping=model_data_mapping,
                    ensemble_method=method,
                    ensemble_data_strategy=strategy,
                meta_classifier=LogisticRegression(),
                    feature_selection=feature_selection,
                    feature_selection_method=feature_selection_method,
                    k=k
                )
                
            # 训练模型
            ensemble.fit() # fit不再需要任何参数
            metrics = ensemble._evaluate_performance() # 评估也从内部获取数据
            
            all_results.append((strategy, method, ensemble, metrics))
                
    # 在循环结束后，保存整个列表
    if all_results:
        model_names_str = '_'.join(sorted(model_data_mapping.keys()))
        save_path = CONFIG['multi_data_cache_path'] / f"{model_names_str}_multi_data_results.joblib"
        dump(all_results, save_path)
        print(f"已将所有 {len(all_results)} 个集成模型的结果保存到: {save_path}")
    
    return all_results

# 示例使用函数
def example_usage():
    """
    多数据源集成学习使用示例（包含SHAP分析）
    """
    # 定义模型与数据源的映射
    model_data_mapping = {
        'RandomForest': 'C:/Users/<USER>/Desktop/dataset1.csv',
        'XGBoost': 'C:/Users/<USER>/Desktop/dataset2.csv',
        'LightGBM': 'C:/Users/<USER>/Desktop/dataset3.csv',
        'CatBoost': 'C:/Users/<USER>/Desktop/dataset4.csv'
    }
    
    # 定义特征名称（可选，如果不提供会自动生成）
    feature_names = [
        'feature_1', 'feature_2', 'feature_3', 'feature_4', 'feature_5',
        'feature_6', 'feature_7', 'feature_8', 'feature_9', 'feature_10'
    ]
    
    # 运行多数据源集成学习（包含SHAP分析）
    results = run_multi_data_ensemble_pipeline(
        model_data_mapping=model_data_mapping,
        ensemble_methods=['voting', 'stacking'],
        ensemble_data_strategies=['unified', 'combined'],
        target_data_path='C:/Users/<USER>/Desktop/target_dataset.csv',
        feature_names=feature_names,
        enable_shap=True,  # 启用SHAP可解释性分析
        feature_selection=True,
        feature_selection_method='weighted',
        k=None
    )
    
    # 打印SHAP分析结果路径
    if SHAP_AVAILABLE:
        print("\n" + "="*60)
        print("🔍 SHAP可解释性分析结果")
        print("="*60)
        
        successful_results = [r for r in results if r['status'] == 'success']
        for result in successful_results:
            strategy = result['strategy']
            method = result['method']
            shap_dir = CONFIG['output_dir'] / 'ensemble' / f"{strategy}_{method}" / 'shap_analysis'
            print(f"\n{strategy}_{method} 模型SHAP分析结果:")
            print(f"  📁 SHAP分析目录: {shap_dir}")
            print(f"  📊 摘要图: {shap_dir / 'multi_data_ensemble_shap_summary.png'}")
            print(f"  📈 特征重要性: {shap_dir / 'multi_data_ensemble_feature_importance.png'}")
            print(f"  🔍 决策图: {shap_dir / 'multi_data_ensemble_dependence.png'}")
        
        # 综合报告路径
        report_path = CONFIG['output_dir'] / 'ensemble' / 'shap_reports' / 'multi_data_ensemble_shap_report.html'
        print(f"\n📋 综合SHAP报告: {report_path}")
        print("\n💡 提示: 打开HTML报告文件可查看完整的可解释性分析结果")
    
    return results

if __name__ == "__main__":
    # 运行示例
    example_usage()