#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后集成学习功能使用示例
展示如何使用新的优化模块进行集成学习
"""

import numpy as np
import pandas as pd
from pathlib import Path

# 导入优化后的模块
from unified_data_preprocessor import UnifiedDataPreprocessor
from simplified_feature_selector import SimplifiedFeatureSelector
from advanced_missing_handler import handle_missing_values_smart
from config_manager import global_config_manager, ConfigKeys
from exception_handling import safe_execute, error_context

# 尝试导入logger
try:
    from logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logging.basicConfig(level=logging.INFO)


@safe_execute(context="优化集成学习示例", max_retries=2)
def optimized_ensemble_example(data_path: str):
    """
    优化后的集成学习完整示例
    
    Args:
        data_path: 数据文件路径
    """
    logger.info("🚀 开始优化后的集成学习示例")
    
    # 1. 配置管理示例
    logger.info("📋 步骤1: 配置系统参数")
    with error_context("配置设置"):
        global_config_manager.set_config(ConfigKeys.SCALING_METHOD, 'standard')
        global_config_manager.set_config(ConfigKeys.RANDOM_STATE, 42)
        global_config_manager.set_config(ConfigKeys.FEATURE_SELECTION, True)
        logger.info("✅ 系统配置完成")
    
    # 2. 统一数据预处理示例
    logger.info("🔧 步骤2: 使用统一数据预处理器")
    with error_context("数据预处理"):
        preprocessor = UnifiedDataPreprocessor(
            scaling_method='standard',
            handle_missing='smart',
            handle_outliers=True,
            random_state=42
        )
        
        # 加载和预处理数据（包含数据质量分析）
        X_train, X_test, y_train, y_test, quality_stats = preprocessor.load_and_preprocess(
            data_path, 
            return_quality_stats=True
        )
        
        logger.info(f"✅ 数据预处理完成")
        logger.info(f"   训练集形状: {X_train.shape}")
        logger.info(f"   测试集形状: {X_test.shape}")
        logger.info(f"   数据质量评分: {quality_stats.get('quality_score', 'N/A')}")
    
    # 3. 高级缺失值处理示例（如果需要）
    if X_train.isnull().sum().sum() > 0:
        logger.info("🔍 步骤3: 处理缺失值")
        with error_context("缺失值处理"):
            X_train_filled, missing_report = handle_missing_values_smart(
                X_train, y_train, strategy='auto'
            )
            X_test_filled = X_test  # 测试集已在预处理中处理
            
            logger.info(f"✅ 缺失值处理完成")
            logger.info(f"   处理成功率: {missing_report['processing_summary']['success_rate']:.2%}")
    else:
        logger.info("✅ 步骤3: 无缺失值，跳过处理")
        X_train_filled, X_test_filled = X_train, X_test
    
    # 4. 简化特征选择示例
    logger.info("🎯 步骤4: 智能特征选择")
    with error_context("特征选择"):
        feature_selector = SimplifiedFeatureSelector(
            method='auto',  # 自动选择最佳方法
            cross_validation=True
        )
        
        X_train_selected, selected_features = feature_selector.fit_transform(
            X_train_filled, y_train
        )
        X_test_selected = feature_selector.transform(X_test_filled)
        
        # 获取特征选择报告
        selection_report = feature_selector.get_selection_report()
        
        logger.info(f"✅ 特征选择完成")
        logger.info(f"   原始特征数: {X_train_filled.shape[1]}")
        logger.info(f"   选择特征数: {len(selected_features)}")
        logger.info(f"   选择方法: {selection_report['method']}")
        
        if 'evaluation' in selection_report and selection_report['evaluation']:
            improvement = selection_report['evaluation'].get('improvement', 0)
            logger.info(f"   性能提升: {improvement:.4f}")
    
    # 5. 集成学习示例（使用现有的集成学习功能）
    logger.info("🎪 步骤5: 执行集成学习")
    with error_context("集成学习"):
        # 这里可以调用现有的集成学习函数，但使用优化后的数据
        from model_ensemble import run_ensemble_pipeline
        
        # 选择要使用的模型
        model_names = ['RandomForest', 'XGBoost', 'LightGBM']
        ensemble_methods = ['voting', 'stacking']
        
        # 运行集成学习（现在数据已经优化预处理）
        ensemble_results = run_ensemble_pipeline(
            X_train=X_train_selected,
            y_train=y_train,
            X_test=X_test_selected,
            y_test=y_test,
            model_names=model_names,
            ensemble_methods=ensemble_methods,
            tune_ensemble=True,
            enable_shap=True
        )
        
        if ensemble_results:
            # 找出最佳模型
            best_model_name = max(
                ensemble_results.keys(),
                key=lambda x: ensemble_results[x]['metrics']['f1_score']
            )
            best_score = ensemble_results[best_model_name]['metrics']['f1_score']
            
            logger.info(f"✅ 集成学习完成")
            logger.info(f"   最佳模型: {best_model_name}")
            logger.info(f"   最佳F1分数: {best_score:.4f}")
        else:
            logger.warning("⚠️ 集成学习未返回结果")
    
    # 6. 结果总结和配置状态
    logger.info("📊 步骤6: 生成优化总结")
    with error_context("结果总结"):
        # 获取配置状态
        config_status = global_config_manager.get_status()
        preprocessor_info = global_config_manager.list_preprocessors()
        
        optimization_summary = {
            'data_shape': {
                'original': (X_train.shape, X_test.shape),
                'after_selection': (X_train_selected.shape, X_test_selected.shape)
            },
            'preprocessing': {
                'scaling_method': preprocessor.scaling_method,
                'missing_handling': preprocessor.handle_missing,
                'outlier_handling': preprocessor.handle_outliers
            },
            'feature_selection': {
                'method': selection_report['method'],
                'selected_count': len(selected_features),
                'reduction_ratio': 1 - (len(selected_features) / X_train.shape[1])
            },
            'system_status': {
                'config_items': config_status['config_items'],
                'preprocessors': len(preprocessor_info),
                'memory_usage_mb': config_status['memory_usage']['total_size_mb']
            }
        }
        
        logger.info("✅ 优化总结完成")
        logger.info(f"   特征减少: {optimization_summary['feature_selection']['reduction_ratio']:.1%}")
        logger.info(f"   系统内存使用: {optimization_summary['system_status']['memory_usage_mb']:.2f} MB")
        
        return optimization_summary


def demonstrate_error_handling():
    """
    演示增强的异常处理机制
    """
    logger.info("🛡️ 演示异常处理机制")
    
    @safe_execute(context="示例异常处理", max_retries=2, return_on_failure="默认结果")
    def example_function_with_error():
        """可能失败的示例函数"""
        import random
        if random.random() < 0.7:  # 70%概率失败
            raise ValueError("模拟的错误")
        return "成功结果"
    
    # 测试异常处理
    result = example_function_with_error()
    logger.info(f"异常处理测试结果: {result}")


def demonstrate_config_management():
    """
    演示配置管理功能
    """
    logger.info("⚙️ 演示配置管理功能")
    
    # 设置配置
    global_config_manager.set_config('demo_param', 'demo_value')
    global_config_manager.update_config({
        'batch_size': 32,
        'learning_rate': 0.001,
        'epochs': 100
    })
    
    # 获取配置
    demo_param = global_config_manager.get_config('demo_param')
    batch_size = global_config_manager.get_config('batch_size')
    
    logger.info(f"演示参数: {demo_param}")
    logger.info(f"批次大小: {batch_size}")
    
    # 保存配置
    config_file = "demo_config.json"
    success = global_config_manager.save_config_to_file(config_file)
    if success:
        logger.info(f"配置已保存到: {config_file}")


def create_sample_data_for_demo():
    """
    创建示例数据用于演示
    """
    logger.info("📝 创建示例数据")
    
    # 创建有缺失值和异常值的示例数据
    np.random.seed(42)
    n_samples = 1000
    n_features = 20
    
    # 生成基础数据
    X = np.random.randn(n_samples, n_features)
    y = (X[:, 0] + X[:, 1] - X[:, 2] + np.random.randn(n_samples) * 0.1 > 0).astype(int)
    
    # 添加一些缺失值
    missing_mask = np.random.random((n_samples, n_features)) < 0.1
    X[missing_mask] = np.nan
    
    # 添加一些异常值
    outlier_mask = np.random.random((n_samples, n_features)) < 0.02
    X[outlier_mask] = X[outlier_mask] * 10
    
    # 创建DataFrame
    feature_names = [f'feature_{i}' for i in range(n_features)]
    df = pd.DataFrame(X, columns=feature_names)
    df['label'] = y
    
    # 保存示例数据
    sample_file = "sample_data_demo.csv"
    df.to_csv(sample_file, index=False)
    
    logger.info(f"✅ 示例数据已创建: {sample_file}")
    logger.info(f"   数据形状: {df.shape}")
    logger.info(f"   缺失值数量: {df.isnull().sum().sum()}")
    
    return sample_file


def main():
    """
    主函数：运行完整的优化示例
    """
    logger.info("=" * 60)
    logger.info("🎯 优化后集成学习功能完整示例")
    logger.info("=" * 60)
    
    try:
        # 1. 创建示例数据
        sample_file = create_sample_data_for_demo()
        
        # 2. 演示配置管理
        demonstrate_config_management()
        
        # 3. 演示异常处理
        demonstrate_error_handling()
        
        # 4. 运行完整的优化集成学习示例
        summary = optimized_ensemble_example(sample_file)
        
        # 5. 输出最终总结
        logger.info("=" * 60)
        logger.info("🎊 优化示例执行成功！")
        logger.info("=" * 60)
        logger.info("主要改进亮点:")
        logger.info("  ✅ 统一的数据预处理流程")
        logger.info("  ✅ 智能的特征选择策略")
        logger.info("  ✅ 高级的缺失值处理")
        logger.info("  ✅ 健壮的异常处理机制")
        logger.info("  ✅ 现代化的配置管理")
        logger.info("  ✅ 完全的向后兼容性")
        
        # 清理示例文件
        try:
            Path(sample_file).unlink()
            Path("demo_config.json").unlink(missing_ok=True)
            logger.info("🧹 清理完成")
        except:
            pass
            
    except Exception as e:
        logger.error(f"❌ 示例执行失败: {e}")
        raise


if __name__ == "__main__":
    main()
