#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import matplotlib
matplotlib.use('Agg')  # 设置非交互式后端，避免GUI警告
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import roc_curve, auc, confusion_matrix
from sklearn.calibration import calibration_curve
import numpy as np
from pathlib import Path
from joblib import load
import os

# 尝试导入配置模块
try:
    from config import OUTPUT_PATH, CACHE_PATH, PLOT_CONFIG, MODEL_DISPLAY_NAMES
    from plot_utils import translate_term, get_save_path, save_plot, setup_matplotlib_for_chinese, get_font_properties, plot_roc_comparison, plot_pr_comparison
except ImportError:
    # 通过获取当前文件的路径确定项目根目录
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    OUTPUT_PATH = PROJECT_ROOT / 'output'
    CACHE_PATH = PROJECT_ROOT / 'cache'
    MODEL_DISPLAY_NAMES = {
        'DecisionTree': 'Decision Tree',
        'RandomForest': 'Random Forest',
        'XGBoost': 'XGBoost',
        'LightGBM': 'LightGBM',
        'CatBoost': 'CatBoost',
        'Logistic': 'Logistic Regression',
        'SVM': 'SVM',
        'KNN': 'KNN',
        'NaiveBayes': 'Naive Bayes',
        'NeuralNet': 'Neural Network'
    }
    PLOT_CONFIG = {
        'dpi': 150,
        'figsize': (10, 8),
        'save_format': 'png'
    }

# 尝试导入日志模块
try:
    from logger import get_default_logger
    logger = get_default_logger("plot_comparison")
except ImportError:
    import logging
    logger = logging.getLogger("plot_comparison")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)

# 初始化matplotlib设置
setup_matplotlib_for_chinese()

# 配置字典
CONFIG = {
    'output_path': OUTPUT_PATH,
    'cache_path': CACHE_PATH,
    'thresholds': np.linspace(0, 0.5, 51),
    'dpi': PLOT_CONFIG.get('dpi', 150),
    'figsize': PLOT_CONFIG.get('figsize', (10, 8))
}

# 模型列表
MODEL_NAMES = ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
               'Logistic', 'SVM', 'NeuralNet', 'NaiveBayes', 'KNN']

# 英文术语映射
TERM_MAPPING = {
    'DCA 对比': 'DCA Comparison',
    '阈值概率': 'Threshold Probability',
    '净收益': 'Net Benefit',
    'CIC 对比': 'CIC Comparison',
    '风险阈值': 'Risk Threshold',
    '高风险比例': 'High Risk Proportion',
    'ROC 对比': 'ROC Comparison',
    '假阳性率': 'False Positive Rate',
    '真阳性率': 'True Positive Rate',
    '校准曲线对比': 'Calibration Curve Comparison',
    '预测概率': 'Predicted Probability',
    '真实概率': 'True Probability',
    '完美校准': 'Perfect Calibration'
}

def translate(term):
    """将中文术语翻译为英文"""
    return TERM_MAPPING.get(term, term)

def plot_curve(ax, x, y, label, title, xlabel, ylabel, color=None, linestyle='-'):
    ax.plot(x, y, label=label, color=color, linestyle=linestyle)
    ax.set_title(translate(title))
    ax.set_xlabel(translate(xlabel))
    ax.set_ylabel(translate(ylabel))
    ax.legend(loc='best')

def calculate_dca(y_true, y_prob, thresholds):
    net_benefit = []
    for thresh in thresholds:
        y_pred = (y_prob >= thresh).astype(int)
        tp = np.sum((y_true == 1) & (y_pred == 1))
        fp = np.sum((y_true == 0) & (y_pred == 1))
        nb = (tp / len(y_true)) - (fp / len(y_true)) * (thresh / (1 - thresh))
        net_benefit.append(nb)
    return np.array(net_benefit)

def calculate_cic(y_prob, thresholds):
    cic_values = []
    for thresh in thresholds:
        high_risk = np.mean(y_prob >= thresh)
        cic_values.append(high_risk)
    return np.array(cic_values)

# 获取全局字体属性
FONT_PROPERTIES = get_font_properties()

def plot_combined_evaluation(results):
    colors = sns.color_palette('tab10', len(results))

    # DCA 组合图
    fig, ax = plt.subplots(figsize=CONFIG['figsize'])
    for (model_name, data), color in zip(results.items(), colors):
        display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
        y_score = data.get('y_score', None)
        dca_data = calculate_dca(data['y_true'], y_score, CONFIG['thresholds']) if y_score is not None and len(y_score) == len(data['y_true']) else np.zeros_like(CONFIG['thresholds'])
        plt.plot(CONFIG['thresholds'], dca_data, label=display_name, color=color)
    ax.set_title(translate('DCA 对比'), fontproperties=FONT_PROPERTIES)
    ax.set_xlabel(translate('阈值概率'), fontproperties=FONT_PROPERTIES)
    ax.set_ylabel(translate('净收益'), fontproperties=FONT_PROPERTIES)
    if FONT_PROPERTIES:
        ax.legend(loc='upper right', fontsize=8, prop=FONT_PROPERTIES)
    else:
        ax.legend(loc='upper right', fontsize=8)
    save_plot(fig, model_name='all', plot_type='dca', file_name="combined_dca.png")

    # CIC 组合图
    fig, ax = plt.subplots(figsize=CONFIG['figsize'])
    for (model_name, data), color in zip(results.items(), colors):
        display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
        y_score = data.get('y_score', None)
        cic_data = calculate_cic(y_score, CONFIG['thresholds']) if y_score is not None and len(y_score) == len(data['y_true']) else np.zeros_like(CONFIG['thresholds'])
        plt.plot(CONFIG['thresholds'], cic_data, label=display_name, color=color)
    ax.set_title(translate('CIC 对比'), fontproperties=FONT_PROPERTIES)
    ax.set_xlabel(translate('风险阈值'), fontproperties=FONT_PROPERTIES)
    ax.set_ylabel(translate('高风险比例'), fontproperties=FONT_PROPERTIES)
    if FONT_PROPERTIES:
        ax.legend(loc='upper right', fontsize=8, prop=FONT_PROPERTIES)
    else:
        ax.legend(loc='upper right', fontsize=8)
    save_plot(fig, model_name='all', plot_type='cic', file_name="combined_cic.png")

    # ROC 组合图
    fig, ax = plot_roc_comparison(results, title='ROC 对比')
    save_plot(fig, model_name='all', plot_type='roc', file_name="combined_roc.png")

    # PR 组合图
    fig, ax = plot_pr_comparison(results, title='PR 对比')
    save_plot(fig, model_name='all', plot_type='pr', file_name="combined_pr.png")

    # 校准曲线组合
    fig, ax = plt.subplots(figsize=CONFIG['figsize'])
    for (model_name, data), color in zip(results.items(), colors):
        display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
        y_score = data.get('y_score', None)
        prob_true, prob_pred = calibration_curve(data['y_true'], y_score, n_bins=10) if y_score is not None and len(y_score) == len(data['y_true']) else ([0, 1], [0, 1])
        plt.plot(prob_pred, prob_true, marker='o', linestyle='-', label=display_name, color=color)
    ax.plot([0, 1], [0, 1], 'k:', label=translate('完美校准'))
    ax.set_title(translate('校准曲线对比'), fontproperties=FONT_PROPERTIES)
    ax.set_xlabel(translate('预测概率'), fontproperties=FONT_PROPERTIES)
    ax.set_ylabel(translate('真实概率'), fontproperties=FONT_PROPERTIES)
    if FONT_PROPERTIES:
        ax.legend(loc='upper left', prop=FONT_PROPERTIES)
    else:
        ax.legend(loc='upper left')
    save_plot(fig, model_name='all', plot_type='calibration', file_name="combined_calibration.png")

def plot_model_comparison(selected_models=None):
    """
    绘制模型比较图表

    Args:
        selected_models: 要比较的模型列表，None表示使用所有可用模型
    """
    results = {}

    # 如果没有指定模型，则自动发现所有可用模型
    if selected_models is None:
        try:
            from config import MODEL_NAMES
            selected_models = MODEL_NAMES
        except ImportError:
            selected_models = list(MODEL_DISPLAY_NAMES.keys())

    for model_name in selected_models:
        cache_file = CACHE_PATH / f"{model_name}_results.joblib"
        if cache_file.exists():
            results[model_name] = load(cache_file)
            logger.info(f"加载模型 {model_name} 的缓存结果")
        else:
            logger.warning(f"模型 {model_name} 的缓存文件不存在: {cache_file}")

    if results:
        plot_combined_evaluation(results)
    else:
        logger.error("没有可用的模型结果进行比较！")
