#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成学习可视化模块
提供集成学习结果的可视化功能，包括性能对比图、权重分析图、集成效果图等
"""

import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，避免字体问题

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import warnings
from joblib import load

from config import OUTPUT_PATH, ENSEMBLE_PATH
from logger import get_logger

warnings.filterwarnings('ignore')
logger = get_logger(__name__)

# 设置安全的matplotlib配置，避免字体问题
plt.rcParams.update(plt.rcParamsDefault)
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.max_open_warning'] = 0

# 不使用seaborn和font_manager，避免字体问题
# from font_manager import initialize_fonts, get_safe_title, get_safe_label
# initialize_fonts()

def get_safe_title(chinese_text, english_text):
    """获取安全的标题文本 - 强制使用英文避免字体问题"""
    return english_text

def get_safe_label(chinese_text, english_text):
    """获取安全的标签文本 - 强制使用英文避免字体问题"""
    return english_text


def plot_ensemble_performance_comparison(ensemble_results, save_path=None, figsize=(12, 8)):
    """
    绘制集成学习性能对比图
    
    Args:
        ensemble_results: 集成结果字典
        save_path: 保存路径
        figsize: 图片大小
    """
    if not ensemble_results:
        logger.warning("没有集成结果可以绘制")
        return
    
    # 准备数据
    methods = []
    metrics_data = {
        'accuracy': [],
        'precision': [],
        'recall': [],
        'f1_score': [],
        'auc': []
    }
    
    for name, result in ensemble_results.items():
        methods.append(name)
        metrics = result['metrics']
        for metric in metrics_data.keys():
            metrics_data[metric].append(metrics.get(metric, 0))
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=figsize)
    fig.suptitle('Ensemble Methods Performance Comparison', fontsize=16, fontweight='bold')

    # 绘制各个指标的对比图
    metrics_names = ['accuracy', 'precision', 'recall', 'f1_score', 'auc']
    metrics_labels = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'AUC']
    
    for i, (metric, label) in enumerate(zip(metrics_names, metrics_labels)):
        row = i // 3
        col = i % 3
        
        ax = axes[row, col]
        bars = ax.bar(methods, metrics_data[metric], alpha=0.7)
        ax.set_title(label, fontweight='bold')
        ax.set_ylabel('Score')
        ax.set_ylim(0, 1)
        
        # 添加数值标签
        for bar, value in zip(bars, metrics_data[metric]):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{value:.3f}', ha='center', va='bottom', fontsize=9)
        
        # 旋转x轴标签
        ax.tick_params(axis='x', rotation=45)
        
        # 高亮最佳性能
        best_idx = np.argmax(metrics_data[metric])
        bars[best_idx].set_color('red')
        bars[best_idx].set_alpha(0.9)
    
    # 删除多余的子图
    axes[1, 2].remove()
    
    # 综合性能雷达图
    ax_radar = fig.add_subplot(2, 3, 6, projection='polar')
    
    # 计算每个方法的综合得分
    angles = np.linspace(0, 2 * np.pi, len(metrics_names), endpoint=False)
    angles = np.concatenate((angles, [angles[0]]))  # 闭合图形
    
    colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
    
    for i, method in enumerate(methods):
        values = [metrics_data[metric][i] for metric in metrics_names]
        values += [values[0]]  # 闭合图形
        
        ax_radar.plot(angles, values, 'o-', linewidth=2, label=method, color=colors[i])
        ax_radar.fill(angles, values, alpha=0.25, color=colors[i])
    
    ax_radar.set_xticks(angles[:-1])
    ax_radar.set_xticklabels(metrics_labels)
    ax_radar.set_ylim(0, 1)
    ax_radar.set_title('综合性能雷达图', fontweight='bold', pad=20)
    ax_radar.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"性能对比图已保存到: {save_path}")
    
    plt.show()


def plot_ensemble_weights_analysis(ensemble_results, save_path=None, figsize=(12, 6)):
    """
    绘制集成学习权重分析图
    
    Args:
        ensemble_results: 集成结果字典
        save_path: 保存路径
        figsize: 图片大小
    """
    voting_results = {k: v for k, v in ensemble_results.items() if 'voting' in k}
    
    if not voting_results:
        logger.warning("没有投票集成结果可以分析权重")
        return
    
    fig, axes = plt.subplots(1, len(voting_results), figsize=figsize)
    if len(voting_results) == 1:
        axes = [axes]
    
    fig.suptitle('投票集成权重分析', fontsize=16, fontweight='bold')
    
    for i, (name, result) in enumerate(voting_results.items()):
        ax = axes[i]
        model = result['model']
        
        # 获取基础模型名称
        base_model_names = list(model.trained_models.keys())
        
        # 如果有权重信息，显示权重
        if hasattr(model.ensemble_model, 'weights') and model.ensemble_model.weights is not None:
            weights = model.ensemble_model.weights
        else:
            # 等权重
            weights = [1.0 / len(base_model_names)] * len(base_model_names)
        
        # 绘制权重饼图
        colors = plt.cm.Set3(np.linspace(0, 1, len(base_model_names)))
        wedges, texts, autotexts = ax.pie(weights, labels=base_model_names, autopct='%1.1f%%',
                                         colors=colors, startangle=90)
        
        title = get_safe_title(f'{name}\n权重分布', f'{name}\nWeight Distribution')
        ax.set_title(title, fontweight='bold')
        
        # 美化文本
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"权重分析图已保存到: {save_path}")
    
    plt.show()


def plot_ensemble_feature_importance(ensemble_results, save_path=None, figsize=(12, 8)):
    """
    绘制集成学习特征重要性图
    
    Args:
        ensemble_results: 集成结果字典
        save_path: 保存路径
        figsize: 图片大小
    """
    # 筛选有特征重要性的模型
    models_with_importance = {}
    for name, result in ensemble_results.items():
        model = result['model']
        if hasattr(model, 'feature_importances_') and model.feature_importances_ is not None:
            models_with_importance[name] = model.feature_importances_
    
    if not models_with_importance:
        logger.warning("没有模型具有特征重要性信息")
        return
    
    # 创建子图
    n_models = len(models_with_importance)
    cols = min(3, n_models)
    rows = (n_models + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=figsize)
    if n_models == 1:
        axes = [axes]
    elif rows == 1:
        axes = axes.reshape(1, -1)
    
    fig.suptitle('集成学习特征重要性分析', fontsize=16, fontweight='bold')
    
    for i, (name, importance) in enumerate(models_with_importance.items()):
        row = i // cols
        col = i % cols
        ax = axes[row, col] if rows > 1 else axes[col]
        
        # 选择前10个最重要的特征
        n_features = min(10, len(importance))
        top_indices = np.argsort(importance)[-n_features:]
        top_importance = importance[top_indices]
        feature_names = [f'Feature_{idx}' for idx in top_indices]
        
        # 绘制水平条形图
        bars = ax.barh(range(n_features), top_importance, alpha=0.7)
        ax.set_yticks(range(n_features))
        ax.set_yticklabels(feature_names)
        ax.set_xlabel('重要性')
        ax.set_title(f'{name}', fontweight='bold')
        
        # 添加数值标签
        for j, (bar, value) in enumerate(zip(bars, top_importance)):
            width = bar.get_width()
            ax.text(width + 0.001, bar.get_y() + bar.get_height()/2.,
                   f'{value:.3f}', ha='left', va='center', fontsize=9)
    
    # 隐藏多余的子图
    for i in range(n_models, rows * cols):
        row = i // cols
        col = i % cols
        if rows > 1:
            axes[row, col].set_visible(False)
        else:
            axes[col].set_visible(False)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"特征重要性图已保存到: {save_path}")
    
    plt.show()


def plot_ensemble_learning_curve(ensemble_results, X_train, y_train, save_path=None, figsize=(12, 6)):
    """
    绘制集成学习的学习曲线
    
    Args:
        ensemble_results: 集成结果字典
        X_train, y_train: 训练数据
        save_path: 保存路径
        figsize: 图片大小
    """
    from sklearn.model_selection import learning_curve
    
    # 选择最佳的几个模型
    sorted_results = sorted(ensemble_results.items(), 
                           key=lambda x: x[1]['metrics']['f1_score'], 
                           reverse=True)[:3]
    
    fig, ax = plt.subplots(figsize=figsize)
    
    colors = ['red', 'blue', 'green']
    
    for i, (name, result) in enumerate(sorted_results):
        try:
            model = result['model'].ensemble_model
            
            # 计算学习曲线
            train_sizes, train_scores, val_scores = learning_curve(
                model, X_train, y_train, cv=5, n_jobs=-1,
                train_sizes=np.linspace(0.1, 1.0, 10),
                scoring='f1_weighted'
            )
            
            # 计算均值和标准差
            train_mean = np.mean(train_scores, axis=1)
            train_std = np.std(train_scores, axis=1)
            val_mean = np.mean(val_scores, axis=1)
            val_std = np.std(val_scores, axis=1)
            
            # 绘制学习曲线
            ax.plot(train_sizes, train_mean, 'o-', color=colors[i], 
                   label=f'{name} (训练)', alpha=0.8)
            ax.fill_between(train_sizes, train_mean - train_std, train_mean + train_std, 
                           alpha=0.1, color=colors[i])
            
            ax.plot(train_sizes, val_mean, 's--', color=colors[i], 
                   label=f'{name} (验证)', alpha=0.8)
            ax.fill_between(train_sizes, val_mean - val_std, val_mean + val_std, 
                           alpha=0.1, color=colors[i])
            
        except Exception as e:
            logger.warning(f"为 {name} 生成学习曲线失败: {e}")
    
    ax.set_xlabel('训练样本数量')
    ax.set_ylabel('F1分数')
    ax.set_title('集成学习模型学习曲线', fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"学习曲线图已保存到: {save_path}")
    
    plt.show()


def visualize_ensemble_results(results_file=None, ensemble_results=None,
                             X_train=None, y_train=None, output_dir=None):
    """
    可视化集成学习结果的主函数 - 使用安全的可视化方法

    Args:
        results_file: 结果文件路径
        ensemble_results: 集成结果字典（如果不提供results_file）
        X_train, y_train: 训练数据（用于学习曲线）
        output_dir: 输出目录
    """
    try:
        # 使用安全的可视化模块
        from safe_visualization import safe_ensemble_performance_plot, safe_create_summary_report

        if output_dir is None:
            output_dir = ENSEMBLE_PATH / 'visualizations'
        else:
            output_dir = Path(output_dir)

        output_dir.mkdir(parents=True, exist_ok=True)

        # 加载结果
        if ensemble_results is None:
            if results_file is None:
                logger.error("Must provide results_file or ensemble_results")
                return

            try:
                data = load(results_file)
                ensemble_results = data.get('ensemble_results', {})
            except Exception as e:
                logger.error(f"Failed to load results file: {e}")
                return

        if not ensemble_results:
            logger.warning("No ensemble results to visualize")
            return

        logger.info("Starting to generate ensemble learning visualization charts")

        # 1. 性能对比图
        logger.info("Generating performance comparison chart")
        plot_success = safe_ensemble_performance_plot(
            ensemble_results,
            output_dir / 'performance_comparison.png'
        )

        # 2. 总结报告
        logger.info("Generating summary report")
        report_success = safe_create_summary_report(
            ensemble_results,
            output_dir
        )

        if plot_success:
            logger.info("Performance comparison chart generated successfully")
        if report_success:
            logger.info("Summary report generated successfully")

        logger.info(f"All visualization charts saved to: {output_dir}")

    except Exception as e:
        logger.error(f"Visualization failed: {e}")
        # 降级处理：至少生成文本报告
        try:
            if ensemble_results and output_dir:
                from safe_visualization import safe_create_summary_report
                safe_create_summary_report(ensemble_results, output_dir)
                logger.info("Fallback: Text summary report generated")
        except:
            pass


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="集成学习可视化工具")
    parser.add_argument("--results_file", type=str, help="集成结果文件路径")
    parser.add_argument("--output_dir", type=str, help="输出目录")
    
    args = parser.parse_args()
    
    visualize_ensemble_results(
        results_file=args.results_file,
        output_dir=args.output_dir
    )
