#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练会话GUI组件
提供会话管理的图形界面功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
from pathlib import Path
import threading
from typing import Dict, List, Optional, Any

# 尝试导入相关模块
try:
    from session_utils import (
        create_new_session, activate_session, get_active_session_id,
        list_all_sessions, delete_session, get_session_summary,
        restore_session_to_cache, export_session_report,
        get_session_statistics
    )
    from training_session_manager import get_session_manager
    from session_loader import get_session_loader
except ImportError:
    # 如果导入失败，提供空函数
    def create_new_session(*args, **kwargs):
        return None
    def activate_session(*args, **kwargs):
        return False
    def get_active_session_id():
        return None
    def list_all_sessions():
        return []
    def delete_session(*args, **kwargs):
        return False
    def get_session_summary(*args, **kwargs):
        return None
    def restore_session_to_cache(*args, **kwargs):
        return False
    def export_session_report(*args, **kwargs):
        return None
    def get_session_statistics():
        return {}
    def get_session_manager():
        return None
    def get_session_loader():
        return None


class SessionManagerGUI:
    """训练会话管理GUI"""
    
    def __init__(self, parent=None):
        """
        初始化会话管理GUI
        
        Args:
            parent: 父窗口
        """
        self.parent = parent
        self.session_window = None
        self.sessions_data = []
        
    def show_session_manager(self):
        """显示会话管理窗口"""
        if self.session_window and self.session_window.winfo_exists():
            self.session_window.lift()
            return
        
        self.session_window = tk.Toplevel(self.parent)
        self.session_window.title("训练会话管理")
        self.session_window.geometry("900x600")
        self.session_window.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.session_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建工具栏
        self._create_toolbar(main_frame)
        
        # 创建会话列表
        self._create_session_list(main_frame)
        
        # 创建详情面板
        self._create_details_panel(main_frame)
        
        # 创建状态栏
        self._create_status_bar(main_frame)
        
        # 初始加载会话列表
        self.refresh_sessions()
    
    def _create_toolbar(self, parent):
        """创建工具栏"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 新建会话按钮
        ttk.Button(
            toolbar_frame, text="新建会话",
            command=self.create_new_session_dialog
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        # 激活会话按钮
        ttk.Button(
            toolbar_frame, text="激活会话",
            command=self.activate_selected_session
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        # 恢复到缓存按钮
        ttk.Button(
            toolbar_frame, text="恢复到缓存",
            command=self.restore_selected_session
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        # 导出报告按钮
        ttk.Button(
            toolbar_frame, text="导出报告",
            command=self.export_selected_session
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        # 删除会话按钮
        ttk.Button(
            toolbar_frame, text="删除会话",
            command=self.delete_selected_session
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        # 分隔符
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # 刷新按钮
        ttk.Button(
            toolbar_frame, text="刷新",
            command=self.refresh_sessions
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        # 统计信息按钮
        ttk.Button(
            toolbar_frame, text="统计信息",
            command=self.show_statistics
        ).pack(side=tk.RIGHT)
    
    def _create_session_list(self, parent):
        """创建会话列表"""
        list_frame = ttk.LabelFrame(parent, text="训练会话列表")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建Treeview
        columns = ('session_id', 'session_name', 'status', 'created_time', 'models', 'plots')
        self.sessions_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        # 设置列标题
        self.sessions_tree.heading('session_id', text='会话ID')
        self.sessions_tree.heading('session_name', text='会话名称')
        self.sessions_tree.heading('status', text='状态')
        self.sessions_tree.heading('created_time', text='创建时间')
        self.sessions_tree.heading('models', text='模型数量')
        self.sessions_tree.heading('plots', text='图片数量')
        
        # 设置列宽
        self.sessions_tree.column('session_id', width=120)
        self.sessions_tree.column('session_name', width=200)
        self.sessions_tree.column('status', width=80)
        self.sessions_tree.column('created_time', width=150)
        self.sessions_tree.column('models', width=80)
        self.sessions_tree.column('plots', width=80)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.sessions_tree.yview)
        self.sessions_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.sessions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定选择事件
        self.sessions_tree.bind('<<TreeviewSelect>>', self.on_session_select)
        
        # 绑定双击事件
        self.sessions_tree.bind('<Double-1>', self.on_session_double_click)
    
    def _create_details_panel(self, parent):
        """创建详情面板"""
        details_frame = ttk.LabelFrame(parent, text="会话详情")
        details_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 创建文本框显示详情
        self.details_text = tk.Text(details_frame, height=8, wrap=tk.WORD)
        details_scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=details_scrollbar.set)
        
        self.details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        details_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def _create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X)
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)
        
        # 当前活动会话标签
        self.active_session_label = ttk.Label(status_frame, text="")
        self.active_session_label.pack(side=tk.RIGHT)
        
        self.update_status_bar()
    
    def refresh_sessions(self):
        """刷新会话列表"""
        try:
            # 清空现有数据
            for item in self.sessions_tree.get_children():
                self.sessions_tree.delete(item)
            
            # 获取会话列表
            self.sessions_data = list_all_sessions()
            
            # 添加到树形控件
            for session in self.sessions_data:
                created_time = datetime.fromisoformat(session['created_time']).strftime('%Y-%m-%d %H:%M')
                
                self.sessions_tree.insert('', tk.END, values=(
                    session['session_id'][:12] + '...',  # 截短显示
                    session['session_name'],
                    session['status'],
                    created_time,
                    session['model_count'],
                    session['plot_count']
                ))
            
            self.status_label.config(text=f"已加载 {len(self.sessions_data)} 个会话")
            self.update_status_bar()
            
        except Exception as e:
            messagebox.showerror("错误", f"刷新会话列表失败: {e}")
    
    def on_session_select(self, event):
        """会话选择事件处理"""
        selection = self.sessions_tree.selection()
        if not selection:
            self.details_text.delete(1.0, tk.END)
            return
        
        # 获取选中的会话
        item = selection[0]
        index = self.sessions_tree.index(item)
        
        if index < len(self.sessions_data):
            session_info = self.sessions_data[index]
            self.show_session_details(session_info['session_id'])
    
    def on_session_double_click(self, event):
        """会话双击事件处理"""
        self.activate_selected_session()
    
    def show_session_details(self, session_id):
        """显示会话详情"""
        try:
            summary = get_session_summary(session_id)
            if not summary:
                self.details_text.delete(1.0, tk.END)
                self.details_text.insert(tk.END, "无法获取会话详情")
                return
            
            # 格式化详情信息
            details = f"""会话名称: {summary['basic_info']['session_name']}
会话ID: {summary['basic_info']['session_id']}
描述: {summary['basic_info']['description']}
创建时间: {summary['basic_info']['created_time']}
最后修改: {summary['basic_info']['last_modified']}
状态: {summary['basic_info']['status']}

统计信息:
- 训练模型: {summary['statistics']['total_models']} 个
- 生成图片: {summary['statistics']['total_plots']} 个
- 数据文件: {summary['statistics']['total_data_files']} 个
- 集成结果: {summary['statistics']['ensemble_results']} 个

训练模型列表:"""
            
            for model in summary['models']:
                status = "✓" if model['file_exists'] else "✗"
                details += f"\n- {model['name']} ({model['type']}) {status}"
            
            details += "\n\n图片统计:"
            for plot_type, plot_info in summary['plots_by_type'].items():
                if plot_info['count'] > 0:
                    details += f"\n- {plot_type}: {plot_info['count']} 个"
            
            self.details_text.delete(1.0, tk.END)
            self.details_text.insert(tk.END, details)
            
        except Exception as e:
            self.details_text.delete(1.0, tk.END)
            self.details_text.insert(tk.END, f"获取会话详情失败: {e}")
    
    def create_new_session_dialog(self):
        """创建新会话对话框"""
        dialog = tk.Toplevel(self.session_window)
        dialog.title("创建新会话")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.transient(self.session_window)
        dialog.grab_set()
        
        # 会话名称
        ttk.Label(dialog, text="会话名称:").pack(pady=10)
        name_entry = ttk.Entry(dialog, width=40)
        name_entry.pack(pady=5)
        name_entry.insert(0, f"训练会话_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        # 会话描述
        ttk.Label(dialog, text="会话描述:").pack(pady=(20, 5))
        desc_text = tk.Text(dialog, width=40, height=8)
        desc_text.pack(pady=5, padx=20, fill=tk.BOTH, expand=True)
        
        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)
        
        def create_session():
            name = name_entry.get().strip()
            description = desc_text.get(1.0, tk.END).strip()
            
            if not name:
                messagebox.showerror("错误", "请输入会话名称")
                return
            
            try:
                session_id = create_new_session(name, description, auto_activate=True)
                if session_id:
                    messagebox.showinfo("成功", f"会话创建成功!\n会话ID: {session_id}")
                    dialog.destroy()
                    self.refresh_sessions()
                    self.update_status_bar()
                else:
                    messagebox.showerror("错误", "创建会话失败")
            except Exception as e:
                messagebox.showerror("错误", f"创建会话失败: {e}")
        
        ttk.Button(button_frame, text="创建", command=create_session).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)
        
        # 聚焦到名称输入框
        name_entry.focus()
    
    def activate_selected_session(self):
        """激活选中的会话"""
        selection = self.sessions_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个会话")
            return
        
        item = selection[0]
        index = self.sessions_tree.index(item)
        
        if index < len(self.sessions_data):
            session_info = self.sessions_data[index]
            session_id = session_info['session_id']
            
            try:
                if activate_session(session_id):
                    messagebox.showinfo("成功", f"会话 '{session_info['session_name']}' 已激活")
                    self.update_status_bar()
                else:
                    messagebox.showerror("错误", "激活会话失败")
            except Exception as e:
                messagebox.showerror("错误", f"激活会话失败: {e}")
    
    def restore_selected_session(self):
        """恢复选中的会话到缓存"""
        selection = self.sessions_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个会话")
            return

        item = selection[0]
        index = self.sessions_tree.index(item)

        if index < len(self.sessions_data):
            session_info = self.sessions_data[index]
            session_id = session_info['session_id']

            # 确认对话框
            if not messagebox.askyesno("确认", f"确定要恢复会话 '{session_info['session_name']}' 到缓存吗?\n这将覆盖现有的缓存数据。"):
                return

            try:
                def restore_task():
                    self.status_label.config(text="正在恢复会话到缓存...")
                    success = restore_session_to_cache(session_id)

                    if success:
                        # 恢复成功后刷新GUI界面
                        self.session_window.after(0, lambda: self._refresh_gui_after_restore(session_info))
                        self.session_window.after(0, lambda: messagebox.showinfo("成功", "会话已成功恢复到缓存\nGUI界面已更新，可以查看模型结果"))
                        self.session_window.after(0, lambda: self.status_label.config(text="会话恢复完成"))
                    else:
                        self.session_window.after(0, lambda: messagebox.showerror("错误", "恢复会话失败"))
                        self.session_window.after(0, lambda: self.status_label.config(text="会话恢复失败"))

                # 在后台线程中执行恢复操作
                threading.Thread(target=restore_task, daemon=True).start()

            except Exception as e:
                messagebox.showerror("错误", f"恢复会话失败: {e}")

    def _refresh_gui_after_restore(self, session_info):
        """恢复会话后刷新GUI界面"""
        try:
            # 获取主GUI对象
            if hasattr(self.parent, 'master') and self.parent.master:
                main_gui = self.parent.master
            else:
                main_gui = self.parent

            # 使用专门的集成模块来刷新GUI
            try:
                from session_gui_integration import create_session_gui_integration
                integration = create_session_gui_integration(main_gui)
                integration.refresh_after_session_restore(session_info['session_id'])

                # 显示成功对话框
                integration.show_session_restore_success_dialog(
                    session_info['session_name'],
                    session_info['model_count'],
                    session_info['plot_count']
                )

            except ImportError:
                # 如果集成模块不可用，使用基本的刷新方法
                self._basic_gui_refresh(main_gui, session_info)

        except Exception as e:
            print(f"刷新GUI界面失败: {e}")

    def _basic_gui_refresh(self, main_gui, session_info):
        """基本的GUI刷新方法（备选方案）"""
        try:
            # 刷新模型选择下拉框
            if hasattr(main_gui, 'refresh_model_options'):
                main_gui.refresh_model_options()

            # 刷新可视化模型选择
            if hasattr(main_gui, 'refresh_visualization_options'):
                main_gui.refresh_visualization_options()

            # 更新数据路径（如果会话中有数据文件信息）
            if hasattr(main_gui, 'current_data_path'):
                try:
                    from session_utils import get_session_summary
                    summary = get_session_summary(session_info['session_id'])
                    if summary and summary.get('data_files'):
                        last_data_file = summary['data_files'][-1]['data_path']
                        main_gui.current_data_path.set(last_data_file)
                except:
                    pass

            # 记录恢复信息
            if hasattr(main_gui, 'log_message'):
                main_gui.log_message(f"已恢复会话: {session_info['session_name']}")
                main_gui.log_message(f"模型数量: {session_info['model_count']}")
                main_gui.log_message("可以在结果可视化选项卡中查看模型性能和图表")

        except Exception as e:
            print(f"基本GUI刷新失败: {e}")
    
    def export_selected_session(self):
        """导出选中会话的报告"""
        selection = self.sessions_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个会话")
            return
        
        item = selection[0]
        index = self.sessions_tree.index(item)
        
        if index < len(self.sessions_data):
            session_info = self.sessions_data[index]
            session_id = session_info['session_id']
            
            # 选择保存路径
            file_path = filedialog.asksaveasfilename(
                title="导出会话报告",
                defaultextension=".html",
                filetypes=[("HTML files", "*.html"), ("All files", "*.*")],
                initialname=f"session_report_{session_info['session_name']}.html"
            )
            
            if file_path:
                try:
                    report_path = export_session_report(session_id, file_path)
                    if report_path:
                        messagebox.showinfo("成功", f"报告已导出到: {report_path}")
                    else:
                        messagebox.showerror("错误", "导出报告失败")
                except Exception as e:
                    messagebox.showerror("错误", f"导出报告失败: {e}")
    
    def delete_selected_session(self):
        """删除选中的会话"""
        selection = self.sessions_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个会话")
            return
        
        item = selection[0]
        index = self.sessions_tree.index(item)
        
        if index < len(self.sessions_data):
            session_info = self.sessions_data[index]
            session_id = session_info['session_id']
            
            # 确认对话框
            if not messagebox.askyesno("确认删除", f"确定要删除会话 '{session_info['session_name']}' 吗?\n此操作不可撤销！"):
                return
            
            try:
                if delete_session(session_id, confirm=True):
                    messagebox.showinfo("成功", "会话已删除")
                    self.refresh_sessions()
                    self.update_status_bar()
                else:
                    messagebox.showerror("错误", "删除会话失败")
            except Exception as e:
                messagebox.showerror("错误", f"删除会话失败: {e}")
    
    def show_statistics(self):
        """显示统计信息"""
        try:
            stats = get_session_statistics()
            
            stats_text = f"""会话统计信息:

总会话数: {stats.get('total_sessions', 0)}
当前活动会话: {stats.get('active_session', '无')}
最新会话: {stats.get('latest_session', '无')}
最旧会话: {stats.get('oldest_session', '无')}

总模型数: {stats.get('total_models', 0)}
总图片数: {stats.get('total_plots', 0)}

按状态分组:"""
            
            for status, count in stats.get('sessions_by_status', {}).items():
                stats_text += f"\n- {status}: {count} 个"
            
            messagebox.showinfo("统计信息", stats_text)
            
        except Exception as e:
            messagebox.showerror("错误", f"获取统计信息失败: {e}")
    
    def update_status_bar(self):
        """更新状态栏"""
        try:
            active_session_id = get_active_session_id()
            if active_session_id:
                self.active_session_label.config(text=f"当前会话: {active_session_id[:12]}...")
            else:
                self.active_session_label.config(text="无活动会话")
        except:
            self.active_session_label.config(text="无活动会话")
