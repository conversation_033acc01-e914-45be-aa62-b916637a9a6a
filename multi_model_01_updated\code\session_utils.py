#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练会话工具函数
提供会话管理的辅助功能和便捷接口
"""

import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import logging

# 尝试导入相关模块
try:
    from training_session_manager import get_session_manager, get_current_session
    from session_loader import get_session_loader
    from logger import get_default_logger
    from config import SESSIONS_PATH, CACHE_PATH, OUTPUT_PATH
except ImportError:
    # 如果导入失败，使用默认配置
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    SESSIONS_PATH = PROJECT_ROOT / 'training_sessions'
    CACHE_PATH = PROJECT_ROOT / 'cache'
    OUTPUT_PATH = PROJECT_ROOT / 'output'
    
    def get_default_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            logger.addHandler(handler)
        return logger

logger = get_default_logger("session_utils")


def create_new_session(session_name: str = None, description: str = "", 
                      auto_activate: bool = True) -> Optional[str]:
    """
    创建新的训练会话的便捷函数
    
    Args:
        session_name: 会话名称
        description: 会话描述
        auto_activate: 是否自动激活为当前会话
        
    Returns:
        str: 会话ID，如果创建失败则返回None
    """
    try:
        session_manager = get_session_manager()
        session = session_manager.create_session(session_name, description)
        
        if auto_activate:
            session_manager.current_session = session
        
        logger.info(f"创建新会话: {session.session_name} (ID: {session.session_id})")
        return session.session_id
        
    except Exception as e:
        logger.error(f"创建会话失败: {e}")
        return None


def activate_session(session_id: str) -> bool:
    """
    激活指定的训练会话
    
    Args:
        session_id: 会话ID
        
    Returns:
        bool: 激活是否成功
    """
    try:
        session_manager = get_session_manager()
        return session_manager.set_current_session(session_id)
    except Exception as e:
        logger.error(f"激活会话失败: {e}")
        return False


def get_active_session_id() -> Optional[str]:
    """
    获取当前活动会话的ID
    
    Returns:
        str: 当前会话ID，如果没有活动会话则返回None
    """
    current_session = get_current_session()
    return current_session.session_id if current_session else None


def save_to_session(data: Any, data_type: str, name: str, **kwargs) -> Optional[str]:
    """
    保存数据到当前会话的便捷函数
    
    Args:
        data: 要保存的数据
        data_type: 数据类型 ('model', 'plot', 'cache', 'config')
        name: 数据名称
        **kwargs: 额外参数
        
    Returns:
        str: 保存的文件路径，如果失败则返回None
    """
    current_session = get_current_session()
    if not current_session:
        logger.warning("没有活动的会话，无法保存数据")
        return None
    
    try:
        if data_type == 'model':
            model_type = kwargs.get('model_type', 'single')
            additional_data = kwargs.get('additional_data')
            return current_session.save_model(data, name, model_type, additional_data)
            
        elif data_type == 'plot':
            plot_type = kwargs.get('plot_type', 'other')
            model_name = kwargs.get('model_name')
            return current_session.save_plot(data, name, plot_type, model_name)
            
        elif data_type == 'cache':
            return current_session.save_cache(data, name)
            
        elif data_type == 'config':
            return current_session.save_config(data, name)
            
        else:
            logger.error(f"不支持的数据类型: {data_type}")
            return None
            
    except Exception as e:
        logger.error(f"保存数据到会话失败: {e}")
        return None


def load_from_session(session_id: str, data_type: str = 'all') -> Dict[str, Any]:
    """
    从指定会话加载数据的便捷函数
    
    Args:
        session_id: 会话ID
        data_type: 要加载的数据类型 ('models', 'cache', 'configs', 'all')
        
    Returns:
        Dict[str, Any]: 加载的数据
    """
    try:
        session_manager = get_session_manager()
        session = session_manager.load_session(session_id)
        
        if not session:
            logger.error(f"无法加载会话: {session_id}")
            return {}
        
        session_loader = get_session_loader()
        result = {}
        
        if data_type in ['models', 'all']:
            result['models'] = session_loader.load_session_models(session)
            
        if data_type in ['cache', 'all']:
            result['cache'] = session_loader.load_session_cache(session)
            
        if data_type in ['configs', 'all']:
            result['configs'] = session_loader.load_session_configs(session)
            
        if data_type in ['plots', 'all']:
            result['plots'] = session_loader.get_session_plots(session)
        
        return result
        
    except Exception as e:
        logger.error(f"从会话加载数据失败: {e}")
        return {}


def restore_session_to_cache(session_id: str) -> bool:
    """
    将指定会话的数据恢复到项目缓存目录
    
    Args:
        session_id: 会话ID
        
    Returns:
        bool: 恢复是否成功
    """
    try:
        session_manager = get_session_manager()
        session = session_manager.load_session(session_id)
        
        if not session:
            logger.error(f"无法加载会话: {session_id}")
            return False
        
        session_loader = get_session_loader()
        return session_loader.restore_session_to_cache(session)
        
    except Exception as e:
        logger.error(f"恢复会话到缓存失败: {e}")
        return False


def list_all_sessions() -> List[Dict[str, Any]]:
    """
    列出所有可用的训练会话
    
    Returns:
        List[Dict[str, Any]]: 会话信息列表
    """
    try:
        session_manager = get_session_manager()
        return session_manager.list_sessions()
    except Exception as e:
        logger.error(f"列出会话失败: {e}")
        return []


def delete_session(session_id: str, confirm: bool = False) -> bool:
    """
    删除指定的训练会话
    
    Args:
        session_id: 会话ID
        confirm: 是否确认删除（安全检查）
        
    Returns:
        bool: 删除是否成功
    """
    if not confirm:
        logger.warning("删除会话需要确认，请设置 confirm=True")
        return False
    
    try:
        session_manager = get_session_manager()
        return session_manager.delete_session(session_id)
    except Exception as e:
        logger.error(f"删除会话失败: {e}")
        return False


def get_session_summary(session_id: str) -> Optional[Dict[str, Any]]:
    """
    获取会话的摘要信息
    
    Args:
        session_id: 会话ID
        
    Returns:
        Dict[str, Any]: 会话摘要信息
    """
    try:
        session_manager = get_session_manager()
        session = session_manager.load_session(session_id)
        
        if not session:
            return None
        
        session_loader = get_session_loader()
        return session_loader.get_session_summary(session)
        
    except Exception as e:
        logger.error(f"获取会话摘要失败: {e}")
        return None


def export_session_report(session_id: str, output_path: str = None) -> Optional[str]:
    """
    导出会话的详细报告
    
    Args:
        session_id: 会话ID
        output_path: 输出路径
        
    Returns:
        str: 报告文件路径
    """
    try:
        session_manager = get_session_manager()
        session = session_manager.load_session(session_id)
        
        if not session:
            return None
        
        session_loader = get_session_loader()
        output_path = Path(output_path) if output_path else None
        return session_loader.export_session_report(session, output_path)
        
    except Exception as e:
        logger.error(f"导出会话报告失败: {e}")
        return None


def migrate_legacy_files() -> bool:
    """
    迁移旧的文件结构到新的会话管理系统
    
    Returns:
        bool: 迁移是否成功
    """
    try:
        logger.info("开始迁移旧文件结构...")
        
        # 创建迁移会话
        migration_session_id = create_new_session(
            session_name="迁移会话_" + datetime.now().strftime("%Y%m%d_%H%M%S"),
            description="从旧文件结构迁移的数据",
            auto_activate=False
        )
        
        if not migration_session_id:
            logger.error("创建迁移会话失败")
            return False
        
        session_manager = get_session_manager()
        migration_session = session_manager.load_session(migration_session_id)
        
        # 迁移缓存文件
        if CACHE_PATH.exists():
            logger.info("迁移缓存文件...")
            for cache_file in CACHE_PATH.glob('*.joblib'):
                if cache_file.name.endswith('_results.joblib'):
                    # 模型结果文件
                    model_name = cache_file.name.replace('_results.joblib', '')
                    try:
                        from joblib import load
                        model_data = load(cache_file)
                        migration_session.save_model(
                            model_data, model_name, 'single',
                            additional_data={'migrated_from': str(cache_file)}
                        )
                        logger.info(f"迁移模型: {model_name}")
                    except Exception as e:
                        logger.warning(f"迁移模型 {model_name} 失败: {e}")
                else:
                    # 其他缓存文件
                    cache_name = cache_file.stem
                    try:
                        from joblib import load
                        cache_data = load(cache_file)
                        migration_session.save_cache(cache_data, cache_name)
                        logger.info(f"迁移缓存: {cache_name}")
                    except Exception as e:
                        logger.warning(f"迁移缓存 {cache_name} 失败: {e}")
        
        # 迁移输出图片
        if OUTPUT_PATH.exists():
            logger.info("迁移输出图片...")
            for model_dir in OUTPUT_PATH.iterdir():
                if model_dir.is_dir():
                    model_name = model_dir.name
                    for plot_file in model_dir.glob('*.png'):
                        try:
                            # 复制图片到会话目录
                            target_path = migration_session.get_path('plots', 'single_model') / plot_file.name
                            target_path.parent.mkdir(parents=True, exist_ok=True)
                            shutil.copy2(plot_file, target_path)
                            
                            # 更新元数据
                            plot_info = {
                                'plot_name': plot_file.stem,
                                'plot_type': 'single_model',
                                'model_name': model_name,
                                'filename': plot_file.name,
                                'filepath': str(target_path),
                                'save_time': datetime.now().isoformat(),
                                'migrated_from': str(plot_file)
                            }
                            migration_session.metadata['plots'].append(plot_info)
                            logger.info(f"迁移图片: {plot_file.name}")
                        except Exception as e:
                            logger.warning(f"迁移图片 {plot_file.name} 失败: {e}")
        
        # 保存迁移会话的元数据
        migration_session._save_metadata()
        migration_session.update_status('migration_completed')
        
        logger.info(f"迁移完成，创建迁移会话: {migration_session_id}")
        return True
        
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        return False


def cleanup_old_files(confirm: bool = False) -> bool:
    """
    清理旧的文件结构（在迁移完成后使用）
    
    Args:
        confirm: 是否确认清理
        
    Returns:
        bool: 清理是否成功
    """
    if not confirm:
        logger.warning("清理旧文件需要确认，请设置 confirm=True")
        return False
    
    try:
        logger.info("开始清理旧文件结构...")
        
        # 备份重要文件
        backup_dir = PROJECT_ROOT / 'backup_before_cleanup'
        backup_dir.mkdir(exist_ok=True)
        
        # 清理缓存目录（保留重要文件）
        if CACHE_PATH.exists():
            important_files = ['last_used_data_file.txt', 'best_model_selection.json']
            for file in CACHE_PATH.iterdir():
                if file.name not in important_files:
                    if file.is_file():
                        file.unlink()
                    elif file.is_dir():
                        shutil.rmtree(file)
        
        # 清理输出目录
        if OUTPUT_PATH.exists():
            for item in OUTPUT_PATH.iterdir():
                if item.is_dir():
                    shutil.rmtree(item)
                else:
                    item.unlink()
        
        logger.info("旧文件结构清理完成")
        return True
        
    except Exception as e:
        logger.error(f"清理失败: {e}")
        return False


def get_session_statistics() -> Dict[str, Any]:
    """
    获取所有会话的统计信息
    
    Returns:
        Dict[str, Any]: 统计信息
    """
    try:
        sessions = list_all_sessions()
        
        stats = {
            'total_sessions': len(sessions),
            'active_session': get_active_session_id(),
            'sessions_by_status': {},
            'total_models': 0,
            'total_plots': 0,
            'latest_session': None,
            'oldest_session': None
        }
        
        if sessions:
            # 按状态分组
            for session in sessions:
                status = session.get('status', 'unknown')
                if status not in stats['sessions_by_status']:
                    stats['sessions_by_status'][status] = 0
                stats['sessions_by_status'][status] += 1
                
                # 累计统计
                stats['total_models'] += session.get('model_count', 0)
                stats['total_plots'] += session.get('plot_count', 0)
            
            # 最新和最旧的会话
            sessions_sorted = sorted(sessions, key=lambda x: x['created_time'])
            stats['oldest_session'] = sessions_sorted[0]['session_id']
            stats['latest_session'] = sessions_sorted[-1]['session_id']
        
        return stats
        
    except Exception as e:
        logger.error(f"获取会话统计失败: {e}")
        return {}


# 便捷的装饰器函数
def with_session(session_name: str = None, description: str = ""):
    """
    装饰器：为函数执行创建临时会话
    
    Args:
        session_name: 会话名称
        description: 会话描述
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 保存当前会话
            original_session = get_current_session()
            
            # 创建临时会话
            temp_session_id = create_new_session(session_name, description, auto_activate=True)
            
            try:
                # 执行函数
                result = func(*args, **kwargs)
                
                # 更新会话状态
                current_session = get_current_session()
                if current_session:
                    current_session.update_status('completed')
                
                return result
                
            finally:
                # 恢复原始会话
                if original_session:
                    session_manager = get_session_manager()
                    session_manager.current_session = original_session
        
        return wrapper
    return decorator
