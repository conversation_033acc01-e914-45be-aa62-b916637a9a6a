#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的特征选择器
移除冗余策略，专注于最有效的特征选择方法
"""

import numpy as np
import pandas as pd
from sklearn.feature_selection import (
    SelectKBest, f_classif, mutual_info_classif, 
    RFE, SelectFromModel, VarianceThreshold
)
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import accuracy_score
from typing import Tuple, List, Optional, Union, Dict, Any
import warnings

# 尝试导入logger
try:
    from logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

# 导入异常处理
try:
    from exception_handling import safe_execute, DataProcessingError
except ImportError:
    # 如果没有异常处理模块，提供简单的装饰器
    def safe_execute(**kwargs):
        def decorator(func):
            return func
        return decorator
    
    class DataProcessingError(Exception):
        pass


class SimplifiedFeatureSelector:
    """
    简化的特征选择器
    专注于最有效的特征选择方法，移除复杂和低效的策略
    """
    
    def __init__(
        self,
        method: str = 'auto',
        k: Optional[int] = None,
        variance_threshold: float = 0.01,
        cross_validation: bool = True,
        cv_folds: int = 5,
        random_state: int = 42
    ):
        """
        初始化特征选择器
        
        Args:
            method: 特征选择方法 ('auto', 'statistical', 'model_based', 'rfe', 'variance')
            k: 要选择的特征数量，None表示自动确定
            variance_threshold: 方差阈值
            cross_validation: 是否使用交叉验证评估特征选择效果
            cv_folds: 交叉验证折数
            random_state: 随机种子
        """
        self.method = method
        self.k = k
        self.variance_threshold = variance_threshold
        self.cross_validation = cross_validation
        self.cv_folds = cv_folds
        self.random_state = random_state
        
        self.selector = None
        self.selected_features = None
        self.feature_scores = None
        self.selection_report = {}
        
    def _determine_optimal_k(self, n_features: int, n_samples: int) -> int:
        """
        自动确定最优的特征数量
        
        Args:
            n_features: 总特征数
            n_samples: 样本数
            
        Returns:
            最优特征数量
        """
        # 基于经验规则确定特征数量
        if n_samples < 100:
            # 小样本：选择较少特征
            optimal_k = min(max(int(n_features * 0.3), 5), 15)
        elif n_samples < 1000:
            # 中等样本：选择中等数量特征
            optimal_k = min(max(int(n_features * 0.5), 10), 50)
        else:
            # 大样本：可以选择更多特征
            optimal_k = min(max(int(n_features * 0.7), 20), 100)
        
        # 确保不超过总特征数
        optimal_k = min(optimal_k, n_features)
        
        logger.info(f"自动确定最优特征数量: {optimal_k} (总特征数: {n_features}, 样本数: {n_samples})")
        return optimal_k
    
    def _remove_low_variance_features(
        self, 
        X: Union[pd.DataFrame, np.ndarray]
    ) -> Tuple[Union[pd.DataFrame, np.ndarray], List[str]]:
        """
        移除低方差特征
        
        Args:
            X: 特征数据
            
        Returns:
            tuple: (过滤后的数据, 保留的特征名称)
        """
        if self.variance_threshold <= 0:
            if hasattr(X, 'columns'):
                return X, X.columns.tolist()
            else:
                return X, [f'feature_{i}' for i in range(X.shape[1])]
        
        variance_selector = VarianceThreshold(threshold=self.variance_threshold)
        X_filtered = variance_selector.fit_transform(X)
        
        if hasattr(X, 'columns'):
            feature_names = X.columns[variance_selector.get_support()].tolist()
            X_filtered = pd.DataFrame(X_filtered, columns=feature_names, index=X.index)
        else:
            selected_indices = np.where(variance_selector.get_support())[0]
            feature_names = [f'feature_{i}' for i in selected_indices]
        
        removed_count = X.shape[1] - X_filtered.shape[1]
        if removed_count > 0:
            logger.info(f"移除了 {removed_count} 个低方差特征")
        
        return X_filtered, feature_names
    
    @safe_execute(context="统计特征选择", raise_on_failure=False, return_on_failure=(None, None, None))
    def _statistical_selection(
        self, 
        X: Union[pd.DataFrame, np.ndarray], 
        y: np.ndarray, 
        k: int
    ) -> Tuple[Union[pd.DataFrame, np.ndarray], List[str], np.ndarray]:
        """
        基于统计测试的特征选择
        
        Args:
            X: 特征数据
            y: 目标变量
            k: 要选择的特征数量
            
        Returns:
            tuple: (选择后的数据, 特征名称, 特征得分)
        """
        # 使用F测试进行特征选择
        selector = SelectKBest(score_func=f_classif, k=k)
        X_selected = selector.fit_transform(X, y)
        
        scores = selector.scores_
        
        if hasattr(X, 'columns'):
            selected_features = X.columns[selector.get_support()].tolist()
            X_selected = pd.DataFrame(X_selected, columns=selected_features, index=X.index)
        else:
            selected_indices = np.where(selector.get_support())[0]
            selected_features = [f'feature_{i}' for i in selected_indices]
        
        self.selector = selector
        logger.info(f"统计特征选择完成，选择了 {len(selected_features)} 个特征")
        
        return X_selected, selected_features, scores
    
    @safe_execute(context="模型特征选择", raise_on_failure=False, return_on_failure=(None, None, None))
    def _model_based_selection(
        self, 
        X: Union[pd.DataFrame, np.ndarray], 
        y: np.ndarray, 
        k: int
    ) -> Tuple[Union[pd.DataFrame, np.ndarray], List[str], np.ndarray]:
        """
        基于模型重要性的特征选择
        
        Args:
            X: 特征数据
            y: 目标变量
            k: 要选择的特征数量
            
        Returns:
            tuple: (选择后的数据, 特征名称, 特征重要性)
        """
        # 使用随机森林计算特征重要性
        rf = RandomForestClassifier(
            n_estimators=100,
            random_state=self.random_state,
            n_jobs=-1
        )
        rf.fit(X, y)
        
        importances = rf.feature_importances_
        
        # 选择top-k特征
        selected_indices = np.argsort(importances)[-k:]
        
        if hasattr(X, 'columns'):
            selected_features = X.columns[selected_indices].tolist()
            X_selected = X.iloc[:, selected_indices]
        else:
            selected_features = [f'feature_{i}' for i in selected_indices]
            X_selected = X[:, selected_indices]
        
        logger.info(f"模型特征选择完成，选择了 {len(selected_features)} 个特征")
        
        return X_selected, selected_features, importances
    
    @safe_execute(context="递归特征消除", raise_on_failure=False, return_on_failure=(None, None, None))
    def _rfe_selection(
        self, 
        X: Union[pd.DataFrame, np.ndarray], 
        y: np.ndarray, 
        k: int
    ) -> Tuple[Union[pd.DataFrame, np.ndarray], List[str], np.ndarray]:
        """
        递归特征消除
        
        Args:
            X: 特征数据
            y: 目标变量
            k: 要选择的特征数量
            
        Returns:
            tuple: (选择后的数据, 特征名称, 特征排名)
        """
        # 使用逻辑回归进行RFE
        estimator = LogisticRegression(
            random_state=self.random_state,
            max_iter=1000,
            n_jobs=-1
        )
        
        selector = RFE(estimator=estimator, n_features_to_select=k)
        X_selected = selector.fit_transform(X, y)
        
        rankings = selector.ranking_
        
        if hasattr(X, 'columns'):
            selected_features = X.columns[selector.get_support()].tolist()
            X_selected = pd.DataFrame(X_selected, columns=selected_features, index=X.index)
        else:
            selected_indices = np.where(selector.get_support())[0]
            selected_features = [f'feature_{i}' for i in selected_indices]
        
        self.selector = selector
        logger.info(f"RFE特征选择完成，选择了 {len(selected_features)} 个特征")
        
        return X_selected, selected_features, rankings
    
    def _evaluate_selection_quality(
        self, 
        X_original: Union[pd.DataFrame, np.ndarray],
        X_selected: Union[pd.DataFrame, np.ndarray],
        y: np.ndarray
    ) -> Dict[str, float]:
        """
        评估特征选择的质量
        
        Args:
            X_original: 原始特征数据
            X_selected: 选择后的特征数据
            y: 目标变量
            
        Returns:
            评估结果字典
        """
        if not self.cross_validation:
            return {}
        
        try:
            # 使用简单的逻辑回归评估
            model = LogisticRegression(random_state=self.random_state, max_iter=1000)
            
            # 原始特征的交叉验证得分
            cv_original = cross_val_score(
                model, X_original, y, 
                cv=StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state),
                scoring='accuracy'
            )
            
            # 选择特征的交叉验证得分
            cv_selected = cross_val_score(
                model, X_selected, y,
                cv=StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state),
                scoring='accuracy'
            )
            
            evaluation = {
                'original_cv_mean': cv_original.mean(),
                'original_cv_std': cv_original.std(),
                'selected_cv_mean': cv_selected.mean(),
                'selected_cv_std': cv_selected.std(),
                'improvement': cv_selected.mean() - cv_original.mean(),
                'feature_reduction_ratio': X_selected.shape[1] / X_original.shape[1]
            }
            
            logger.info(f"特征选择质量评估:")
            logger.info(f"  原始特征CV准确率: {evaluation['original_cv_mean']:.4f} ± {evaluation['original_cv_std']:.4f}")
            logger.info(f"  选择特征CV准确率: {evaluation['selected_cv_mean']:.4f} ± {evaluation['selected_cv_std']:.4f}")
            logger.info(f"  性能提升: {evaluation['improvement']:.4f}")
            logger.info(f"  特征减少比例: {1 - evaluation['feature_reduction_ratio']:.2%}")
            
            return evaluation
            
        except Exception as e:
            logger.warning(f"特征选择质量评估失败: {e}")
            return {}
    
    def fit_transform(
        self, 
        X: Union[pd.DataFrame, np.ndarray], 
        y: np.ndarray,
        feature_names: Optional[List[str]] = None
    ) -> Tuple[Union[pd.DataFrame, np.ndarray], List[str]]:
        """
        拟合特征选择器并转换数据
        
        Args:
            X: 特征数据
            y: 目标变量
            feature_names: 特征名称列表
            
        Returns:
            tuple: (转换后的数据, 选择的特征名称)
        """
        logger.info(f"开始特征选择，方法: {self.method}")
        
        # 如果输入是numpy数组，尝试转换为DataFrame
        if not hasattr(X, 'columns') and feature_names is not None:
            X = pd.DataFrame(X, columns=feature_names)
        
        original_shape = X.shape
        
        # 1. 移除低方差特征
        X_filtered, remaining_features = self._remove_low_variance_features(X)
        
        # 2. 确定要选择的特征数量
        if self.k is None:
            k = self._determine_optimal_k(X_filtered.shape[1], X_filtered.shape[0])
        else:
            k = min(self.k, X_filtered.shape[1])
        
        # 如果过滤后的特征数量已经小于等于k，直接返回
        if X_filtered.shape[1] <= k:
            logger.info(f"过滤后特征数量({X_filtered.shape[1]})已满足要求，无需进一步选择")
            self.selected_features = remaining_features
            self.selection_report = self._evaluate_selection_quality(X, X_filtered, y)
            return X_filtered, remaining_features
        
        # 3. 根据方法选择特征选择策略
        if self.method == 'auto':
            # 自动选择最适合的方法
            if X_filtered.shape[0] > 1000 and X_filtered.shape[1] > 50:
                method = 'model_based'  # 大数据集使用模型方法
            elif X_filtered.shape[1] > 20:
                method = 'statistical'  # 中等特征数使用统计方法
            else:
                method = 'rfe'  # 少量特征使用RFE
            logger.info(f"自动选择特征选择方法: {method}")
        else:
            method = self.method
        
        # 4. 执行特征选择
        X_selected, selected_features, scores = None, None, None
        
        if method == 'statistical':
            X_selected, selected_features, scores = self._statistical_selection(X_filtered, y, k)
        elif method == 'model_based':
            X_selected, selected_features, scores = self._model_based_selection(X_filtered, y, k)
        elif method == 'rfe':
            X_selected, selected_features, scores = self._rfe_selection(X_filtered, y, k)
        elif method == 'variance':
            # 只使用方差过滤
            X_selected, selected_features = X_filtered, remaining_features
            scores = np.var(X_filtered, axis=0) if hasattr(X_filtered, 'values') else np.var(X_filtered, axis=0)
        else:
            raise ValueError(f"未知的特征选择方法: {method}")
        
        # 5. 处理选择失败的情况
        if X_selected is None or selected_features is None:
            logger.warning(f"特征选择方法 {method} 失败，使用方差过滤作为备选")
            X_selected, selected_features = X_filtered, remaining_features
            if len(selected_features) > k:
                # 随机选择k个特征
                selected_indices = np.random.choice(len(selected_features), k, replace=False)
                if hasattr(X_filtered, 'columns'):
                    selected_features = [selected_features[i] for i in selected_indices]
                    X_selected = X_filtered.iloc[:, selected_indices]
                else:
                    selected_features = [selected_features[i] for i in selected_indices]
                    X_selected = X_filtered[:, selected_indices]
        
        # 6. 保存结果
        self.selected_features = selected_features
        self.feature_scores = scores
        
        # 7. 评估特征选择质量
        self.selection_report = self._evaluate_selection_quality(X, X_selected, y)
        
        logger.info(f"特征选择完成: {original_shape[1]} -> {len(selected_features)} 特征")
        
        return X_selected, selected_features
    
    def transform(self, X: Union[pd.DataFrame, np.ndarray]) -> Union[pd.DataFrame, np.ndarray]:
        """
        使用已拟合的选择器转换新数据
        
        Args:
            X: 要转换的数据
            
        Returns:
            转换后的数据
        """
        if self.selected_features is None:
            raise ValueError("特征选择器尚未拟合，请先调用fit_transform")
        
        if hasattr(X, 'columns'):
            # DataFrame
            available_features = [f for f in self.selected_features if f in X.columns]
            if len(available_features) != len(self.selected_features):
                logger.warning(f"部分特征不可用，使用 {len(available_features)}/{len(self.selected_features)} 个特征")
            return X[available_features]
        else:
            # numpy array
            if self.selector is not None:
                return self.selector.transform(X)
            else:
                logger.warning("无法转换numpy数组，返回原始数据")
                return X
    
    def get_feature_importance(self) -> Optional[Dict[str, float]]:
        """
        获取特征重要性
        
        Returns:
            特征重要性字典
        """
        if self.selected_features is None or self.feature_scores is None:
            return None
        
        if len(self.selected_features) == len(self.feature_scores):
            return dict(zip(self.selected_features, self.feature_scores))
        else:
            return None
    
    def get_selection_report(self) -> Dict[str, Any]:
        """
        获取特征选择报告
        
        Returns:
            特征选择报告
        """
        report = {
            'method': self.method,
            'selected_features_count': len(self.selected_features) if self.selected_features else 0,
            'selected_features': self.selected_features,
            'feature_importance': self.get_feature_importance(),
            'evaluation': self.selection_report
        }
        
        return report


def select_features_simple(
    X_train: Union[pd.DataFrame, np.ndarray],
    y_train: np.ndarray,
    X_test: Union[pd.DataFrame, np.ndarray],
    method: str = 'auto',
    k: Optional[int] = None,
    feature_names: Optional[List[str]] = None
) -> Tuple[Union[pd.DataFrame, np.ndarray], Union[pd.DataFrame, np.ndarray], List[str], Dict[str, Any]]:
    """
    简化的特征选择接口
    
    Args:
        X_train: 训练集特征
        y_train: 训练集标签
        X_test: 测试集特征
        method: 特征选择方法
        k: 特征数量
        feature_names: 特征名称
        
    Returns:
        tuple: (X_train_selected, X_test_selected, selected_features, report)
    """
    selector = SimplifiedFeatureSelector(method=method, k=k)
    
    X_train_selected, selected_features = selector.fit_transform(X_train, y_train, feature_names)
    X_test_selected = selector.transform(X_test)
    
    report = selector.get_selection_report()
    
    return X_train_selected, X_test_selected, selected_features, report


# 向后兼容的接口
def feature_selection_for_ensemble(
    X_train: Union[pd.DataFrame, np.ndarray],
    y_train: np.ndarray,
    X_test: Union[pd.DataFrame, np.ndarray],
    base_models: Optional[Dict] = None,
    feature_names: Optional[List[str]] = None,
    method: str = 'auto',
    k: Optional[int] = None
) -> Tuple[Union[pd.DataFrame, np.ndarray], Union[pd.DataFrame, np.ndarray], List[str], Any]:
    """
    向后兼容的集成特征选择接口
    
    Returns:
        tuple: (X_train_selected, X_test_selected, selected_features, selector)
    """
    logger.info("使用简化的特征选择器进行集成特征选择")
    
    # 忽略base_models参数，使用自动方法
    selector = SimplifiedFeatureSelector(method=method, k=k)
    X_train_selected, selected_features = selector.fit_transform(X_train, y_train, feature_names)
    X_test_selected = selector.transform(X_test)
    
    return X_train_selected, X_test_selected, selected_features, selector
