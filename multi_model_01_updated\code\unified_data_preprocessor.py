#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据预处理器
解决数据预处理逻辑不一致的问题，提供标准化的数据处理接口
"""

import pandas as pd
import numpy as np
import re
from pathlib import Path
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.impute import SimpleImputer, KNNImputer
from typing import Tuple, Optional, Dict, Any, Union
import logging

# 尝试导入logger，如果失败则使用标准logger
try:
    from logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class UnifiedDataPreprocessor:
    """
    统一的数据预处理器
    提供标准化的数据加载、清理、变换和分割功能
    """
    
    def __init__(
        self,
        scaling_method: str = 'standard',
        test_size: float = 0.2,
        random_state: int = 42,
        handle_missing: str = 'simple',
        missing_strategy: str = 'median',
        handle_outliers: bool = False,
        outlier_method: str = 'iqr'
    ):
        """
        初始化统一数据预处理器
        
        Args:
            scaling_method: 特征缩放方法 ('standard', 'minmax', 'robust', 'none')
            test_size: 测试集比例
            random_state: 随机种子
            handle_missing: 缺失值处理方法 ('simple', 'knn', 'none')
            missing_strategy: 简单填充策略 ('mean', 'median', 'most_frequent', 'constant')
            handle_outliers: 是否处理异常值
            outlier_method: 异常值检测方法 ('iqr', 'zscore')
        """
        self.scaling_method = scaling_method
        self.test_size = test_size
        self.random_state = random_state
        self.handle_missing = handle_missing
        self.missing_strategy = missing_strategy
        self.handle_outliers = handle_outliers
        self.outlier_method = outlier_method
        
        # 存储拟合的转换器
        self.scaler = None
        self.imputer = None
        self.label_mapping = None
        self.feature_names = None
        
        # 数据质量统计
        self.data_quality_stats = {}
        
    def clean_column_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理DataFrame的列名：去除特殊字符，转为小写
        
        Args:
            df: 输入DataFrame
            
        Returns:
            清理后的DataFrame
        """
        df = df.copy()
        original_cols = df.columns.tolist()
        new_cols = []
        
        for col in original_cols:
            # 去除特殊字符，保留字母、数字和下划线
            new_col = re.sub(r'[^A-Za-z0-9_]+', '_', str(col))
            new_col = new_col.lower().strip('_')
            # 确保列名不为空
            if not new_col:
                new_col = f'col_{len(new_cols)}'
            new_cols.append(new_col)
        
        df.columns = new_cols
        
        if original_cols != new_cols:
            logger.info("列名已标准化")
            logger.debug(f"原列名: {original_cols[:5]}...")
            logger.debug(f"新列名: {new_cols[:5]}...")
            
        return df
    
    def detect_target_column(self, df: pd.DataFrame, target_col_name: Optional[str] = None) -> str:
        """
        检测或验证目标列
        
        Args:
            df: 输入DataFrame
            target_col_name: 指定的目标列名
            
        Returns:
            目标列名
            
        Raises:
            ValueError: 如果找不到目标列
        """
        if target_col_name:
            # 清理目标列名
            target_col_name = re.sub(r'[^A-Za-z0-9_]+', '_', str(target_col_name)).lower().strip('_')
            if target_col_name in df.columns:
                return target_col_name
            else:
                logger.warning(f"指定的目标列 '{target_col_name}' 不存在")
        
        # 尝试自动检测目标列
        possible_targets = ['label', 'target', 'class', 'y', 'outcome', 'result']
        for col in possible_targets:
            if col in df.columns:
                logger.info(f"自动检测到目标列: {col}")
                return col
        
        # 如果都没找到，使用最后一列
        target_col = df.columns[-1]
        logger.info(f"使用最后一列作为目标列: {target_col}")
        return target_col
    
    def ensure_binary_labels(self, y: Union[pd.Series, np.ndarray]) -> Tuple[np.ndarray, Optional[Dict]]:
        """
        将任意二分类标签映射为{0,1}，并返回映射后的y与映射表
        
        Args:
            y: 目标变量
            
        Returns:
            tuple: (映射后的y, 映射字典)
        """
        y_arr = np.asarray(y)
        classes = np.unique(y_arr)
        
        if len(classes) != 2:
            logger.warning(f"检测到 {len(classes)} 个类别，可能不是二分类问题")
            return y_arr, None
        
        # 按排序固定映射：将较小值映射为0，较大值为1
        sorted_classes = np.sort(classes)
        mapping = {sorted_classes[0]: 0, sorted_classes[1]: 1}
        y_mapped = np.vectorize(lambda v: mapping.get(v, v))(y_arr)
        
        self.label_mapping = mapping
        logger.info(f"标签映射: {mapping}")
        
        return y_mapped, mapping
    
    def analyze_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析数据质量
        
        Args:
            df: 输入DataFrame
            
        Returns:
            数据质量统计字典
        """
        stats = {
            'shape': df.shape,
            'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024**2,
            'missing_values': {
                'total': df.isnull().sum().sum(),
                'by_column': df.isnull().sum().to_dict(),
                'percentage': (df.isnull().sum() / len(df) * 100).to_dict()
            },
            'duplicates': {
                'count': df.duplicated().sum(),
                'percentage': (df.duplicated().sum() / len(df)) * 100
            },
            'data_types': df.dtypes.to_dict(),
            'numeric_columns': df.select_dtypes(include=[np.number]).columns.tolist(),
            'categorical_columns': df.select_dtypes(include=['object', 'category']).columns.tolist()
        }
        
        # 检查异常值（仅对数值列）
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        outliers_info = {}
        
        for col in numeric_cols:
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)][col]
            outliers_info[col] = {
                'count': len(outliers),
                'percentage': (len(outliers) / len(df)) * 100,
                'bounds': {'lower': lower_bound, 'upper': upper_bound}
            }
        
        stats['outliers'] = outliers_info
        self.data_quality_stats = stats
        
        return stats
    
    def handle_missing_values(self, X_train: pd.DataFrame, X_test: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        处理缺失值
        
        Args:
            X_train: 训练集特征
            X_test: 测试集特征
            
        Returns:
            tuple: (处理后的训练集, 处理后的测试集)
        """
        if self.handle_missing == 'none':
            return X_train, X_test
        
        # 检查是否有缺失值
        if X_train.isnull().sum().sum() == 0 and X_test.isnull().sum().sum() == 0:
            logger.info("数据中无缺失值，跳过缺失值处理")
            return X_train, X_test
        
        if self.handle_missing == 'simple':
            # 区分数值列和分类列
            numeric_cols = X_train.select_dtypes(include=[np.number]).columns
            categorical_cols = X_train.select_dtypes(include=['object', 'category']).columns
            
            X_train_processed = X_train.copy()
            X_test_processed = X_test.copy()
            
            # 处理数值列
            if len(numeric_cols) > 0:
                numeric_strategy = self.missing_strategy if self.missing_strategy in ['mean', 'median'] else 'median'
                numeric_imputer = SimpleImputer(strategy=numeric_strategy)
                X_train_processed[numeric_cols] = numeric_imputer.fit_transform(X_train[numeric_cols])
                X_test_processed[numeric_cols] = numeric_imputer.transform(X_test[numeric_cols])
                logger.info(f"数值列缺失值处理策略: {numeric_strategy}")
            
            # 处理分类列
            if len(categorical_cols) > 0:
                categorical_imputer = SimpleImputer(strategy='most_frequent')
                X_train_processed[categorical_cols] = categorical_imputer.fit_transform(X_train[categorical_cols])
                X_test_processed[categorical_cols] = categorical_imputer.transform(X_test[categorical_cols])
                logger.info("分类列缺失值处理策略: most_frequent")
            
            return X_train_processed, X_test_processed
            
        elif self.handle_missing == 'knn':
            self.imputer = KNNImputer(n_neighbors=5)
            X_train_imputed = self.imputer.fit_transform(X_train)
            X_test_imputed = self.imputer.transform(X_test)
            
            # 转换回DataFrame
            X_train_processed = pd.DataFrame(X_train_imputed, columns=X_train.columns, index=X_train.index)
            X_test_processed = pd.DataFrame(X_test_imputed, columns=X_test.columns, index=X_test.index)
            
            logger.info("使用KNN填充缺失值")
            return X_train_processed, X_test_processed
        
        return X_train, X_test
    
    def handle_outliers_data(self, X_train: pd.DataFrame, X_test: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        处理异常值
        
        Args:
            X_train: 训练集特征
            X_test: 测试集特征
            
        Returns:
            tuple: (处理后的训练集, 处理后的测试集)
        """
        if not self.handle_outliers:
            return X_train, X_test
        
        X_train_processed = X_train.copy()
        X_test_processed = X_test.copy()
        
        numeric_cols = X_train.select_dtypes(include=[np.number]).columns
        outlier_count = 0
        
        for col in numeric_cols:
            if self.outlier_method == 'iqr':
                Q1 = X_train[col].quantile(0.25)
                Q3 = X_train[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                # 裁剪异常值到边界值
                X_train_processed[col] = X_train[col].clip(lower_bound, upper_bound)
                X_test_processed[col] = X_test[col].clip(lower_bound, upper_bound)
                
                outlier_count += ((X_train[col] < lower_bound) | (X_train[col] > upper_bound)).sum()
        
        if outlier_count > 0:
            logger.info(f"使用{self.outlier_method}方法处理了{outlier_count}个异常值")
        
        return X_train_processed, X_test_processed
    
    def apply_scaling(self, X_train: pd.DataFrame, X_test: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        应用特征缩放
        
        Args:
            X_train: 训练集特征
            X_test: 测试集特征
            
        Returns:
            tuple: (缩放后的训练集, 缩放后的测试集)
        """
        if self.scaling_method == 'none':
            return X_train, X_test
        
        # 选择缩放器
        if self.scaling_method == 'standard':
            self.scaler = StandardScaler()
        elif self.scaling_method == 'minmax':
            self.scaler = MinMaxScaler()
        elif self.scaling_method == 'robust':
            self.scaler = RobustScaler()
        else:
            logger.warning(f"未知的缩放方法: {self.scaling_method}，使用标准化")
            self.scaler = StandardScaler()
        
        # 拟合和转换
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 转换回DataFrame
        X_train_processed = pd.DataFrame(X_train_scaled, columns=X_train.columns, index=X_train.index)
        X_test_processed = pd.DataFrame(X_test_scaled, columns=X_test.columns, index=X_test.index)
        
        logger.info(f"应用了 {self.scaling_method} 特征缩放")
        return X_train_processed, X_test_processed
    
    def split_data_safely(self, X: pd.DataFrame, y: np.ndarray) -> Tuple[pd.DataFrame, pd.DataFrame, np.ndarray, np.ndarray]:
        """
        安全地分割数据，确保每个类别都有足够的样本
        
        Args:
            X: 特征数据
            y: 目标变量
            
        Returns:
            tuple: (X_train, X_test, y_train, y_test)
        """
        # 检查类别分布
        unique_classes, class_counts = np.unique(y, return_counts=True)
        min_class_count = np.min(class_counts)
        min_test_per_class = 2
        min_total_test = len(unique_classes) * min_test_per_class
        
        # 动态调整测试集比例
        test_size = self.test_size
        if len(y) * test_size < min_total_test:
            adjusted_test_size = min(max(min_total_test / len(y), 0.1), 0.5)
            logger.info(f"数据集较小，调整测试集比例从 {test_size:.2f} 到 {adjusted_test_size:.2f}")
            test_size = adjusted_test_size
        
        # 选择分割策略
        if min_class_count < 2:
            logger.warning(f"某些类别样本数量不足（最少类别样本数：{min_class_count}），不使用分层抽样")
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=self.random_state
            )
        else:
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=self.random_state, stratify=y
            )
            logger.info("使用分层抽样分割数据")
        
        logger.info(f"数据分割完成: 训练集 {X_train.shape}, 测试集 {X_test.shape}")
        return X_train, X_test, y_train, y_test
    
    def load_and_preprocess(
        self, 
        data_path: Union[str, Path], 
        target_col_name: Optional[str] = None,
        return_quality_stats: bool = False
    ) -> Union[Tuple[pd.DataFrame, pd.DataFrame, np.ndarray, np.ndarray], 
               Tuple[pd.DataFrame, pd.DataFrame, np.ndarray, np.ndarray, Dict[str, Any]]]:
        """
        统一的数据加载和预处理接口
        
        Args:
            data_path: 数据文件路径
            target_col_name: 目标列名（可选）
            return_quality_stats: 是否返回数据质量统计
            
        Returns:
            tuple: (X_train, X_test, y_train, y_test) 或包含质量统计的元组
        """
        logger.info(f"开始加载和预处理数据: {data_path}")
        
        # 1. 加载数据
        try:
            df = pd.read_csv(data_path, encoding='utf-8')
        except UnicodeDecodeError:
            df = pd.read_csv(data_path, encoding='gbk')
            logger.info("使用GBK编码加载数据")
        
        logger.info(f"原始数据形状: {df.shape}")
        
        # 2. 清理列名
        df = self.clean_column_names(df)
        
        # 3. 分析数据质量
        quality_stats = self.analyze_data_quality(df)
        
        # 4. 检测目标列
        target_col = self.detect_target_column(df, target_col_name)
        
        # 5. 分离特征和目标
        X = df.drop(columns=[target_col])
        y = df[target_col]
        
        # 6. 确保二分类标签
        y, label_mapping = self.ensure_binary_labels(y)
        
        # 7. 保存特征名称
        self.feature_names = X.columns.tolist()
        
        # 8. 分割数据
        X_train, X_test, y_train, y_test = self.split_data_safely(X, y)
        
        # 9. 处理缺失值
        X_train, X_test = self.handle_missing_values(X_train, X_test)
        
        # 10. 处理异常值
        X_train, X_test = self.handle_outliers_data(X_train, X_test)
        
        # 11. 应用特征缩放
        X_train, X_test = self.apply_scaling(X_train, X_test)
        
        logger.info("数据预处理完成")
        
        if return_quality_stats:
            return X_train, X_test, y_train, y_test, quality_stats
        else:
            return X_train, X_test, y_train, y_test
    
    def get_preprocessing_config(self) -> Dict[str, Any]:
        """
        获取预处理配置信息
        
        Returns:
            配置字典
        """
        return {
            'scaling_method': self.scaling_method,
            'test_size': self.test_size,
            'random_state': self.random_state,
            'handle_missing': self.handle_missing,
            'missing_strategy': self.missing_strategy,
            'handle_outliers': self.handle_outliers,
            'outlier_method': self.outlier_method,
            'label_mapping': self.label_mapping,
            'feature_names': self.feature_names
        }
    
    def transform_new_data(self, X_new: pd.DataFrame) -> pd.DataFrame:
        """
        使用已拟合的预处理器转换新数据
        
        Args:
            X_new: 新的特征数据
            
        Returns:
            转换后的数据
        """
        if self.scaler is None:
            raise ValueError("预处理器尚未拟合，请先调用 load_and_preprocess 方法")
        
        X_processed = X_new.copy()
        
        # 应用缺失值处理
        if self.imputer is not None:
            X_processed = pd.DataFrame(
                self.imputer.transform(X_processed),
                columns=X_processed.columns,
                index=X_processed.index
            )
        
        # 应用特征缩放
        if self.scaler is not None:
            X_processed = pd.DataFrame(
                self.scaler.transform(X_processed),
                columns=X_processed.columns,
                index=X_processed.index
            )
        
        return X_processed


# 创建全局预处理器实例（保持向后兼容）
default_preprocessor = UnifiedDataPreprocessor()

def load_and_preprocess_data(data_path, model_name=None, test_size=0.2, random_state=42, scaling_method='standard'):
    """
    向后兼容的数据预处理接口
    
    Args:
        data_path: 数据文件路径
        model_name: 模型名称（用于日志）
        test_size: 测试集比例
        random_state: 随机种子
        scaling_method: 特征缩放方法
        
    Returns:
        X_train, X_test, y_train, y_test: 分割后的训练集和测试集
    """
    preprocessor = UnifiedDataPreprocessor(
        scaling_method=scaling_method,
        test_size=test_size,
        random_state=random_state
    )
    
    X_train, X_test, y_train, y_test = preprocessor.load_and_preprocess(data_path)
    
    if model_name:
        logger.info(f"为模型 {model_name} 完成数据预处理")
    
    return X_train, X_test, y_train, y_test

def get_current_scaler():
    """
    获取当前使用的scaler（向后兼容接口）
    """
    if hasattr(default_preprocessor, 'scaler'):
        return default_preprocessor.scaler
    return None
