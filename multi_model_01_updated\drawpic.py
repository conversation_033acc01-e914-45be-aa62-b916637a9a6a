# -*- coding: utf-8 -*-
"""
相关系数矩阵可视化脚本
生成两种风格的相关系数矩阵图：
1. 标准正方形标记图
2. 根据相关系数大小使用不同标记的图（圆形/正方形）
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import scipy.stats as stats
from matplotlib.colors import LinearSegmentedColormap, Normalize
import warnings
import os
from pathlib import Path

# 设置matplotlib参数
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['axes.unicode_minus'] = False

# 忽略所有警告
warnings.filterwarnings("ignore")

# 移除了硬编码的默认数据路径，推荐使用GUI界面

def load_data_file(file_path):
    """
    加载数据文件，支持CSV和Excel格式
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        pd.DataFrame: 加载的数据
        
    Raises:
        ValueError: 不支持的文件格式
        FileNotFoundError: 文件不存在
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    file_extension = Path(file_path).suffix.lower()
    
    try:
        if file_extension == '.csv':
            # 尝试不同的编码格式读取CSV
            encodings = ['utf-8', 'gbk', 'utf-8-sig', 'latin1']
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    print(f"成功使用 {encoding} 编码读取CSV文件")
                    return df
                except UnicodeDecodeError:
                    continue
            raise ValueError("无法使用常见编码格式读取CSV文件")
            
        elif file_extension in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
            print("成功读取Excel文件")
            return df
            
        else:
            raise ValueError(f"不支持的文件格式: {file_extension}。支持的格式: .csv, .xlsx, .xls")
            
    except Exception as e:
        raise ValueError(f"读取文件失败: {str(e)}")

def validate_data(df):
    """
    验证数据是否适合进行相关系数分析
    
    Args:
        df (pd.DataFrame): 输入数据
        
    Returns:
        tuple: (是否有效, 错误信息)
    """
    if df.empty:
        return False, "数据文件为空"
    
    if df.shape[1] < 2:
        return False, "需要至少2列数据进行相关性分析"
    
    # 检查数值列
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) < 2:
        return False, "需要至少2列数值数据进行相关性分析"
    
    # 检查是否有足够的非空数据
    if df[numeric_cols].dropna().shape[0] < 3:
        return False, "有效数值数据行数不足（至少需要3行）"
    
    return True, "数据验证通过"

def prepare_data_for_analysis(df, selected_columns=None, excluded_columns=None):
    """
    准备用于分析的数据，只保留数值列
    
    Args:
        df (pd.DataFrame): 原始数据
        selected_columns (list, optional): 指定要分析的列名列表。如果提供，只分析这些列
        excluded_columns (list, optional): 指定要排除的列名列表。如果提供，排除这些列
        
    Returns:
        pd.DataFrame: 处理后的数值数据
    """
    # 首先复制数据以避免修改原始数据
    work_df = df.copy()
    
    # 处理列选择逻辑
    if selected_columns is not None:
        # 如果指定了要分析的列，先检查这些列是否存在
        available_columns = [col for col in selected_columns if col in work_df.columns]
        if not available_columns:
            raise ValueError("指定的列在数据中不存在")
        work_df = work_df[available_columns]
        print(f"选择了 {len(available_columns)} 列: {available_columns}")
        
    elif excluded_columns is not None:
        # 如果指定了要排除的列，移除这些列
        columns_to_exclude = [col for col in excluded_columns if col in work_df.columns]
        if columns_to_exclude:
            work_df = work_df.drop(columns=columns_to_exclude)
            print(f"排除了 {len(columns_to_exclude)} 列: {columns_to_exclude}")
    
    # 只保留数值列
    numeric_df = work_df.select_dtypes(include=[np.number])
    
    # 验证是否有数值列
    if numeric_df.empty or numeric_df.shape[1] == 0:
        raise ValueError("选择的列中没有有效的数值列")
    
    # 移除完全为空的列
    numeric_df = numeric_df.dropna(axis=1, how='all')
    
    # 移除完全为空的行
    numeric_df = numeric_df.dropna(axis=0, how='all')
    
    # 确保数据类型正确（强制转换为float）
    for col in numeric_df.columns:
        try:
            numeric_df[col] = pd.to_numeric(numeric_df[col], errors='coerce')
        except:
            # 如果转换失败，移除该列
            numeric_df = numeric_df.drop(columns=[col])
            print(f"警告: 列 '{col}' 无法转换为数值，已移除")
    
    # 再次移除转换后产生的空列
    numeric_df = numeric_df.dropna(axis=1, how='all')
    
    if numeric_df.empty or numeric_df.shape[1] == 0:
        raise ValueError("处理后的数据为空，请检查列选择或数据质量")
    
    print(f"数据准备完成: {numeric_df.shape[0]}行 x {numeric_df.shape[1]}列")
    print(f"分析的列: {list(numeric_df.columns)}")
    return numeric_df

def get_numeric_columns(df):
    """
    获取数据中的所有数值列
    
    Args:
        df (pd.DataFrame): 输入数据
        
    Returns:
        list: 数值列名列表
    """
    return list(df.select_dtypes(include=[np.number]).columns)

def get_column_info(df):
    """
    获取数据列的详细信息
    
    Args:
        df (pd.DataFrame): 输入数据
        
    Returns:
        dict: 包含列信息的字典
    """
    info = {
        'numeric_columns': list(df.select_dtypes(include=[np.number]).columns),
        'non_numeric_columns': list(df.select_dtypes(exclude=[np.number]).columns),
        'total_columns': len(df.columns),
        'column_details': {}
    }
    
    for col in df.columns:
        info['column_details'][col] = {
            'dtype': str(df[col].dtype),
            'non_null_count': df[col].count(),
            'null_count': df[col].isnull().sum(),
            'is_numeric': col in info['numeric_columns']
        }
    
    return info

def calculate_correlation_and_pvalues(df):
    """计算相关系数矩阵和p值矩阵"""
    corr = df.corr()
    p_values = pd.DataFrame(np.zeros_like(corr), columns=corr.columns, index=corr.index)
    
    # 计算每个相关系数的p值
    for i in range(len(corr.columns)):
        for j in range(len(corr.columns)):
            if i > j:
                _, p_value = stats.spearmanr(df.iloc[:, i], df.iloc[:, j])
                p_values.iloc[i, j] = p_value
                p_values.iloc[j, i] = p_value
    
    return corr, p_values

def create_correlation_plot(corr, p_values, title="相关系数矩阵", filename="corr-1.pdf", use_variable_markers=False):
    """创建相关系数可视化图"""
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 10))
    
    # 自定义渐变色映射
    colors = ['#e6f1f5', '#b3d4e6', '#81b8d6', '#5892c2', '#3c66a4']
    cmap = LinearSegmentedColormap.from_list("custom_blue", colors, N=256)
    norm = Normalize(vmin=-1, vmax=1)
    
    # 绘制相关系数矩阵
    for i in range(len(corr.columns)):
        for j in range(len(corr.columns)):
            if i > j:  # 对角线左下部分，显示散点图
                size = np.abs(corr.iloc[i, j]) * 1000  # 大小根据相关系数绝对值确定
                color = cmap(norm(corr.iloc[i, j]))  # 颜色映射
                
                if use_variable_markers:
                    # 根据相关系数大小选择标记
                    if np.abs(corr.iloc[i, j]) > 0.75:
                        marker = 'o'  # 圆形标记
                    else:
                        marker = 's'  # 正方形标记
                else:
                    marker = 's'  # 统一使用正方形标记
                
                ax.scatter(i, j, s=size, color=color, alpha=0.75, marker=marker)
                
                # 如果存在显著性 (p < 0.05)，添加 '*' 标记
                if p_values.iloc[i, j] < 0.05:
                    ax.text(i, j, '*', ha='center', va='center', 
                           color='black', fontsize=16, fontweight='bold')
            
            elif i < j:  # 对角线右上部分，显示相关系数数值
                # 根据p值判断字体颜色
                if p_values.iloc[i, j] > 0.05:
                    font_color = 'red'  # 无显著性，使用红色字体
                else:
                    font_color = 'black'  # 存在显著性，使用黑色字体
                
                ax.text(i, j, f'{corr.iloc[i, j]:.2f}', ha='center', va='center', 
                       color=font_color, fontsize=12, fontweight='bold')
            
            else:  # 对角线部分，显示变量名缩写
                variable_name = corr.columns[i]
                abbreviation = variable_name.split()[-1]
                ax.text(i, j, abbreviation, ha='center', va='center', 
                       fontsize=14, color='gray', fontweight='bold')
            
            # 为每个格子添加边框
            ax.plot([i - 0.5, i + 0.5], [j - 0.5, j - 0.5], color='black', lw=1)  # 上边框
            ax.plot([i - 0.5, i + 0.5], [j + 0.5, j + 0.5], color='black', lw=1)  # 下边框
            ax.plot([i - 0.5, i - 0.5], [j - 0.5, j + 0.5], color='black', lw=1)  # 左边框
            ax.plot([i + 0.5, i + 0.5], [j - 0.5, j + 0.5], color='black', lw=1)  # 右边框
    
    # 设置坐标轴标签
    ax.set_xticks(range(len(corr.columns)))
    ax.set_xticklabels(corr.columns, rotation=45, ha='right', fontsize=14, fontweight='bold')
    ax.set_yticks(range(len(corr.columns)))
    ax.set_yticklabels(corr.columns, fontsize=14, fontweight='bold')
    
    # 设置x和y轴的比例相同
    ax.set_aspect('equal')
    
    # 强制使x轴和y轴的范围相同
    ax.set_xlim(-0.5, len(corr.columns) - 0.5)
    ax.set_ylim(-0.5, len(corr.columns) - 0.5)
    
    # 添加颜色条
    sm = plt.cm.ScalarMappable(cmap=cmap, norm=norm)
    sm.set_array([])
    cbar = fig.colorbar(sm, ax=ax, fraction=0.06, pad=0.04)
    cbar.ax.tick_params(labelsize=18)
    
    # 保存和显示图形
    plt.tight_layout()
    
    # 优化PDF输出，保留图层信息用于编辑
    plt.savefig(filename, format='pdf', bbox_inches='tight', dpi=300, 
                transparent=True, edgecolor='none', facecolor='white',
                metadata={'Title': title, 'Creator': 'Correlation Matrix Visualization'})
    
    # 同时保存PNG格式用于预览
    png_filename = filename.replace('.pdf', '.png')
    plt.savefig(png_filename, format='png', bbox_inches='tight', dpi=300, 
                transparent=False, facecolor='white')
    
    return fig  # 返回图形对象以便GUI显示

def main(data_path=None, df=None, output_dir="./", filename_base="correlation_matrix", 
         selected_columns=None, excluded_columns=None):
    """主函数（命令行模式）
    
    Args:
        data_path (str, optional): 数据文件路径。默认为 None，使用 DEFAULT_DATA_PATH
        df (DataFrame, optional): 直接传入的数据DataFrame。如果提供，则忽略 data_path
        output_dir (str): 输出目录路径
        filename_base (str): 输出文件名前缀
        selected_columns (list, optional): 指定要分析的列名列表
        excluded_columns (list, optional): 指定要排除的列名列表
    """
    try:
        # 获取数据
        if df is not None:
            raw_data = df
            print("使用传入的DataFrame数据")
        else:
            if data_path is None:
                print("错误：未指定数据文件路径")
                print("请使用以下方式之一：")
                print("  1. 启动GUI界面：python drawpic.py --gui")
                print("  2. 指定数据文件：python drawpic.py 数据文件路径")
                return False
            print(f"正在加载数据文件: {data_path}")
            raw_data = load_data_file(data_path)
            
            # 验证原始数据
            is_valid, message = validate_data(raw_data)
            if not is_valid:
                print(f"数据验证失败: {message}")
                return False
        
        # 准备分析数据（应用列选择）
        data = prepare_data_for_analysis(raw_data, selected_columns, excluded_columns)
        
        # 计算相关系数矩阵和p值矩阵
        print("计算相关系数矩阵和p值...")
        corr, p_values = calculate_correlation_and_pvalues(data)
        
        # 生成输出文件路径
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        file1_path = os.path.join(output_dir, f"{filename_base}_standard.pdf")
        file2_path = os.path.join(output_dir, f"{filename_base}_variable_markers.pdf")
        
        # 生成第一种图：标准正方形标记
        print("生成标准相关系数矩阵图...")
        create_correlation_plot(corr, p_values, "标准相关系数矩阵", file1_path, use_variable_markers=False)
        
        # 生成第二种图：根据相关系数大小使用不同标记
        print("生成变化标记相关系数矩阵图...")
        create_correlation_plot(corr, p_values, "变化标记相关系数矩阵", file2_path, use_variable_markers=True)
        
        print(f"图形生成完成！")
        print(f"输出文件保存在: {output_dir}")
        print(f"生成的文件:")
        print(f"  - {os.path.basename(file1_path)}")
        print(f"  - {os.path.basename(file2_path)}")
        print(f"  - 对应的PNG预览文件")
        
        return True
        
    except Exception as e:
        print(f"执行失败: {str(e)}")
        return False

def run_gui():
    """启动GUI界面"""
    try:
        from gui_interface import create_gui
        print("启动GUI界面...")
        app = create_gui()
        app.run()
    except ImportError:
        print("错误: 无法导入GUI模块。请确保 gui_interface.py 文件存在。")
    except Exception as e:
        print(f"启动GUI失败: {str(e)}")

if __name__ == "__main__":
    import sys
    
    print("相关系数矩阵可视化工具")
    print("使用方式:")
    print("  python drawpic.py                 # 启动GUI界面（推荐）")
    print("  python drawpic.py --gui           # 启动GUI界面")
    print("  python drawpic.py 数据文件路径     # 命令行模式")
    print()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1].lower() == "--gui":
            # 显式启动GUI模式
            run_gui()
        else:
            # 使用命令行指定的文件
            main(data_path=sys.argv[1])
    else:
        # 没有参数时默认启动GUI界面
        print("未指定参数，启动GUI界面...")
        run_gui()