#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quantified策略多样性指标表格导出功能使用示例
演示如何使用新增的多样性分析表格导出功能
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parents[1]
sys.path.append(str(project_root / 'code'))

from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from quantified_diversity_evaluator import QuantifiedDiversityEvaluator
from itertools import combinations

def create_sample_models_and_predictions():
    """创建示例模型和预测结果"""
    print("📊 创建示例数据和训练模型...")
    
    # 生成二分类数据
    X, y = make_classification(
        n_samples=300, 
        n_features=15, 
        n_classes=2,
        n_informative=10, 
        n_redundant=3,
        n_clusters_per_class=1,
        random_state=42
    )
    
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    # 定义多个不同类型的模型
    models = {
        'RandomForest': RandomForestClassifier(
            n_estimators=100, 
            max_depth=10, 
            random_state=42
        ),
        'DecisionTree': DecisionTreeClassifier(
            max_depth=8, 
            random_state=42
        ),
        'LogisticRegression': LogisticRegression(
            random_state=42, 
            max_iter=1000
        ),
        'SVM': SVC(
            kernel='rbf', 
            probability=True, 
            random_state=42
        )
    }
    
    predictions_dict = {}
    performance_scores = {}
    
    print("🤖 训练模型并获取预测结果...")
    for name, model in models.items():
        print(f"  训练 {name}...")
        
        # 训练模型
        model.fit(X_train, y_train)
        
        # 获取预测结果
        y_pred = model.predict(X_test)
        predictions_dict[name] = y_pred
        
        # 计算性能得分
        from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
        accuracy = accuracy_score(y_test, y_pred)
        f1 = f1_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred)
        recall = recall_score(y_test, y_pred)
        
        # 综合性能得分
        performance_scores[name] = (accuracy + f1 + precision + recall) / 4
        
        print(f"    准确率: {accuracy:.4f}")
        print(f"    F1分数: {f1:.4f}")
        print(f"    精确率: {precision:.4f}")
        print(f"    召回率: {recall:.4f}")
        print(f"    综合得分: {performance_scores[name]:.4f}")
        print()
    
    return y_test, predictions_dict, performance_scores

def demonstrate_diversity_export():
    """演示多样性指标表格导出功能"""
    print("=" * 80)
    print("Quantified策略多样性指标表格导出功能演示")
    print("=" * 80)
    
    # 创建示例数据
    y_test, predictions_dict, performance_scores = create_sample_models_and_predictions()
    
    # 生成所有可能的模型组合
    model_names = list(predictions_dict.keys())
    all_combinations = []
    
    print("🔄 生成模型组合...")
    for size in range(2, len(model_names) + 1):
        combos = list(combinations(model_names, size))
        all_combinations.extend(combos)
        print(f"  {size}个模型的组合: {len(combos)} 个")
    
    print(f"总共生成 {len(all_combinations)} 个组合")
    
    # 创建多样性评估器
    evaluator = QuantifiedDiversityEvaluator()
    
    # 设置导出目录
    export_dir = project_root / "output" / "diversity_analysis_demo"
    print(f"\n📁 导出目录: {export_dir}")
    
    # 导出多样性指标表格
    print("\n📊 开始导出多样性指标详细表格...")
    exported_files = evaluator.export_diversity_tables(
        y_true=y_test,
        predictions_dict=predictions_dict,
        performance_scores=performance_scores,
        combinations=all_combinations,
        diversity_weight=0.5,  # 平衡性能和多样性
        export_dir=str(export_dir)
    )
    
    if exported_files:
        print("\n✅ 导出成功!")
        print("\n📋 导出的文件列表:")
        
        for file_type, file_path in exported_files.items():
            if file_type != 'export_directory':
                file_name = Path(file_path).name
                print(f"  📄 {file_type}: {file_name}")
        
        print(f"\n📂 所有文件保存在: {exported_files['export_directory']}")
        
        # 展示部分结果
        print("\n🔍 结果预览:")
        show_results_preview(exported_files)
        
        return exported_files
    else:
        print("❌ 导出失败!")
        return None

def show_results_preview(exported_files):
    """展示导出结果的预览"""
    import pandas as pd
    
    # 预览模型对多样性指标表格
    pairwise_file = exported_files['pairwise_diversity_table']
    if Path(pairwise_file).exists():
        df_pairwise = pd.read_csv(pairwise_file)
        print(f"\n📊 模型对多样性指标表格 ({len(df_pairwise)} 行):")
        print("前3个模型对的多样性指标:")
        preview_cols = ['模型对', 'Q统计量多样性', '不一致性度量', '综合多样性得分', '多样性等级']
        print(df_pairwise[preview_cols].head(3).to_string(index=False))
    
    # 预览组合评估结果表格
    combination_file = exported_files['combination_evaluation_table']
    if Path(combination_file).exists():
        df_combinations = pd.read_csv(combination_file)
        print(f"\n🏆 组合评估结果表格 ({len(df_combinations)} 行):")
        print("前5个最佳组合:")
        preview_cols = ['模型组合', '综合得分', '综合多样性得分', '多样性等级', '推荐集成方法']
        print(df_combinations[preview_cols].head(5).to_string(index=False))
    
    # 预览综合分析报告
    report_file = exported_files['diversity_summary_report']
    if Path(report_file).exists():
        with open(report_file, 'r', encoding='utf-8') as f:
            report_lines = f.readlines()
        
        print(f"\n📝 综合分析报告预览:")
        print("".join(report_lines[:15]))  # 显示前15行

def main():
    """主函数"""
    try:
        exported_files = demonstrate_diversity_export()
        
        if exported_files:
            print("\n" + "=" * 80)
            print("🎉 演示完成!")
            print("=" * 80)
            print("\n💡 使用说明:")
            print("1. 模型对多样性指标表格: 包含所有模型对的详细多样性指标")
            print("2. 组合评估结果表格: 包含所有组合的综合评估结果，按得分排序")
            print("3. 综合分析报告: 包含整体多样性分析和改进建议")
            print("\n🔧 在实际项目中的使用方法:")
            print("python code/main.py --model RandomForest,DecisionTree,LogisticRegression,SVM \\")
            print("                    --mode ensemble \\")
            print("                    --data your_data.csv \\")
            print("                    --diversity_export_dir ./my_diversity_analysis")
        else:
            print("\n❌ 演示失败!")
            
    except Exception as e:
        print(f"\n💥 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
