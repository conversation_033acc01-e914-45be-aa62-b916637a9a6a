#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI数据探索模块
为主GUI添加数据探索功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import numpy as np
from pathlib import Path
import threading
from typing import List, Dict, Any

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / 'code'))

from data_exploration import DataExplorer
from data_preprocessing import load_and_clean_data
from logger import get_logger

logger = get_logger(__name__)

class DataExplorationGUI:
    """数据探索GUI组件"""
    
    def __init__(self, parent_gui):
        """
        初始化数据探索GUI
        
        Args:
            parent_gui: 父GUI对象
        """
        self.parent_gui = parent_gui
        self.explorer = DataExplorer()
        self.current_df = None
        self.target_var = None
        self.continuous_vars = []
        
        logger.info("数据探索GUI组件初始化完成")
    
    def create_exploration_tab(self, notebook):
        """
        创建数据探索选项卡
        
        Args:
            notebook: 父级notebook组件
        """
        # 创建数据探索选项卡
        exploration_tab = ttk.Frame(notebook)
        notebook.add(exploration_tab, text="🔍 数据探索")
        
        # 创建主要区域
        self.create_data_selection_area(exploration_tab)
        self.create_analysis_config_area(exploration_tab)
        self.create_analysis_control_area(exploration_tab)
        self.create_results_display_area(exploration_tab)
        
        return exploration_tab
    
    def create_data_selection_area(self, parent):
        """创建数据选择区域"""
        data_frame = ttk.LabelFrame(parent, text="数据选择")
        data_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 数据源选择
        ttk.Label(data_frame, text="数据源:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.data_source_var = tk.StringVar(value="current")
        ttk.Radiobutton(data_frame, text="使用当前加载的数据", 
                       variable=self.data_source_var, value="current").grid(
                           row=0, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Radiobutton(data_frame, text="选择新的数据文件", 
                       variable=self.data_source_var, value="new").grid(
                           row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 文件选择
        self.file_path_var = tk.StringVar()
        self.file_entry = ttk.Entry(data_frame, textvariable=self.file_path_var, width=50)
        self.file_entry.grid(row=2, column=1, padx=5, pady=5)
        ttk.Button(data_frame, text="浏览...", 
                  command=self.browse_data_file).grid(row=2, column=2, padx=5, pady=5)
        
        # 加载数据按钮
        ttk.Button(data_frame, text="📊 加载数据", 
                  command=self.load_exploration_data).grid(row=3, column=1, padx=5, pady=10)
        
        # 数据信息显示
        self.data_info_text = tk.Text(data_frame, height=4, wrap=tk.WORD)
        self.data_info_text.grid(row=4, column=0, columnspan=3, padx=5, pady=5, sticky="ew")
        
        data_frame.columnconfigure(1, weight=1)
    
    def create_analysis_config_area(self, parent):
        """创建分析配置区域"""
        config_frame = ttk.LabelFrame(parent, text="分析配置")
        config_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 目标变量选择
        ttk.Label(config_frame, text="目标变量:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.target_var_combo = ttk.Combobox(config_frame, width=20, state="readonly")
        self.target_var_combo.grid(row=0, column=1, padx=5, pady=5)
        
        # 连续变量选择
        ttk.Label(config_frame, text="连续变量:").grid(row=1, column=0, sticky=tk.NW, padx=5, pady=5)
        
        # 创建变量选择框架
        var_frame = ttk.Frame(config_frame)
        var_frame.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky="ew")
        
        # 变量列表框
        self.var_listbox = tk.Listbox(var_frame, selectmode=tk.MULTIPLE, height=6)
        var_scrollbar = ttk.Scrollbar(var_frame, orient=tk.VERTICAL, command=self.var_listbox.yview)
        self.var_listbox.configure(yscrollcommand=var_scrollbar.set)
        
        self.var_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        var_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 变量选择按钮
        button_frame = ttk.Frame(config_frame)
        button_frame.grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Button(button_frame, text="全选", command=self.select_all_vars).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="清除", command=self.clear_var_selection).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="推荐", command=self.recommend_vars).pack(side=tk.LEFT, padx=2)
        
        # 分箱配置
        ttk.Label(config_frame, text="分箱设置:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        
        bin_frame = ttk.Frame(config_frame)
        bin_frame.grid(row=3, column=1, padx=5, pady=5, sticky="w")
        
        ttk.Label(bin_frame, text="分箱数:").pack(side=tk.LEFT, padx=2)
        self.n_bins_var = tk.IntVar(value=5)
        ttk.Spinbox(bin_frame, from_=3, to=10, textvariable=self.n_bins_var, width=5).pack(side=tk.LEFT, padx=2)
        
        ttk.Label(bin_frame, text="方法:").pack(side=tk.LEFT, padx=(10, 2))
        self.binning_method_var = tk.StringVar(value="tree")  # 默认使用决策树分箱
        method_combo = ttk.Combobox(bin_frame, textvariable=self.binning_method_var,
                                   values=["tree", "chi2", "qcut", "cut"], state="readonly", width=8)
        method_combo.pack(side=tk.LEFT, padx=2)

        # 添加方法说明按钮
        def show_method_help():
            help_text = """分箱方法说明：

• tree (推荐): 决策树分箱，利用目标信息自动找最优切分点，区分度强
• chi2: 卡方合并分箱，从细分开始迭代合并相似箱，稳健性好
• qcut: 等频分箱，每箱样本数相等，适合偏态分布
• cut: 等距分箱，每箱区间长度相等，适合均匀分布

对于200样本的数据，推荐使用tree或chi2方法。"""
            messagebox.showinfo("分箱方法说明", help_text)

        help_btn = ttk.Button(bin_frame, text="?", width=2, command=show_method_help)
        help_btn.pack(side=tk.LEFT, padx=2)

        # 添加最小分箱样本数设置
        ttk.Label(bin_frame, text="最小样本:").pack(side=tk.LEFT, padx=(10, 2))
        self.min_bin_size_var = tk.IntVar(value=20)
        ttk.Spinbox(bin_frame, from_=10, to=50, textvariable=self.min_bin_size_var, width=5).pack(side=tk.LEFT, padx=2)
        
        config_frame.columnconfigure(1, weight=1)
    
    def create_analysis_control_area(self, parent):
        """创建分析控制区域"""
        control_frame = ttk.LabelFrame(parent, text="分析控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 分析类型选择
        ttk.Label(control_frame, text="分析类型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.analysis_types = {
            'probability': tk.BooleanVar(value=True),
            'correlation': tk.BooleanVar(value=True),
            'distribution': tk.BooleanVar(value=False)
        }
        
        type_frame = ttk.Frame(control_frame)
        type_frame.grid(row=0, column=1, padx=5, pady=5, sticky="w")
        
        ttk.Checkbutton(type_frame, text="分组概率分析",
                       variable=self.analysis_types['probability']).pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(type_frame, text="相关性分析",
                       variable=self.analysis_types['correlation']).pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(type_frame, text="分布分析",
                       variable=self.analysis_types['distribution']).pack(side=tk.LEFT, padx=5)

        # 图表配置
        ttk.Label(control_frame, text="图表设置:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)

        chart_frame = ttk.Frame(control_frame)
        chart_frame.grid(row=1, column=1, padx=5, pady=5, sticky="w")

        # 图表类型选择
        ttk.Label(chart_frame, text="类型:").pack(side=tk.LEFT, padx=2)
        self.chart_type_var = tk.StringVar(value="both")
        chart_combo = ttk.Combobox(chart_frame, textvariable=self.chart_type_var,
                                  values=["bar", "line", "both"], state="readonly", width=8)
        chart_combo.pack(side=tk.LEFT, padx=2)

        # 标签模式选择
        ttk.Label(chart_frame, text="标签:").pack(side=tk.LEFT, padx=(10, 2))
        self.label_mode_var = tk.StringVar(value="group+interval")
        label_combo = ttk.Combobox(chart_frame, textvariable=self.label_mode_var,
                                  values=["group_only", "interval_only", "group+interval"],
                                  state="readonly", width=12)
        label_combo.pack(side=tk.LEFT, padx=2)

        # 添加图表设置说明按钮
        def show_chart_help():
            help_text = """图表设置说明：

图表类型：
• bar: 仅生成柱状图
• line: 仅生成折线图
• both: 同时生成柱状图和折线图

标签模式：
• group_only: 仅显示 "Group 1, Group 2..."
• interval_only: 仅显示分箱区间 "(0.1, 0.5]"
• group+interval: 显示 "Group 1\\n(0.1, 0.5]"

推荐使用 both + group+interval 获得最完整的可视化效果。"""
            messagebox.showinfo("图表设置说明", help_text)

        chart_help_btn = ttk.Button(chart_frame, text="?", width=2, command=show_chart_help)
        chart_help_btn.pack(side=tk.LEFT, padx=2)

        # 折线图的两个独立图例控制（取代原“显示图例”）
        self.show_legend_line_detail_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(chart_frame, text="折线-单变量图例", variable=self.show_legend_line_detail_var).pack(side=tk.LEFT, padx=(10,2))
        self.show_legend_line_comprehensive_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(chart_frame, text="折线-综合图例", variable=self.show_legend_line_comprehensive_var).pack(side=tk.LEFT, padx=(6,2))

        # CI 颜色一致性开关
        self.consistent_ci_color_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(chart_frame, text="同变量CI颜色一致", variable=self.consistent_ci_color_var).pack(side=tk.LEFT, padx=(10,2))

        # 控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="🚀 开始分析", 
                  command=self.start_analysis).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📊 查看结果", 
                  command=self.view_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📁 打开输出目录", 
                  command=self.open_output_dir).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧹 清除结果", 
                  command=self.clear_results).pack(side=tk.LEFT, padx=5)
    
    def create_results_display_area(self, parent):
        """创建结果显示区域"""
        results_frame = ttk.LabelFrame(parent, text="分析结果")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建结果显示的notebook
        self.results_notebook = ttk.Notebook(results_frame)
        self.results_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 分析日志选项卡
        log_tab = ttk.Frame(self.results_notebook)
        self.results_notebook.add(log_tab, text="分析日志")
        
        self.analysis_log = tk.Text(log_tab, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_tab, orient=tk.VERTICAL, command=self.analysis_log.yview)
        self.analysis_log.configure(yscrollcommand=log_scrollbar.set)
        
        self.analysis_log.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 结果摘要选项卡
        summary_tab = ttk.Frame(self.results_notebook)
        self.results_notebook.add(summary_tab, text="结果摘要")
        
        self.results_summary = tk.Text(summary_tab, wrap=tk.WORD)
        summary_scrollbar = ttk.Scrollbar(summary_tab, orient=tk.VERTICAL, command=self.results_summary.yview)
        self.results_summary.configure(yscrollcommand=summary_scrollbar.set)
        
        self.results_summary.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def browse_data_file(self):
        """浏览数据文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("CSV files", "*.csv"), ("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if file_path:
            self.file_path_var.set(file_path)
    
    def load_exploration_data(self):
        """加载探索数据"""
        try:
            if self.data_source_var.get() == "current":
                # 使用当前加载的数据
                if hasattr(self.parent_gui, 'current_data_path') and self.parent_gui.current_data_path.get():
                    data_path = self.parent_gui.current_data_path.get()
                    self.current_df, self.target_var = load_and_clean_data(data_path)
                else:
                    messagebox.showwarning("警告", "请先在数据管理选项卡中加载数据")
                    return
            else:
                # 使用新选择的数据文件
                file_path = self.file_path_var.get()
                if not file_path:
                    messagebox.showwarning("警告", "请选择数据文件")
                    return
                self.current_df, self.target_var = load_and_clean_data(file_path)
            
            # 更新界面
            self.update_variable_lists()
            self.update_data_info()
            
            self.log_message("数据加载成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败: {e}")
            self.log_message(f"数据加载失败: {e}")
    
    def update_variable_lists(self):
        """更新变量列表"""
        if self.current_df is None:
            return
        
        # 更新目标变量下拉框
        columns = list(self.current_df.columns)
        self.target_var_combo['values'] = columns
        if self.target_var in columns:
            self.target_var_combo.set(self.target_var)
        
        # 更新连续变量列表框
        numeric_cols = self.current_df.select_dtypes(include=[np.number]).columns.tolist()
        if self.target_var in numeric_cols:
            numeric_cols.remove(self.target_var)
        
        self.var_listbox.delete(0, tk.END)
        for col in numeric_cols:
            self.var_listbox.insert(tk.END, col)
    
    def update_data_info(self):
        """更新数据信息显示"""
        if self.current_df is None:
            return
        
        info_text = f"""数据形状: {self.current_df.shape[0]} 行 × {self.current_df.shape[1]} 列
目标变量: {self.target_var}
数值型变量: {len(self.current_df.select_dtypes(include=[np.number]).columns)} 个
缺失值: {self.current_df.isnull().sum().sum()} 个"""
        
        self.data_info_text.delete(1.0, tk.END)
        self.data_info_text.insert(tk.END, info_text)
    
    def select_all_vars(self):
        """全选变量"""
        self.var_listbox.select_set(0, tk.END)
    
    def clear_var_selection(self):
        """清除变量选择"""
        self.var_listbox.selection_clear(0, tk.END)
    
    def recommend_vars(self):
        """推荐变量"""
        if self.current_df is None:
            return
        
        # 简单推荐：选择与目标变量相关性较高的变量
        try:
            target = self.target_var_combo.get()
            if not target:
                messagebox.showwarning("警告", "请先选择目标变量")
                return
            
            numeric_cols = self.current_df.select_dtypes(include=[np.number]).columns.tolist()
            if target in numeric_cols:
                numeric_cols.remove(target)
            
            # 计算相关性
            correlations = {}
            for col in numeric_cols:
                try:
                    corr = self.current_df[col].corr(self.current_df[target])
                    if not np.isnan(corr):
                        correlations[col] = abs(corr)
                except:
                    continue
            
            # 选择相关性最高的前5个变量
            top_vars = sorted(correlations.items(), key=lambda x: x[1], reverse=True)[:5]
            
            self.clear_var_selection()
            for var, _ in top_vars:
                try:
                    idx = list(self.var_listbox.get(0, tk.END)).index(var)
                    self.var_listbox.select_set(idx)
                except ValueError:
                    continue
            
            self.log_message(f"推荐了 {len(top_vars)} 个相关性较高的变量")
            
        except Exception as e:
            messagebox.showerror("错误", f"推荐变量失败: {e}")
    
    def log_message(self, message):
        """记录日志消息"""
        timestamp = pd.Timestamp.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.analysis_log.insert(tk.END, log_entry)
        self.analysis_log.see(tk.END)
        
        # 同时记录到父GUI的日志
        if hasattr(self.parent_gui, 'log_message'):
            self.parent_gui.log_message(f"数据探索: {message}")
    
    def start_analysis(self):
        """开始分析"""
        if self.current_df is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
        
        # 获取选择的变量
        selected_indices = self.var_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("警告", "请选择至少一个连续变量")
            return
        
        selected_vars = [self.var_listbox.get(i) for i in selected_indices]
        target_var = self.target_var_combo.get()
        
        if not target_var:
            messagebox.showwarning("警告", "请选择目标变量")
            return
        
        # 在后台线程中执行分析
        analysis_thread = threading.Thread(
            target=self._run_analysis,
            args=(selected_vars, target_var),
            daemon=True
        )
        analysis_thread.start()
    
    def _run_analysis(self, selected_vars: List[str], target_var: str):
        """在后台运行分析"""
        try:
            # 首先检查数据是否有效
            if self.current_df is None:
                error_msg = "数据未加载，无法进行分析"
                self.log_message(error_msg)
                return

            if self.current_df.empty:
                error_msg = "数据为空，无法进行分析"
                self.log_message(error_msg)
                return

            self.log_message("开始数据探索分析...")

            analysis_results = {}

            # 分组概率分析
            if self.analysis_types['probability'].get():
                self.log_message("执行分组概率分析...")
                try:
                    prob_results = self.explorer.create_binned_probability_analysis(
                        self.current_df, selected_vars, target_var,
                        n_bins=self.n_bins_var.get(),
                        binning_method=self.binning_method_var.get(),
                        min_bin_size=self.min_bin_size_var.get(),
                        chart_types=self.chart_type_var.get(),
                        label_mode=self.label_mode_var.get(),
                        show_legend_bar=True,  # 柱状图始终显示图例（如果有的话）
                        show_legend_line_detail=self.show_legend_line_detail_var.get(),
                        show_legend_line_comprehensive=self.show_legend_line_comprehensive_var.get(),
                        consistent_ci_color=self.consistent_ci_color_var.get()
                    )
                    analysis_results['probability_analysis'] = prob_results
                    self.log_message(f"分组概率分析完成，分析了 {len(prob_results)} 个变量")
                except Exception as e:
                    error_msg = f"分组概率分析失败: {e}"
                    self.log_message(error_msg)
                    logger.error(error_msg, exc_info=True)

            # 相关性分析
            if self.analysis_types['correlation'].get():
                self.log_message("执行相关性分析...")
                try:
                    corr_results = self.explorer.create_correlation_heatmap(
                        self.current_df, target_var
                    )
                    analysis_results['correlation_analysis'] = corr_results
                    self.log_message("相关性分析完成")
                except Exception as e:
                    error_msg = f"相关性分析失败: {e}"
                    self.log_message(error_msg)
                    logger.error(error_msg, exc_info=True)

            # 分布分析
            if self.analysis_types['distribution'].get():
                self.log_message("执行分布分析...")
                try:
                    dist_results = self.explorer.create_distribution_analysis(
                        self.current_df, selected_vars, target_var
                    )
                    analysis_results['distribution_analysis'] = dist_results
                    self.log_message(f"分布分析完成，分析了 {len(dist_results)} 个变量")
                except Exception as e:
                    error_msg = f"分布分析失败: {e}"
                    self.log_message(error_msg)
                    logger.error(error_msg, exc_info=True)

            # 生成报告
            if analysis_results:  # 只有在有分析结果时才生成报告
                self.log_message("生成分析报告...")
                try:
                    report_path = self.explorer.generate_exploration_report(
                        self.current_df, selected_vars, target_var, analysis_results
                    )

                    # 更新结果摘要
                    self._update_results_summary(analysis_results)

                    self.log_message(f"数据探索分析完成！报告已保存至: {report_path}")
                except Exception as e:
                    error_msg = f"生成报告失败: {e}"
                    self.log_message(error_msg)
                    logger.error(error_msg, exc_info=True)
            else:
                self.log_message("没有成功完成任何分析，跳过报告生成")

        except Exception as e:
            error_msg = f"分析过程中出现未预期错误: {e}"
            self.log_message(error_msg)
            logger.error(error_msg, exc_info=True)
    
    def _update_results_summary(self, results: Dict[str, Any]):
        """更新结果摘要"""
        summary_text = "=== 数据探索分析结果摘要 ===\n\n"
        
        if 'probability_analysis' in results:
            prob_results = results['probability_analysis']
            summary_text += "📊 分组概率分析:\n"
            summary_text += f"- 分析变量数: {len(prob_results)}\n"
            for var, result in prob_results.items():
                overall_prob = result['overall_probability']
                n_groups = result['n_bins']
                summary_text += f"  • {var}: {n_groups} 组, 总体概率 {overall_prob:.3f}\n"
            summary_text += "\n"
        
        if 'correlation_analysis' in results:
            corr_results = results['correlation_analysis']
            target_corr = corr_results.get('target_correlations', pd.Series())
            summary_text += "🔗 相关性分析:\n"
            if len(target_corr) > 0:
                summary_text += f"- 最强正相关: {target_corr.idxmax()} ({target_corr.max():.3f})\n"
                summary_text += f"- 最强负相关: {target_corr.idxmin()} ({target_corr.min():.3f})\n"
            summary_text += "\n"
        
        if 'distribution_analysis' in results:
            dist_results = results['distribution_analysis']
            summary_text += "📈 分布分析:\n"
            summary_text += f"- 分析变量数: {len(dist_results)}\n"
            summary_text += "\n"
        
        summary_text += f"📁 输出目录: {self.explorer.output_dir}\n"
        summary_text += f"🕒 分析时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        # 更新摘要显示
        self.results_summary.delete(1.0, tk.END)
        self.results_summary.insert(tk.END, summary_text)
    
    def view_results(self):
        """查看结果"""
        import subprocess
        import sys
        
        try:
            if sys.platform.startswith('win'):
                subprocess.run(['explorer', str(self.explorer.output_dir)])
            elif sys.platform.startswith('darwin'):
                subprocess.run(['open', str(self.explorer.output_dir)])
            else:
                subprocess.run(['xdg-open', str(self.explorer.output_dir)])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开结果目录: {e}")
    
    def open_output_dir(self):
        """打开输出目录"""
        self.view_results()
    
    def clear_results(self):
        """清除结果"""
        self.analysis_log.delete(1.0, tk.END)
        self.results_summary.delete(1.0, tk.END)
        self.log_message("结果已清除")
