# -*- coding: utf-8 -*-
"""
相关系数矩阵可视化工具 GUI 界面
提供用户友好的图形界面用于文件选择和参数配置
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
from pathlib import Path
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading

# 导入核心分析功能
from drawpic import (
    load_data_file, 
    validate_data, 
    prepare_data_for_analysis,
    calculate_correlation_and_pvalues, 
    create_correlation_plot,
    get_column_info,
    get_numeric_columns
)

class CorrelationAnalysisGUI:
    """相关系数分析GUI界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("相关系数矩阵可视化工具")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 数据存储
        self.current_data = None
        self.raw_data = None  # 原始未处理的数据
        self.column_info = None  # 列信息
        self.selected_columns = []  # 选中要分析的列
        self.excluded_columns = []  # 排除的列
        
        # 界面变量
        self.input_file_path = tk.StringVar()
        self.output_dir_path = tk.StringVar(value=os.getcwd())
        self.output_filename_base = tk.StringVar(value="correlation_matrix")
        
        # 设置默认输出目录
        self.output_dir_path.set(os.getcwd())
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # 标题
        title_label = ttk.Label(main_frame, text="相关系数矩阵可视化工具", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=row, column=0, columnspan=3, pady=(0, 20))
        row += 1
        
        # 文件输入部分
        input_frame = ttk.LabelFrame(main_frame, text="输入文件", padding="10")
        input_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(1, weight=1)
        row += 1
        
        ttk.Label(input_frame, text="数据文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Entry(input_frame, textvariable=self.input_file_path, width=50).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(input_frame, text="浏览...", command=self.browse_input_file).grid(
            row=0, column=2, sticky=tk.W)
        
        # 支持格式说明
        format_label = ttk.Label(input_frame, text="支持格式: CSV (.csv), Excel (.xlsx, .xls)", 
                                foreground="gray")
        format_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
        
        # 输出配置部分
        output_frame = ttk.LabelFrame(main_frame, text="输出配置", padding="10")
        output_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        output_frame.columnconfigure(1, weight=1)
        row += 1
        
        ttk.Label(output_frame, text="输出目录:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Entry(output_frame, textvariable=self.output_dir_path, width=50).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(output_frame, text="浏览...", command=self.browse_output_dir).grid(
            row=0, column=2, sticky=tk.W)
        
        ttk.Label(output_frame, text="文件名前缀:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Entry(output_frame, textvariable=self.output_filename_base, width=30).grid(
            row=1, column=1, sticky=tk.W, pady=(5, 0))
        
        # 列选择部分
        column_frame = ttk.LabelFrame(main_frame, text="列选择设置", padding="10")
        column_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        column_frame.columnconfigure(1, weight=1)
        row += 1
        
        # 列选择说明
        ttk.Label(column_frame, text="数据分析列选择:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        # 列选择按钮
        column_button_frame = ttk.Frame(column_frame)
        column_button_frame.grid(row=0, column=1, sticky=tk.W, pady=(0, 5))
        
        self.column_select_button = ttk.Button(column_button_frame, text="选择分析列", 
                                              command=self.open_column_selection, state="disabled")
        self.column_select_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.reset_columns_button = ttk.Button(column_button_frame, text="重置选择", 
                                              command=self.reset_column_selection, state="disabled")
        self.reset_columns_button.pack(side=tk.LEFT)
        
        # 列选择状态显示
        self.column_status_label = ttk.Label(column_frame, text="请先加载数据", foreground="gray")
        self.column_status_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

        # 数据预览部分
        preview_frame = ttk.LabelFrame(main_frame, text="数据预览", padding="10")
        preview_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        preview_frame.columnconfigure(0, weight=1)
        row += 1
        
        # 创建文本框用于显示数据信息
        self.data_info_text = tk.Text(preview_frame, height=6, width=70)
        scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=self.data_info_text.yview)
        self.data_info_text.configure(yscrollcommand=scrollbar.set)
        
        self.data_info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        preview_frame.rowconfigure(0, weight=1)
        
        # 操作按钮部分
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=3, pady=(10, 0))
        row += 1
        
        ttk.Button(button_frame, text="加载数据", command=self.load_data).pack(
            side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="生成图表", command=self.generate_plots).pack(
            side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清除", command=self.clear_data).pack(
            side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(
            side=tk.RIGHT)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def browse_input_file(self):
        """浏览输入文件"""
        filetypes = [
            ("所有支持的格式", "*.csv;*.xlsx;*.xls"),
            ("CSV文件", "*.csv"),
            ("Excel文件", "*.xlsx;*.xls"),
            ("所有文件", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=filetypes
        )
        
        if filename:
            self.input_file_path.set(filename)
            # 自动设置输出文件名
            base_name = Path(filename).stem
            self.output_filename_base.set(f"{base_name}_correlation")
    
    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(
            title="选择输出目录",
            initialdir=self.output_dir_path.get()
        )
        
        if directory:
            self.output_dir_path.set(directory)
    
    def load_data(self):
        """加载数据文件"""
        if not self.input_file_path.get():
            messagebox.showerror("错误", "请先选择输入文件")
            return
        
        try:
            self.progress.start()
            self.update_data_info("正在加载数据...")
            
            # 加载数据
            df = load_data_file(self.input_file_path.get())
            
            # 验证数据
            is_valid, message = validate_data(df)
            if not is_valid:
                messagebox.showerror("数据验证失败", message)
                return
            
            # 保存原始数据和列信息
            self.raw_data = df
            self.column_info = get_column_info(df)
            
            # 重置列选择
            self.reset_column_selection()
            
            # 准备数据（使用当前的列选择）
            self.current_data = prepare_data_for_analysis(df, self.selected_columns if self.selected_columns else None)
            
            # 启用列选择按钮
            self.column_select_button.config(state="normal")
            self.reset_columns_button.config(state="normal")
            
            # 更新列选择状态
            self.update_column_status()
            
            # 显示数据信息
            self.display_data_info()
            
            messagebox.showinfo("成功", "数据加载成功！")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败:\n{str(e)}")
        finally:
            self.progress.stop()
    
    def display_data_info(self):
        """显示数据信息"""
        if self.current_data is None:
            return
        
        info_text = f"""数据基本信息:
行数: {self.current_data.shape[0]}
列数: {self.current_data.shape[1]}

变量列表:
{chr(10).join([f"  {i+1}. {col}" for i, col in enumerate(self.current_data.columns)])}

数据统计:
{self.current_data.describe().to_string()}

缺失值统计:
{self.current_data.isnull().sum().to_string()}
"""
        self.update_data_info(info_text)
    
    def update_data_info(self, text):
        """更新数据信息显示"""
        self.data_info_text.delete(1.0, tk.END)
        self.data_info_text.insert(1.0, text)
    
    def generate_plots(self):
        """生成相关系数图表"""
        if self.current_data is None:
            messagebox.showerror("错误", "请先加载数据")
            return
        
        if not self.output_dir_path.get():
            messagebox.showerror("错误", "请选择输出目录")
            return
        
        try:
            self.progress.start()
            
            # 在新线程中执行图表生成以避免界面冻结
            thread = threading.Thread(target=self._generate_plots_thread)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"生成图表失败:\n{str(e)}")
            self.progress.stop()
    
    def _generate_plots_thread(self):
        """在后台线程中生成图表"""
        try:
            # 确保使用最新的列选择重新准备数据
            if self.raw_data is not None:
                self.current_data = prepare_data_for_analysis(
                    self.raw_data, 
                    self.selected_columns if self.selected_columns else None
                )
            
            # 计算相关系数矩阵和p值
            corr, p_values = calculate_correlation_and_pvalues(self.current_data)
            
            # 生成输出文件路径
            output_dir = self.output_dir_path.get()
            base_filename = self.output_filename_base.get()
            
            file1_path = os.path.join(output_dir, f"{base_filename}_standard.pdf")
            file2_path = os.path.join(output_dir, f"{base_filename}_variable_markers.pdf")
            
            # 生成第一种图：标准正方形标记
            create_correlation_plot(
                corr, p_values, 
                "标准相关系数矩阵", 
                file1_path, 
                use_variable_markers=False
            )
            plt.close()  # 关闭图形以释放内存
            
            # 生成第二种图：根据相关系数大小使用不同标记
            create_correlation_plot(
                corr, p_values, 
                "变化标记相关系数矩阵", 
                file2_path, 
                use_variable_markers=True
            )
            plt.close()  # 关闭图形以释放内存
            
            # 在主线程中显示完成消息
            self.root.after(0, self._on_plots_generated, file1_path, file2_path)
            
        except Exception as e:
            self.root.after(0, self._on_plots_error, str(e))
    
    def _on_plots_generated(self, file1_path, file2_path):
        """图表生成完成回调"""
        self.progress.stop()
        
        message = f"""图表生成完成！

输出文件:
• {os.path.basename(file1_path)}
• {os.path.basename(file2_path)}
• 对应的PNG预览文件

输出目录: {os.path.dirname(file1_path)}

同时生成了PDF和PNG格式:
- PDF格式保留图层信息，适合进一步编辑
- PNG格式用于预览和文档插入"""
        
        messagebox.showinfo("完成", message)
    
    def _on_plots_error(self, error_message):
        """图表生成错误回调"""
        self.progress.stop()
        messagebox.showerror("错误", f"生成图表失败:\n{error_message}")
    
    def clear_data(self):
        """清除当前数据"""
        self.current_data = None
        self.raw_data = None
        self.column_info = None
        self.selected_columns = []
        self.excluded_columns = []
        
        # 禁用列选择按钮
        self.column_select_button.config(state="disabled")
        self.reset_columns_button.config(state="disabled")
        
        # 更新状态显示
        self.column_status_label.config(text="请先加载数据", foreground="gray")
        
        self.update_data_info("数据已清除")
        messagebox.showinfo("完成", "数据已清除")
    
    def open_column_selection(self):
        """打开列选择窗口"""
        if self.raw_data is None:
            messagebox.showerror("错误", "请先加载数据")
            return
        
        # 创建列选择窗口
        ColumnSelectionWindow(self)
    
    def reset_column_selection(self):
        """重置列选择"""
        if self.raw_data is not None:
            # 重置为所有数值列
            self.selected_columns = get_numeric_columns(self.raw_data)
            self.excluded_columns = []
            self.update_column_status()
            
            # 重新准备数据
            try:
                self.current_data = prepare_data_for_analysis(
                    self.raw_data, 
                    self.selected_columns if self.selected_columns else None
                )
                self.display_data_info()
            except Exception as e:
                messagebox.showerror("错误", f"重置列选择失败:\n{str(e)}")
    
    def update_column_status(self):
        """更新列选择状态显示"""
        if not self.raw_data is None and self.column_info:
            total_numeric = len(self.column_info['numeric_columns'])
            selected_count = len(self.selected_columns) if self.selected_columns else total_numeric
            
            status_text = f"共{total_numeric}个数值列，当前分析{selected_count}列"
            if self.selected_columns and len(self.selected_columns) < total_numeric:
                status_text += f" (已排除{total_numeric - selected_count}列)"
            
            self.column_status_label.config(text=status_text, foreground="blue")
        else:
            self.column_status_label.config(text="请先加载数据", foreground="gray")
    
    def apply_column_selection(self, selected_columns):
        """应用列选择"""
        self.selected_columns = selected_columns
        
        try:
            # 重新准备数据
            self.current_data = prepare_data_for_analysis(
                self.raw_data, 
                self.selected_columns if self.selected_columns else None
            )
            
            # 更新显示
            self.update_column_status()
            self.display_data_info()
            
            messagebox.showinfo("成功", f"已更新分析列选择，当前分析{len(self.selected_columns)}列")
            
        except Exception as e:
            messagebox.showerror("错误", f"应用列选择失败:\n{str(e)}")
    
    def run(self):
        """运行GUI应用"""
        self.root.mainloop()

class ColumnSelectionWindow:
    """列选择窗口"""
    
    def __init__(self, parent_gui):
        self.parent = parent_gui
        self.selected_columns = parent_gui.selected_columns.copy()
        
        # 创建窗口
        self.window = tk.Toplevel(parent_gui.root)
        self.window.title("选择分析列")
        self.window.geometry("600x500")
        self.window.resizable(True, True)
        
        # 设置窗口为模态
        self.window.transient(parent_gui.root)
        self.window.grab_set()
        
        self.setup_ui()
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """居中显示窗口"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
    
    def setup_ui(self):
        """设置UI界面"""
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题和说明
        title_label = ttk.Label(main_frame, text="选择要进行相关性分析的数值列", 
                               font=('Arial', 12, 'bold'))
        title_label.pack(pady=(0, 10))
        
        info_label = ttk.Label(main_frame, text="勾选要分析的列，取消勾选将排除该列", 
                              foreground="gray")
        info_label.pack(pady=(0, 10))
        
        # 列列表框架
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建列表框和滚动条
        self.listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE, height=15)
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.listbox.yview)
        self.listbox.configure(yscrollcommand=scrollbar.set)
        
        self.listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 填充列信息
        self.populate_column_list()
        
        # 统计信息
        self.stats_label = ttk.Label(main_frame, text="", foreground="blue")
        self.stats_label.pack(pady=(5, 10))
        self.update_stats()
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="全选", command=self.select_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="全不选", command=self.deselect_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="反选", command=self.invert_selection).pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Button(button_frame, text="确定", command=self.apply_selection).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 绑定选择变化事件
        self.listbox.bind('<<ListboxSelect>>', self.on_selection_change)
    
    def populate_column_list(self):
        """填充列列表"""
        if not self.parent.column_info:
            return
        
        # 添加数值列
        for i, col in enumerate(self.parent.column_info['numeric_columns']):
            col_info = self.parent.column_info['column_details'][col]
            display_text = f"{col} ({col_info['dtype']}) - {col_info['non_null_count']}个有效值"
            self.listbox.insert(tk.END, display_text)
            
            # 如果列在当前选择中，则选中
            if col in self.selected_columns:
                self.listbox.selection_set(i)
    
    def update_stats(self):
        """更新统计信息"""
        total_cols = len(self.parent.column_info['numeric_columns'])
        selected_count = len(self.listbox.curselection())
        
        stats_text = f"总共{total_cols}个数值列，已选择{selected_count}列"
        if selected_count < 2:
            stats_text += " (至少需要选择2列进行相关性分析)"
            self.stats_label.config(foreground="red")
        else:
            self.stats_label.config(foreground="blue")
        
        self.stats_label.config(text=stats_text)
    
    def on_selection_change(self, event):
        """选择变化事件"""
        self.update_stats()
    
    def select_all(self):
        """全选"""
        self.listbox.selection_set(0, tk.END)
        self.update_stats()
    
    def deselect_all(self):
        """全不选"""
        self.listbox.selection_clear(0, tk.END)
        self.update_stats()
    
    def invert_selection(self):
        """反选"""
        for i in range(self.listbox.size()):
            if self.listbox.selection_includes(i):
                self.listbox.selection_clear(i)
            else:
                self.listbox.selection_set(i)
        self.update_stats()
    
    def apply_selection(self):
        """应用选择"""
        selected_indices = self.listbox.curselection()
        
        if len(selected_indices) < 2:
            messagebox.showerror("错误", "至少需要选择2列进行相关性分析")
            return
        
        # 获取选中的列名
        selected_columns = []
        for i in selected_indices:
            col_name = self.parent.column_info['numeric_columns'][i]
            selected_columns.append(col_name)
        
        # 应用到父窗口
        self.parent.apply_column_selection(selected_columns)
        
        # 关闭窗口
        self.window.destroy()
    
    def cancel(self):
        """取消"""
        self.window.destroy()

def create_gui():
    """创建并运行GUI应用"""
    app = CorrelationAnalysisGUI()
    return app

if __name__ == "__main__":
    app = create_gui()
    app.run()
