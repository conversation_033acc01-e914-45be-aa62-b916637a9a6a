#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线程安全的GUI更新工具模块

提供线程安全的GUI更新函数，避免"main thread is not in main loop"错误
"""

import tkinter as tk
from tkinter import messagebox
import threading
from functools import wraps


class ThreadSafeGUI:
    """线程安全的GUI更新工具类"""
    
    def __init__(self, root_widget):
        """
        初始化线程安全GUI工具
        
        Args:
            root_widget: 主窗口或根控件
        """
        self.root = root_widget
        self._is_destroyed = False
        
    def safe_call(self, func, *args, **kwargs):
        """
        线程安全地调用GUI函数
        
        Args:
            func: 要调用的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
        """
        def _call():
            try:
                if not self._is_destroyed:
                    func(*args, **kwargs)
            except tk.TclError:
                # GUI已销毁，忽略错误
                self._is_destroyed = True
        
        try:
            if threading.current_thread() == threading.main_thread():
                # 如果已经在主线程中，直接调用
                _call()
            else:
                # 在后台线程中，使用after调度到主线程
                self.root.after(0, _call)
        except tk.TclError:
            # GUI已销毁，忽略错误
            self._is_destroyed = True
    
    def safe_update_text(self, text_widget, message, append=True):
        """
        线程安全地更新文本控件
        
        Args:
            text_widget: 文本控件
            message: 要显示的消息
            append: 是否追加（True）还是替换（False）
        """
        def _update():
            try:
                if append:
                    text_widget.insert(tk.END, message + "\n")
                    text_widget.see(tk.END)
                else:
                    text_widget.delete(1.0, tk.END)
                    text_widget.insert(1.0, message)
                text_widget.update_idletasks()
            except tk.TclError:
                self._is_destroyed = True
        
        self.safe_call(_update)
    
    def safe_update_progress(self, progress_var, value):
        """
        线程安全地更新进度条
        
        Args:
            progress_var: 进度变量
            value: 进度值
        """
        self.safe_call(lambda: progress_var.set(value))
    
    def safe_update_status(self, status_var, message):
        """
        线程安全地更新状态文本
        
        Args:
            status_var: 状态变量
            message: 状态消息
        """
        self.safe_call(lambda: status_var.set(message))
    
    def safe_show_message(self, title, message, msg_type="info"):
        """
        线程安全地显示消息框
        
        Args:
            title: 消息框标题
            message: 消息内容
            msg_type: 消息类型 ("info", "warning", "error")
        """
        def _show():
            try:
                if msg_type == "info":
                    messagebox.showinfo(title, message)
                elif msg_type == "warning":
                    messagebox.showwarning(title, message)
                elif msg_type == "error":
                    messagebox.showerror(title, message)
            except tk.TclError:
                self._is_destroyed = True
        
        self.safe_call(_show)
    
    def safe_log_message(self, log_func, message):
        """
        线程安全地记录日志消息
        
        Args:
            log_func: 日志记录函数
            message: 日志消息
        """
        self.safe_call(log_func, message)
    
    def safe_update_widget_config(self, widget, **config):
        """
        线程安全地更新控件配置
        
        Args:
            widget: 要更新的控件
            **config: 配置参数
        """
        def _update():
            try:
                widget.config(**config)
            except tk.TclError:
                self._is_destroyed = True
        
        self.safe_call(_update)
    
    def safe_update_combobox_values(self, combobox, values):
        """
        线程安全地更新下拉框选项
        
        Args:
            combobox: 下拉框控件
            values: 新的选项列表
        """
        def _update():
            try:
                combobox['values'] = values
            except tk.TclError:
                self._is_destroyed = True
        
        self.safe_call(_update)
    
    def safe_set_variable(self, var, value):
        """
        线程安全地设置tkinter变量
        
        Args:
            var: tkinter变量
            value: 要设置的值
        """
        self.safe_call(lambda: var.set(value))
    
    def is_destroyed(self):
        """检查GUI是否已销毁"""
        return self._is_destroyed
    
    def mark_destroyed(self):
        """标记GUI已销毁"""
        self._is_destroyed = True


def thread_safe_gui_update(root_widget):
    """
    装饰器：使函数的GUI更新操作线程安全
    
    Args:
        root_widget: 主窗口或根控件
    
    Returns:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            def _call():
                try:
                    return func(*args, **kwargs)
                except tk.TclError:
                    # GUI已销毁，忽略错误
                    pass
            
            if threading.current_thread() == threading.main_thread():
                return _call()
            else:
                root_widget.after(0, _call)
        
        return wrapper
    return decorator


def create_progress_updater(progress_window, progress_text):
    """
    创建线程安全的进度更新函数
    
    Args:
        progress_window: 进度窗口
        progress_text: 进度文本控件
    
    Returns:
        进度更新函数
    """
    gui_updater = ThreadSafeGUI(progress_window)
    
    def update_progress(message):
        """线程安全的进度更新函数"""
        gui_updater.safe_update_text(progress_text, message, append=True)
    
    return update_progress


def safe_matplotlib_backend_switch():
    """
    线程安全地切换matplotlib后端到非交互式模式
    
    这个函数应该在后台线程开始时调用，以避免GUI冲突
    """
    try:
        import matplotlib
        current_backend = matplotlib.get_backend()
        
        # 如果当前不是Agg后端，切换到Agg
        if current_backend != 'Agg':
            matplotlib.use('Agg')
            
        return True
    except Exception:
        return False


# 使用示例和测试函数
def example_usage():
    """使用示例"""
    import threading
    import time
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("线程安全GUI测试")
    root.geometry("400x300")
    
    # 创建控件
    status_var = tk.StringVar(value="就绪")
    progress_var = tk.DoubleVar(value=0)
    
    status_label = tk.Label(root, textvariable=status_var)
    status_label.pack(pady=10)
    
    progress_bar = tk.Scale(root, from_=0, to=100, orient=tk.HORIZONTAL, variable=progress_var)
    progress_bar.pack(fill=tk.X, padx=20, pady=10)
    
    text_widget = tk.Text(root, height=10)
    text_widget.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
    
    # 创建线程安全GUI工具
    gui_updater = ThreadSafeGUI(root)
    
    def background_task():
        """后台任务示例"""
        for i in range(10):
            time.sleep(0.5)
            
            # 线程安全的GUI更新
            gui_updater.safe_update_status(status_var, f"处理中... {i+1}/10")
            gui_updater.safe_update_progress(progress_var, (i+1) * 10)
            gui_updater.safe_update_text(text_widget, f"完成步骤 {i+1}")
        
        gui_updater.safe_update_status(status_var, "完成")
        gui_updater.safe_show_message("完成", "后台任务已完成！", "info")
    
    # 启动后台线程
    thread = threading.Thread(target=background_task, daemon=True)
    thread.start()
    
    # 运行GUI
    root.mainloop()


if __name__ == "__main__":
    example_usage()
