#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示训练会话管理系统（已归档至 tools/demos/）
展示如何创建会话、保存结果、恢复会话并在GUI中查看
"""

import sys
from pathlib import Path
from datetime import datetime
import numpy as np
import pandas as pd

# 添加代码路径
sys.path.append(str(Path(__file__).parents[2] / 'code'))


def demonstrate_session_workflow():
    """演示完整的会话管理工作流程"""
    print("🎯 训练会话管理系统演示")
    print("=" * 60)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 步骤1：创建新会话
    print("📁 步骤1：创建新的训练会话")
    print("-" * 30)

    try:
        from session_utils import create_new_session, get_active_session_id

        session_name = f"演示会话_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        description = "演示训练会话管理系统的完整功能"

        session_id = create_new_session(session_name, description, auto_activate=True)

        if session_id:
            print(f"✅ 会话创建成功")
            print(f"   会话ID: {session_id}")
            print(f"   会话名称: {session_name}")
            print(f"   当前活动会话: {get_active_session_id()}")
        else:
            print("❌ 会话创建失败")
            return False

    except Exception as e:
        print(f"❌ 创建会话时出错: {e}")
        return False

    print()

    # 步骤2：模拟训练过程并保存结果
    print("🤖 步骤2：模拟模型训练并保存结果")
    print("-" * 30)

    try:
        from session_utils import save_to_session
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.linear_model import LogisticRegression
        from sklearn.datasets import make_classification
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score

        # 创建演示数据
        print("📊 生成演示数据集...")
        X, y = make_classification(
            n_samples=500, n_features=20, n_informative=15,
            n_redundant=5, n_classes=2, random_state=42
        )

        feature_names = [f'特征_{i+1}' for i in range(X.shape[1])]
        import pandas as pd
        X_df = pd.DataFrame(X, columns=feature_names)

        X_train, X_test, y_train, y_test = train_test_split(
            X_df, y, test_size=0.3, random_state=42
        )

        print(f"   训练集大小: {X_train.shape}")
        print(f"   测试集大小: {X_test.shape}")

        # 训练多个模型
        models = {
            'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42),
            'LogisticRegression': LogisticRegression(random_state=42, max_iter=1000)
        }

        for model_name, model in models.items():
            print(f"🔄 训练模型: {model_name}")

            model.fit(X_train, y_train)

            y_pred = model.predict(X_test)
            y_pred_proba = model.predict_proba(X_test)

            acc = accuracy_score(y_test, y_pred)
            print(f"   准确率: {acc:.4f}")

            # 保存模型结果到会话
            model_data = {
                'model': model,
                'y_true': y_test,
                'y_pred': y_pred,
                'y_pred_proba': y_pred_proba,
                'X_test': X_test,
                'feature_names': feature_names,
                'accuracy': acc
            }

            save_to_session(
                model_data, 'model', model_name,
                model_type='single',
                additional_data={'accuracy': acc}
            )

        print("✅ 训练与保存完成")

    except Exception as e:
        print(f"❌ 训练或保存失败: {e}")
        return False

    print()

    # 步骤3：恢复会话到缓存（演示）
    print("♻️ 步骤3：恢复会话到缓存（演示）")
    print("-" * 30)
    try:
        from session_utils import get_current_session, restore_session_to_cache
        s = get_current_session()
        if s:
            print("当前会话:", s)
            restore_session_to_cache(s['id'])
            print("✅ 已恢复到缓存")
        else:
            print("未找到当前会话")
    except Exception as e:
        print(f"❌ 恢复会话失败: {e}")
        return False

    print("🎉 演示完成")
    return True


if __name__ == '__main__':
    demonstrate_session_workflow()

