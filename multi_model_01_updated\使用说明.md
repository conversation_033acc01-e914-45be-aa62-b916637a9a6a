# 相关系数矩阵可视化工具 使用说明

## 概述
这是一个增强版的相关系数矩阵可视化工具，支持多种数据格式输入和用户友好的GUI界面。

## 主要功能

### 1. 支持的数据格式
- **CSV文件** (.csv) - 支持多种编码格式自动检测
- **Excel文件** (.xlsx, .xls) - 支持标准Excel格式

### 2. 输出格式
- **PDF格式** - 保留原始图层信息，适合在插图软件中进一步编辑
- **PNG格式** - 高质量预览图像，适合文档插入

### 3. 两种使用模式

#### GUI模式（推荐）
```bash
python drawpic.py --gui
```
启动图形用户界面，可以：
- 通过文件浏览器选择输入文件
- 设置输出目录和文件名前缀
- **手动选择要分析的数据列**（新功能）
- 实时预览数据信息和列选择状态
- 进度条显示处理状态

#### 命令行模式
```bash
# 使用默认数据文件
python drawpic.py

# 指定数据文件
python drawpic.py 数据文件路径.csv
```

## 使用步骤

### GUI模式使用步骤
1. 运行 `python drawpic.py --gui` 启动界面
2. 点击"浏览..."按钮选择数据文件（CSV或Excel）
3. 选择输出目录和设置文件名前缀
4. 点击"加载数据"查看数据预览和验证
5. **【新功能】** 点击"选择分析列"自定义要分析的数据列
   - 查看所有数值列及其统计信息
   - 选择或排除特定列进行分析
   - 支持全选、全不选、反选操作
6. 点击"生成图表"创建相关系数矩阵图

### 数据要求
- 至少包含2列数值数据
- 至少有3行有效数据
- 非数值列会自动被忽略

## 列选择功能（新增）

### 功能说明
新版本支持手动选择要进行相关性分析的数据列，让您可以：
- 排除ID列、序号列等不相关的数值列
- 专注于特定的变量进行分析
- 提高分析结果的针对性和可读性

### 使用方法
1. **加载数据后**，点击"选择分析列"按钮
2. 在弹出的列选择窗口中：
   - 查看所有数值列的详细信息（数据类型、有效值数量）
   - 默认选择所有数值列
   - 取消勾选不需要分析的列
   - 使用"全选"、"全不选"、"反选"快速操作
3. 确认选择后，数据预览将更新显示当前分析的列
4. 生成图表时将只分析选定的列

### 应用场景
- **包含ID列的数据**：排除无意义的ID、序号列
- **混合数据类型**：排除计数、分类编码等不适合相关性分析的列
- **大量变量数据**：选择感兴趣的核心变量进行分析
- **特定主题分析**：只分析某一类相关变量

### 注意事项
- 至少需要选择2列才能进行相关性分析
- 重新选择列后，数据预览会自动更新
- 列选择会影响最终生成的相关系数矩阵大小

## 输出文件

每次运行会生成4个文件：
- `文件名前缀_standard.pdf` - 标准正方形标记的相关系数矩阵
- `文件名前缀_standard.png` - 标准图的PNG预览
- `文件名前缀_variable_markers.pdf` - 根据相关系数大小使用不同标记的图
- `文件名前缀_variable_markers.png` - 变化标记图的PNG预览

## 图表说明

### 图表布局
- **左下三角**: 散点图显示，大小表示相关系数绝对值，颜色表示相关性方向
- **右上三角**: 显示具体的相关系数数值
- **对角线**: 显示变量名缩写
- **星号(*)**: 标识统计显著性 (p < 0.05)

### 颜色说明
- **蓝色渐变**: 表示相关系数从弱到强
- **黑色数字**: 有统计显著性的相关系数
- **红色数字**: 无统计显著性的相关系数

### 标记类型（变化标记图）
- **圆形**: 强相关性 (|r| > 0.75)
- **正方形**: 中等或弱相关性 (|r| ≤ 0.75)

## 文件结构

- `drawpic.py` - 核心分析和可视化功能
- `gui_interface.py` - GUI界面模块

## 技术特性

- **智能列选择**: 支持手动选择分析列，排除无关变量
- **自动数据预处理**: 自动筛选数值列，处理缺失值和类型转换
- **多编码支持**: CSV文件支持UTF-8、GBK等多种编码自动检测
- **高质量输出**: PDF输出DPI=300，保留图层信息便于编辑
- **内存优化**: 图表生成后自动释放内存
- **异步处理**: GUI界面使用多线程避免界面冻结
- **用户友好**: 列选择窗口提供详细的列信息和操作提示

## 故障排除

### 常见问题
1. **CSV文件乱码**: 工具会自动尝试多种编码格式
2. **数据验证失败**: 确保至少有2列数值数据和3行有效数据
3. **GUI无法启动**: 确保已安装tkinter模块（Python标准库）

### 错误信息
- "数据文件为空": 文件没有数据或格式错误
- "需要至少2列数值数据": 数据中数值列不足
- "不支持的文件格式": 只支持CSV和Excel格式

## 依赖库

- pandas: 数据处理
- numpy: 数值计算
- matplotlib: 图表绘制
- scipy: 统计计算
- tkinter: GUI界面（Python标准库）

---

**版本**: 增强版 v2.1 - 列选择功能版  
**更新时间**: 2024年  
**新增功能**: 手动选择数据分析列  
**作者**: AI助手
