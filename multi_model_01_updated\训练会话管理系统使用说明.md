# 训练会话管理系统使用说明

## 概述

训练会话管理系统是一个全新的文件组织和管理解决方案，旨在解决训练过程中文件保存混乱的问题。该系统为每次训练创建独立的文件夹，并能完整保存和恢复所有训练结果。

## 主要功能

### 1. 自动文件组织
- **独立会话目录**：每次训练创建独立的时间戳命名目录
- **标准化结构**：统一的子目录组织（models、plots、cache、config等）
- **元数据管理**：自动记录会话信息、文件列表和训练历史

### 2. 完整状态保存
- **模型文件**：训练好的模型及相关数据
- **图片文件**：所有生成的可视化图表
- **缓存数据**：模型结果、特征名称等
- **配置文件**：训练参数和设置
- **日志文件**：训练过程记录

### 3. 便捷恢复功能
- **一键恢复**：将历史会话数据恢复到项目缓存
- **状态重现**：完整恢复之前的训练状态
- **数据完整性**：确保所有相关文件都能正确加载

## 目录结构

每个训练会话的目录结构如下：

```
training_sessions/
└── 20250807_023615/                    # 会话ID（时间戳）
    ├── session_metadata.json          # 会话元数据
    ├── models/                         # 模型文件
    │   ├── RandomForest_single_*.joblib
    │   └── XGBoost_ensemble_*.joblib
    ├── plots/                          # 图片文件
    │   ├── single_model/              # 单模型图片
    │   ├── comparison/                # 模型比较图片
    │   ├── ensemble/                  # 集成学习图片
    │   └── shap/                      # SHAP解释图片
    ├── cache/                         # 缓存数据
    │   ├── model_results.joblib
    │   └── feature_names.joblib
    ├── config/                        # 配置文件
    │   └── training_config.json
    ├── logs/                          # 日志文件
    ├── reports/                       # 报告文件
    └── data_info/                     # 数据信息
```

## GUI界面使用

### 1. 会话菜单
在主界面菜单栏中新增了"会话"菜单，包含以下功能：

- **会话管理器**：打开完整的会话管理界面
- **新建会话**：快速创建新的训练会话
- **自动创建会话**：为当前训练自动创建会话

### 2. 会话管理器界面
会话管理器提供了完整的会话管理功能：

#### 工具栏功能
- **新建会话**：创建新的训练会话
- **激活会话**：设置选中会话为当前活动会话
- **恢复到缓存**：将选中会话的数据恢复到项目缓存
- **导出报告**：生成会话的HTML报告
- **删除会话**：删除选中的会话（不可撤销）
- **刷新**：刷新会话列表
- **统计信息**：显示所有会话的统计信息

#### 会话列表
显示所有可用的训练会话，包括：
- 会话ID（时间戳格式）
- 会话名称
- 状态（created、completed等）
- 创建时间
- 模型数量
- 图片数量

#### 详情面板
选中会话后显示详细信息：
- 基本信息（名称、描述、时间等）
- 统计信息（文件数量）
- 模型列表（包括文件存在状态）
- 图片分类统计

### 3. 自动会话创建
系统会在以下情况自动创建会话：
- 开始训练时没有活动会话
- 基于当前数据文件名称生成会话名称
- 自动激活新创建的会话

## 编程接口使用

### 1. 基本操作

```python
from session_utils import (
    create_new_session, activate_session, 
    save_to_session, load_from_session
)

# 创建新会话
session_id = create_new_session("我的训练会话", "描述信息")

# 激活会话
activate_session(session_id)

# 保存数据到会话
save_to_session(model, 'model', 'RandomForest', model_type='single')
save_to_session(fig, 'plot', 'roc_curve', plot_type='single_model')
save_to_session(cache_data, 'cache', 'results')
save_to_session(config, 'config', 'training_params')

# 从会话加载数据
data = load_from_session(session_id, 'all')
```

### 2. 会话管理

```python
from session_utils import (
    list_all_sessions, get_session_summary,
    restore_session_to_cache, delete_session
)

# 列出所有会话
sessions = list_all_sessions()

# 获取会话摘要
summary = get_session_summary(session_id)

# 恢复会话到缓存
restore_session_to_cache(session_id)

# 删除会话
delete_session(session_id, confirm=True)
```

### 3. 装饰器使用

```python
from session_utils import with_session

@with_session("实验会话", "自动创建的实验会话")
def run_experiment():
    # 在这个函数中的所有保存操作都会自动使用临时会话
    train_model()
    generate_plots()
    # 函数结束后会话状态自动更新为completed
```

## 兼容性说明

### 1. 向后兼容
- 系统保持与现有代码的完全兼容
- 如果会话管理不可用，自动回退到传统保存方式
- 现有的缓存和输出目录继续正常工作

### 2. 迁移功能
```python
from session_utils import migrate_legacy_files, cleanup_old_files

# 迁移旧文件到会话系统
migrate_legacy_files()

# 清理旧文件（可选，需要确认）
cleanup_old_files(confirm=True)
```

## 最佳实践

### 1. 会话命名
- 使用描述性的会话名称
- 包含数据集名称和实验目的
- 例如："iris_分类实验_20250807"

### 2. 会话管理
- 定期清理不需要的会话
- 为重要实验导出报告
- 使用会话描述记录实验目的和结果

### 3. 数据恢复
- 在需要重现结果时使用"恢复到缓存"功能
- 恢复前备份当前缓存数据
- 验证恢复的数据完整性

## 故障排除

### 1. 会话创建失败
- 检查磁盘空间是否充足
- 确认training_sessions目录的写入权限
- 查看日志文件了解详细错误信息

### 2. 文件保存失败
- 检查会话目录是否存在
- 确认有足够的磁盘空间
- 检查文件路径是否包含非法字符

### 3. 会话加载失败
- 确认会话ID正确
- 检查session_metadata.json文件是否完整
- 验证相关文件是否存在

## 技术细节

### 1. 文件格式
- **模型文件**：使用joblib格式保存
- **图片文件**：PNG格式，300 DPI
- **配置文件**：JSON格式，UTF-8编码
- **元数据**：JSON格式，包含完整的文件索引

### 2. 性能优化
- 异步文件操作避免界面卡顿
- 智能缓存减少重复加载
- 压缩存储节省磁盘空间

### 3. 安全性
- 文件路径验证防止目录遍历
- 权限检查确保安全访问
- 错误处理避免数据丢失

## 更新日志

### v1.0.0 (2025-08-07)
- 初始版本发布
- 完整的会话管理功能
- GUI界面集成
- 自动文件组织
- 数据恢复功能
- 向后兼容支持

---

如有问题或建议，请查看项目文档或联系开发团队。
