# 多模型集成机器学习平台 - 重构版

一个功能完整的机器学习平台，支持多种算法、自动化训练、可视化分析和报告生成。

## 🚀 快速启动

### 方法1：简化GUI模式（推荐）
```bash
cd refactored_ml_platform
python start.py
```

### 方法2：CLI命令行模式（功能最完整）
```bash
cd refactored_ml_platform

# 查看帮助
python -m cli.main --help

# 训练所有模型
python -m cli.main --data data.csv --target target --mode train --model All

# 一键完整分析
python -m cli.pipeline --data data.csv --target target --strategy balanced
```

## 🏗️ 架构设计

### 目录结构

```
refactored_ml_platform/
├── gui/                        # GUI模块
│   ├── core/                   # 核心基础模块
│   │   ├── base_gui.py        # GUI基础类
│   │   ├── event_manager.py   # 事件管理器
│   │   ├── config_manager.py  # 配置管理
│   │   ├── component_factory.py # 组件工厂
│   │   └── utils.py          # 通用工具
│   ├── components/            # 可复用组件
│   │   ├── data_widgets.py   # 数据相关控件
│   │   ├── chart_widgets.py  # 图表控件
│   │   ├── progress_widgets.py # 进度控件
│   │   └── dialog_widgets.py # 对话框控件
│   ├── modules/              # 功能模块
│   │   ├── data_management/  # 数据管理模块
│   │   ├── model_training/   # 模型训练模块
│   │   ├── visualization/    # 可视化模块
│   │   ├── ensemble/         # 集成学习模块
│   │   └── session/          # 会话管理模块
│   └── layouts/              # 界面布局
├── core/                     # 核心业务逻辑
├── utils/                    # 通用工具
├── config/                   # 配置文件
├── logs/                     # 日志文件
└── main.py                   # 主入口
```

### 核心设计原则

1. **单一职责原则**：每个模块只负责一个特定功能
2. **依赖倒置原则**：通过抽象接口实现解耦
3. **开闭原则**：对扩展开放，对修改关闭
4. **事件驱动架构**：模块间通过事件通信，避免直接依赖

## 🚀 主要改进

### 1. 模块化设计
- **原始版本**：单个文件3000+行代码
- **重构版本**：每个模块100-500行，职责清晰

### 2. 事件驱动架构
```python
# 模块间通过事件通信
event_manager.publish(EventTypes.DATA_LOADED, data)
event_manager.subscribe(EventTypes.DATA_LOADED, callback)
```

### 3. 统一配置管理
```python
# 集中管理所有配置
config = get_gui_config()
window_size = config.get('window.size', [1400, 900])
```

### 4. 组件工厂模式
```python
# 统一创建标准化组件
factory = get_component_factory()
button = factory.create_button(parent, text="确定", style='primary')
```

### 5. 可复用组件
- 数据表格组件：`DataTableWidget`
- 文件选择器：`FileSelector`
- 进度条组件：`ProgressWidget`
- 日志查看器：`LogViewer`

## 📋 功能模块

### 数据管理模块 (`data_management/`)
- **数据加载**：支持CSV文件加载和验证
- **数据预览**：表格显示和基本统计信息
- **数据验证**：数据质量检查和报告
- **数据预处理**：缺失值处理、特征缩放等

### 模型训练模块 (`model_training/`)
- **模型选择**：支持多种机器学习算法
- **训练配置**：参数设置和超参数调优
- **训练监控**：实时进度和日志显示
- **结果管理**：模型保存和加载

### 可视化模块 (`visualization/`)
- **图表管理**：统一的图表创建和管理
- **SHAP分析**：模型可解释性分析
- **模型比较**：多模型性能对比
- **报告生成**：自动生成分析报告

### 集成学习模块 (`ensemble/`)
- **模型选择**：智能选择最优模型组合
- **集成策略**：多种集成方法支持
- **性能评估**：集成模型效果评估

### 会话管理模块 (`session/`)
- **会话创建**：创建和管理训练会话
- **状态保存**：持久化训练状态
- **会话恢复**：恢复历史训练会话

## 🔧 技术栈

- **GUI框架**：tkinter + ttk
- **事件系统**：自定义事件管理器
- **配置管理**：JSON配置文件
- **日志系统**：Python logging
- **数据处理**：pandas, numpy
- **机器学习**：scikit-learn, xgboost等

## 📦 安装和使用

### 环境要求
- Python 3.7+
- 依赖包见 `requirements.txt`

### 启动应用
```bash
cd refactored_ml_platform
python main.py
```

### 开发模式
```bash
# 启用调试日志
export LOG_LEVEL=DEBUG
python main.py
```

## 🔄 与原版本的对比

| 方面 | 原版本 | 重构版本 |
|------|--------|----------|
| 代码结构 | 单体文件 | 模块化设计 |
| 代码行数 | 3000+行/文件 | 100-500行/模块 |
| 模块耦合 | 高耦合 | 事件驱动解耦 |
| 可维护性 | 困难 | 容易 |
| 可扩展性 | 受限 | 高度可扩展 |
| 测试便利性 | 困难 | 模块独立测试 |
| 团队协作 | 冲突频繁 | 并行开发 |

## 🚧 开发状态

### 已完成
- [x] 核心架构设计
- [x] 基础模块实现
- [x] 数据管理模块
- [x] 可复用组件库
- [x] 事件驱动系统
- [x] 配置管理系统

### 进行中
- [ ] 模型训练模块
- [ ] 可视化模块
- [ ] 集成学习模块
- [ ] 会话管理模块

### 计划中
- [ ] 单元测试覆盖
- [ ] 性能优化
- [ ] 文档完善
- [ ] 部署脚本

## 📝 开发指南

### 添加新模块
1. 继承 `BaseGUI` 类
2. 实现 `_setup_ui()` 方法
3. 使用事件系统进行通信
4. 遵循单一职责原则

### 创建新组件
1. 使用 `ComponentFactory` 创建标准组件
2. 注册到组件容器中
3. 提供清晰的API接口

### 事件处理
```python
# 订阅事件
self.subscribe_event(EventTypes.DATA_LOADED, self._on_data_loaded)

# 发布事件
self.publish_event(EventTypes.DATA_LOADED, data)
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 创建 Issue
- 发送邮件
- 项目讨论区

---

**注意**：这是重构版本，原始代码仍保留在父目录中作为参考。
