#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法模块包
包含所有机器学习算法的实现
"""

# 导入核心算法模块
from .model_training import (
    MODEL_TRAINERS,
    train_decision_tree, train_random_forest, train_xgboost,
    train_lightgbm, train_catboost, train_logistic,
    train_svm, train_knn, train_naive_bayes, train_neural_net
)
from .data_preprocessing import (
    DataPreprocessor, load_and_preprocess_data,
    load_and_clean_data, preprocess_data
)
from .hyperparameter_tuning import tune_model
from .model_ensemble import run_ensemble_pipeline, EnsembleModel
from .external_validation import run_external_validation, ExternalValidator
from .multi_data_ensemble import run_multi_data_ensemble_pipeline, MultiDataEnsemble
from .config import (
    MODEL_NAMES, MODEL_DISPLAY_NAMES, HYPERPARAMETER_GRIDS,
    RANDOM_SEED, set_global_seed
)
from .logger import get_logger, setup_logger

__all__ = [
    # 模型训练
    'MODEL_TRAINERS',
    'train_decision_tree', 'train_random_forest', 'train_xgboost',
    'train_lightgbm', 'train_catboost', 'train_logistic',
    'train_svm', 'train_knn', 'train_naive_bayes', 'train_neural_net',

    # 数据预处理
    'DataPreprocessor', 'load_and_preprocess_data',
    'load_and_clean_data', 'preprocess_data',

    # 超参数调优
    'tune_model',

    # 集成学习
    'run_ensemble_pipeline', 'EnsembleModel',

    # 外部验证
    'run_external_validation', 'ExternalValidator',

    # 多数据源集成
    'run_multi_data_ensemble_pipeline', 'MultiDataEnsemble',

    # 配置和工具
    'MODEL_NAMES', 'MODEL_DISPLAY_NAMES', 'HYPERPARAMETER_GRIDS',
    'RANDOM_SEED', 'set_global_seed',
    'get_logger', 'setup_logger'
]
