#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法模块配置文件
集中管理所有算法相关的配置参数
"""

import os
from pathlib import Path
import matplotlib.font_manager as fm
import platform

# 基础路径配置
PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DATA_PATH = PROJECT_ROOT / 'data'
OUTPUT_PATH = PROJECT_ROOT / 'output'
CACHE_PATH = PROJECT_ROOT / 'cache'
MODELS_PATH = PROJECT_ROOT / 'models'
ENSEMBLE_PATH = PROJECT_ROOT / 'ensemble'
MULTI_DATA_CACHE_PATH = PROJECT_ROOT / 'multi_data_cache'
LOG_PATH = PROJECT_ROOT / 'logs'
SESSIONS_PATH = PROJECT_ROOT / 'training_sessions'

# 自动创建必要的目录
for path in [DATA_PATH, OUTPUT_PATH, CACHE_PATH, MODELS_PATH, ENSEMBLE_PATH, 
             MULTI_DATA_CACHE_PATH, LOG_PATH, SESSIONS_PATH]:
    path.mkdir(parents=True, exist_ok=True)

# 随机种子设置
RANDOM_SEED = 42

# 数据预处理配置
DATA_CONFIG = {
    'test_size': 0.2,
    'random_state': RANDOM_SEED,
    'default_scaling': 'standard',
    'feature_selection_threshold': 0.01,
    'feature_selection_k': 10,
    'cv_folds': 5
}

# 模型训练配置
TRAINING_CONFIG = {
    'cv_folds': 5,
    'n_jobs': -1,
    'verbose': 1
}

# 模型名称映射
MODEL_NAMES = [
    'DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
    'Logistic', 'SVM', 'KNN', 'NaiveBayes', 'NeuralNet'
]

MODEL_DISPLAY_NAMES = {
    'DecisionTree': '决策树',
    'RandomForest': '随机森林',
    'XGBoost': 'XGBoost',
    'LightGBM': 'LightGBM',
    'CatBoost': 'CatBoost',
    'Logistic': '逻辑回归',
    'SVM': '支持向量机',
    'KNN': 'K近邻',
    'NaiveBayes': '朴素贝叶斯',
    'NeuralNet': '神经网络'
}

# 模型超参数网格
HYPERPARAMETER_GRIDS = {
    'DecisionTree': {
        'max_depth': [3, 5, 7, 10, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4],
        'criterion': ['gini', 'entropy']
    },
    'RandomForest': {
        'n_estimators': [50, 100, 200],
        'max_depth': [5, 10, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4]
    },
    'XGBoost': {
        'n_estimators': [50, 100, 200],
        'learning_rate': [0.01, 0.1, 0.3],
        'max_depth': [3, 5, 7],
        'min_child_weight': [1, 3, 5],
        'subsample': [0.7, 0.8, 0.9]
    },
    'LightGBM': {
        'n_estimators': [50, 100, 200],
        'learning_rate': [0.01, 0.1, 0.3],
        'max_depth': [3, 5, 7, -1],
        'num_leaves': [31, 50, 100],
        'subsample': [0.7, 0.8, 0.9]
    },
    'CatBoost': {
        'iterations': [50, 100, 200],
        'learning_rate': [0.01, 0.1, 0.3],
        'depth': [4, 6, 8],
        'l2_leaf_reg': [1, 3, 5, 7]
    },
    'Logistic': {
        'C': [0.001, 0.01, 0.1, 1, 10, 100],
        'penalty': ['l1', 'l2', 'elasticnet', None],
        'solver': ['newton-cg', 'lbfgs', 'liblinear', 'sag', 'saga']
    },
    'SVM': {
        'C': [0.1, 1, 10, 100],
        'gamma': ['scale', 'auto', 0.1, 0.01],
        'kernel': ['rbf', 'linear', 'poly', 'sigmoid']
    },
    'KNN': {
        'n_neighbors': [3, 5, 7, 9, 11],
        'weights': ['uniform', 'distance'],
        'algorithm': ['auto', 'ball_tree', 'kd_tree', 'brute'],
        'p': [1, 2]
    },
    'NaiveBayes': {
        'var_smoothing': [1e-9, 1e-8, 1e-7, 1e-6]
    },
    'NeuralNet': {
        'hidden_layer_sizes': [(50,), (100,), (50, 50), (100, 50)],
        'activation': ['relu', 'tanh', 'logistic'],
        'alpha': [0.0001, 0.001, 0.01],
        'learning_rate': ['constant', 'adaptive', 'invscaling']
    }
}

# 绘图配置
PLOT_CONFIG = {
    'dpi': 150,
    'figsize': (10, 8),
    'save_format': 'png',
    'font_family': ['DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
}

# 集成学习配置
ENSEMBLE_CONFIG = {
    'voting_methods': ['hard', 'soft'],
    'stacking_cv': 5,
    'weighted_methods': ['performance', 'diversity', 'equal'],
    'dynamic_selection_methods': ['confidence', 'local_accuracy'],
    'ensemble_methods': ['voting', 'bagging', 'boosting', 'stacking'],
    'default_ensemble_methods': ['voting', 'stacking'],
    'n_estimators': 10,
    'enable_shap': True,
    'save_results': True
}

# 多数据源集成配置
MULTI_DATA_CONFIG = {
    'ensemble_methods': ['voting', 'stacking', 'weighted'],
    'ensemble_data_strategies': ['unified', 'original', 'combined']
}

def set_global_seed(seed: int = RANDOM_SEED):
    """统一设置全局随机种子"""
    try:
        import random, numpy as np, os
        random.seed(seed)
        np.random.seed(seed)
        os.environ['PYTHONHASHSEED'] = str(seed)
        
        # 如果有PyTorch，也设置其随机种子
        try:
            import torch
            torch.manual_seed(seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed(seed)
                torch.cuda.manual_seed_all(seed)
        except ImportError:
            pass
            
    except Exception as e:
        print(f"设置随机种子时出错: {e}")

# 初始化随机种子
set_global_seed(RANDOM_SEED)
