#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理模块
提供数据加载、清洗、预处理等功能
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.feature_selection import RFECV, SelectKBest, f_classif, mutual_info_classif, VarianceThreshold
from sklearn.ensemble import RandomForestClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.linear_model import LogisticRegression
import os
import joblib
from joblib import dump, load
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score
import re

# 导入配置
try:
    from .config import CACHE_PATH, OUTPUT_PATH, RANDOM_SEED
    from .logger import get_logger
except ImportError:
    # 使用默认配置
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    CACHE_PATH = PROJECT_ROOT / 'cache'
    OUTPUT_PATH = PROJECT_ROOT / 'output'
    RANDOM_SEED = 42
    
    # 创建简单的日志记录器
    import logging
    def get_logger(name):
        return logging.getLogger(name)

# 确保缓存目录存在
CACHE_PATH.mkdir(parents=True, exist_ok=True)
OUTPUT_PATH.mkdir(parents=True, exist_ok=True)

logger = get_logger(__name__)

def _perform_basic_validation(df):
    """
    执行基本的数据验证
    
    Args:
        df: 数据框
        
    Returns:
        dict: 验证结果
    """
    validation_results = {
        'shape': df.shape,
        'missing_values': {
            'total': df.isnull().sum().sum(),
            'by_column': df.isnull().sum().to_dict(),
            'percentage': (df.isnull().sum() / len(df) * 100).to_dict()
        },
        'duplicates': {
            'count': df.duplicated().sum(),
            'percentage': (df.duplicated().sum() / len(df)) * 100
        },
        'data_types': df.dtypes.to_dict(),
        'numeric_columns': df.select_dtypes(include=[np.number]).columns.tolist(),
        'categorical_columns': df.select_dtypes(include=['object', 'category']).columns.tolist(),
        'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024**2
    }
    
    # 检查异常值（仅对数值列）
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    outliers_info = {}
    
    for col in numeric_cols:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)][col]
        outliers_info[col] = {
            'count': len(outliers),
            'percentage': (len(outliers) / len(df)) * 100,
            'bounds': {'lower': lower_bound, 'upper': upper_bound}
        }
    
    validation_results['outliers'] = outliers_info
    return validation_results

def _print_validation_summary(validation_results):
    """
    打印验证结果摘要
    
    Args:
        validation_results: 验证结果字典
    """
    if not validation_results:
        return
    
    print("\n" + "="*50)
    print("数据质量验证摘要")
    print("="*50)
    
    # 基本信息
    print(f"数据集形状: {validation_results['shape']}")
    print(f"内存使用: {validation_results['memory_usage_mb']:.2f} MB")
    
    # 缺失值
    missing = validation_results['missing_values']
    print(f"\n缺失值统计:")
    print(f"  总缺失值: {missing['total']}")
    if missing['total'] > 0:
        print("  各列缺失值:")
        for col, count in missing['by_column'].items():
            if count > 0:
                percentage = missing['percentage'][col]
                print(f"    {col}: {count} ({percentage:.1f}%)")
    else:
        print("  ✓ 无缺失值")
    
    # 重复值
    duplicates = validation_results['duplicates']
    print(f"\n重复值统计:")
    print(f"  重复行数: {duplicates['count']} ({duplicates['percentage']:.1f}%)")
    
    # 数据类型
    print(f"\n数据类型:")
    print(f"  数值列: {len(validation_results['numeric_columns'])}")
    print(f"  分类列: {len(validation_results['categorical_columns'])}")
    
    # 异常值摘要
    outliers = validation_results['outliers']
    total_outliers = sum(info['count'] for info in outliers.values())
    print(f"\n异常值统计:")
    print(f"  总异常值: {total_outliers}")
    if total_outliers > 0:
        print("  各列异常值:")
        for col, info in outliers.items():
            if info['count'] > 0:
                print(f"    {col}: {info['count']} ({info['percentage']:.1f}%)")

def clean_col_names(df):
    """
    清理列名，移除特殊字符并转换为小写
    
    Args:
        df: 数据框
        
    Returns:
        pd.DataFrame: 清理后的数据框
    """
    df = df.copy()
    
    # 清理列名：移除特殊字符，转换为小写，用下划线替换空格
    new_columns = []
    for col in df.columns:
        # 移除特殊字符，只保留字母、数字和下划线
        clean_col = re.sub(r'[^A-Za-z0-9_]+', '', str(col).strip())
        # 转换为小写
        clean_col = clean_col.lower()
        # 如果列名为空，使用原始列名
        if not clean_col:
            clean_col = f"col_{len(new_columns)}"
        new_columns.append(clean_col)
    
    df.columns = new_columns
    logger.info(f"列名清理完成，共处理 {len(df.columns)} 列")
    return df

def detect_target_column(df, target_col_name=None):
    """
    检测目标列
    
    Args:
        df: 数据框
        target_col_name: 指定的目标列名
        
    Returns:
        str: 目标列名
    """
    if target_col_name and target_col_name in df.columns:
        return target_col_name
    
    # 自动检测可能的目标列
    possible_targets = ['label', 'target', 'class', 'y', 'outcome']
    
    for col in possible_targets:
        if col in df.columns:
            logger.info(f"自动检测到目标列: {col}")
            return col
    
    # 如果没有找到，使用最后一列
    target_col = df.columns[-1]
    logger.warning(f"未找到明确的目标列，使用最后一列: {target_col}")
    return target_col

def load_and_clean_data(data_path, target_col_name=None):
    """
    加载并清理数据
    
    Args:
        data_path: 数据文件路径
        target_col_name: 目标列名
        
    Returns:
        tuple: (数据框, 目标列名)
    """
    try:
        # 尝试不同的编码方式加载数据
        try:
            df = pd.read_csv(data_path, encoding='utf-8')
        except UnicodeDecodeError:
            df = pd.read_csv(data_path, encoding='gbk')
        except Exception:
            df = pd.read_csv(data_path, encoding='latin-1')
        
        logger.info(f"成功加载数据: {data_path}, 形状: {df.shape}")
        
        # 清理列名
        df = clean_col_names(df)
        
        # 检测目标列
        if target_col_name:
            target_col_name = re.sub(r'[^A-Za-z0-9_]+', '', target_col_name).lower()
        
        target_col = detect_target_column(df, target_col_name)
        
        if target_col not in df.columns:
            raise ValueError(f"无法找到目标列: {target_col}")
        
        return df, target_col
        
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        raise

def preprocess_data(X, y, test_size=0.2, random_state=42, scaling_method='standard'):
    """
    数据预处理（标准化、归一化）

    Args:
        X: 特征数据
        y: 目标变量
        test_size: 测试集比例
        random_state: 随机种子
        scaling_method: 缩放方法 ('standard', 'minmax', 'robust', 'none')

    Returns:
        tuple: (X_train, X_test, y_train, y_test, scaler)
    """
    # 对于小数据集，动态调整测试集比例，确保每个类别至少有2个样本
    unique_classes = len(np.unique(y))
    min_samples_per_class = 2
    min_test_samples = unique_classes * min_samples_per_class

    # 如果数据集太小，则减小测试集比例
    if len(y) * test_size < min_test_samples:
        adjusted_test_size = min(max(min_test_samples / len(y), 0.1), 0.5)
        logger.warning(f"数据集较小，调整测试集比例为: {adjusted_test_size:.2f}")
        test_size = adjusted_test_size

    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )

    # 数据缩放
    scaler = None
    if scaling_method == 'standard':
        scaler = StandardScaler()
    elif scaling_method == 'minmax':
        scaler = MinMaxScaler()
    elif scaling_method == 'robust':
        scaler = RobustScaler()
    elif scaling_method == 'none':
        logger.info("跳过数据缩放")
        return X_train, X_test, y_train, y_test, None

    if scaler:
        X_train = scaler.fit_transform(X_train)
        X_test = scaler.transform(X_test)
        logger.info(f"应用 {scaling_method} 缩放")

    return X_train, X_test, y_train, y_test, scaler

def load_and_preprocess_data(data_path, target_col_name=None, test_size=0.2,
                           scaling_method='standard', feature_selection=False,
                           feature_selection_k=10, validation=True):
    """
    加载并预处理数据的完整流程

    Args:
        data_path: 数据文件路径
        target_col_name: 目标列名
        test_size: 测试集比例
        scaling_method: 缩放方法
        feature_selection: 是否进行特征选择
        feature_selection_k: 特征选择数量
        validation: 是否进行数据验证

    Returns:
        dict: 包含预处理后数据的字典
    """
    logger.info(f"开始加载和预处理数据: {data_path}")

    # 加载数据
    df, target_col = load_and_clean_data(data_path, target_col_name)

    # 数据验证
    if validation:
        validation_results = _perform_basic_validation(df)
        _print_validation_summary(validation_results)

    # 分离特征和目标变量
    X = df.drop(columns=[target_col])
    y = df[target_col]

    # 确保二分类标签
    y, label_mapping = ensure_binary_labels(y)

    # 保存特征名称
    feature_names = X.columns.tolist()

    # 数据预处理
    X_train, X_test, y_train, y_test, scaler = preprocess_data(
        X, y, test_size=test_size, random_state=RANDOM_SEED,
        scaling_method=scaling_method
    )

    # 特征选择
    if feature_selection and feature_selection_k > 0:
        selector = SelectKBest(score_func=f_classif, k=min(feature_selection_k, X_train.shape[1]))
        X_train = selector.fit_transform(X_train, y_train)
        X_test = selector.transform(X_test)

        # 更新特征名称
        selected_features = selector.get_support(indices=True)
        feature_names = [feature_names[i] for i in selected_features]
        logger.info(f"特征选择完成，保留 {len(feature_names)} 个特征")

    # 转换为DataFrame（保持特征名称）
    X_train = pd.DataFrame(X_train, columns=feature_names)
    X_test = pd.DataFrame(X_test, columns=feature_names)

    result = {
        'X_train': X_train,
        'X_test': X_test,
        'y_train': y_train,
        'y_test': y_test,
        'feature_names': feature_names,
        'target_col': target_col,
        'label_mapping': label_mapping,
        'scaler': scaler,
        'data_path': data_path
    }

    logger.info(f"数据预处理完成 - 训练集: {X_train.shape}, 测试集: {X_test.shape}")
    return result

def ensure_binary_labels(y):
    """
    确保标签是二分类格式 (0, 1)

    Args:
        y: 目标变量

    Returns:
        tuple: (转换后的标签, 标签映射)
    """
    unique_labels = y.unique()

    if len(unique_labels) != 2:
        raise ValueError(f"目标变量必须是二分类，当前有 {len(unique_labels)} 个类别: {unique_labels}")

    # 创建标签映射
    label_mapping = {unique_labels[0]: 0, unique_labels[1]: 1}

    # 转换标签
    y_binary = y.map(label_mapping)

    logger.info(f"标签映射: {label_mapping}")
    return y_binary, label_mapping

class DataPreprocessor:
    """
    数据预处理器类
    提供完整的数据预处理功能
    """

    def __init__(self):
        """初始化数据预处理器"""
        self.scaler = None
        self.feature_selector = None
        self.feature_names = None
        self.label_mapping = None
        self.logger = get_logger(self.__class__.__name__)

    def fit_transform(self, X, y, scaling_method='standard', feature_selection=False,
                     feature_selection_k=10):
        """
        拟合并转换训练数据

        Args:
            X: 特征数据
            y: 目标变量
            scaling_method: 缩放方法
            feature_selection: 是否进行特征选择
            feature_selection_k: 特征选择数量

        Returns:
            tuple: (转换后的X, 转换后的y)
        """
        # 保存特征名称
        if hasattr(X, 'columns'):
            self.feature_names = X.columns.tolist()
        else:
            self.feature_names = [f'feature_{i}' for i in range(X.shape[1])]

        # 确保二分类标签
        y_transformed, self.label_mapping = ensure_binary_labels(y)

        # 数据缩放
        if scaling_method != 'none':
            if scaling_method == 'standard':
                self.scaler = StandardScaler()
            elif scaling_method == 'minmax':
                self.scaler = MinMaxScaler()
            elif scaling_method == 'robust':
                self.scaler = RobustScaler()

            X_transformed = self.scaler.fit_transform(X)
            self.logger.info(f"应用 {scaling_method} 缩放")
        else:
            X_transformed = X.values if hasattr(X, 'values') else X

        # 特征选择
        if feature_selection and feature_selection_k > 0:
            self.feature_selector = SelectKBest(
                score_func=f_classif,
                k=min(feature_selection_k, X_transformed.shape[1])
            )
            X_transformed = self.feature_selector.fit_transform(X_transformed, y_transformed)

            # 更新特征名称
            selected_features = self.feature_selector.get_support(indices=True)
            self.feature_names = [self.feature_names[i] for i in selected_features]
            self.logger.info(f"特征选择完成，保留 {len(self.feature_names)} 个特征")

        # 转换为DataFrame
        X_transformed = pd.DataFrame(X_transformed, columns=self.feature_names)

        return X_transformed, y_transformed

    def transform(self, X):
        """
        转换测试数据

        Args:
            X: 特征数据

        Returns:
            pd.DataFrame: 转换后的数据
        """
        X_transformed = X.values if hasattr(X, 'values') else X

        # 应用缩放
        if self.scaler is not None:
            X_transformed = self.scaler.transform(X_transformed)

        # 应用特征选择
        if self.feature_selector is not None:
            X_transformed = self.feature_selector.transform(X_transformed)

        # 转换为DataFrame
        X_transformed = pd.DataFrame(X_transformed, columns=self.feature_names)

        return X_transformed

    def save(self, filepath):
        """
        保存预处理器

        Args:
            filepath: 保存路径
        """
        preprocessor_data = {
            'scaler': self.scaler,
            'feature_selector': self.feature_selector,
            'feature_names': self.feature_names,
            'label_mapping': self.label_mapping
        }

        dump(preprocessor_data, filepath)
        self.logger.info(f"预处理器已保存到: {filepath}")

    def load(self, filepath):
        """
        加载预处理器

        Args:
            filepath: 文件路径
        """
        preprocessor_data = load(filepath)

        self.scaler = preprocessor_data['scaler']
        self.feature_selector = preprocessor_data['feature_selector']
        self.feature_names = preprocessor_data['feature_names']
        self.label_mapping = preprocessor_data['label_mapping']

        self.logger.info(f"预处理器已从 {filepath} 加载")
