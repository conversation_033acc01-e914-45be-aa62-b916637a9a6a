#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外部验证模块
提供模型外部验证功能
"""

import pandas as pd
import numpy as np
from sklearn.metrics import (
    accuracy_score, roc_auc_score, precision_score, 
    recall_score, f1_score, confusion_matrix, classification_report
)
import joblib
from pathlib import Path
import time
import json

# 导入配置
try:
    from .config import OUTPUT_PATH, RANDOM_SEED
    from .logger import get_logger
    from .data_preprocessing import load_and_clean_data, DataPreprocessor
except ImportError:
    # 使用默认配置
    import os
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    OUTPUT_PATH = PROJECT_ROOT / 'output'
    OUTPUT_PATH.mkdir(parents=True, exist_ok=True)
    RANDOM_SEED = 42
    
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    def load_and_clean_data(data_path, target_col_name=None):
        df = pd.read_csv(data_path)
        target_col = df.columns[-1]  # 假设最后一列是目标变量
        return df, target_col
    
    class DataPreprocessor:
        def transform(self, X):
            return X

logger = get_logger(__name__)

class ExternalValidator:
    """
    外部验证器类
    """
    
    def __init__(self, model_path=None, preprocessor_path=None):
        """
        初始化外部验证器
        
        Args:
            model_path: 模型文件路径
            preprocessor_path: 预处理器文件路径
        """
        self.model = None
        self.preprocessor = None
        self.model_path = model_path
        self.preprocessor_path = preprocessor_path
        
        if model_path:
            self.load_model(model_path)
        
        if preprocessor_path:
            self.load_preprocessor(preprocessor_path)
    
    def load_model(self, model_path):
        """
        加载模型
        
        Args:
            model_path: 模型文件路径
        """
        try:
            self.model = joblib.load(model_path)
            logger.info(f"成功加载模型: {model_path}")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def load_preprocessor(self, preprocessor_path):
        """
        加载预处理器
        
        Args:
            preprocessor_path: 预处理器文件路径
        """
        try:
            self.preprocessor = joblib.load(preprocessor_path)
            logger.info(f"成功加载预处理器: {preprocessor_path}")
        except Exception as e:
            logger.error(f"加载预处理器失败: {e}")
            raise
    
    def validate(self, external_data_path, target_col_name=None):
        """
        执行外部验证
        
        Args:
            external_data_path: 外部数据路径
            target_col_name: 目标列名
            
        Returns:
            dict: 验证结果
        """
        if self.model is None:
            raise ValueError("模型尚未加载")
        
        logger.info(f"开始外部验证: {external_data_path}")
        
        # 加载外部数据
        df, target_col = load_and_clean_data(external_data_path, target_col_name)
        
        # 分离特征和目标变量
        X = df.drop(columns=[target_col])
        y = df[target_col]
        
        # 数据预处理
        if self.preprocessor:
            X_processed = self.preprocessor.transform(X)
        else:
            X_processed = X
        
        # 预测
        y_pred = self.model.predict(X_processed)
        
        # 获取预测概率
        y_pred_proba = None
        if hasattr(self.model, 'predict_proba'):
            y_pred_proba = self.model.predict_proba(X_processed)[:, 1]
        elif hasattr(self.model, 'decision_function'):
            y_pred_proba = self.model.decision_function(X_processed)
        
        # 计算评估指标
        metrics = self._calculate_metrics(y, y_pred, y_pred_proba)
        
        # 生成分类报告
        class_report = classification_report(y, y_pred, output_dict=True)
        
        # 混淆矩阵
        conf_matrix = confusion_matrix(y, y_pred)
        
        # 构建结果
        result = {
            'external_data_path': external_data_path,
            'data_shape': df.shape,
            'target_column': target_col,
            'predictions': {
                'y_true': y.tolist(),
                'y_pred': y_pred.tolist(),
                'y_pred_proba': y_pred_proba.tolist() if y_pred_proba is not None else None
            },
            'metrics': metrics,
            'classification_report': class_report,
            'confusion_matrix': conf_matrix.tolist(),
            'validation_time': time.time()
        }
        
        logger.info("外部验证完成")
        logger.info(f"准确率: {metrics['accuracy']:.4f}")
        if 'auc' in metrics:
            logger.info(f"AUC: {metrics['auc']:.4f}")
        
        return result
    
    def _calculate_metrics(self, y_true, y_pred, y_pred_proba=None):
        """
        计算评估指标
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签
            y_pred_proba: 预测概率
            
        Returns:
            dict: 评估指标
        """
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='binary'),
            'recall': recall_score(y_true, y_pred, average='binary'),
            'f1': f1_score(y_true, y_pred, average='binary')
        }
        
        # 如果有预测概率，计算AUC
        if y_pred_proba is not None:
            try:
                metrics['auc'] = roc_auc_score(y_true, y_pred_proba)
            except Exception as e:
                logger.warning(f"计算AUC时出错: {e}")
        
        return metrics
    
    def save_results(self, results, save_path=None):
        """
        保存验证结果
        
        Args:
            results: 验证结果
            save_path: 保存路径
        """
        if save_path is None:
            timestamp = int(time.time())
            save_path = OUTPUT_PATH / f"external_validation_{timestamp}.json"
        
        # 确保目录存在
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存为JSON文件
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"验证结果已保存到: {save_path}")
        return save_path

def run_external_validation(model_path, external_data_path, 
                          preprocessor_path=None, target_col_name=None,
                          save_results=True):
    """
    运行外部验证的便捷函数
    
    Args:
        model_path: 模型文件路径
        external_data_path: 外部数据路径
        preprocessor_path: 预处理器文件路径
        target_col_name: 目标列名
        save_results: 是否保存结果
        
    Returns:
        dict: 验证结果
    """
    logger.info("开始外部验证流程")
    
    # 创建验证器
    validator = ExternalValidator(model_path, preprocessor_path)
    
    # 执行验证
    results = validator.validate(external_data_path, target_col_name)
    
    # 保存结果
    if save_results:
        validator.save_results(results)
    
    logger.info("外部验证流程完成")
    return results

def batch_external_validation(model_paths, external_data_paths, 
                            preprocessor_paths=None, target_col_names=None):
    """
    批量外部验证
    
    Args:
        model_paths: 模型文件路径列表
        external_data_paths: 外部数据路径列表
        preprocessor_paths: 预处理器文件路径列表
        target_col_names: 目标列名列表
        
    Returns:
        dict: 批量验证结果
    """
    logger.info("开始批量外部验证")
    
    results = {}
    
    for i, model_path in enumerate(model_paths):
        for j, data_path in enumerate(external_data_paths):
            key = f"model_{i}_data_{j}"
            
            preprocessor_path = None
            if preprocessor_paths and i < len(preprocessor_paths):
                preprocessor_path = preprocessor_paths[i]
            
            target_col_name = None
            if target_col_names and j < len(target_col_names):
                target_col_name = target_col_names[j]
            
            try:
                result = run_external_validation(
                    model_path, data_path, preprocessor_path, 
                    target_col_name, save_results=False
                )
                results[key] = result
                logger.info(f"完成验证: {key}")
            except Exception as e:
                logger.error(f"验证失败 {key}: {e}")
                results[key] = {"error": str(e)}
    
    logger.info("批量外部验证完成")
    return results
