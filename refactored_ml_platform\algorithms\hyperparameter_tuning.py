#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超参数调优模块
使用Optuna优化各种机器学习模型的超参数
"""

import optuna
import numpy as np
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler
import warnings
import time
import matplotlib.pyplot as plt
from pathlib import Path
import os

# 过滤警告
warnings.filterwarnings("ignore", category=UserWarning, module="xgboost")
warnings.filterwarnings("ignore", category=FutureWarning, module="sklearn")

# 导入模型
from xgboost import XGBClassifier
from lightgbm import LGBMClassifier
from catboost import CatBoostClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.neural_network import MLPClassifier

# 导入配置
try:
    from .config import OUTPUT_PATH, RANDOM_SEED, HYPERPARAMETER_GRIDS
    from .logger import get_logger
except ImportError:
    # 使用默认配置
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    OUTPUT_PATH = PROJECT_ROOT / 'output'
    OUTPUT_PATH.mkdir(parents=True, exist_ok=True)
    RANDOM_SEED = 42
    
    # 默认超参数网格
    HYPERPARAMETER_GRIDS = {
        'DecisionTree': {
            'max_depth': [3, 5, 7, 10, None],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4],
            'criterion': ['gini', 'entropy']
        },
        'RandomForest': {
            'n_estimators': [50, 100, 200],
            'max_depth': [5, 10, None],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
    }
    
    import logging
    def get_logger(name):
        return logging.getLogger(name)

logger = get_logger(__name__)

def save_plot(fig, model_name, plot_type, file_name=None, close_fig=True):
    """
    保存图形到指定目录
    
    Args:
        fig: matplotlib图形对象
        model_name: 模型名称
        plot_type: 绘图类型
        file_name: 文件名
        close_fig: 保存后是否关闭图形
        
    Returns:
        str: 保存的文件路径
    """
    if file_name is None:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        file_name = f"{model_name}_{plot_type}_{timestamp}.png"
    
    # 确保输出目录存在
    model_dir = OUTPUT_PATH / model_name
    model_dir.mkdir(parents=True, exist_ok=True)
    
    save_path = model_dir / file_name
    fig.savefig(save_path, dpi=150, bbox_inches='tight')
    
    if close_fig:
        plt.close(fig)
    
    return str(save_path)

def create_objective_function(model_class, X, y, cv_folds=5, scoring='roc_auc'):
    """
    创建Optuna优化目标函数
    
    Args:
        model_class: 模型类
        X: 特征数据
        y: 目标变量
        cv_folds: 交叉验证折数
        scoring: 评分方法
        
    Returns:
        function: 目标函数
    """
    def objective(trial):
        # 根据模型类型定义超参数搜索空间
        if model_class == DecisionTreeClassifier:
            params = {
                'max_depth': trial.suggest_int('max_depth', 3, 20),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                'criterion': trial.suggest_categorical('criterion', ['gini', 'entropy']),
                'random_state': RANDOM_SEED
            }
        elif model_class == RandomForestClassifier:
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 50, 300),
                'max_depth': trial.suggest_int('max_depth', 3, 20),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                'random_state': RANDOM_SEED
            }
        elif model_class == XGBClassifier:
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 50, 300),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'min_child_weight': trial.suggest_int('min_child_weight', 1, 10),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'random_state': RANDOM_SEED,
                'eval_metric': 'logloss'
            }
        elif model_class == LGBMClassifier:
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 50, 300),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'num_leaves': trial.suggest_int('num_leaves', 10, 100),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'random_state': RANDOM_SEED,
                'verbosity': -1
            }
        elif model_class == CatBoostClassifier:
            params = {
                'iterations': trial.suggest_int('iterations', 50, 300),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'depth': trial.suggest_int('depth', 3, 10),
                'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 1, 10),
                'random_state': RANDOM_SEED,
                'verbose': False
            }
        elif model_class == LogisticRegression:
            params = {
                'C': trial.suggest_float('C', 0.001, 100, log=True),
                'penalty': trial.suggest_categorical('penalty', ['l1', 'l2', 'elasticnet', None]),
                'solver': trial.suggest_categorical('solver', ['newton-cg', 'lbfgs', 'liblinear', 'sag', 'saga']),
                'random_state': RANDOM_SEED,
                'max_iter': 1000
            }
        elif model_class == SVC:
            params = {
                'C': trial.suggest_float('C', 0.1, 100, log=True),
                'gamma': trial.suggest_categorical('gamma', ['scale', 'auto']) if trial.suggest_categorical('kernel', ['rbf', 'poly', 'sigmoid']) != 'linear' else 'scale',
                'kernel': trial.suggest_categorical('kernel', ['rbf', 'linear', 'poly', 'sigmoid']),
                'random_state': RANDOM_SEED,
                'probability': True
            }
        elif model_class == KNeighborsClassifier:
            params = {
                'n_neighbors': trial.suggest_int('n_neighbors', 3, 20),
                'weights': trial.suggest_categorical('weights', ['uniform', 'distance']),
                'algorithm': trial.suggest_categorical('algorithm', ['auto', 'ball_tree', 'kd_tree', 'brute']),
                'p': trial.suggest_int('p', 1, 2)
            }
        elif model_class == MLPClassifier:
            params = {
                'hidden_layer_sizes': trial.suggest_categorical('hidden_layer_sizes', [(50,), (100,), (50, 50), (100, 50), (100, 100)]),
                'activation': trial.suggest_categorical('activation', ['relu', 'tanh', 'logistic']),
                'alpha': trial.suggest_float('alpha', 0.0001, 0.01, log=True),
                'learning_rate': trial.suggest_categorical('learning_rate', ['constant', 'adaptive', 'invscaling']),
                'random_state': RANDOM_SEED,
                'max_iter': 2000
            }
        else:
            raise ValueError(f"不支持的模型类型: {model_class}")
        
        # 创建模型
        model = model_class(**params)
        
        # 交叉验证
        cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=RANDOM_SEED)
        scores = cross_val_score(model, X, y, cv=cv, scoring=scoring, n_jobs=-1)
        
        return scores.mean()
    
    return objective

def tune_model(model_name, X_train, y_train, n_trials=100, cv_folds=5, 
               scoring='roc_auc', timeout=None):
    """
    使用Optuna调优模型超参数
    
    Args:
        model_name: 模型名称
        X_train: 训练特征
        y_train: 训练标签
        n_trials: 试验次数
        cv_folds: 交叉验证折数
        scoring: 评分方法
        timeout: 超时时间（秒）
        
    Returns:
        dict: 调优结果
    """
    logger.info(f"开始调优模型: {model_name}")
    
    # 模型映射
    model_mapping = {
        'DecisionTree': DecisionTreeClassifier,
        'RandomForest': RandomForestClassifier,
        'XGBoost': XGBClassifier,
        'LightGBM': LGBMClassifier,
        'CatBoost': CatBoostClassifier,
        'Logistic': LogisticRegression,
        'SVM': SVC,
        'KNN': KNeighborsClassifier,
        'NeuralNet': MLPClassifier
    }
    
    if model_name not in model_mapping:
        raise ValueError(f"不支持的模型: {model_name}")
    
    model_class = model_mapping[model_name]
    
    # 创建研究
    study = optuna.create_study(direction='maximize')
    
    # 创建目标函数
    objective = create_objective_function(model_class, X_train, y_train, cv_folds, scoring)
    
    # 开始优化
    start_time = time.time()
    study.optimize(objective, n_trials=n_trials, timeout=timeout)
    optimization_time = time.time() - start_time
    
    # 获取最佳参数
    best_params = study.best_params
    best_score = study.best_value
    
    logger.info(f"模型 {model_name} 调优完成")
    logger.info(f"最佳分数: {best_score:.4f}")
    logger.info(f"最佳参数: {best_params}")
    logger.info(f"优化时间: {optimization_time:.2f}秒")
    
    # 构建结果
    result = {
        'model_name': model_name,
        'best_params': best_params,
        'best_score': best_score,
        'n_trials': len(study.trials),
        'optimization_time': optimization_time,
        'study': study
    }
    
    return result
