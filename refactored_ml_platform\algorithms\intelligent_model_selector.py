#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能模型选择器
实现基于性能和多样性的智能模型选择策略
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional
from sklearn.metrics import accuracy_score, roc_auc_score, f1_score
from sklearn.model_selection import cross_val_score
from scipy.spatial.distance import pdist, squareform
from scipy.stats import pearsonr
import logging

from ..utils.error_handler import get_error_handler


class IntelligentModelSelector:
    """智能模型选择器"""
    
    def __init__(self):
        """初始化智能模型选择器"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        
        # 选择策略配置
        self.strategies = {
            'performance': self._select_by_performance,
            'diversity': self._select_by_diversity,
            'performance_diversity': self._select_by_performance_diversity,
            'pareto_optimal': self._select_by_pareto_optimal,
            'ensemble_pruning': self._select_by_ensemble_pruning
        }
        
        self.logger.info("智能模型选择器初始化完成")
    
    def select_models(self, models: Dict[str, Any], X_test: pd.DataFrame, y_test: pd.Series,
                     strategy: str = 'performance_diversity', max_models: int = 5,
                     diversity_threshold: float = 0.1, performance_weight: float = 0.7,
                     **kwargs) -> List[str]:
        """
        智能选择模型
        
        Args:
            models: 模型字典 {model_name: model_object}
            X_test: 测试特征
            y_test: 测试标签
            strategy: 选择策略
            max_models: 最大模型数量
            diversity_threshold: 多样性阈值
            performance_weight: 性能权重（用于performance_diversity策略）
            
        Returns:
            选中的模型名称列表
        """
        if not models:
            raise ValueError("没有可选择的模型")
        
        if strategy not in self.strategies:
            raise ValueError(f"不支持的选择策略: {strategy}")
        
        self.logger.info(f"开始智能模型选择，策略: {strategy}, 候选模型数: {len(models)}")
        
        try:
            # 计算模型性能
            performance_scores = self._calculate_performance_scores(models, X_test, y_test)
            
            # 计算模型多样性
            diversity_matrix = self._calculate_diversity_matrix(models, X_test)
            
            # 根据策略选择模型
            selected_models = self.strategies[strategy](
                models, performance_scores, diversity_matrix, max_models,
                diversity_threshold, performance_weight, **kwargs
            )
            
            self.logger.info(f"智能选择完成，选中 {len(selected_models)} 个模型: {selected_models}")
            return selected_models
            
        except Exception as e:
            self.error_handler.handle_error(e, "智能模型选择")
            return list(models.keys())[:max_models]  # 回退策略
    
    def _calculate_performance_scores(self, models: Dict[str, Any], 
                                    X_test: pd.DataFrame, y_test: pd.Series) -> Dict[str, Dict[str, float]]:
        """计算模型性能分数"""
        performance_scores = {}
        
        for model_name, model in models.items():
            try:
                # 预测
                y_pred = model.predict(X_test)
                y_pred_proba = None
                
                if hasattr(model, 'predict_proba'):
                    y_pred_proba = model.predict_proba(X_test)
                    if y_pred_proba.shape[1] > 1:
                        y_pred_proba = y_pred_proba[:, 1]  # 取正类概率
                
                # 计算各种性能指标
                scores = {
                    'accuracy': accuracy_score(y_test, y_pred),
                    'f1_score': f1_score(y_test, y_pred, average='weighted')
                }
                
                if y_pred_proba is not None:
                    scores['auc'] = roc_auc_score(y_test, y_pred_proba)
                else:
                    scores['auc'] = scores['accuracy']  # 回退到准确率
                
                # 计算综合分数
                scores['composite'] = (scores['accuracy'] + scores['f1_score'] + scores['auc']) / 3
                
                performance_scores[model_name] = scores
                
            except Exception as e:
                self.logger.warning(f"计算模型 {model_name} 性能时出错: {e}")
                performance_scores[model_name] = {
                    'accuracy': 0.5, 'f1_score': 0.5, 'auc': 0.5, 'composite': 0.5
                }
        
        return performance_scores
    
    def _calculate_diversity_matrix(self, models: Dict[str, Any], X_test: pd.DataFrame) -> np.ndarray:
        """计算模型多样性矩阵"""
        model_names = list(models.keys())
        n_models = len(model_names)
        
        # 获取所有模型的预测结果
        predictions = {}
        for model_name, model in models.items():
            try:
                pred = model.predict(X_test)
                predictions[model_name] = pred
            except Exception as e:
                self.logger.warning(f"获取模型 {model_name} 预测结果时出错: {e}")
                predictions[model_name] = np.zeros(len(X_test))
        
        # 计算多样性矩阵（基于预测结果的不一致性）
        diversity_matrix = np.zeros((n_models, n_models))
        
        for i, model1 in enumerate(model_names):
            for j, model2 in enumerate(model_names):
                if i == j:
                    diversity_matrix[i, j] = 0  # 自己与自己的多样性为0
                else:
                    # 计算预测不一致率作为多样性度量
                    pred1 = predictions[model1]
                    pred2 = predictions[model2]
                    disagreement = np.mean(pred1 != pred2)
                    diversity_matrix[i, j] = disagreement
        
        return diversity_matrix
    
    def _select_by_performance(self, models: Dict[str, Any], performance_scores: Dict[str, Dict[str, float]],
                             diversity_matrix: np.ndarray, max_models: int, **kwargs) -> List[str]:
        """基于性能选择模型"""
        # 按综合性能排序
        sorted_models = sorted(
            performance_scores.items(),
            key=lambda x: x[1]['composite'],
            reverse=True
        )
        
        return [model_name for model_name, _ in sorted_models[:max_models]]
    
    def _select_by_diversity(self, models: Dict[str, Any], performance_scores: Dict[str, Dict[str, float]],
                           diversity_matrix: np.ndarray, max_models: int, 
                           diversity_threshold: float = 0.1, **kwargs) -> List[str]:
        """基于多样性选择模型"""
        model_names = list(models.keys())
        
        if len(model_names) <= max_models:
            return model_names
        
        # 贪心算法选择多样性最大的模型组合
        selected_indices = []
        remaining_indices = list(range(len(model_names)))
        
        # 选择性能最好的模型作为起始
        best_model_idx = max(range(len(model_names)), 
                           key=lambda i: performance_scores[model_names[i]]['composite'])
        selected_indices.append(best_model_idx)
        remaining_indices.remove(best_model_idx)
        
        # 贪心选择剩余模型
        while len(selected_indices) < max_models and remaining_indices:
            best_candidate = None
            best_diversity = -1
            
            for candidate_idx in remaining_indices:
                # 计算候选模型与已选模型的平均多样性
                avg_diversity = np.mean([diversity_matrix[candidate_idx, selected_idx] 
                                       for selected_idx in selected_indices])
                
                if avg_diversity > best_diversity:
                    best_diversity = avg_diversity
                    best_candidate = candidate_idx
            
            if best_candidate is not None and best_diversity >= diversity_threshold:
                selected_indices.append(best_candidate)
                remaining_indices.remove(best_candidate)
            else:
                break
        
        return [model_names[i] for i in selected_indices]
    
    def _select_by_performance_diversity(self, models: Dict[str, Any], 
                                       performance_scores: Dict[str, Dict[str, float]],
                                       diversity_matrix: np.ndarray, max_models: int,
                                       diversity_threshold: float = 0.1, 
                                       performance_weight: float = 0.7, **kwargs) -> List[str]:
        """基于性能和多样性的综合选择"""
        model_names = list(models.keys())
        
        if len(model_names) <= max_models:
            return model_names
        
        # 计算每个模型的综合分数
        composite_scores = {}
        
        for i, model_name in enumerate(model_names):
            performance_score = performance_scores[model_name]['composite']
            
            # 计算与其他模型的平均多样性
            if len(model_names) > 1:
                diversity_scores = [diversity_matrix[i, j] for j in range(len(model_names)) if i != j]
                avg_diversity = np.mean(diversity_scores)
            else:
                avg_diversity = 0
            
            # 综合分数 = 性能权重 * 性能分数 + (1-性能权重) * 多样性分数
            composite_score = performance_weight * performance_score + (1 - performance_weight) * avg_diversity
            composite_scores[model_name] = composite_score
        
        # 按综合分数排序选择
        sorted_models = sorted(composite_scores.items(), key=lambda x: x[1], reverse=True)
        
        return [model_name for model_name, _ in sorted_models[:max_models]]
    
    def _select_by_pareto_optimal(self, models: Dict[str, Any], 
                                performance_scores: Dict[str, Dict[str, float]],
                                diversity_matrix: np.ndarray, max_models: int, **kwargs) -> List[str]:
        """基于帕累托最优选择模型"""
        model_names = list(models.keys())
        
        # 计算每个模型的性能和多样性分数
        model_scores = []
        for i, model_name in enumerate(model_names):
            performance = performance_scores[model_name]['composite']
            diversity = np.mean([diversity_matrix[i, j] for j in range(len(model_names)) if i != j]) if len(model_names) > 1 else 0
            model_scores.append((model_name, performance, diversity))
        
        # 找到帕累托最优解
        pareto_optimal = []
        for i, (name1, perf1, div1) in enumerate(model_scores):
            is_dominated = False
            for j, (name2, perf2, div2) in enumerate(model_scores):
                if i != j and perf2 >= perf1 and div2 >= div1 and (perf2 > perf1 or div2 > div1):
                    is_dominated = True
                    break
            if not is_dominated:
                pareto_optimal.append(name1)
        
        # 如果帕累托最优解太多，按性能排序选择
        if len(pareto_optimal) > max_models:
            pareto_scores = {name: performance_scores[name]['composite'] for name in pareto_optimal}
            sorted_pareto = sorted(pareto_scores.items(), key=lambda x: x[1], reverse=True)
            return [name for name, _ in sorted_pareto[:max_models]]
        
        return pareto_optimal
    
    def _select_by_ensemble_pruning(self, models: Dict[str, Any], 
                                  performance_scores: Dict[str, Dict[str, float]],
                                  diversity_matrix: np.ndarray, max_models: int, **kwargs) -> List[str]:
        """基于集成剪枝选择模型"""
        model_names = list(models.keys())
        
        if len(model_names) <= max_models:
            return model_names
        
        # 从所有模型开始，逐步移除贡献最小的模型
        current_models = model_names.copy()
        
        while len(current_models) > max_models:
            # 计算移除每个模型后的集成性能损失
            min_loss = float('inf')
            model_to_remove = None
            
            for model_name in current_models:
                # 计算移除该模型后的预期性能
                remaining_models = [m for m in current_models if m != model_name]
                avg_performance = np.mean([performance_scores[m]['composite'] for m in remaining_models])
                
                # 计算性能损失（简化版本）
                loss = performance_scores[model_name]['composite'] - avg_performance
                
                if loss < min_loss:
                    min_loss = loss
                    model_to_remove = model_name
            
            if model_to_remove:
                current_models.remove(model_to_remove)
        
        return current_models
    
    def evaluate_selection(self, selected_models: List[str], models: Dict[str, Any],
                         X_test: pd.DataFrame, y_test: pd.Series) -> Dict[str, float]:
        """评估模型选择结果"""
        if not selected_models:
            return {}
        
        try:
            # 计算选中模型的性能指标
            performance_scores = self._calculate_performance_scores(
                {name: models[name] for name in selected_models}, X_test, y_test
            )
            
            # 计算多样性指标
            diversity_matrix = self._calculate_diversity_matrix(
                {name: models[name] for name in selected_models}, X_test
            )
            
            # 汇总评估结果
            avg_performance = np.mean([scores['composite'] for scores in performance_scores.values()])
            avg_diversity = np.mean(diversity_matrix[np.triu_indices_from(diversity_matrix, k=1)])
            
            evaluation = {
                'num_models': len(selected_models),
                'avg_performance': avg_performance,
                'avg_diversity': avg_diversity,
                'performance_std': np.std([scores['composite'] for scores in performance_scores.values()]),
                'diversity_std': np.std(diversity_matrix[np.triu_indices_from(diversity_matrix, k=1)])
            }
            
            self.logger.info(f"模型选择评估完成: {evaluation}")
            return evaluation
            
        except Exception as e:
            self.error_handler.handle_error(e, "评估模型选择结果")
            return {}


# 全局实例
_intelligent_selector = None

def get_intelligent_model_selector() -> IntelligentModelSelector:
    """获取智能模型选择器实例"""
    global _intelligent_selector
    if _intelligent_selector is None:
        _intelligent_selector = IntelligentModelSelector()
    return _intelligent_selector
