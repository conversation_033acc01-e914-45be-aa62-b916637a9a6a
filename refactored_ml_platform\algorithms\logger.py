#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志记录模块
提供统一的日志记录接口，支持控制台和文件输出
"""

import logging
import os
from pathlib import Path
import sys
from datetime import datetime

# 导入配置
try:
    from .config import LOG_PATH
except ImportError:
    # 使用默认配置
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    LOG_PATH = PROJECT_ROOT / 'logs'
    LOG_PATH.mkdir(parents=True, exist_ok=True)

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'date_format': '%Y-%m-%d %H:%M:%S'
}

# 日志级别映射
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

def setup_logger(name, log_file=None, level=None, console_output=True):
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径，如果为None则只输出到控制台
        level: 日志级别，如果为None则使用配置文件中的级别
        console_output: 是否输出到控制台
        
    Returns:
        logging.Logger: 日志记录器对象
    """
    # 获取日志级别
    if level is None:
        level = LOG_CONFIG.get('level', 'INFO')
    
    # 将字符串转换为日志级别常量
    log_level = LOG_LEVELS.get(level.upper(), logging.INFO)
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(log_level)
    
    # 避免重复设置处理器
    if logger.handlers:
        return logger
    
    # 设置格式化器
    formatter = logging.Formatter(
        fmt=LOG_CONFIG.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
        datefmt=LOG_CONFIG.get('date_format', '%Y-%m-%d %H:%M:%S')
    )
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # 如果需要控制台输出，添加控制台处理器
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return logger

def get_default_logger(name=None, console_output=True, include_timestamp=True):
    """
    获取默认日志记录器
    
    Args:
        name: 日志记录器名称，如果为None则使用调用模块的名称
        console_output: 是否输出到控制台
        include_timestamp: 是否在日志文件名中包含时间戳
        
    Returns:
        logging.Logger: 日志记录器对象
    """
    if name is None:
        # 获取调用模块的名称
        frame = sys._getframe(1)
        name = frame.f_globals.get('__name__', 'unknown')
    
    # 生成日志文件名
    if include_timestamp:
        timestamp = datetime.now().strftime('%Y%m%d')
        log_filename = f"{name}_{timestamp}.log"
    else:
        log_filename = f"{name}.log"
    
    log_file = LOG_PATH / log_filename
    
    return setup_logger(name, str(log_file), console_output=console_output)

def get_logger(name):
    """
    获取指定名称的日志记录器（简化版本）
    
    Args:
        name: 日志记录器名称
        
    Returns:
        logging.Logger: 日志记录器对象
    """
    return get_default_logger(name)

# 创建默认的应用程序日志记录器
app_logger = get_default_logger('app')

def log_info(message):
    """记录信息日志"""
    app_logger.info(message)

def log_warning(message):
    """记录警告日志"""
    app_logger.warning(message)

def log_error(message):
    """记录错误日志"""
    app_logger.error(message)

def log_debug(message):
    """记录调试日志"""
    app_logger.debug(message)
