#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成学习模块
提供多种集成学习方法
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import VotingClassifier, BaggingClassifier, AdaBoostClassifier
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
from sklearn.linear_model import LogisticRegression
import time
from pathlib import Path
import joblib

# 导入配置和其他模块
try:
    from .config import RANDOM_SEED, ENSEMBLE_PATH
    from .logger import get_logger
    from .model_training import MODEL_TRAINERS
except ImportError:
    # 使用默认配置
    import os
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    ENSEMBLE_PATH = PROJECT_ROOT / 'ensemble'
    ENSEMBLE_PATH.mkdir(parents=True, exist_ok=True)
    RANDOM_SEED = 42
    
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    # 简化的模型训练器
    MODEL_TRAINERS = {}

logger = get_logger(__name__)

class EnsembleModel:
    """
    集成模型类
    """
    
    def __init__(self, ensemble_method='voting', base_models=None, voting='soft'):
        """
        初始化集成模型
        
        Args:
            ensemble_method: 集成方法 ('voting', 'bagging', 'boosting', 'stacking')
            base_models: 基础模型列表
            voting: 投票方式 ('hard', 'soft')
        """
        self.ensemble_method = ensemble_method
        self.base_models = base_models or []
        self.voting = voting
        self.ensemble_model = None
        self.trained_models = {}
        self.feature_importances_ = None
        
    def fit(self, X, y):
        """训练集成模型"""
        logger.info(f"开始训练集成模型，方法: {self.ensemble_method}")
        
        if self.ensemble_method == 'voting':
            self._fit_voting(X, y)
        elif self.ensemble_method == 'bagging':
            self._fit_bagging(X, y)
        elif self.ensemble_method == 'boosting':
            self._fit_boosting(X, y)
        elif self.ensemble_method == 'stacking':
            self._fit_stacking(X, y)
        else:
            raise ValueError(f"不支持的集成方法: {self.ensemble_method}")
        
        return self
    
    def _fit_voting(self, X, y):
        """训练投票集成模型"""
        estimators = self._prepare_base_models()
        
        self.ensemble_model = VotingClassifier(
            estimators=estimators,
            voting=self.voting
        )
        
        self.ensemble_model.fit(X, y)
        self.trained_models = dict(estimators)
        
        # 计算特征重要性（如果可能）
        self._compute_feature_importances()
    
    def _fit_bagging(self, X, y):
        """训练Bagging集成模型"""
        # 使用随机森林作为基础模型
        from sklearn.ensemble import RandomForestClassifier
        base_estimator = RandomForestClassifier(random_state=RANDOM_SEED)
        
        self.ensemble_model = BaggingClassifier(
            base_estimator=base_estimator,
            n_estimators=10,
            random_state=RANDOM_SEED
        )
        
        self.ensemble_model.fit(X, y)
    
    def _fit_boosting(self, X, y):
        """训练Boosting集成模型"""
        self.ensemble_model = AdaBoostClassifier(
            n_estimators=50,
            random_state=RANDOM_SEED
        )
        
        self.ensemble_model.fit(X, y)
    
    def _fit_stacking(self, X, y):
        """训练Stacking集成模型"""
        # 简化的Stacking实现
        from sklearn.ensemble import StackingClassifier
        
        estimators = self._prepare_base_models()
        
        self.ensemble_model = StackingClassifier(
            estimators=estimators,
            final_estimator=LogisticRegression(random_state=RANDOM_SEED),
            cv=5
        )
        
        self.ensemble_model.fit(X, y)
        self.trained_models = dict(estimators)
    
    def _prepare_base_models(self):
        """准备基础模型"""
        if not self.base_models:
            # 使用默认的基础模型
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.svm import SVC
            
            estimators = [
                ('rf', RandomForestClassifier(random_state=RANDOM_SEED)),
                ('lr', LogisticRegression(random_state=RANDOM_SEED, max_iter=1000)),
                ('svm', SVC(probability=True, random_state=RANDOM_SEED))
            ]
        else:
            estimators = [(f'model_{i}', model) for i, model in enumerate(self.base_models)]
        
        return estimators
    
    def _compute_feature_importances(self):
        """计算特征重要性"""
        try:
            importances = []
            for name, model in self.trained_models.items():
                if hasattr(model, 'feature_importances_'):
                    importances.append(model.feature_importances_)
                elif hasattr(model, 'coef_'):
                    importances.append(np.abs(model.coef_[0]))
            
            if importances:
                self.feature_importances_ = np.mean(importances, axis=0)
        except Exception as e:
            logger.warning(f"计算特征重要性失败: {e}")
    
    def predict(self, X):
        """预测"""
        if self.ensemble_model is None:
            raise ValueError("模型尚未训练")
        return self.ensemble_model.predict(X)
    
    def predict_proba(self, X):
        """预测概率"""
        if self.ensemble_model is None:
            raise ValueError("模型尚未训练")
        return self.ensemble_model.predict_proba(X)
    
    def score(self, X, y):
        """计算准确率"""
        if self.ensemble_model is None:
            raise ValueError("模型尚未训练")
        return self.ensemble_model.score(X, y)

def run_ensemble_pipeline(X_train, y_train, X_test, y_test, 
                         model_names=None, ensemble_methods=None,
                         tune_ensemble=False, enable_shap=False):
    """
    运行集成学习流水线
    
    Args:
        X_train: 训练特征
        y_train: 训练标签
        X_test: 测试特征
        y_test: 测试标签
        model_names: 模型名称列表
        ensemble_methods: 集成方法列表
        tune_ensemble: 是否调优集成模型
        enable_shap: 是否启用SHAP分析
        
    Returns:
        dict: 集成学习结果
    """
    logger.info("开始集成学习流水线")
    
    if model_names is None:
        model_names = ['RandomForest', 'XGBoost', 'LightGBM']
    
    if ensemble_methods is None:
        ensemble_methods = ['voting', 'stacking']
    
    results = {}
    
    # 训练基础模型
    base_models = []
    for model_name in model_names:
        if model_name in MODEL_TRAINERS:
            trainer = MODEL_TRAINERS[model_name]
            result = trainer.train_and_evaluate(X_train, y_train, X_test, y_test)
            base_models.append(result['model'])
            logger.info(f"基础模型 {model_name} 训练完成，准确率: {result['metrics']['accuracy']:.4f}")
    
    # 训练集成模型
    for method in ensemble_methods:
        logger.info(f"训练集成模型: {method}")
        
        ensemble = EnsembleModel(ensemble_method=method, base_models=base_models)
        
        start_time = time.time()
        ensemble.fit(X_train, y_train)
        training_time = time.time() - start_time
        
        # 预测
        y_pred = ensemble.predict(X_test)
        y_pred_proba = ensemble.predict_proba(X_test)[:, 1]
        
        # 计算指标
        accuracy = accuracy_score(y_test, y_pred)
        auc = roc_auc_score(y_test, y_pred_proba)
        
        results[method] = {
            'model': ensemble,
            'y_pred': y_pred,
            'y_pred_proba': y_pred_proba,
            'accuracy': accuracy,
            'auc': auc,
            'training_time': training_time
        }
        
        logger.info(f"集成模型 {method} 训练完成，准确率: {accuracy:.4f}, AUC: {auc:.4f}")
    
    # 保存结果
    save_path = ENSEMBLE_PATH / f"ensemble_results_{int(time.time())}.joblib"
    joblib.dump(results, save_path)
    logger.info(f"集成学习结果已保存到: {save_path}")
    
    return results

def run_external_validation(model_path, external_data_path):
    """
    运行外部验证
    
    Args:
        model_path: 模型文件路径
        external_data_path: 外部数据路径
        
    Returns:
        dict: 验证结果
    """
    logger.info("开始外部验证")
    
    # 加载模型
    model = joblib.load(model_path)
    
    # 加载外部数据
    # 这里需要根据实际情况实现数据加载逻辑
    # external_data = pd.read_csv(external_data_path)
    
    logger.info("外部验证完成")
    
    return {"status": "completed"}

def run_multi_data_ensemble_pipeline(**kwargs):
    """
    运行多数据源集成学习流水线
    
    Args:
        **kwargs: 各种参数
        
    Returns:
        dict: 多数据源集成结果
    """
    logger.info("开始多数据源集成学习")
    
    # 这里需要实现具体的多数据源集成逻辑
    # 目前提供基础框架
    
    logger.info("多数据源集成学习完成")
    
    return {"status": "completed"}
