#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多数据源集成学习模块
提供多数据源集成学习功能
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, roc_auc_score
import joblib
from pathlib import Path
import time
import json

# 导入配置
try:
    from .config import MULTI_DATA_CACHE_PATH, RANDOM_SEED
    from .logger import get_logger
    from .data_preprocessing import load_and_preprocess_data
    from .model_training import MODEL_TRAINERS
except ImportError:
    # 使用默认配置
    import os
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    MULTI_DATA_CACHE_PATH = PROJECT_ROOT / 'multi_data_cache'
    MULTI_DATA_CACHE_PATH.mkdir(parents=True, exist_ok=True)
    RANDOM_SEED = 42
    
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    def load_and_preprocess_data(data_path, **kwargs):
        df = pd.read_csv(data_path)
        target_col = df.columns[-1]
        X = df.drop(columns=[target_col])
        y = df[target_col]
        return {
            'X_train': X[:int(0.8*len(X))],
            'X_test': X[int(0.8*len(X)):],
            'y_train': y[:int(0.8*len(y))],
            'y_test': y[int(0.8*len(y)):],
            'feature_names': list(X.columns),
            'data_path': data_path
        }
    
    MODEL_TRAINERS = {}

logger = get_logger(__name__)

class MultiDataEnsemble:
    """
    多数据源集成学习类
    """
    
    def __init__(self, model_data_mapping=None, ensemble_methods=None, data_strategies=None,
                 feature_selection=True, feature_selection_method='weighted', k=None):
        """
        初始化多数据源集成学习

        Args:
            model_data_mapping: 模型与数据源的映射字典 {model_name: data_path}
            ensemble_methods: 集成方法列表
            data_strategies: 数据策略列表
            feature_selection: 是否进行特征选择
            feature_selection_method: 特征选择方法
            k: 选择的特征数量
        """
        self.model_data_mapping = model_data_mapping or {}
        self.ensemble_methods = ensemble_methods or ['voting', 'stacking', 'weighted']
        self.data_strategies = data_strategies or ['unified', 'original', 'combined']
        self.feature_selection = feature_selection
        self.feature_selection_method = feature_selection_method
        self.k = k

        # 数据存储
        self.datasets = {}
        self.trained_models = {}
        self.ensemble_models = {}
        self.feature_selectors = {}
        self.selected_features = {}

        # 结果存储
        self.training_results = {}
        self.ensemble_results = {}
        self.shap_results = {}

        # 智能模型选择
        self.model_performance = {}
        self.model_diversity = {}
        self.selected_base_models = []

    def load_and_prepare_data(self):
        """
        加载和预处理所有数据源

        Returns:
            dict: 包含所有数据源的字典
        """
        logger.info("开始加载多数据源...")
        datasets = {}

        for model_name, data_path in self.model_data_mapping.items():
            logger.info(f"加载 {model_name} 的数据: {data_path}")
            try:
                # 使用数据加载器加载数据
                data_loader = get_data_loader()
                df = data_loader.load_data(data_path)

                if df is None or df.empty:
                    logger.warning(f"数据源 {data_path} 为空，跳过")
                    continue

                # 数据预处理
                preprocessor = get_data_preprocessor()
                processed_data = preprocessor.preprocess_data(df)

                if processed_data:
                    datasets[model_name] = processed_data
                    logger.info(f"成功加载 {model_name} 数据，训练集大小: {processed_data['X_train'].shape}")
                else:
                    logger.warning(f"数据预处理失败: {model_name}")

            except Exception as e:
                logger.error(f"加载数据源 {data_path} 失败: {e}")
                continue

        self.datasets = datasets
        logger.info(f"成功加载 {len(datasets)} 个数据源")
        return datasets

    def prepare_ensemble_data(self, target_data_path=None, strategy='unified'):
        """
        根据策略准备集成数据

        Args:
            target_data_path: 目标数据路径（用于unified策略）
            strategy: 数据策略 ('unified', 'original', 'combined')

        Returns:
            dict: 包含训练和测试数据的字典
        """
        logger.info(f"准备集成数据，策略: {strategy}")

        if strategy == 'unified':
            # 使用统一的数据源
            if target_data_path:
                data_loader = get_data_loader()
                df = data_loader.load_data(target_data_path)
                preprocessor = get_data_preprocessor()
                return preprocessor.preprocess_data(df)
            else:
                # 使用第一个数据源作为统一数据
                if self.datasets:
                    first_dataset = list(self.datasets.values())[0]
                    return first_dataset
                else:
                    raise ValueError("没有可用的数据源")

        elif strategy == 'original':
            # 每个模型使用其原始数据源
            return self.datasets

        elif strategy == 'combined':
            # 合并所有数据源
            return self._combine_datasets()

        else:
            raise ValueError(f"不支持的数据策略: {strategy}")

    def _combine_datasets(self):
        """
        合并所有数据源

        Returns:
            dict: 合并后的数据集
        """
        if not self.datasets:
            raise ValueError("没有可用的数据源")

        logger.info("合并所有数据源...")

        # 收集所有数据
        all_X_train = []
        all_y_train = []
        all_X_test = []
        all_y_test = []

        for dataset_name, dataset in self.datasets.items():
            all_X_train.append(dataset['X_train'])
            all_y_train.append(dataset['y_train'])
            all_X_test.append(dataset['X_test'])
            all_y_test.append(dataset['y_test'])

        # 合并数据
        import pandas as pd

        # 找到共同特征
        common_features = None
        for X_train in all_X_train:
            if hasattr(X_train, 'columns'):
                if common_features is None:
                    common_features = set(X_train.columns)
                else:
                    common_features = common_features.intersection(set(X_train.columns))

        if common_features:
            common_features = list(common_features)
            logger.info(f"找到 {len(common_features)} 个共同特征")

            # 只保留共同特征
            X_train_combined = pd.concat([X[common_features] for X in all_X_train], ignore_index=True)
            X_test_combined = pd.concat([X[common_features] for X in all_X_test], ignore_index=True)
        else:
            # 如果没有共同特征，直接合并
            X_train_combined = pd.concat(all_X_train, ignore_index=True)
            X_test_combined = pd.concat(all_X_test, ignore_index=True)

        y_train_combined = pd.concat(all_y_train, ignore_index=True)
        y_test_combined = pd.concat(all_y_test, ignore_index=True)

        logger.info(f"合并后数据形状: 训练集 {X_train_combined.shape}, 测试集 {X_test_combined.shape}")

        return {
            'X_train': X_train_combined,
            'y_train': y_train_combined,
            'X_test': X_test_combined,
            'y_test': y_test_combined
        }

    def train_base_models(self, model_names=None, use_cache=True, enable_shap=True):
        """
        使用不同数据源训练基础模型

        Args:
            model_names: 要训练的模型名称列表
            use_cache: 是否使用缓存
            enable_shap: 是否启用SHAP分析
        """
        logger.info("开始训练基础模型...")

        if not self.datasets:
            raise ValueError("请先加载数据源")

        # 获取模型训练器
        model_trainer = get_model_trainer()

        # 默认模型列表
        if model_names is None:
            model_names = ['RandomForest', 'XGBoost', 'LightGBM', 'LogisticRegression', 'SVM']

        for dataset_name, dataset in self.datasets.items():
            logger.info(f"使用数据源 {dataset_name} 训练模型...")

            dataset_models = {}
            dataset_performance = {}

            for model_name in model_names:
                try:
                    logger.info(f"训练模型: {model_name}")

                    # 训练模型
                    model_result = model_trainer.train_model(
                        model_name=model_name,
                        X_train=dataset['X_train'],
                        y_train=dataset['y_train'],
                        X_test=dataset['X_test'],
                        y_test=dataset['y_test'],
                        enable_shap=enable_shap
                    )

                    if model_result and 'model' in model_result:
                        model_key = f"{dataset_name}_{model_name}"
                        dataset_models[model_key] = model_result['model']
                        dataset_performance[model_key] = model_result.get('metrics', {})

                        logger.info(f"模型 {model_key} 训练完成")

                        # 保存SHAP结果
                        if enable_shap and 'shap_results' in model_result:
                            self.shap_results[model_key] = model_result['shap_results']

                except Exception as e:
                    logger.error(f"训练模型 {model_name} 失败: {e}")
                    continue

            # 保存结果
            self.trained_models[dataset_name] = dataset_models
            self.model_performance[dataset_name] = dataset_performance

        logger.info(f"基础模型训练完成，共训练 {sum(len(models) for models in self.trained_models.values())} 个模型")

    def intelligent_model_selection(self, selection_strategy='performance_diversity',
                                  max_models=5, diversity_threshold=0.1):
        """
        智能基模型选择

        Args:
            selection_strategy: 选择策略 ('performance', 'diversity', 'performance_diversity')
            max_models: 最大模型数量
            diversity_threshold: 多样性阈值

        Returns:
            list: 选中的模型键列表
        """
        logger.info(f"开始智能模型选择，策略: {selection_strategy}")

        if not self.trained_models:
            raise ValueError("请先训练基础模型")

        # 收集所有模型
        all_models = {}
        all_performance = {}

        for dataset_name, models in self.trained_models.items():
            all_models.update(models)
            all_performance.update(self.model_performance[dataset_name])

        if not all_models:
            raise ValueError("没有可用的训练模型")

        # 根据策略选择模型
        if selection_strategy == 'performance':
            selected_models = self._select_by_performance(all_performance, max_models)
        elif selection_strategy == 'diversity':
            selected_models = self._select_by_diversity(all_models, max_models, diversity_threshold)
        elif selection_strategy == 'performance_diversity':
            selected_models = self._select_by_performance_diversity(
                all_models, all_performance, max_models, diversity_threshold
            )
        else:
            raise ValueError(f"不支持的选择策略: {selection_strategy}")

        self.selected_base_models = selected_models
        logger.info(f"智能选择了 {len(selected_models)} 个基模型: {selected_models}")

        return selected_models

    def _select_by_performance(self, performance_dict, max_models):
        """基于性能选择模型"""
        # 按AUC或准确率排序
        sorted_models = sorted(
            performance_dict.items(),
            key=lambda x: x[1].get('auc', x[1].get('accuracy', 0)),
            reverse=True
        )

        return [model_name for model_name, _ in sorted_models[:max_models]]

    def _select_by_diversity(self, models_dict, max_models, diversity_threshold):
        """基于多样性选择模型"""
        # 这里可以实现基于预测结果多样性的选择算法
        # 简化实现：随机选择不同类型的模型
        model_types = {}
        for model_name in models_dict.keys():
            model_type = model_name.split('_')[-1]  # 获取模型类型
            if model_type not in model_types:
                model_types[model_type] = []
            model_types[model_type].append(model_name)

        # 从每种类型中选择一个模型
        selected = []
        for model_type, model_list in model_types.items():
            if len(selected) < max_models:
                selected.append(model_list[0])  # 选择第一个

        return selected[:max_models]

    def _select_by_performance_diversity(self, models_dict, performance_dict,
                                       max_models, diversity_threshold):
        """基于性能和多样性选择模型"""
        # 先按性能选择前2*max_models个模型
        performance_candidates = self._select_by_performance(performance_dict, max_models * 2)

        # 从候选模型中基于多样性选择
        candidate_models = {name: models_dict[name] for name in performance_candidates if name in models_dict}

        if len(candidate_models) <= max_models:
            return list(candidate_models.keys())

        # 基于多样性进一步筛选
        diversity_selected = self._select_by_diversity(candidate_models, max_models, diversity_threshold)

        return diversity_selected
    
    def add_dataset(self, data_path, model_name, dataset_name=None):
        """
        添加数据集
        
        Args:
            data_path: 数据文件路径
            model_name: 对应的模型名称
            dataset_name: 数据集名称
        """
        if dataset_name is None:
            dataset_name = f"dataset_{len(self.datasets)}"
        
        logger.info(f"添加数据集: {dataset_name} -> {model_name}")
        
        # 加载和预处理数据
        dataset = load_and_preprocess_data(data_path)
        dataset['model_name'] = model_name
        dataset['dataset_name'] = dataset_name
        
        self.datasets[dataset_name] = dataset
    
    def train_base_models(self):
        """训练基础模型"""
        logger.info("开始训练基础模型")
        
        for dataset_name, dataset in self.datasets.items():
            model_name = dataset['model_name']
            
            if model_name in MODEL_TRAINERS:
                logger.info(f"训练模型 {model_name} 在数据集 {dataset_name}")
                
                trainer = MODEL_TRAINERS[model_name]
                result = trainer.train_and_evaluate(
                    dataset['X_train'], dataset['y_train'],
                    dataset['X_test'], dataset['y_test']
                )
                
                self.trained_models[f"{dataset_name}_{model_name}"] = result
                logger.info(f"模型训练完成，准确率: {result['metrics']['accuracy']:.4f}")
            else:
                logger.warning(f"未找到模型训练器: {model_name}")
    
    def create_ensemble_models(self):
        """创建集成模型"""
        logger.info("开始创建集成模型")
        
        for method in self.ensemble_methods:
            for strategy in self.data_strategies:
                ensemble_name = f"{method}_{strategy}"
                logger.info(f"创建集成模型: {ensemble_name}")
                
                if method == 'voting':
                    ensemble_model = self._create_voting_ensemble(strategy)
                elif method == 'stacking':
                    ensemble_model = self._create_stacking_ensemble(strategy)
                else:
                    logger.warning(f"不支持的集成方法: {method}")
                    continue
                
                self.ensemble_models[ensemble_name] = ensemble_model
    
    def _create_voting_ensemble(self, strategy):
        """创建投票集成模型"""
        estimators = []
        
        for key, result in self.trained_models.items():
            model = result['model']
            estimators.append((key, model))
        
        if not estimators:
            logger.warning("没有可用的基础模型")
            return None
        
        ensemble = VotingClassifier(
            estimators=estimators,
            voting='soft'
        )
        
        return ensemble
    
    def _create_stacking_ensemble(self, strategy):
        """创建堆叠集成模型"""
        from sklearn.ensemble import StackingClassifier
        
        estimators = []
        
        for key, result in self.trained_models.items():
            model = result['model']
            estimators.append((key, model))
        
        if not estimators:
            logger.warning("没有可用的基础模型")
            return None
        
        ensemble = StackingClassifier(
            estimators=estimators,
            final_estimator=LogisticRegression(random_state=RANDOM_SEED),
            cv=5
        )
        
        return ensemble
    
    def evaluate_ensembles(self, test_data_path=None):
        """评估集成模型"""
        logger.info("开始评估集成模型")
        
        results = {}
        
        # 如果提供了测试数据，使用测试数据评估
        if test_data_path:
            test_dataset = load_and_preprocess_data(test_data_path)
            X_test = test_dataset['X_test']
            y_test = test_dataset['y_test']
        else:
            # 使用第一个数据集的测试数据
            first_dataset = list(self.datasets.values())[0]
            X_test = first_dataset['X_test']
            y_test = first_dataset['y_test']
        
        for ensemble_name, ensemble_model in self.ensemble_models.items():
            if ensemble_model is None:
                continue
            
            logger.info(f"评估集成模型: {ensemble_name}")
            
            try:
                # 训练集成模型（使用第一个数据集的训练数据）
                first_dataset = list(self.datasets.values())[0]
                X_train = first_dataset['X_train']
                y_train = first_dataset['y_train']
                
                ensemble_model.fit(X_train, y_train)
                
                # 预测
                y_pred = ensemble_model.predict(X_test)
                y_pred_proba = ensemble_model.predict_proba(X_test)[:, 1]
                
                # 计算指标
                accuracy = accuracy_score(y_test, y_pred)
                auc = roc_auc_score(y_test, y_pred_proba)
                
                results[ensemble_name] = {
                    'accuracy': accuracy,
                    'auc': auc,
                    'y_pred': y_pred.tolist(),
                    'y_pred_proba': y_pred_proba.tolist()
                }
                
                logger.info(f"集成模型 {ensemble_name} - 准确率: {accuracy:.4f}, AUC: {auc:.4f}")
                
            except Exception as e:
                logger.error(f"评估集成模型 {ensemble_name} 时出错: {e}")
                results[ensemble_name] = {"error": str(e)}
        
        return results
    
    def save_results(self, results, save_path=None):
        """保存结果"""
        if save_path is None:
            timestamp = int(time.time())
            save_path = MULTI_DATA_CACHE_PATH / f"multi_data_ensemble_{timestamp}.json"
        
        # 确保目录存在
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存为JSON文件
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"多数据源集成结果已保存到: {save_path}")
        return save_path

def run_multi_data_ensemble_pipeline(model_data_mapping, ensemble_methods=None,
                                    ensemble_data_strategies=None, target_data_path=None,
                                    feature_names=None, enable_shap=True,
                                    feature_selection=False, feature_selection_method='combined',
                                    k=None):
    """
    运行多数据源集成学习流水线
    
    Args:
        model_data_mapping: 模型-数据映射字典
        ensemble_methods: 集成方法列表
        ensemble_data_strategies: 数据策略列表
        target_data_path: 目标数据路径
        feature_names: 特征名称列表
        enable_shap: 是否启用SHAP分析
        feature_selection: 是否进行特征选择
        feature_selection_method: 特征选择方法
        k: 特征选择数量
        
    Returns:
        dict: 多数据源集成结果
    """
    logger.info("开始多数据源集成学习流水线")
    
    # 创建多数据源集成学习器
    ensemble_learner = MultiDataEnsemble(ensemble_methods, ensemble_data_strategies)
    
    # 添加数据集
    for model_name, data_path in model_data_mapping.items():
        ensemble_learner.add_dataset(data_path, model_name)
    
    # 训练基础模型
    ensemble_learner.train_base_models()
    
    # 创建集成模型
    ensemble_learner.create_ensemble_models()
    
    # 评估集成模型
    results = ensemble_learner.evaluate_ensembles(target_data_path)
    
    # 保存结果
    ensemble_learner.save_results(results)
    
    logger.info("多数据源集成学习流水线完成")
    return results
