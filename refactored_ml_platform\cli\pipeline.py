#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键二分类分析流水线
提供完整的自动化机器学习分析流程
"""

import argparse
import sys
import os
import logging
import time
from pathlib import Path
from datetime import datetime
from typing import List, Optional, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from cli.main import MLPlatformCLI


class BinaryClassificationPipeline:
    """二分类分析流水线"""
    
    def __init__(self):
        """初始化流水线"""
        self.cli = MLPlatformCLI()
        self.logger = logging.getLogger(__name__)
        
        # 预定义的模型策略
        self.model_strategies = {
            'fast': ['RandomForest', 'XGBoost'],
            'balanced': ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic'],
            'comprehensive': ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 
                            'CatBoost', 'Logistic', 'SVM', 'KNN'],
            'ensemble': ['RandomForest', 'XGBoost', 'LightGBM', 'CatBoost'],
            'linear': ['Logistic', 'SVM'],
            'tree': ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost']
        }
    
    def create_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description='一键二分类分析流水线',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
  # 快速分析（推荐用于初步探索）
  python -m cli.pipeline --data data.csv --target target --strategy fast
  
  # 平衡分析（推荐用于一般项目）
  python -m cli.pipeline --data data.csv --target target --strategy balanced
  
  # 全面分析（推荐用于重要项目）
  python -m cli.pipeline --data data.csv --target target --strategy comprehensive
  
  # 自定义模型
  python -m cli.pipeline --data data.csv --target target --models RandomForest,XGBoost,LightGBM
  
  # 启用所有高级功能
  python -m cli.pipeline --data data.csv --target target --strategy comprehensive --enable-tuning --enable-delong --enable-shap
            """
        )
        
        # 必需参数
        parser.add_argument('--data', '-d', type=str, required=True,
                          help='数据文件路径 (CSV/Excel)')
        
        parser.add_argument('--target', '-t', type=str, required=True,
                          help='目标列名称')
        
        # 策略选择
        strategy_group = parser.add_mutually_exclusive_group()
        strategy_group.add_argument('--strategy', '-s', type=str, 
                                  choices=list(self.model_strategies.keys()),
                                  default='balanced',
                                  help='预定义的模型策略 (默认: balanced)')
        
        strategy_group.add_argument('--models', type=str,
                                  help='自定义模型列表，用逗号分隔')
        
        # 输出设置
        parser.add_argument('--output', '-o', type=str,
                          help='输出目录路径 (默认: 基于数据文件名自动生成)')
        
        parser.add_argument('--session-name', type=str,
                          help='会话名称 (默认: 自动生成)')
        
        # 数据分割参数
        parser.add_argument('--test-size', type=float, default=0.2,
                          help='测试集比例 (默认: 0.2)')
        
        parser.add_argument('--cv-folds', type=int, default=5,
                          help='交叉验证折数 (默认: 5)')
        
        # 功能开关
        parser.add_argument('--enable-tuning', action='store_true',
                          help='启用超参数调优 (会显著增加运行时间)')
        
        parser.add_argument('--enable-delong', action='store_true',
                          help='启用DeLong统计检验')
        
        parser.add_argument('--enable-shap', action='store_true',
                          help='启用SHAP可解释性分析')
        
        parser.add_argument('--enable-external-validation', action='store_true',
                          help='启用外部验证 (需要提供验证数据)')
        
        parser.add_argument('--validation-data', type=str,
                          help='外部验证数据文件路径')
        
        # 高级参数
        parser.add_argument('--alpha', type=float, default=0.05,
                          help='DeLong检验显著性水平 (默认: 0.05)')
        
        parser.add_argument('--random-state', type=int, default=42,
                          help='随机种子 (默认: 42)')
        
        parser.add_argument('--gpu', action='store_true',
                          help='启用GPU加速 (如果可用)')
        
        parser.add_argument('--n-jobs', type=int, default=-1,
                          help='并行作业数 (默认: -1, 使用所有CPU核心)')
        
        # 输出控制
        parser.add_argument('--no-plots', action='store_true',
                          help='不生成图表')
        
        parser.add_argument('--no-reports', action='store_true',
                          help='不生成报告')
        
        parser.add_argument('--open-results', action='store_true',
                          help='完成后自动打开结果目录')
        
        # 日志控制
        parser.add_argument('--verbose', '-v', action='store_true',
                          help='详细输出')
        
        parser.add_argument('--quiet', '-q', action='store_true',
                          help='静默模式')
        
        parser.add_argument('--log-file', type=str,
                          help='日志文件路径')
        
        return parser
    
    def setup_output_directory(self, args) -> Path:
        """设置输出目录"""
        if args.output:
            output_dir = Path(args.output)
        else:
            # 基于数据文件名和时间戳自动生成
            data_name = Path(args.data).stem
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_dir = Path(f'results_{data_name}_{timestamp}')
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        (output_dir / 'models').mkdir(exist_ok=True)
        (output_dir / 'plots').mkdir(exist_ok=True)
        (output_dir / 'reports').mkdir(exist_ok=True)
        (output_dir / 'logs').mkdir(exist_ok=True)
        
        return output_dir
    
    def setup_logging(self, args, output_dir: Path):
        """设置日志"""
        log_file = args.log_file or (output_dir / 'logs' / 'pipeline.log')
        
        # 确保日志目录存在
        Path(log_file).parent.mkdir(parents=True, exist_ok=True)
        
        # 设置日志级别
        if args.quiet:
            level = logging.ERROR
        elif args.verbose:
            level = logging.DEBUG
        else:
            level = logging.INFO
        
        # 配置日志
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger.info(f"日志文件: {log_file}")
    
    def get_models(self, args) -> List[str]:
        """获取要训练的模型列表"""
        if args.models:
            return [m.strip() for m in args.models.split(',')]
        else:
            return self.model_strategies[args.strategy]
    
    def print_pipeline_info(self, args, models: List[str], output_dir: Path):
        """打印流水线信息"""
        print("=" * 60)
        print("🚀 二分类分析流水线")
        print("=" * 60)
        print(f"📁 数据文件: {args.data}")
        print(f"🎯 目标列: {args.target}")
        print(f"🤖 模型策略: {args.strategy if not args.models else '自定义'}")
        print(f"📊 模型列表: {', '.join(models)}")
        print(f"📂 输出目录: {output_dir.absolute()}")
        print(f"🔢 测试集比例: {args.test_size}")
        print(f"🔄 交叉验证: {args.cv_folds} 折")
        
        # 功能开关状态
        features = []
        if args.enable_tuning:
            features.append("超参数调优")
        if args.enable_delong:
            features.append("DeLong检验")
        if args.enable_shap:
            features.append("SHAP分析")
        if args.enable_external_validation:
            features.append("外部验证")
        if args.gpu:
            features.append("GPU加速")
        
        if features:
            print(f"⚡ 启用功能: {', '.join(features)}")
        
        print("=" * 60)
        print()
    
    def estimate_runtime(self, models: List[str], enable_tuning: bool) -> str:
        """估算运行时间"""
        base_time_per_model = 30  # 秒
        tuning_multiplier = 5 if enable_tuning else 1
        
        total_seconds = len(models) * base_time_per_model * tuning_multiplier
        
        if total_seconds < 60:
            return f"约 {total_seconds} 秒"
        elif total_seconds < 3600:
            return f"约 {total_seconds // 60} 分钟"
        else:
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            return f"约 {hours} 小时 {minutes} 分钟"
    
    def run_pipeline(self, args):
        """运行完整流水线"""
        start_time = time.time()
        
        try:
            # 1. 设置输出目录
            output_dir = self.setup_output_directory(args)
            
            # 2. 设置日志
            self.setup_logging(args, output_dir)
            
            # 3. 获取模型列表
            models = self.get_models(args)
            
            # 4. 打印流水线信息
            self.print_pipeline_info(args, models, output_dir)
            
            # 5. 估算运行时间
            estimated_time = self.estimate_runtime(models, args.enable_tuning)
            print(f"⏱️  预计运行时间: {estimated_time}")
            print()
            
            # 6. 确认开始
            if not args.quiet:
                response = input("是否开始分析？(y/N): ")
                if response.lower() not in ['y', 'yes', '是']:
                    print("❌ 分析已取消")
                    return
            
            print("🚀 开始分析...")
            print()
            
            # 7. 构建CLI参数
            cli_args = argparse.Namespace(
                data=args.data,
                target=args.target,
                mode='pipeline',
                model=','.join(models),
                output=str(output_dir),
                session_name=args.session_name,
                test_size=args.test_size,
                cv_folds=args.cv_folds,
                random_state=args.random_state,
                no_preprocessing=False,
                no_tuning=not args.enable_tuning,
                alpha=args.alpha,
                gpu=args.gpu,
                verbose=args.verbose,
                quiet=args.quiet,
                strict_reproducibility=False
            )
            
            # 8. 运行主流水线
            self.cli.run(cli_args)
            
            # 9. 额外功能
            if args.enable_shap:
                self.logger.info("执行SHAP分析...")
                print("🔍 执行SHAP分析...")
                # 这里可以添加SHAP分析逻辑
            
            if args.enable_external_validation and args.validation_data:
                self.logger.info("执行外部验证...")
                print("🔬 执行外部验证...")
                # 这里可以添加外部验证逻辑
            
            # 10. 生成流水线摘要
            self.generate_pipeline_summary(args, models, output_dir, start_time)
            
            # 11. 打开结果目录
            if args.open_results:
                self.open_results_directory(output_dir)
            
            print()
            print("🎉 分析流水线执行完成！")
            print(f"📂 结果保存在: {output_dir.absolute()}")
            
        except KeyboardInterrupt:
            print("\n❌ 分析被用户中断")
            sys.exit(1)
        
        except Exception as e:
            self.logger.error(f"流水线执行失败: {e}")
            print(f"\n❌ 分析失败: {e}")
            sys.exit(1)
    
    def generate_pipeline_summary(self, args, models: List[str], output_dir: Path, start_time: float):
        """生成流水线摘要"""
        end_time = time.time()
        duration = end_time - start_time
        
        summary = f"""
# 二分类分析流水线摘要

## 基本信息
- 数据文件: {args.data}
- 目标列: {args.target}
- 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 运行时长: {duration:.2f} 秒 ({duration/60:.1f} 分钟)

## 模型配置
- 策略: {args.strategy if not args.models else '自定义'}
- 模型数量: {len(models)}
- 模型列表: {', '.join(models)}
- 测试集比例: {args.test_size}
- 交叉验证: {args.cv_folds} 折

## 功能配置
- 超参数调优: {'是' if args.enable_tuning else '否'}
- DeLong检验: {'是' if args.enable_delong else '否'}
- SHAP分析: {'是' if args.enable_shap else '否'}
- 外部验证: {'是' if args.enable_external_validation else '否'}
- GPU加速: {'是' if args.gpu else '否'}

## 输出文件
- 模型结果: {output_dir}/models/
- 可视化图表: {output_dir}/plots/
- 性能报告: {output_dir}/reports/
- 日志文件: {output_dir}/logs/

## 使用建议
1. 查看 reports/ 目录中的HTML报告了解模型性能
2. 查看 plots/ 目录中的图表进行可视化分析
3. 如需重现结果，使用相同的随机种子: {args.random_state}

---
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        summary_file = output_dir / 'pipeline_summary.md'
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary)
        
        self.logger.info(f"流水线摘要已保存: {summary_file}")
    
    def open_results_directory(self, output_dir: Path):
        """打开结果目录"""
        try:
            import webbrowser
            import platform
            
            if platform.system() == 'Windows':
                os.startfile(str(output_dir))
            elif platform.system() == 'Darwin':  # macOS
                os.system(f'open "{output_dir}"')
            else:  # Linux
                os.system(f'xdg-open "{output_dir}"')
                
        except Exception as e:
            self.logger.warning(f"无法自动打开结果目录: {e}")


def main():
    """主函数"""
    pipeline = BinaryClassificationPipeline()
    parser = pipeline.create_parser()
    args = parser.parse_args()
    
    pipeline.run_pipeline(args)


if __name__ == '__main__':
    main()
