{"window": {"title": "多模型集成机器学习平台", "size": [1400, 900], "min_size": [1200, 800], "resizable": true, "center_on_screen": true, "position": [260, 90]}, "theme": {"name": "default", "colors": {"primary": "#2E86AB", "secondary": "#A23B72", "success": "#F18F01", "warning": "#C73E1D", "background": "#FFFFFF", "surface": "#F5F5F5", "text_primary": "#212121", "text_secondary": "#757575"}, "fonts": {"default_family": "Microsoft YaHei UI", "default_size": 9, "title_size": 12, "small_size": 8}}, "layout": {"navigation_width": 200, "config_panel_width": 300, "status_bar_height": 25, "toolbar_height": 35, "padding": 10, "spacing": 5}, "components": {"progress_bar": {"height": 20, "show_percentage": true}, "data_table": {"max_rows_display": 100, "column_width": 100}, "chart": {"default_size": [600, 400], "dpi": 100, "style": "seaborn"}}, "behavior": {"auto_save_interval": 300, "max_log_lines": 1000, "confirm_on_exit": true, "remember_window_state": true}}