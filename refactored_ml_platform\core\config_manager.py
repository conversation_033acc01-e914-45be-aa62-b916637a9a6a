#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心配置管理器
管理机器学习平台的核心配置
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, Optional


class ConfigManager:
    """核心配置管理器"""
    
    def __init__(self, config_path: Optional[Path] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        
        # 默认配置文件路径
        if config_path is None:
            project_root = Path(__file__).parent.parent
            config_path = project_root / 'config' / 'core_config.json'
        
        self.config_path = config_path
        self._config = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                self.logger.info(f"已加载配置文件: {self.config_path}")
            else:
                self._config = self._get_default_config()
                self._save_config()
                self.logger.info(f"创建默认配置文件: {self.config_path}")
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self._config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "models": {
                "default_test_size": 0.2,
                "random_state": 42,
                "cv_folds": 5,
                "scoring": "accuracy",
                "n_jobs": -1
            },
            "data": {
                "max_file_size_mb": 500,
                "supported_formats": ["csv", "xlsx", "json"],
                "encoding": "utf-8",
                "missing_threshold": 0.5
            },
            "training": {
                "timeout_seconds": 3600,
                "early_stopping": True,
                "save_models": True,
                "model_dir": "models"
            },
            "visualization": {
                "figure_size": [10, 6],
                "dpi": 100,
                "style": "seaborn",
                "color_palette": "viridis"
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "max_file_size_mb": 10,
                "backup_count": 5
            }
        }
    
    def _save_config(self):
        """保存配置文件"""
        try:
            # 确保配置目录存在
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置已保存到: {self.config_path}")
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any, save: bool = True):
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
            save: 是否立即保存到文件
        """
        keys = key.split('.')
        config = self._config
        
        # 导航到目标位置
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
        
        if save:
            self._save_config()
    
    def get_model_config(self) -> Dict[str, Any]:
        """获取模型配置"""
        return self.get('models', {})
    
    def get_data_config(self) -> Dict[str, Any]:
        """获取数据配置"""
        return self.get('data', {})
    
    def get_training_config(self) -> Dict[str, Any]:
        """获取训练配置"""
        return self.get('training', {})
    
    def get_visualization_config(self) -> Dict[str, Any]:
        """获取可视化配置"""
        return self.get('visualization', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get('logging', {})


# 全局配置管理器实例
_global_config = None


def get_config() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _global_config
    if _global_config is None:
        _global_config = ConfigManager()
    return _global_config

def get_config_manager():
    """获取全局配置管理器实例（别名）"""
    return get_config()
