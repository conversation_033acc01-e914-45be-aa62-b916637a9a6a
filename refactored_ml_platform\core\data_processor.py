#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理器
负责数据的加载、预处理和验证
"""

import logging
import pandas as pd
import numpy as np
from typing import Optional, Tuple, Dict, Any, List
from pathlib import Path
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder

from .config_manager import get_config


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.logger = logging.getLogger(__name__)
        self.config = get_config()
        self.data = None
        self.X = None
        self.y = None
        self.feature_names = None
        self.target_name = None
        self.scalers = {}
        self.encoders = {}
    
    def load_data(self, file_path: str) -> pd.DataFrame:
        """
        加载数据文件
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            加载的数据DataFrame
        """
        try:
            file_path = Path(file_path)
            data_config = self.config.get_data_config()
            
            # 检查文件大小
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            max_size = data_config.get('max_file_size_mb', 500)
            
            if file_size_mb > max_size:
                raise ValueError(f"文件大小 {file_size_mb:.1f}MB 超过限制 {max_size}MB")
            
            # 根据文件扩展名选择加载方式
            if file_path.suffix.lower() == '.csv':
                encoding = data_config.get('encoding', 'utf-8')
                self.data = pd.read_csv(file_path, encoding=encoding)
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                self.data = pd.read_excel(file_path)
            elif file_path.suffix.lower() == '.json':
                self.data = pd.read_json(file_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")
            
            self.logger.info(f"成功加载数据文件: {file_path}")
            self.logger.info(f"数据形状: {self.data.shape}")
            
            return self.data
            
        except Exception as e:
            self.logger.error(f"加载数据文件失败: {e}")
            raise
    
    def prepare_data(self, target_column: Optional[str] = None) -> Tuple[pd.DataFrame, pd.Series]:
        """
        准备训练数据
        
        Args:
            target_column: 目标列名，如果为None则自动检测
            
        Returns:
            特征数据和目标数据的元组
        """
        if self.data is None:
            raise ValueError("请先加载数据")
        
        # 自动检测目标列
        if target_column is None:
            possible_targets = ['label', 'target', 'y', 'class', 'outcome']
            for col in possible_targets:
                if col in self.data.columns:
                    target_column = col
                    break
            
            if target_column is None:
                # 如果没有找到标准目标列，使用最后一列
                target_column = self.data.columns[-1]
                self.logger.warning(f"未找到标准目标列，使用最后一列作为目标: {target_column}")
        
        if target_column not in self.data.columns:
            raise ValueError(f"目标列 '{target_column}' 不存在")
        
        # 分离特征和目标
        self.X = self.data.drop(columns=[target_column])
        self.y = self.data[target_column]
        self.feature_names = self.X.columns.tolist()
        self.target_name = target_column
        
        self.logger.info(f"准备数据完成，特征数: {len(self.feature_names)}, 样本数: {len(self.X)}")
        
        return self.X, self.y
    
    def split_data(self, test_size: Optional[float] = None, 
                  random_state: Optional[int] = None) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series]:
        """
        分割训练和测试数据
        
        Args:
            test_size: 测试集比例
            random_state: 随机种子
            
        Returns:
            X_train, X_test, y_train, y_test
        """
        if self.X is None or self.y is None:
            raise ValueError("请先准备数据")
        
        model_config = self.config.get_model_config()
        
        if test_size is None:
            test_size = model_config.get('default_test_size', 0.2)
        
        if random_state is None:
            random_state = model_config.get('random_state', 42)
        
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y, test_size=test_size, random_state=random_state, stratify=self.y
        )
        
        self.logger.info(f"数据分割完成，训练集: {len(X_train)}, 测试集: {len(X_test)}")
        
        return X_train, X_test, y_train, y_test
    
    def validate_data(self) -> Dict[str, Any]:
        """
        验证数据质量
        
        Returns:
            验证结果字典
        """
        if self.data is None:
            raise ValueError("请先加载数据")
        
        validation_results = {
            'shape': self.data.shape,
            'columns': self.data.columns.tolist(),
            'dtypes': self.data.dtypes.to_dict(),
            'missing_values': self.data.isnull().sum().to_dict(),
            'missing_percentage': (self.data.isnull().sum() / len(self.data) * 100).to_dict(),
            'duplicated_rows': self.data.duplicated().sum(),
            'numeric_columns': self.data.select_dtypes(include=[np.number]).columns.tolist(),
            'categorical_columns': self.data.select_dtypes(include=['object']).columns.tolist(),
            'memory_usage': self.data.memory_usage(deep=True).sum() / 1024 / 1024  # MB
        }
        
        # 检查数据质量问题
        issues = []
        data_config = self.config.get_data_config()
        missing_threshold = data_config.get('missing_threshold', 0.5)
        
        # 检查缺失值过多的列
        for col, pct in validation_results['missing_percentage'].items():
            if pct > missing_threshold * 100:
                issues.append(f"列 '{col}' 缺失值过多 ({pct:.1f}%)")
        
        # 检查重复行
        if validation_results['duplicated_rows'] > 0:
            issues.append(f"存在 {validation_results['duplicated_rows']} 行重复数据")
        
        # 检查数据类型
        for col in validation_results['categorical_columns']:
            unique_count = self.data[col].nunique()
            if unique_count > 50:
                issues.append(f"分类列 '{col}' 唯一值过多 ({unique_count})")
        
        validation_results['issues'] = issues
        validation_results['quality_score'] = max(0, 100 - len(issues) * 10)
        
        self.logger.info(f"数据验证完成，质量评分: {validation_results['quality_score']}")
        
        return validation_results
    
    def preprocess_data(self, X_train: pd.DataFrame, X_test: pd.DataFrame,
                       scaling_method: str = 'standard',
                       handle_missing: str = 'drop') -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        预处理数据
        
        Args:
            X_train: 训练特征
            X_test: 测试特征
            scaling_method: 缩放方法 ('standard', 'minmax', 'none')
            handle_missing: 缺失值处理方法 ('drop', 'mean', 'median', 'mode')
            
        Returns:
            预处理后的训练和测试特征
        """
        X_train_processed = X_train.copy()
        X_test_processed = X_test.copy()
        
        # 处理缺失值
        if handle_missing == 'drop':
            # 删除有缺失值的行
            X_train_processed = X_train_processed.dropna()
            X_test_processed = X_test_processed.dropna()
        elif handle_missing in ['mean', 'median']:
            # 数值列填充
            numeric_cols = X_train_processed.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if handle_missing == 'mean':
                    fill_value = X_train_processed[col].mean()
                else:
                    fill_value = X_train_processed[col].median()
                
                X_train_processed[col].fillna(fill_value, inplace=True)
                X_test_processed[col].fillna(fill_value, inplace=True)
        elif handle_missing == 'mode':
            # 所有列用众数填充
            for col in X_train_processed.columns:
                mode_value = X_train_processed[col].mode()
                if len(mode_value) > 0:
                    fill_value = mode_value[0]
                    X_train_processed[col].fillna(fill_value, inplace=True)
                    X_test_processed[col].fillna(fill_value, inplace=True)
        
        # 编码分类变量
        categorical_cols = X_train_processed.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            encoder = LabelEncoder()
            # 合并训练和测试数据来拟合编码器
            combined_values = pd.concat([X_train_processed[col], X_test_processed[col]]).astype(str)
            encoder.fit(combined_values)
            
            X_train_processed[col] = encoder.transform(X_train_processed[col].astype(str))
            X_test_processed[col] = encoder.transform(X_test_processed[col].astype(str))
            
            self.encoders[col] = encoder
        
        # 特征缩放
        if scaling_method != 'none':
            numeric_cols = X_train_processed.select_dtypes(include=[np.number]).columns
            
            if scaling_method == 'standard':
                scaler = StandardScaler()
            elif scaling_method == 'minmax':
                scaler = MinMaxScaler()
            else:
                raise ValueError(f"不支持的缩放方法: {scaling_method}")
            
            X_train_processed[numeric_cols] = scaler.fit_transform(X_train_processed[numeric_cols])
            X_test_processed[numeric_cols] = scaler.transform(X_test_processed[numeric_cols])
            
            self.scalers[scaling_method] = scaler
        
        self.logger.info(f"数据预处理完成，缩放方法: {scaling_method}, 缺失值处理: {handle_missing}")
        
        return X_train_processed, X_test_processed
    
    def get_data_info(self) -> Dict[str, Any]:
        """获取数据信息"""
        if self.data is None:
            return {}
        
        return {
            'shape': self.data.shape,
            'columns': self.data.columns.tolist(),
            'feature_names': self.feature_names,
            'target_name': self.target_name,
            'data_types': self.data.dtypes.to_dict(),
            'memory_usage_mb': self.data.memory_usage(deep=True).sum() / 1024 / 1024
        }
