#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话管理器
负责训练会话的创建、保存、恢复和管理
"""

import json
import pickle
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging
import uuid

from .config_manager import get_config_manager
# 尝试导入事件管理器，如果失败则使用占位符
try:
    from gui.core.event_manager import get_event_manager
except ImportError:
    # 创建占位符事件管理器
    class DummyEventManager:
        def publish_event(self, event_type, data=None):
            pass
        def subscribe_event(self, event_type, callback):
            pass

    def get_event_manager():
        return DummyEventManager()


class TrainingSession:
    """训练会话类"""
    
    def __init__(self, session_id: str = None, session_name: str = None, description: str = ""):
        """
        初始化训练会话
        
        Args:
            session_id: 会话ID，如果为None则自动生成
            session_name: 会话名称
            description: 会话描述
        """
        self.session_id = session_id or self._generate_session_id()
        self.session_name = session_name or f"Session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.description = description
        
        # 会话元数据
        self.created_time = datetime.now()
        self.modified_time = datetime.now()
        self.status = "created"  # created, active, completed, archived
        
        # 会话数据
        self.models = {}  # 存储训练的模型
        self.results = {}  # 存储训练结果
        self.plots = {}  # 存储生成的图表
        self.configs = {}  # 存储配置信息
        self.data_info = {}  # 存储数据信息
        
        # 文件路径
        self.session_path = None
        
        self.logger = logging.getLogger(f"{__name__}.{self.session_id}")
    
    def _generate_session_id(self) -> str:
        """生成唯一的会话ID"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_id = str(uuid.uuid4())[:8]
        return f"session_{timestamp}_{unique_id}"
    
    def add_model(self, model_name: str, model_data: Any, model_type: str = "single"):
        """
        添加模型到会话
        
        Args:
            model_name: 模型名称
            model_data: 模型数据
            model_type: 模型类型 (single, ensemble, etc.)
        """
        self.models[model_name] = {
            'data': model_data,
            'type': model_type,
            'added_time': datetime.now().isoformat()
        }
        self.modified_time = datetime.now()
        self.logger.info(f"添加模型到会话: {model_name}")
    
    def add_result(self, result_name: str, result_data: Any, result_type: str = "training"):
        """
        添加结果到会话
        
        Args:
            result_name: 结果名称
            result_data: 结果数据
            result_type: 结果类型 (training, validation, etc.)
        """
        self.results[result_name] = {
            'data': result_data,
            'type': result_type,
            'added_time': datetime.now().isoformat()
        }
        self.modified_time = datetime.now()
        self.logger.info(f"添加结果到会话: {result_name}")
    
    def add_plot(self, plot_name: str, plot_data: Any, plot_type: str = "single_model"):
        """
        添加图表到会话
        
        Args:
            plot_name: 图表名称
            plot_data: 图表数据
            plot_type: 图表类型 (single_model, comparison, etc.)
        """
        self.plots[plot_name] = {
            'data': plot_data,
            'type': plot_type,
            'added_time': datetime.now().isoformat()
        }
        self.modified_time = datetime.now()
        self.logger.info(f"添加图表到会话: {plot_name}")
    
    def add_config(self, config_name: str, config_data: Any):
        """
        添加配置到会话
        
        Args:
            config_name: 配置名称
            config_data: 配置数据
        """
        self.configs[config_name] = {
            'data': config_data,
            'added_time': datetime.now().isoformat()
        }
        self.modified_time = datetime.now()
        self.logger.info(f"添加配置到会话: {config_name}")
    
    def get_summary(self) -> Dict[str, Any]:
        """
        获取会话摘要信息
        
        Returns:
            会话摘要字典
        """
        return {
            'session_id': self.session_id,
            'session_name': self.session_name,
            'description': self.description,
            'status': self.status,
            'created_time': self.created_time.isoformat(),
            'modified_time': self.modified_time.isoformat(),
            'model_count': len(self.models),
            'result_count': len(self.results),
            'plot_count': len(self.plots),
            'config_count': len(self.configs)
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将会话转换为字典格式
        
        Returns:
            会话字典
        """
        return {
            'session_id': self.session_id,
            'session_name': self.session_name,
            'description': self.description,
            'created_time': self.created_time.isoformat(),
            'modified_time': self.modified_time.isoformat(),
            'status': self.status,
            'models': {k: {**v, 'data': None} for k, v in self.models.items()},  # 不包含实际数据
            'results': {k: {**v, 'data': None} for k, v in self.results.items()},
            'plots': {k: {**v, 'data': None} for k, v in self.plots.items()},
            'configs': self.configs,
            'data_info': self.data_info
        }


class SessionManager:
    """会话管理器"""
    
    def __init__(self):
        """初始化会话管理器"""
        self.config_manager = get_config_manager()
        self.event_manager = get_event_manager()
        self.logger = logging.getLogger(__name__)
        
        # 会话存储路径
        self.sessions_path = Path(self.config_manager.get('paths.sessions', 'training_sessions'))
        self.sessions_path.mkdir(parents=True, exist_ok=True)
        
        # 当前活动会话
        self.current_session: Optional[TrainingSession] = None
        
        # 会话缓存
        self._session_cache: Dict[str, TrainingSession] = {}
        
        self.logger.info("会话管理器初始化完成")
    
    def create_session(self, session_name: str = None, description: str = "") -> TrainingSession:
        """
        创建新的训练会话
        
        Args:
            session_name: 会话名称
            description: 会话描述
            
        Returns:
            新创建的会话对象
        """
        session = TrainingSession(session_name=session_name, description=description)
        
        # 创建会话目录
        session_dir = self.sessions_path / session.session_id
        session_dir.mkdir(parents=True, exist_ok=True)
        session.session_path = session_dir
        
        # 创建子目录
        (session_dir / 'models').mkdir(exist_ok=True)
        (session_dir / 'results').mkdir(exist_ok=True)
        (session_dir / 'plots').mkdir(exist_ok=True)
        (session_dir / 'configs').mkdir(exist_ok=True)
        
        # 保存会话元数据
        self._save_session_metadata(session)
        
        # 缓存会话
        self._session_cache[session.session_id] = session
        
        # 设置为当前活动会话
        self.current_session = session
        
        # 发布事件
        self.event_manager.publish('session_created', {
            'session_id': session.session_id,
            'session_name': session.session_name
        })
        
        self.logger.info(f"创建新会话: {session.session_id}")
        return session
    
    def save_session(self, session: TrainingSession = None) -> bool:
        """
        保存会话
        
        Args:
            session: 要保存的会话，如果为None则保存当前会话
            
        Returns:
            是否保存成功
        """
        if session is None:
            session = self.current_session
        
        if session is None:
            self.logger.warning("没有可保存的会话")
            return False
        
        try:
            # 保存会话元数据
            self._save_session_metadata(session)
            
            # 保存会话数据
            self._save_session_data(session)
            
            session.status = "completed"
            session.modified_time = datetime.now()
            
            # 发布事件
            self.event_manager.publish('session_saved', {
                'session_id': session.session_id,
                'session_name': session.session_name
            })
            
            self.logger.info(f"保存会话: {session.session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存会话失败: {e}")
            return False
    
    def load_session(self, session_id: str) -> Optional[TrainingSession]:
        """
        加载会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            加载的会话对象
        """
        # 先检查缓存
        if session_id in self._session_cache:
            return self._session_cache[session_id]
        
        session_dir = self.sessions_path / session_id
        if not session_dir.exists():
            self.logger.warning(f"会话目录不存在: {session_id}")
            return None
        
        try:
            # 加载会话元数据
            metadata_file = session_dir / 'session_metadata.json'
            if not metadata_file.exists():
                self.logger.warning(f"会话元数据文件不存在: {session_id}")
                return None
            
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # 创建会话对象
            session = TrainingSession(
                session_id=metadata['session_id'],
                session_name=metadata['session_name'],
                description=metadata['description']
            )
            
            session.created_time = datetime.fromisoformat(metadata['created_time'])
            session.modified_time = datetime.fromisoformat(metadata['modified_time'])
            session.status = metadata['status']
            session.session_path = session_dir
            session.data_info = metadata.get('data_info', {})
            
            # 加载会话数据
            self._load_session_data(session)
            
            # 缓存会话
            self._session_cache[session_id] = session
            
            self.logger.info(f"加载会话: {session_id}")
            return session
            
        except Exception as e:
            self.logger.error(f"加载会话失败: {e}")
            return None
    
    def delete_session(self, session_id: str) -> bool:
        """
        删除会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            是否删除成功
        """
        try:
            session_dir = self.sessions_path / session_id
            if session_dir.exists():
                shutil.rmtree(session_dir)
            
            # 从缓存中移除
            if session_id in self._session_cache:
                del self._session_cache[session_id]
            
            # 如果是当前会话，清空当前会话
            if self.current_session and self.current_session.session_id == session_id:
                self.current_session = None
            
            # 发布事件
            self.event_manager.publish('session_deleted', {
                'session_id': session_id
            })
            
            self.logger.info(f"删除会话: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除会话失败: {e}")
            return False
    
    def list_sessions(self) -> List[Dict[str, Any]]:
        """
        列出所有会话
        
        Returns:
            会话摘要列表
        """
        sessions = []
        
        for session_dir in self.sessions_path.iterdir():
            if session_dir.is_dir():
                metadata_file = session_dir / 'session_metadata.json'
                if metadata_file.exists():
                    try:
                        with open(metadata_file, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                        sessions.append(metadata)
                    except Exception as e:
                        self.logger.warning(f"读取会话元数据失败: {session_dir.name}, {e}")
        
        # 按修改时间排序
        sessions.sort(key=lambda x: x.get('modified_time', ''), reverse=True)
        return sessions
    
    def activate_session(self, session_id: str) -> bool:
        """
        激活会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            是否激活成功
        """
        session = self.load_session(session_id)
        if session:
            self.current_session = session
            session.status = "active"
            
            # 发布事件
            self.event_manager.publish('session_activated', {
                'session_id': session_id,
                'session_name': session.session_name
            })
            
            self.logger.info(f"激活会话: {session_id}")
            return True
        
        return False
    
    def get_current_session(self) -> Optional[TrainingSession]:
        """获取当前活动会话"""
        return self.current_session
    
    def _save_session_metadata(self, session: TrainingSession):
        """保存会话元数据"""
        metadata_file = session.session_path / 'session_metadata.json'
        metadata = session.to_dict()
        
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
    
    def _save_session_data(self, session: TrainingSession):
        """保存会话数据"""
        # 保存模型
        for model_name, model_info in session.models.items():
            model_file = session.session_path / 'models' / f'{model_name}.pkl'
            with open(model_file, 'wb') as f:
                pickle.dump(model_info['data'], f)
        
        # 保存结果
        for result_name, result_info in session.results.items():
            result_file = session.session_path / 'results' / f'{result_name}.pkl'
            with open(result_file, 'wb') as f:
                pickle.dump(result_info['data'], f)
        
        # 保存图表
        for plot_name, plot_info in session.plots.items():
            plot_file = session.session_path / 'plots' / f'{plot_name}.pkl'
            with open(plot_file, 'wb') as f:
                pickle.dump(plot_info['data'], f)
    
    def _load_session_data(self, session: TrainingSession):
        """加载会话数据"""
        # 加载模型信息（不加载实际数据，按需加载）
        models_dir = session.session_path / 'models'
        if models_dir.exists():
            for model_file in models_dir.glob('*.pkl'):
                model_name = model_file.stem
                session.models[model_name] = {
                    'data': None,  # 按需加载
                    'type': 'single',
                    'file_path': str(model_file),
                    'added_time': datetime.fromtimestamp(model_file.stat().st_mtime).isoformat()
                }
        
        # 类似地处理结果和图表
        results_dir = session.session_path / 'results'
        if results_dir.exists():
            for result_file in results_dir.glob('*.pkl'):
                result_name = result_file.stem
                session.results[result_name] = {
                    'data': None,
                    'type': 'training',
                    'file_path': str(result_file),
                    'added_time': datetime.fromtimestamp(result_file.stat().st_mtime).isoformat()
                }
        
        plots_dir = session.session_path / 'plots'
        if plots_dir.exists():
            for plot_file in plots_dir.glob('*.pkl'):
                plot_name = plot_file.stem
                session.plots[plot_name] = {
                    'data': None,
                    'type': 'single_model',
                    'file_path': str(plot_file),
                    'added_time': datetime.fromtimestamp(plot_file.stat().st_mtime).isoformat()
                }


# 全局会话管理器实例
_session_manager = None

def get_session_manager() -> SessionManager:
    """
    获取全局会话管理器实例
    
    Returns:
        会话管理器实例
    """
    global _session_manager
    if _session_manager is None:
        _session_manager = SessionManager()
    return _session_manager
