#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构版多模型集成机器学习平台部署脚本
自动化部署和环境检查
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


class Deployer:
    """部署器类"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.python_executable = sys.executable
        
    def check_python_version(self):
        """检查Python版本"""
        print("检查Python版本...")
        version = sys.version_info
        
        if version.major < 3 or (version.major == 3 and version.minor < 7):
            print("❌ Python版本过低，需要Python 3.7+")
            return False
        
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    
    def install_dependencies(self):
        """安装依赖包"""
        print("\n安装依赖包...")
        requirements_file = self.project_root / 'requirements.txt'
        
        if not requirements_file.exists():
            print("❌ requirements.txt文件不存在")
            return False
        
        try:
            cmd = [self.python_executable, '-m', 'pip', 'install', '-r', str(requirements_file)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 依赖包安装成功")
                return True
            else:
                print(f"❌ 依赖包安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 安装依赖包时出错: {e}")
            return False
    
    def check_directories(self):
        """检查必要的目录结构"""
        print("\n检查目录结构...")
        
        required_dirs = [
            'core',
            'gui',
            'gui/core',
            'gui/components', 
            'gui/modules',
            'gui/layouts',
            'utils',
            'config',
            'logs'
        ]
        
        missing_dirs = []
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if not dir_path.exists():
                missing_dirs.append(dir_name)
        
        if missing_dirs:
            print(f"❌ 缺少目录: {', '.join(missing_dirs)}")
            return False
        
        print("✅ 目录结构完整")
        return True
    
    def check_config_files(self):
        """检查配置文件"""
        print("\n检查配置文件...")
        
        config_files = [
            'config/core_config.json',
            'config/gui_config.json'
        ]
        
        missing_files = []
        for file_name in config_files:
            file_path = self.project_root / file_name
            if not file_path.exists():
                missing_files.append(file_name)
        
        if missing_files:
            print(f"⚠️ 缺少配置文件: {', '.join(missing_files)}")
            print("系统将使用默认配置")
        else:
            print("✅ 配置文件完整")
        
        return True
    
    def create_shortcuts(self):
        """创建快捷方式（Windows）"""
        if platform.system() != 'Windows':
            return True
            
        print("\n创建启动脚本...")
        
        # 创建批处理文件
        bat_content = f'''@echo off
cd /d "{self.project_root}"
"{self.python_executable}" main.py
pause
'''
        
        bat_file = self.project_root / 'start_app.bat'
        try:
            with open(bat_file, 'w', encoding='utf-8') as f:
                f.write(bat_content)
            print("✅ 创建启动脚本: start_app.bat")
            return True
        except Exception as e:
            print(f"❌ 创建启动脚本失败: {e}")
            return False
    
    def test_import(self):
        """测试关键模块导入"""
        print("\n测试模块导入...")
        
        test_imports = [
            ('tkinter', 'GUI框架'),
            ('pandas', '数据处理'),
            ('numpy', '数值计算'),
            ('sklearn', '机器学习'),
            ('matplotlib', '图表绘制')
        ]
        
        failed_imports = []
        for module, description in test_imports:
            try:
                __import__(module)
                print(f"✅ {description} ({module})")
            except ImportError:
                print(f"❌ {description} ({module}) - 导入失败")
                failed_imports.append(module)
        
        if failed_imports:
            print(f"\n⚠️ 以下模块导入失败: {', '.join(failed_imports)}")
            print("请检查依赖包安装情况")
            return False
        
        return True
    
    def deploy(self):
        """执行完整部署"""
        print("=" * 60)
        print("重构版多模型集成机器学习平台 - 部署脚本")
        print("=" * 60)
        
        steps = [
            ("检查Python版本", self.check_python_version),
            ("检查目录结构", self.check_directories),
            ("检查配置文件", self.check_config_files),
            ("安装依赖包", self.install_dependencies),
            ("测试模块导入", self.test_import),
            ("创建启动脚本", self.create_shortcuts)
        ]
        
        failed_steps = []
        for step_name, step_func in steps:
            if not step_func():
                failed_steps.append(step_name)
        
        print("\n" + "=" * 60)
        if not failed_steps:
            print("🎉 部署成功！")
            print("\n启动方式:")
            print("1. 运行: python main.py")
            print("2. 或双击: start_app.bat (Windows)")
        else:
            print("❌ 部署失败！")
            print(f"失败步骤: {', '.join(failed_steps)}")
        print("=" * 60)
        
        return len(failed_steps) == 0


def main():
    """主函数"""
    deployer = Deployer()
    success = deployer.deploy()
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
