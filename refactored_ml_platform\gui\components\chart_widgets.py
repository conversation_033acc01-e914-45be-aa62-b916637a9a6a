#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表组件模块
提供各种图表显示和工具栏组件
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Dict, Any
import pandas as pd

try:
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    try:
        from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
    except ImportError:
        from matplotlib.backends._backend_tk import NavigationToolbar2Tk
    from matplotlib.figure import Figure
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib未安装，图表功能将受限")

from ..core.base_gui import BaseGUI
from ..core.component_factory import get_component_factory


class ChartWidget(BaseGUI):
    """
    图表显示组件
    提供基于matplotlib的图表显示功能
    """
    
    def __init__(self, parent: tk.Widget, chart_type: str = "line", figsize=(8, 6)):
        """初始化图表组件"""
        self.chart_type = chart_type
        self.chart_data = None
        self.figsize = figsize
        self.figure = None
        self.canvas = None
        self.toolbar = None
        super().__init__(parent)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='card')
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        if MATPLOTLIB_AVAILABLE:
            # 创建matplotlib图形
            self.figure = Figure(figsize=self.figsize, dpi=100)
            self.canvas = FigureCanvasTkAgg(self.figure, self.main_frame)
            self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 创建工具栏
            toolbar_frame = factory.create_frame(self.main_frame)
            toolbar_frame.pack(fill=tk.X)
            self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
            self.toolbar.update()
            
            self.register_component('canvas_widget', self.canvas.get_tk_widget())
            self.register_component('toolbar_frame', toolbar_frame)
        else:
            # 占位符标签
            placeholder_label = factory.create_label(
                self.main_frame,
                text=f"📊 {self.chart_type.title()} 图表\n(需要安装matplotlib)",
                style='title'
            )
            placeholder_label.pack(expand=True)
            self.register_component('placeholder_label', placeholder_label)
    
    def load_data(self, data: Any, config: Optional[Dict[str, Any]] = None):
        """加载图表数据并绘制"""
        self.chart_data = data
        if config:
            self.chart_config = config
        
        if MATPLOTLIB_AVAILABLE and self.figure and self.chart_data is not None:
            self._draw_chart()
        
        self.logger.info(f"图表数据已加载: {self.chart_type}")
    
    def _draw_chart(self):
        """绘制图表"""
        if not MATPLOTLIB_AVAILABLE or self.chart_data is None or not self.figure:
            return
        
        try:
            # 清除之前的图表
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            
            # 根据图表类型绘制
            if self.chart_type == "line" and isinstance(self.chart_data, pd.DataFrame):
                self.chart_data.plot(kind='line', ax=ax)
            elif self.chart_type == "bar" and isinstance(self.chart_data, pd.DataFrame):
                self.chart_data.plot(kind='bar', ax=ax)
            elif self.chart_type == "scatter" and isinstance(self.chart_data, pd.DataFrame):
                if len(self.chart_data.columns) >= 2:
                    ax.scatter(self.chart_data.iloc[:, 0], self.chart_data.iloc[:, 1])
                    ax.set_xlabel(str(self.chart_data.columns[0]))
                    ax.set_ylabel(str(self.chart_data.columns[1]))
            elif self.chart_type == "histogram" and isinstance(self.chart_data, pd.DataFrame):
                self.chart_data.hist(ax=ax, bins=30)
            elif self.chart_type == "pie" and isinstance(self.chart_data, pd.Series):
                ax.pie(self.chart_data.values.tolist(), labels=[str(x) for x in self.chart_data.index], autopct='%1.1f%%')
            else:
                # 默认处理
                if isinstance(self.chart_data, pd.DataFrame):
                    self.chart_data.plot(ax=ax)
                elif isinstance(self.chart_data, pd.Series):
                    self.chart_data.plot(ax=ax)
                else:
                    ax.text(0.5, 0.5, '不支持的数据格式', ha='center', va='center', transform=ax.transAxes)
            
            # 设置标题
            ax.set_title(f'{self.chart_type.title()} 图表')
            
            # 调整布局
            if self.figure:
                self.figure.tight_layout()
            
            # 刷新画布
            if self.canvas:
                self.canvas.draw()
            
        except Exception as e:
            self.logger.error(f"绘制图表时出错: {e}")
            # 显示错误信息
            if self.figure and self.canvas:
                ax = self.figure.add_subplot(111)
                ax.text(0.5, 0.5, f'绘制图表时出错:\n{str(e)}', ha='center', va='center', transform=ax.transAxes)
                self.canvas.draw()
    
    def clear_chart(self):
        """清空图表"""
        self.chart_data = None
        if MATPLOTLIB_AVAILABLE and self.figure:
            self.figure.clear()
            if self.canvas:
                self.canvas.draw()
        self.logger.info("图表已清空")
    
    def save_chart(self, filename: str):
        """保存图表"""
        if MATPLOTLIB_AVAILABLE and self.figure:
            try:
                self.figure.savefig(filename, dpi=300, bbox_inches='tight')
                self.logger.info(f"图表已保存: {filename}")
                return True
            except Exception as e:
                self.logger.error(f"保存图表时出错: {e}")
                return False
        return False


class ChartToolbar(BaseGUI):
    """
    图表工具栏组件
    提供图表操作和类型切换功能
    """
    
    def __init__(self, parent: tk.Widget, chart_widget: Optional[ChartWidget] = None):
        """初始化图表工具栏"""
        self.chart_widget = chart_widget
        self.chart_change_callback = None
        super().__init__(parent)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, relief=tk.RAISED, bd=1)
        self.main_frame.pack(fill=tk.X)
        
        # 图表类型按钮
        chart_frame = factory.create_frame(self.main_frame)
        chart_frame.pack(side=tk.LEFT, padx=5)
        
        factory.create_label(chart_frame, text="图表类型:").pack(side=tk.LEFT)
        
        chart_buttons = [
            ("📊 折线图", "line", self._create_line_chart),
            ("📈 柱状图", "bar", self._create_bar_chart),
            ("🥧 饼图", "pie", self._create_pie_chart),
            ("📉 散点图", "scatter", self._create_scatter_chart),
            ("📊 直方图", "histogram", self._create_histogram)
        ]
        
        for text, _, command in chart_buttons:
            btn = factory.create_button(
                chart_frame,
                text=text,
                command=command,
                style='small'
            )
            btn.pack(side=tk.LEFT, padx=2, pady=2)
        
        # 分隔符
        ttk.Separator(self.main_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 操作按钮
        action_frame = factory.create_frame(self.main_frame)
        action_frame.pack(side=tk.LEFT, padx=5)
        
        action_buttons = [
            ("🔄 刷新", self._refresh_chart),
            ("🗑️ 清空", self._clear_chart),
            ("💾 保存", self._save_chart),
            ("📋 复制", self._copy_chart)
        ]
        
        for text, command in action_buttons:
            btn = factory.create_button(
                action_frame,
                text=text,
                command=command,
                style='small'
            )
            btn.pack(side=tk.LEFT, padx=2, pady=2)
    
    def set_chart_widget(self, chart_widget: ChartWidget):
        """设置关联的图表组件"""
        self.chart_widget = chart_widget
    
    def set_chart_change_callback(self, callback):
        """设置图表类型变化回调"""
        self.chart_change_callback = callback
    
    def _create_line_chart(self):
        """创建折线图"""
        self._change_chart_type("line")
    
    def _create_bar_chart(self):
        """创建柱状图"""
        self._change_chart_type("bar")
    
    def _create_pie_chart(self):
        """创建饼图"""
        self._change_chart_type("pie")
    
    def _create_scatter_chart(self):
        """创建散点图"""
        self._change_chart_type("scatter")
    
    def _create_histogram(self):
        """创建直方图"""
        self._change_chart_type("histogram")
    
    def _change_chart_type(self, chart_type: str):
        """改变图表类型"""
        if self.chart_widget:
            self.chart_widget.chart_type = chart_type
            if self.chart_widget.chart_data is not None:
                self.chart_widget._draw_chart()
        
        if self.chart_change_callback:
            self.chart_change_callback(chart_type)
        
        self.logger.info(f"切换到{chart_type}图表")
    
    def _refresh_chart(self):
        """刷新图表"""
        if self.chart_widget and self.chart_widget.chart_data is not None:
            self.chart_widget._draw_chart()
            self.logger.info("图表已刷新")
    
    def _clear_chart(self):
        """清空图表"""
        if self.chart_widget:
            self.chart_widget.clear_chart()
    
    def _save_chart(self):
        """保存图表"""
        if not self.chart_widget or not MATPLOTLIB_AVAILABLE:
            messagebox.showwarning("警告", "无法保存图表")
            return
        
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="保存图表",
                defaultextension=".png",
                filetypes=[
                    ("PNG文件", "*.png"),
                    ("PDF文件", "*.pdf"),
                    ("SVG文件", "*.svg"),
                    ("所有文件", "*.*")
                ]
            )
            
            if filename:
                if self.chart_widget.save_chart(filename):
                    messagebox.showinfo("成功", f"图表已保存到:\n{filename}")
                else:
                    messagebox.showerror("错误", "保存图表失败")
        
        except Exception as e:
            self.logger.error(f"保存图表时出错: {e}")
            messagebox.showerror("错误", f"保存图表时出错:\n{str(e)}")
    
    def _copy_chart(self):
        """复制图表到剪贴板"""
        if self.chart_widget and MATPLOTLIB_AVAILABLE:
            try:
                # 这里可以实现复制到剪贴板的功能
                self.logger.info("图表复制功能（待实现）")
                messagebox.showinfo("提示", "图表复制功能正在开发中")
            except Exception as e:
                self.logger.error(f"复制图表时出错: {e}")
        else:
            messagebox.showwarning("警告", "无法复制图表")
