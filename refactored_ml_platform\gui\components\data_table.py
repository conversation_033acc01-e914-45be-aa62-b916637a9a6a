#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据表格组件
提供数据表格显示和操作功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Any
import logging


class DataTableWidget(ttk.Frame):
    """数据表格组件"""
    
    def __init__(self, parent, **kwargs):
        """初始化数据表格组件"""
        super().__init__(parent, **kwargs)
        
        self.logger = logging.getLogger(__name__)
        self.current_data = None
        
        # 创建界面
        self._create_interface()
        
        self.logger.info("数据表格组件初始化完成")
    
    def _create_interface(self):
        """创建界面"""
        # 工具栏
        toolbar_frame = ttk.Frame(self)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar_frame, text="📋 复制", 
                  command=self._copy_data).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="💾 导出", 
                  command=self._export_data).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="🔍 搜索", 
                  command=self._toggle_search).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="🗑️ 清空", 
                  command=self.clear).pack(side=tk.LEFT, padx=2)
        
        # 信息标签
        self.info_label = ttk.Label(toolbar_frame, text="无数据")
        self.info_label.pack(side=tk.RIGHT, padx=5)
        
        # 搜索框（默认隐藏）
        self.search_frame = ttk.Frame(self)
        
        ttk.Label(self.search_frame, text="搜索:").pack(side=tk.LEFT, padx=5)
        self.search_var = tk.StringVar()
        self.search_var.trace_add('write', self._on_search_change)
        search_entry = ttk.Entry(self.search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(self.search_frame, text="✖", 
                  command=self._toggle_search).pack(side=tk.LEFT, padx=2)
        
        # 表格框架
        table_frame = ttk.Frame(self)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建Treeview
        self.tree = ttk.Treeview(table_frame, show='tree headings')
        
        # 垂直滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        # 水平滚动条
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定事件
        self.tree.bind('<Double-1>', self._on_double_click)
        self.tree.bind('<Button-3>', self._on_right_click)
        
        # 右键菜单
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="复制行", command=self._copy_selected_row)
        self.context_menu.add_command(label="复制列", command=self._copy_selected_column)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="查看详情", command=self._show_cell_details)
    
    def update_data(self, data: pd.DataFrame):
        """更新表格数据"""
        try:
            if data is None or data.empty:
                self.clear()
                return
            
            self.current_data = data.copy()
            
            # 清空现有数据
            self.tree.delete(*self.tree.get_children())
            
            # 设置列
            columns = list(data.columns)
            self.tree['columns'] = columns
            self.tree['show'] = 'headings'
            
            # 配置列标题和宽度
            for col in columns:
                self.tree.heading(col, text=col, anchor=tk.W)
                # 根据数据类型设置列宽
                if data[col].dtype in ['int64', 'float64']:
                    width = 100
                else:
                    width = min(max(len(str(col)) * 8, 80), 200)
                self.tree.column(col, width=width, anchor=tk.W)
            
            # 插入数据
            for index, row in data.iterrows():
                values = []
                for col in columns:
                    value = row[col]
                    if pd.isna(value):
                        values.append("NaN")
                    elif isinstance(value, float):
                        values.append(f"{value:.4f}")
                    else:
                        values.append(str(value))
                
                self.tree.insert('', tk.END, values=values)
            
            # 更新信息标签
            self._update_info_label()
            
            self.logger.info(f"表格数据已更新: {data.shape[0]}行 × {data.shape[1]}列")
            
        except Exception as e:
            self.logger.error(f"更新表格数据失败: {e}")
            messagebox.showerror("错误", f"更新表格数据失败: {e}")
    
    def clear(self):
        """清空表格"""
        self.tree.delete(*self.tree.get_children())
        self.tree['columns'] = ()
        self.current_data = None
        self.info_label.config(text="无数据")
        
        # 隐藏搜索框
        self.search_frame.pack_forget()
    
    def _update_info_label(self):
        """更新信息标签"""
        if self.current_data is not None:
            rows, cols = self.current_data.shape
            self.info_label.config(text=f"{rows} 行 × {cols} 列")
        else:
            self.info_label.config(text="无数据")
    
    def _toggle_search(self):
        """切换搜索框显示"""
        if self.search_frame.winfo_viewable():
            self.search_frame.pack_forget()
            self.search_var.set("")  # 清空搜索
        else:
            self.search_frame.pack(fill=tk.X, padx=5, pady=5, after=self.children[list(self.children.keys())[0]])
    
    def _on_search_change(self, *args):
        """搜索内容变化"""
        if self.current_data is None:
            return
        
        search_text = self.search_var.get().lower()
        
        if not search_text:
            # 如果搜索为空，显示所有数据
            self.update_data(self.current_data)
            return
        
        try:
            # 过滤数据
            mask = self.current_data.astype(str).apply(
                lambda x: x.str.lower().str.contains(search_text, na=False)
            ).any(axis=1)
            
            filtered_data = self.current_data[mask]
            
            # 更新显示
            self._update_tree_with_data(filtered_data)
            
        except Exception as e:
            self.logger.error(f"搜索失败: {e}")
    
    def _update_tree_with_data(self, data: pd.DataFrame):
        """用指定数据更新树形控件"""
        # 清空现有数据
        self.tree.delete(*self.tree.get_children())
        
        # 插入过滤后的数据
        columns = list(data.columns)
        for index, row in data.iterrows():
            values = []
            for col in columns:
                value = row[col]
                if pd.isna(value):
                    values.append("NaN")
                elif isinstance(value, float):
                    values.append(f"{value:.4f}")
                else:
                    values.append(str(value))
            
            self.tree.insert('', tk.END, values=values)
    
    def _copy_data(self):
        """复制所有数据到剪贴板"""
        if self.current_data is None:
            messagebox.showwarning("警告", "没有数据可复制")
            return
        
        try:
            # 将数据转换为制表符分隔的文本
            text_data = self.current_data.to_csv(sep='\t', index=False)
            
            # 复制到剪贴板
            self.clipboard_clear()
            self.clipboard_append(text_data)
            
            messagebox.showinfo("成功", "数据已复制到剪贴板")
            
        except Exception as e:
            self.logger.error(f"复制数据失败: {e}")
            messagebox.showerror("错误", f"复制数据失败: {e}")
    
    def _export_data(self):
        """导出数据到文件"""
        if self.current_data is None:
            messagebox.showwarning("警告", "没有数据可导出")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="导出数据",
            defaultextension=".csv",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Excel files", "*.xlsx"),
                ("TSV files", "*.tsv"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                if file_path.endswith('.xlsx'):
                    self.current_data.to_excel(file_path, index=False)
                elif file_path.endswith('.tsv'):
                    self.current_data.to_csv(file_path, sep='\t', index=False)
                else:
                    self.current_data.to_csv(file_path, index=False)
                
                messagebox.showinfo("成功", f"数据已导出到: {file_path}")
                self.logger.info(f"数据已导出到: {file_path}")
                
            except Exception as e:
                self.logger.error(f"导出数据失败: {e}")
                messagebox.showerror("错误", f"导出数据失败: {e}")
    
    def _on_double_click(self, event):
        """双击事件"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if item:
            self._show_cell_details()
    
    def _on_right_click(self, event):
        """右键点击事件"""
        # 选择点击的项目
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def _copy_selected_row(self):
        """复制选中行"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一行")
            return
        
        try:
            # 获取选中行的数据
            item = selection[0]
            values = self.tree.item(item, 'values')
            
            # 转换为制表符分隔的文本
            text_data = '\t'.join(str(v) for v in values)
            
            # 复制到剪贴板
            self.clipboard_clear()
            self.clipboard_append(text_data)
            
            messagebox.showinfo("成功", "选中行已复制到剪贴板")
            
        except Exception as e:
            self.logger.error(f"复制行失败: {e}")
            messagebox.showerror("错误", f"复制行失败: {e}")
    
    def _copy_selected_column(self):
        """复制选中列"""
        # 这个功能需要更复杂的实现，暂时显示提示
        messagebox.showinfo("提示", "列复制功能正在开发中")
    
    def _show_cell_details(self):
        """显示单元格详情"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一行")
            return
        
        try:
            item = selection[0]
            values = self.tree.item(item, 'values')
            columns = self.tree['columns']
            
            # 创建详情窗口
            detail_window = tk.Toplevel(self)
            detail_window.title("单元格详情")
            detail_window.geometry("400x300")
            
            # 创建文本框显示详情
            text_widget = tk.Text(detail_window, wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(detail_window, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            # 显示数据
            for col, val in zip(columns, values):
                text_widget.insert(tk.END, f"{col}: {val}\n")
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
            
        except Exception as e:
            self.logger.error(f"显示单元格详情失败: {e}")
            messagebox.showerror("错误", f"显示详情失败: {e}")
