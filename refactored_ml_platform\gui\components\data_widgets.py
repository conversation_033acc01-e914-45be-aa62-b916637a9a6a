#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据相关GUI组件
提供数据表格、文件选择器、数据预览等组件
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from typing import Optional, List, Dict, Any, Callable
import pandas as pd
from pathlib import Path

from ..core.base_gui import BaseGUI
from ..core.component_factory import get_component_factory
from ..core.utils import GUIUtils
from ..core.event_manager import EventTypes


class FileSelector(BaseGUI):
    """文件选择器组件"""
    
    def __init__(self, parent: tk.Widget, 
                 title: str = "选择文件",
                 filetypes: List[tuple] = None,
                 on_file_selected: Optional[Callable] = None):
        """
        初始化文件选择器
        
        Args:
            parent: 父组件
            title: 选择器标题
            filetypes: 文件类型过滤器
            on_file_selected: 文件选择回调
        """
        self.title = title
        self.filetypes = filetypes or [("CSV文件", "*.csv"), ("所有文件", "*.*")]
        self.on_file_selected = on_file_selected
        
        super().__init__(parent)
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='card')
        self.main_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 文件路径显示
        path_frame = factory.create_frame(self.main_frame)
        path_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(path_frame, text="文件路径:", style='default').pack(side=tk.LEFT)
        
        self.file_path_var = tk.StringVar()
        self.register_variable('file_path', self.file_path_var)
        
        path_entry = factory.create_entry(path_frame, textvariable=self.file_path_var, state='readonly')
        path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        self.register_component('path_entry', path_entry)
        
        # 浏览按钮
        browse_btn = factory.create_button(path_frame, text="浏览...", 
                                         command=self._browse_file, style='primary')
        browse_btn.pack(side=tk.RIGHT, padx=(5, 0))
        self.register_component('browse_button', browse_btn)
        
        # 文件信息显示
        info_frame = factory.create_frame(self.main_frame)
        info_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        self.file_info_var = tk.StringVar(value="未选择文件")
        info_label = factory.create_label(info_frame, textvariable=self.file_info_var, 
                                        style='small')
        info_label.pack(side=tk.LEFT)
        self.register_component('info_label', info_label)
    
    def _browse_file(self):
        """浏览文件"""
        filename = GUIUtils.browse_file(
            title=self.title,
            filetypes=self.filetypes
        )
        
        if filename:
            self.set_file_path(filename)
    
    def set_file_path(self, file_path: str):
        """
        设置文件路径
        
        Args:
            file_path: 文件路径
        """
        if not file_path:
            return
        
        path = Path(file_path)
        if not path.exists():
            self.show_error("错误", f"文件不存在: {file_path}")
            return
        
        self.file_path_var.set(file_path)
        
        # 更新文件信息
        file_size = GUIUtils.format_file_size(path.stat().st_size)
        self.file_info_var.set(f"文件大小: {file_size}")
        
        # 触发回调
        if self.on_file_selected:
            self.on_file_selected(file_path)
        
        # 发布事件
        self.publish_event(EventTypes.DATA_LOADED, {'file_path': file_path})
    
    def get_file_path(self) -> str:
        """获取当前文件路径"""
        return self.file_path_var.get()
    
    def clear(self):
        """清除选择"""
        self.file_path_var.set("")
        self.file_info_var.set("未选择文件")


class DataTableWidget(BaseGUI):
    """数据表格组件"""
    
    def __init__(self, parent: tk.Widget, 
                 max_rows: int = 100,
                 show_index: bool = True):
        """
        初始化数据表格
        
        Args:
            parent: 父组件
            max_rows: 最大显示行数
            show_index: 是否显示索引
        """
        self.max_rows = max_rows
        self.show_index = show_index
        self.data = None
        
        super().__init__(parent)
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='card')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 工具栏
        toolbar = factory.create_frame(self.main_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(toolbar, text="数据表格", style='title').pack(side=tk.LEFT)
        
        # 导出按钮
        export_btn = factory.create_button(toolbar, text="导出", 
                                         command=self._export_data, style='secondary')
        export_btn.pack(side=tk.RIGHT)
        self.register_component('export_button', export_btn)
        
        # 表格框架
        table_frame = factory.create_frame(self.main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        
        # 创建Treeview
        self.tree = factory.create_treeview(table_frame)
        self.register_component('treeview', self.tree)
        
        # 滚动条
        v_scrollbar = factory.create_scrollbar(table_frame, orient=tk.VERTICAL, 
                                             command=self.tree.yview)
        h_scrollbar = factory.create_scrollbar(table_frame, orient=tk.HORIZONTAL,
                                             command=self.tree.xview)
        
        self.tree.configure(yscrollcommand=v_scrollbar.set, 
                           xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # 状态标签
        self.status_var = tk.StringVar(value="无数据")
        status_label = factory.create_label(self.main_frame, textvariable=self.status_var,
                                          style='small')
        status_label.pack(side=tk.BOTTOM, anchor=tk.W, padx=5, pady=(0, 5))
        self.register_component('status_label', status_label)
    
    def load_data(self, data: pd.DataFrame):
        """
        加载数据到表格
        
        Args:
            data: pandas DataFrame
        """
        self.data = data
        
        # 清除现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if data is None or data.empty:
            self.status_var.set("无数据")
            return
        
        # 设置列
        columns = list(data.columns)
        if self.show_index:
            columns = ['索引'] + columns
        
        self.tree['columns'] = columns
        self.tree['show'] = 'headings'
        
        # 设置列标题和宽度
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100, minwidth=50)
        
        # 限制显示行数
        display_data = data.head(self.max_rows)
        
        # 插入数据
        for idx, row in display_data.iterrows():
            values = list(row.values)
            if self.show_index:
                values = [idx] + values
            
            # 处理NaN值
            values = [str(v) if pd.notna(v) else "" for v in values]
            
            self.tree.insert('', 'end', values=values)
        
        # 更新状态
        total_rows = len(data)
        displayed_rows = len(display_data)
        
        if total_rows > self.max_rows:
            status_text = f"显示 {displayed_rows} / {total_rows} 行 (限制显示前 {self.max_rows} 行)"
        else:
            status_text = f"共 {total_rows} 行 {len(data.columns)} 列"
        
        self.status_var.set(status_text)
    
    def _export_data(self):
        """导出数据"""
        if self.data is None or self.data.empty:
            self.show_warning("警告", "没有数据可导出")
            return
        
        filename = GUIUtils.save_file(
            title="导出数据",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx")],
            defaultextension=".csv"
        )
        
        if filename:
            try:
                if filename.endswith('.xlsx'):
                    self.data.to_excel(filename, index=False)
                else:
                    self.data.to_csv(filename, index=False, encoding='utf-8-sig')
                
                self.show_info("成功", f"数据已导出到: {filename}")
            except Exception as e:
                self.show_error("错误", f"导出失败: {e}")
    
    def clear(self):
        """清除数据"""
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        self.data = None
        self.status_var.set("无数据")


class DataPreviewWidget(BaseGUI):
    """数据预览组件"""
    
    def __init__(self, parent: tk.Widget):
        """初始化数据预览组件"""
        self.data = None
        super().__init__(parent)
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='card')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 标题
        title_frame = factory.create_frame(self.main_frame)
        title_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(title_frame, text="数据预览", style='title').pack(side=tk.LEFT)
        
        # 创建Notebook用于多标签页
        self.notebook = factory.create_notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        self.register_component('notebook', self.notebook)
        
        # 数据表格标签页
        self.table_frame = factory.create_frame(self.notebook)
        self.notebook.add(self.table_frame, text="数据表格")
        
        self.data_table = DataTableWidget(self.table_frame)
        
        # 数据信息标签页
        self.info_frame = factory.create_frame(self.notebook)
        self.notebook.add(self.info_frame, text="数据信息")
        
        self._setup_info_tab()
    
    def _setup_info_tab(self):
        """设置数据信息标签页"""
        factory = get_component_factory()
        
        # 创建滚动文本框显示数据信息
        info_text = factory.create_text(self.info_frame, height=20, state=tk.DISABLED)
        info_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.register_component('info_text', info_text)
        
        # 滚动条
        info_scrollbar = factory.create_scrollbar(self.info_frame, orient=tk.VERTICAL,
                                                 command=info_text.yview)
        info_text.configure(yscrollcommand=info_scrollbar.set)
    
    def load_data(self, data: pd.DataFrame):
        """
        加载数据
        
        Args:
            data: pandas DataFrame
        """
        self.data = data
        
        # 更新数据表格
        self.data_table.load_data(data)
        
        # 更新数据信息
        self._update_info()
    
    def _update_info(self):
        """更新数据信息"""
        info_text = self.get_component('info_text')
        if not info_text or self.data is None:
            return
        
        # 启用编辑
        info_text.config(state=tk.NORMAL)
        info_text.delete(1.0, tk.END)
        
        # 基本信息
        info_lines = [
            "=== 数据基本信息 ===",
            f"数据形状: {self.data.shape[0]} 行 × {self.data.shape[1]} 列",
            f"内存使用: {self.data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB",
            "",
            "=== 列信息 ===",
        ]
        
        # 列信息
        for col in self.data.columns:
            dtype = str(self.data[col].dtype)
            null_count = self.data[col].isnull().sum()
            null_pct = (null_count / len(self.data)) * 100
            
            info_lines.append(f"{col}: {dtype}, 缺失值: {null_count} ({null_pct:.1f}%)")
        
        info_lines.extend([
            "",
            "=== 数值列统计 ===",
        ])
        
        # 数值列统计
        numeric_cols = self.data.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            desc = self.data[numeric_cols].describe()
            info_lines.append(str(desc))
        else:
            info_lines.append("无数值列")
        
        # 显示信息
        info_text.insert(tk.END, "\n".join(info_lines))
        
        # 禁用编辑
        info_text.config(state=tk.DISABLED)
    
    def clear(self):
        """清除数据"""
        self.data = None
        self.data_table.clear()
        
        info_text = self.get_component('info_text')
        if info_text:
            info_text.config(state=tk.NORMAL)
            info_text.delete(1.0, tk.END)
            info_text.config(state=tk.DISABLED)
