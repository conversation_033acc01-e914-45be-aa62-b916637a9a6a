#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对话框组件模块
提供各种对话框和弹窗组件
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Optional, Dict, Any, List, Callable

from ..core.base_gui import BaseGUI
from ..core.component_factory import get_component_factory


class CustomDialog(BaseGUI):
    """
    自定义对话框基类
    提供通用的对话框功能
    """
    
    def __init__(self, parent: tk.Widget, title: str = "对话框", size: tuple = (400, 300)):
        """初始化自定义对话框"""
        self.title = title
        self.size = size
        self.result = None
        
        # 创建顶层窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry(f"{size[0]}x{size[1]}")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        super().__init__(self.dialog)
        
        # 居中显示
        self._center_dialog()
    
    def _setup_ui(self):
        """设置UI - 子类需要重写"""
        pass
    
    def _center_dialog(self):
        """将对话框居中显示"""
        self.dialog.update_idletasks()
        
        # 获取对话框大小
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        
        # 获取屏幕大小
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        
        # 计算居中位置
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def show(self) -> Any:
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result
    
    def close(self, result: Any = None):
        """关闭对话框"""
        self.result = result
        self.dialog.destroy()


class ProgressDialog(CustomDialog):
    """
    进度对话框
    显示长时间操作的进度
    """
    
    def __init__(self, parent: tk.Widget, title: str = "处理中...", message: str = "请稍候..."):
        self.message = message
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value=message)
        
        super().__init__(parent, title, (400, 150))
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        main_frame = factory.create_frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 状态标签
        status_label = factory.create_label(main_frame, textvariable=self.status_var)
        status_label.pack(pady=10)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(
            main_frame,
            variable=self.progress_var,
            maximum=100,
            mode='determinate'
        )
        self.progress_bar.pack(fill=tk.X, pady=10)
        
        # 取消按钮
        cancel_btn = factory.create_button(
            main_frame,
            text="取消",
            command=lambda: self.close("cancelled")
        )
        cancel_btn.pack(pady=10)
    
    def update_progress(self, value: float, status: str = None):
        """更新进度"""
        self.progress_var.set(value)
        if status:
            self.status_var.set(status)
        self.dialog.update()


class SettingsDialog(CustomDialog):
    """
    设置对话框
    提供应用程序设置界面
    """
    
    def __init__(self, parent: tk.Widget, settings: Dict[str, Any] = None):
        self.settings = settings or {}
        self.new_settings = self.settings.copy()
        
        super().__init__(parent, "设置", (500, 400))
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        main_frame = factory.create_frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 设置标签页
        notebook = factory.create_notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 通用设置标签页
        general_frame = factory.create_frame(notebook)
        notebook.add(general_frame, text="通用")
        
        # 占位符内容
        factory.create_label(
            general_frame,
            text="设置功能开发中...\n\n这里将包含：\n• 界面主题设置\n• 语言设置\n• 默认路径设置\n• 性能选项",
            justify=tk.LEFT
        ).pack(padx=20, pady=20)
        
        # 按钮框架
        button_frame = factory.create_frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 确定和取消按钮
        factory.create_button(
            button_frame,
            text="确定",
            command=self._on_ok,
            style='primary'
        ).pack(side=tk.RIGHT, padx=5)
        
        factory.create_button(
            button_frame,
            text="取消",
            command=self._on_cancel
        ).pack(side=tk.RIGHT, padx=5)
    
    def _on_ok(self):
        """确定按钮处理"""
        self.close(self.new_settings)
    
    def _on_cancel(self):
        """取消按钮处理"""
        self.close(None)


class FileDialogHelper:
    """
    文件对话框辅助类
    提供统一的文件选择接口
    """
    
    @staticmethod
    def open_file(title: str = "选择文件", 
                  filetypes: List[tuple] = None,
                  initialdir: str = None) -> Optional[str]:
        """打开文件对话框"""
        if filetypes is None:
            filetypes = [("所有文件", "*.*")]
        
        return filedialog.askopenfilename(
            title=title,
            filetypes=filetypes,
            initialdir=initialdir
        )
    
    @staticmethod
    def save_file(title: str = "保存文件",
                  filetypes: List[tuple] = None,
                  defaultextension: str = None,
                  initialdir: str = None) -> Optional[str]:
        """保存文件对话框"""
        if filetypes is None:
            filetypes = [("所有文件", "*.*")]
        
        return filedialog.asksaveasfilename(
            title=title,
            filetypes=filetypes,
            defaultextension=defaultextension,
            initialdir=initialdir
        )
    
    @staticmethod
    def open_directory(title: str = "选择文件夹",
                       initialdir: str = None) -> Optional[str]:
        """选择文件夹对话框"""
        return filedialog.askdirectory(
            title=title,
            initialdir=initialdir
        )


class MessageHelper:
    """
    消息对话框辅助类
    提供统一的消息显示接口
    """
    
    @staticmethod
    def show_info(title: str, message: str, parent: tk.Widget = None):
        """显示信息对话框"""
        messagebox.showinfo(title, message, parent=parent)
    
    @staticmethod
    def show_warning(title: str, message: str, parent: tk.Widget = None):
        """显示警告对话框"""
        messagebox.showwarning(title, message, parent=parent)
    
    @staticmethod
    def show_error(title: str, message: str, parent: tk.Widget = None):
        """显示错误对话框"""
        messagebox.showerror(title, message, parent=parent)
    
    @staticmethod
    def ask_yes_no(title: str, message: str, parent: tk.Widget = None) -> bool:
        """显示是/否确认对话框"""
        return messagebox.askyesno(title, message, parent=parent)
    
    @staticmethod
    def ask_ok_cancel(title: str, message: str, parent: tk.Widget = None) -> bool:
        """显示确定/取消对话框"""
        return messagebox.askokcancel(title, message, parent=parent)
