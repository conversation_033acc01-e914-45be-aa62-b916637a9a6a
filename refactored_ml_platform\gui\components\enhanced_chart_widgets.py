#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强图表组件模块
提供高级数据可视化功能，包括统计图表、模型结果可视化等
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Dict, Any, Union
import pandas as pd
import numpy as np

try:
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    try:
        from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
    except ImportError:
        from matplotlib.backends._backend_tk import NavigationToolbar2Tk
    from matplotlib.figure import Figure
    import matplotlib.pyplot as plt
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib/seaborn未安装，高级图表功能将受限")

from ..core.base_gui import BaseGUI
from ..core.component_factory import get_component_factory


class EnhancedChartWidget(BaseGUI):
    """
    增强图表显示组件
    支持多种统计图表和模型结果可视化
    """
    
    def __init__(self, parent: tk.Widget, figsize=(10, 6)):
        """初始化增强图表组件"""
        self.figsize = figsize
        self.figure = None
        self.canvas = None
        self.toolbar = None
        self.current_data = None
        self.current_config = {}
        super().__init__(parent)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='card')
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        if MATPLOTLIB_AVAILABLE:
            # 设置matplotlib样式
            plt.style.use('seaborn-v0_8' if hasattr(plt.style, 'available') and 'seaborn-v0_8' in plt.style.available else 'default')
            
            # 创建matplotlib图形
            self.figure = Figure(figsize=self.figsize, dpi=100)
            self.canvas = FigureCanvasTkAgg(self.figure, self.main_frame)
            self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 创建工具栏
            toolbar_frame = factory.create_frame(self.main_frame)
            toolbar_frame.pack(fill=tk.X)
            self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
            self.toolbar.update()
            
            self.register_component('canvas_widget', self.canvas.get_tk_widget())
            self.register_component('toolbar_frame', toolbar_frame)
        else:
            # 占位符标签
            placeholder_label = factory.create_label(
                self.main_frame,
                text="📊 高级图表组件\n(需要安装matplotlib和seaborn)",
                style='title'
            )
            placeholder_label.pack(expand=True)
            self.register_component('placeholder_label', placeholder_label)
    
    def create_data_exploration_chart(self, data: pd.DataFrame, chart_type: str, 
                                    x_col: str, y_col: Optional[str] = None, 
                                    group_col: Optional[str] = None, **kwargs):
        """创建数据探索图表"""
        if not MATPLOTLIB_AVAILABLE or not self.figure:
            return False
        
        try:
            self.figure.clear()
            
            if chart_type == "histogram":
                self._create_histogram(data, x_col, group_col, **kwargs)
            elif chart_type == "boxplot":
                self._create_boxplot(data, x_col, y_col, group_col, **kwargs)
            elif chart_type == "scatter":
                self._create_scatter(data, x_col, y_col, group_col, **kwargs)
            elif chart_type == "correlation":
                self._create_correlation_heatmap(data, **kwargs)
            elif chart_type == "distribution":
                self._create_distribution_plot(data, x_col, group_col, **kwargs)
            elif chart_type == "pairplot":
                self._create_pairplot(data, group_col, **kwargs)
            elif chart_type == "violin":
                self._create_violin_plot(data, x_col, y_col, group_col, **kwargs)
            else:
                self._create_default_plot(data, x_col, y_col)
            
            self.figure.tight_layout()
            if self.canvas:
                self.canvas.draw()
            return True
            
        except Exception as e:
            self.logger.error(f"创建数据探索图表时出错: {e}")
            self._show_error_message(f"创建图表时出错: {str(e)}")
            return False
    
    def create_model_result_chart(self, result_type: str, model_results: Dict[str, Any], **kwargs):
        """创建模型结果图表"""
        if not MATPLOTLIB_AVAILABLE or not self.figure:
            return False
        
        try:
            self.figure.clear()
            
            if result_type == "roc_curve":
                self._create_roc_curve(model_results, **kwargs)
            elif result_type == "confusion_matrix":
                self._create_confusion_matrix(model_results, **kwargs)
            elif result_type == "feature_importance":
                self._create_feature_importance(model_results, **kwargs)
            elif result_type == "learning_curve":
                self._create_learning_curve(model_results, **kwargs)
            elif result_type == "precision_recall":
                self._create_precision_recall_curve(model_results, **kwargs)
            elif result_type == "calibration":
                self._create_calibration_curve(model_results, **kwargs)
            elif result_type == "model_comparison":
                self._create_model_comparison(model_results, **kwargs)
            else:
                self._show_error_message(f"不支持的结果类型: {result_type}")
                return False
            
            self.figure.tight_layout()
            if self.canvas:
                self.canvas.draw()
            return True
            
        except Exception as e:
            self.logger.error(f"创建模型结果图表时出错: {e}")
            self._show_error_message(f"创建模型结果图表时出错: {str(e)}")
            return False
    
    def _create_histogram(self, data: pd.DataFrame, x_col: str, group_col: Optional[str] = None, **kwargs):
        """创建直方图"""
        ax = self.figure.add_subplot(111)
        
        if group_col and group_col in data.columns:
            for group in data[group_col].unique():
                group_data = data[data[group_col] == group][x_col]
                ax.hist(group_data, alpha=0.7, label=str(group), bins=kwargs.get('bins', 30))
            ax.legend()
        else:
            ax.hist(data[x_col], bins=kwargs.get('bins', 30), alpha=0.7)
        
        ax.set_xlabel(x_col)
        ax.set_ylabel('频次')
        ax.set_title(f'{x_col} 分布直方图')
        ax.grid(True, alpha=0.3)
    
    def _create_boxplot(self, data: pd.DataFrame, x_col: str, y_col: Optional[str] = None, 
                       group_col: Optional[str] = None, **kwargs):
        """创建箱线图"""
        ax = self.figure.add_subplot(111)
        
        if y_col and y_col in data.columns:
            if group_col and group_col in data.columns:
                # 分组箱线图
                groups = data[group_col].unique()
                positions = range(len(groups))
                box_data = [data[data[group_col] == group][y_col].values for group in groups]
                ax.boxplot(box_data, positions=positions, labels=groups)
                ax.set_xlabel(group_col)
            else:
                # 简单箱线图
                ax.boxplot([data[y_col].values], labels=[y_col])
            ax.set_ylabel(y_col)
            ax.set_title(f'{y_col} 箱线图')
        else:
            # 数值列的箱线图
            numeric_cols = data.select_dtypes(include=[np.number]).columns[:5]  # 最多5列
            if len(numeric_cols) > 0:
                ax.boxplot([data[col].values for col in numeric_cols], labels=numeric_cols)
                ax.set_title('数值变量箱线图')
        
        ax.grid(True, alpha=0.3)
    
    def _create_scatter(self, data: pd.DataFrame, x_col: str, y_col: Optional[str] = None, 
                       group_col: Optional[str] = None, **kwargs):
        """创建散点图"""
        ax = self.figure.add_subplot(111)
        
        if not y_col or y_col not in data.columns:
            # 如果没有y列，使用索引
            y_data = range(len(data))
            y_label = '索引'
        else:
            y_data = data[y_col]
            y_label = y_col
        
        if group_col and group_col in data.columns:
            for group in data[group_col].unique():
                mask = data[group_col] == group
                ax.scatter(data[mask][x_col], y_data[mask] if isinstance(y_data, pd.Series) else [y_data[i] for i in range(len(data)) if mask.iloc[i]], 
                          label=str(group), alpha=0.7)
            ax.legend()
        else:
            ax.scatter(data[x_col], y_data, alpha=0.7)
        
        ax.set_xlabel(x_col)
        ax.set_ylabel(y_label)
        ax.set_title(f'{x_col} vs {y_label} 散点图')
        ax.grid(True, alpha=0.3)
    
    def _create_correlation_heatmap(self, data: pd.DataFrame, **kwargs):
        """创建相关性热图"""
        ax = self.figure.add_subplot(111)
        
        # 只选择数值列
        numeric_data = data.select_dtypes(include=[np.number])
        if len(numeric_data.columns) < 2:
            ax.text(0.5, 0.5, '数据中数值列不足，无法创建相关性热图', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # 计算相关性矩阵
        corr_matrix = numeric_data.corr()
        
        # 创建热图
        im = ax.imshow(corr_matrix, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)
        
        # 设置标签
        ax.set_xticks(range(len(corr_matrix.columns)))
        ax.set_yticks(range(len(corr_matrix.columns)))
        ax.set_xticklabels(corr_matrix.columns, rotation=45, ha='right')
        ax.set_yticklabels(corr_matrix.columns)
        
        # 添加数值标注
        for i in range(len(corr_matrix.columns)):
            for j in range(len(corr_matrix.columns)):
                ax.text(j, i, f'{corr_matrix.iloc[i, j]:.2f}', 
                       ha='center', va='center', color='black' if abs(corr_matrix.iloc[i, j]) < 0.5 else 'white')
        
        # 添加颜色条
        self.figure.colorbar(im, ax=ax)
        ax.set_title('特征相关性热图')
    
    def _create_distribution_plot(self, data: pd.DataFrame, x_col: str, group_col: Optional[str] = None, **kwargs):
        """创建分布图"""
        ax = self.figure.add_subplot(111)
        
        if group_col and group_col in data.columns:
            for group in data[group_col].unique():
                group_data = data[data[group_col] == group][x_col]
                ax.hist(group_data, alpha=0.5, label=str(group), density=True, bins=30)
            ax.legend()
        else:
            ax.hist(data[x_col], alpha=0.7, density=True, bins=30)
        
        ax.set_xlabel(x_col)
        ax.set_ylabel('密度')
        ax.set_title(f'{x_col} 概率密度分布')
        ax.grid(True, alpha=0.3)
    
    def _create_pairplot(self, data: pd.DataFrame, group_col: Optional[str] = None, **kwargs):
        """创建配对图（简化版）"""
        numeric_cols = data.select_dtypes(include=[np.number]).columns[:4]  # 最多4列
        if len(numeric_cols) < 2:
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, '数据中数值列不足，无法创建配对图', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        n_cols = len(numeric_cols)
        for i, col1 in enumerate(numeric_cols):
            for j, col2 in enumerate(numeric_cols):
                ax = self.figure.add_subplot(n_cols, n_cols, i * n_cols + j + 1)
                
                if i == j:
                    # 对角线：直方图
                    ax.hist(data[col1], bins=20, alpha=0.7)
                    ax.set_title(col1)
                else:
                    # 非对角线：散点图
                    ax.scatter(data[col2], data[col1], alpha=0.5, s=10)
                    if i == n_cols - 1:
                        ax.set_xlabel(col2)
                    if j == 0:
                        ax.set_ylabel(col1)
    
    def _create_violin_plot(self, data: pd.DataFrame, x_col: str, y_col: Optional[str] = None, 
                           group_col: Optional[str] = None, **kwargs):
        """创建小提琴图（简化版）"""
        ax = self.figure.add_subplot(111)
        
        if y_col and y_col in data.columns:
            if group_col and group_col in data.columns:
                groups = data[group_col].unique()
                positions = range(len(groups))
                violin_data = [data[data[group_col] == group][y_col].values for group in groups]
                ax.violinplot(violin_data, positions=positions)
                ax.set_xticks(positions)
                ax.set_xticklabels(groups)
                ax.set_xlabel(group_col)
            else:
                ax.violinplot([data[y_col].values])
            ax.set_ylabel(y_col)
            ax.set_title(f'{y_col} 小提琴图')
        else:
            ax.text(0.5, 0.5, '需要指定Y轴变量', ha='center', va='center', transform=ax.transAxes)
        
        ax.grid(True, alpha=0.3)
    
    def _create_default_plot(self, data: pd.DataFrame, x_col: str, y_col: Optional[str] = None):
        """创建默认图表"""
        ax = self.figure.add_subplot(111)
        
        if y_col and y_col in data.columns:
            ax.plot(data[x_col], data[y_col], marker='o', markersize=3)
            ax.set_ylabel(y_col)
        else:
            ax.plot(data[x_col], marker='o', markersize=3)
        
        ax.set_xlabel(x_col)
        ax.set_title(f'{x_col} 趋势图')
        ax.grid(True, alpha=0.3)
    
    def _create_roc_curve(self, model_results: Dict[str, Any], **kwargs):
        """创建ROC曲线"""
        ax = self.figure.add_subplot(111)
        
        # 模拟ROC曲线数据
        fpr = np.linspace(0, 1, 100)
        tpr = np.sqrt(fpr)  # 简单的模拟曲线
        auc_score = 0.85  # 模拟AUC分数
        
        ax.plot(fpr, tpr, 'b-', linewidth=2, label=f'ROC曲线 (AUC = {auc_score:.3f})')
        ax.plot([0, 1], [0, 1], 'r--', linewidth=1, label='随机分类器')
        
        ax.set_xlabel('假阳性率 (FPR)')
        ax.set_ylabel('真阳性率 (TPR)')
        ax.set_title('ROC曲线')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _create_confusion_matrix(self, model_results: Dict[str, Any], **kwargs):
        """创建混淆矩阵"""
        ax = self.figure.add_subplot(111)
        
        # 模拟混淆矩阵数据
        cm = np.array([[85, 15], [20, 80]])
        classes = ['类别0', '类别1']
        
        im = ax.imshow(cm, interpolation='nearest', cmap='Blues')
        ax.set_title('混淆矩阵')
        
        # 设置标签
        tick_marks = np.arange(len(classes))
        ax.set_xticks(tick_marks)
        ax.set_yticks(tick_marks)
        ax.set_xticklabels(classes)
        ax.set_yticklabels(classes)
        
        # 添加数值标注
        thresh = cm.max() / 2.
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                ax.text(j, i, format(cm[i, j], 'd'),
                       ha="center", va="center",
                       color="white" if cm[i, j] > thresh else "black")
        
        ax.set_ylabel('真实标签')
        ax.set_xlabel('预测标签')
        self.figure.colorbar(im, ax=ax)
    
    def _create_feature_importance(self, model_results: Dict[str, Any], **kwargs):
        """创建特征重要性图"""
        ax = self.figure.add_subplot(111)
        
        # 模拟特征重要性数据
        features = [f'特征{i+1}' for i in range(10)]
        importance = np.random.exponential(0.1, 10)
        importance = importance / importance.sum()
        
        # 排序
        sorted_idx = np.argsort(importance)
        pos = np.arange(sorted_idx.shape[0]) + .5
        
        ax.barh(pos, importance[sorted_idx], align='center')
        ax.set_yticks(pos)
        ax.set_yticklabels([features[i] for i in sorted_idx])
        ax.set_xlabel('重要性')
        ax.set_title('特征重要性')
        ax.grid(True, alpha=0.3)
    
    def _create_learning_curve(self, model_results: Dict[str, Any], **kwargs):
        """创建学习曲线"""
        ax = self.figure.add_subplot(111)
        
        # 模拟学习曲线数据
        train_sizes = np.linspace(0.1, 1.0, 10)
        train_scores = 1 - np.exp(-train_sizes * 3) + np.random.normal(0, 0.02, 10)
        val_scores = 1 - np.exp(-train_sizes * 2.5) + np.random.normal(0, 0.03, 10)
        
        ax.plot(train_sizes, train_scores, 'o-', color='blue', label='训练分数')
        ax.plot(train_sizes, val_scores, 'o-', color='red', label='验证分数')
        
        ax.set_xlabel('训练集大小比例')
        ax.set_ylabel('准确率')
        ax.set_title('学习曲线')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _create_precision_recall_curve(self, model_results: Dict[str, Any], **kwargs):
        """创建精确率-召回率曲线"""
        ax = self.figure.add_subplot(111)
        
        # 模拟PR曲线数据
        recall = np.linspace(0, 1, 100)
        precision = 1 - recall * 0.5 + np.random.normal(0, 0.05, 100)
        precision = np.clip(precision, 0, 1)
        
        ax.plot(recall, precision, 'b-', linewidth=2, label='PR曲线')
        ax.axhline(y=0.5, color='r', linestyle='--', label='基线')
        
        ax.set_xlabel('召回率')
        ax.set_ylabel('精确率')
        ax.set_title('精确率-召回率曲线')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _create_calibration_curve(self, model_results: Dict[str, Any], **kwargs):
        """创建校准曲线"""
        ax = self.figure.add_subplot(111)
        
        # 模拟校准曲线数据
        mean_predicted_value = np.linspace(0, 1, 10)
        fraction_of_positives = mean_predicted_value + np.random.normal(0, 0.05, 10)
        fraction_of_positives = np.clip(fraction_of_positives, 0, 1)
        
        ax.plot(mean_predicted_value, fraction_of_positives, 'o-', linewidth=2, label='校准曲线')
        ax.plot([0, 1], [0, 1], 'r--', label='完美校准')
        
        ax.set_xlabel('平均预测概率')
        ax.set_ylabel('正例比例')
        ax.set_title('校准曲线')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _create_model_comparison(self, model_results: Dict[str, Any], **kwargs):
        """创建模型对比图"""
        ax = self.figure.add_subplot(111)
        
        # 模拟模型对比数据
        models = ['随机森林', '支持向量机', '逻辑回归', '神经网络', '梯度提升']
        scores = [0.85, 0.82, 0.78, 0.88, 0.86]
        
        bars = ax.bar(models, scores, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'])
        
        # 添加数值标注
        for bar, score in zip(bars, scores):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{score:.3f}', ha='center', va='bottom')
        
        ax.set_ylabel('准确率')
        ax.set_title('模型性能对比')
        ax.set_ylim(0, 1)
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
        ax.grid(True, alpha=0.3)
    
    def _show_error_message(self, message: str):
        """显示错误信息"""
        if self.figure:
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, message, ha='center', va='center', transform=ax.transAxes,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral", alpha=0.7))
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
    
    def clear_chart(self):
        """清空图表"""
        if MATPLOTLIB_AVAILABLE and self.figure:
            self.figure.clear()
            if self.canvas:
                self.canvas.draw()
        self.logger.info("图表已清空")
    
    def save_chart(self, filename: str):
        """保存图表"""
        if MATPLOTLIB_AVAILABLE and self.figure:
            try:
                self.figure.savefig(filename, dpi=300, bbox_inches='tight')
                self.logger.info(f"图表已保存: {filename}")
                return True
            except Exception as e:
                self.logger.error(f"保存图表时出错: {e}")
                return False
        return False
