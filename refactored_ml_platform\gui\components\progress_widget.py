#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度条组件
提供进度显示和状态管理功能
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
from typing import Optional, Callable
import logging


class ProgressWidget(ttk.Frame):
    """进度条组件"""
    
    def __init__(self, parent, **kwargs):
        """初始化进度条组件"""
        super().__init__(parent, **kwargs)
        
        self.logger = logging.getLogger(__name__)
        self.is_running = False
        self.animation_thread = None
        
        # 创建界面
        self._create_interface()
        
        self.logger.info("进度条组件初始化完成")
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_bar = ttk.Progressbar(
            main_frame, 
            mode='indeterminate',
            length=200
        )
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 取消按钮（默认隐藏）
        self.cancel_button = ttk.Button(
            main_frame, 
            text="取消", 
            command=self._on_cancel,
            state=tk.DISABLED
        )
        self.cancel_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 回调函数
        self.cancel_callback = None
    
    def start(self, message: str = "处理中...", show_cancel: bool = False, 
              cancel_callback: Optional[Callable] = None):
        """开始进度显示"""
        if self.is_running:
            return
        
        self.is_running = True
        self.status_label.config(text=message)
        self.progress_bar.start(10)  # 每10ms更新一次
        
        # 设置取消按钮
        if show_cancel and cancel_callback:
            self.cancel_callback = cancel_callback
            self.cancel_button.config(state=tk.NORMAL)
        else:
            self.cancel_button.config(state=tk.DISABLED)
        
        self.logger.info(f"进度开始: {message}")
    
    def update(self, message: str):
        """更新进度消息"""
        if self.is_running:
            self.status_label.config(text=message)
            self.logger.debug(f"进度更新: {message}")
    
    def complete(self, message: str = "完成"):
        """完成进度"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.progress_bar.stop()
        self.status_label.config(text=message)
        self.cancel_button.config(state=tk.DISABLED)
        self.cancel_callback = None
        
        # 短暂显示完成状态后重置
        self.after(2000, self._reset_to_ready)
        
        self.logger.info(f"进度完成: {message}")
    
    def error(self, message: str = "出错"):
        """显示错误状态"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.progress_bar.stop()
        self.status_label.config(text=f"❌ {message}")
        self.cancel_button.config(state=tk.DISABLED)
        self.cancel_callback = None
        
        # 显示错误状态后重置
        self.after(3000, self._reset_to_ready)
        
        self.logger.error(f"进度错误: {message}")
    
    def stop(self):
        """停止进度"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.progress_bar.stop()
        self.status_label.config(text="已停止")
        self.cancel_button.config(state=tk.DISABLED)
        self.cancel_callback = None
        
        # 短暂显示停止状态后重置
        self.after(2000, self._reset_to_ready)
        
        self.logger.info("进度已停止")
    
    def _reset_to_ready(self):
        """重置到就绪状态"""
        if not self.is_running:
            self.status_label.config(text="就绪")
    
    def _on_cancel(self):
        """取消按钮点击事件"""
        if self.cancel_callback:
            try:
                self.cancel_callback()
            except Exception as e:
                self.logger.error(f"执行取消回调失败: {e}")
        
        self.stop()
    
    def is_active(self) -> bool:
        """检查是否正在运行"""
        return self.is_running
    
    def set_determinate_mode(self, maximum: int = 100):
        """设置为确定进度模式"""
        self.progress_bar.config(mode='determinate', maximum=maximum)
        self.progress_bar['value'] = 0
    
    def set_indeterminate_mode(self):
        """设置为不确定进度模式"""
        self.progress_bar.config(mode='indeterminate')
    
    def set_progress(self, value: int):
        """设置进度值（仅在确定模式下有效）"""
        if self.progress_bar['mode'] == 'determinate':
            self.progress_bar['value'] = value
            self.update_idletasks()  # 立即更新显示


class ProgressDialog:
    """进度对话框"""
    
    def __init__(self, parent, title: str = "处理中", message: str = "请稍候..."):
        """初始化进度对话框"""
        self.parent = parent
        self.dialog = None
        self.progress_widget = None
        self.is_cancelled = False
        
        self.title = title
        self.message = message
        
        self.logger = logging.getLogger(__name__)
    
    def show(self, show_cancel: bool = False, cancel_callback: Optional[Callable] = None):
        """显示进度对话框"""
        # 创建对话框窗口
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("400x120")
        self.dialog.resizable(False, False)
        
        # 设置为模态对话框
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self._center_dialog()
        
        # 创建内容
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 消息标签
        message_label = ttk.Label(main_frame, text=self.message, font=('Arial', 10))
        message_label.pack(pady=(0, 10))
        
        # 进度条
        self.progress_widget = ProgressWidget(main_frame)
        self.progress_widget.pack(fill=tk.X)
        
        # 开始进度
        self.progress_widget.start(
            message="处理中...", 
            show_cancel=show_cancel, 
            cancel_callback=self._on_cancel if show_cancel else None
        )
        
        # 设置取消回调
        self.cancel_callback = cancel_callback
        
        # 禁用关闭按钮
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_close)
        
        self.logger.info(f"进度对话框已显示: {self.title}")
    
    def update(self, message: str):
        """更新进度消息"""
        if self.progress_widget:
            self.progress_widget.update(message)
    
    def complete(self, message: str = "完成"):
        """完成进度并关闭对话框"""
        if self.progress_widget:
            self.progress_widget.complete(message)
        
        # 延迟关闭对话框
        self.dialog.after(1000, self._close_dialog)
        
        self.logger.info(f"进度对话框完成: {message}")
    
    def error(self, message: str = "出错"):
        """显示错误并关闭对话框"""
        if self.progress_widget:
            self.progress_widget.error(message)
        
        # 延迟关闭对话框
        self.dialog.after(2000, self._close_dialog)
        
        self.logger.error(f"进度对话框错误: {message}")
    
    def close(self):
        """关闭对话框"""
        self._close_dialog()
    
    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        
        # 获取对话框大小
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        # 获取父窗口位置和大小
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # 计算居中位置
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
    
    def _on_cancel(self):
        """取消按钮点击事件"""
        self.is_cancelled = True
        
        if self.cancel_callback:
            try:
                self.cancel_callback()
            except Exception as e:
                self.logger.error(f"执行取消回调失败: {e}")
        
        self._close_dialog()
    
    def _on_close(self):
        """窗口关闭事件"""
        # 如果正在运行，则不允许直接关闭
        if self.progress_widget and self.progress_widget.is_active():
            return
        
        self._close_dialog()
    
    def _close_dialog(self):
        """关闭对话框"""
        if self.dialog:
            self.dialog.grab_release()
            self.dialog.destroy()
            self.dialog = None
            self.progress_widget = None
        
        self.logger.info("进度对话框已关闭")
    
    def is_cancelled_by_user(self) -> bool:
        """检查是否被用户取消"""
        return self.is_cancelled
