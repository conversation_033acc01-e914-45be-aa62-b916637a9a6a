#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度相关GUI组件
提供进度条、状态指示器、日志显示等组件
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional, List, Callable
import threading
import queue
from datetime import datetime

from ..core.base_gui import BaseGUI
from ..core.component_factory import get_component_factory


class ProgressWidget(BaseGUI):
    """进度条组件"""
    
    def __init__(self, parent: tk.Widget, 
                 show_percentage: bool = True,
                 show_status: bool = True):
        """
        初始化进度条组件
        
        Args:
            parent: 父组件
            show_percentage: 是否显示百分比
            show_status: 是否显示状态文本
        """
        self.show_percentage = show_percentage
        self.show_status = show_status
        self.current_value = 0
        self.max_value = 100
        
        super().__init__(parent)
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent)
        self.main_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 状态文本
        if self.show_status:
            self.status_var = tk.StringVar(value="准备中...")
            status_label = factory.create_label(self.main_frame, textvariable=self.status_var,
                                              style='default')
            status_label.pack(anchor=tk.W)
            self.register_variable('status', self.status_var)
            self.register_component('status_label', status_label)
        
        # 进度条框架
        progress_frame = factory.create_frame(self.main_frame)
        progress_frame.pack(fill=tk.X, pady=(5, 0))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = factory.create_progressbar(progress_frame, 
                                                      variable=self.progress_var,
                                                      maximum=100)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.register_variable('progress', self.progress_var)
        self.register_component('progress_bar', self.progress_bar)
        
        # 百分比显示
        if self.show_percentage:
            self.percentage_var = tk.StringVar(value="0%")
            percentage_label = factory.create_label(progress_frame, 
                                                   textvariable=self.percentage_var,
                                                   style='small', width=6)
            percentage_label.pack(side=tk.RIGHT, padx=(5, 0))
            self.register_variable('percentage', self.percentage_var)
            self.register_component('percentage_label', percentage_label)
    
    def set_progress(self, value: float, status: str = None):
        """
        设置进度
        
        Args:
            value: 进度值 (0-100)
            status: 状态文本
        """
        self.current_value = max(0, min(100, value))
        self.progress_var.set(self.current_value)
        
        if self.show_percentage:
            self.percentage_var.set(f"{self.current_value:.1f}%")
        
        if status and self.show_status:
            self.status_var.set(status)
    
    def reset(self):
        """重置进度"""
        self.set_progress(0, "准备中...")
    
    def complete(self, status: str = "完成"):
        """设置为完成状态"""
        self.set_progress(100, status)


class StatusIndicator(BaseGUI):
    """状态指示器组件"""
    
    def __init__(self, parent: tk.Widget):
        """初始化状态指示器"""
        self.current_status = "idle"
        super().__init__(parent)
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent)
        self.main_frame.pack(fill=tk.X, padx=5, pady=2)
        
        # 状态指示圆点
        self.indicator_canvas = tk.Canvas(self.main_frame, width=12, height=12, 
                                        highlightthickness=0)
        self.indicator_canvas.pack(side=tk.LEFT, padx=(0, 5))
        self.register_component('indicator', self.indicator_canvas)
        
        # 状态文本
        self.status_var = tk.StringVar(value="就绪")
        status_label = factory.create_label(self.main_frame, textvariable=self.status_var,
                                          style='small')
        status_label.pack(side=tk.LEFT)
        self.register_variable('status', self.status_var)
        self.register_component('status_label', status_label)
        
        # 时间戳
        self.time_var = tk.StringVar()
        time_label = factory.create_label(self.main_frame, textvariable=self.time_var,
                                        style='small')
        time_label.pack(side=tk.RIGHT)
        self.register_variable('time', self.time_var)
        self.register_component('time_label', time_label)
        
        # 初始状态
        self.set_status("idle", "就绪")
    
    def set_status(self, status: str, message: str = None):
        """
        设置状态
        
        Args:
            status: 状态类型 (idle, running, success, error, warning)
            message: 状态消息
        """
        self.current_status = status
        
        # 状态颜色映射
        colors = {
            'idle': '#808080',      # 灰色
            'running': '#2E86AB',   # 蓝色
            'success': '#28A745',   # 绿色
            'error': '#DC3545',     # 红色
            'warning': '#FFC107'    # 黄色
        }
        
        # 更新指示器颜色
        color = colors.get(status, '#808080')
        self.indicator_canvas.delete("all")
        self.indicator_canvas.create_oval(2, 2, 10, 10, fill=color, outline=color)
        
        # 更新状态文本
        if message:
            self.status_var.set(message)
        
        # 更新时间戳
        self.time_var.set(datetime.now().strftime("%H:%M:%S"))


class LogViewer(BaseGUI):
    """日志查看器组件"""
    
    def __init__(self, parent: tk.Widget, 
                 max_lines: int = 1000,
                 auto_scroll: bool = True):
        """
        初始化日志查看器
        
        Args:
            parent: 父组件
            max_lines: 最大显示行数
            auto_scroll: 是否自动滚动
        """
        self.max_lines = max_lines
        self.auto_scroll = auto_scroll
        self.log_queue = queue.Queue()
        self.line_count = 0
        
        super().__init__(parent)
        
        # 启动日志处理线程
        self._start_log_processor()
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='card')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 工具栏
        toolbar = factory.create_frame(self.main_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(toolbar, text="日志", style='title').pack(side=tk.LEFT)
        
        # 清除按钮
        clear_btn = factory.create_button(toolbar, text="清除", 
                                        command=self.clear_logs, style='secondary')
        clear_btn.pack(side=tk.RIGHT, padx=(5, 0))
        self.register_component('clear_button', clear_btn)
        
        # 保存按钮
        save_btn = factory.create_button(toolbar, text="保存", 
                                       command=self.save_logs, style='secondary')
        save_btn.pack(side=tk.RIGHT)
        self.register_component('save_button', save_btn)
        
        # 日志文本框架
        log_frame = factory.create_frame(self.main_frame)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        
        # 日志文本框
        self.log_text = factory.create_text(log_frame, height=15, state=tk.DISABLED,
                                           font=("Consolas", 9))
        self.register_component('log_text', self.log_text)
        
        # 滚动条
        scrollbar = factory.create_scrollbar(log_frame, orient=tk.VERTICAL,
                                           command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 配置文本标签样式
        self.log_text.tag_configure("INFO", foreground="black")
        self.log_text.tag_configure("WARNING", foreground="orange")
        self.log_text.tag_configure("ERROR", foreground="red")
        self.log_text.tag_configure("DEBUG", foreground="gray")
    
    def _start_log_processor(self):
        """启动日志处理线程"""
        def process_logs():
            while True:
                try:
                    log_entry = self.log_queue.get(timeout=0.1)
                    if log_entry is None:  # 停止信号
                        break
                    
                    # 在主线程中更新UI
                    self.main_frame.after(0, self._add_log_to_ui, log_entry)
                except queue.Empty:
                    continue
        
        self.log_thread = threading.Thread(target=process_logs, daemon=True)
        self.log_thread.start()
    
    def _add_log_to_ui(self, log_entry: dict):
        """在UI中添加日志条目"""
        level = log_entry.get('level', 'INFO')
        message = log_entry.get('message', '')
        timestamp = log_entry.get('timestamp', datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        # 格式化日志消息
        formatted_message = f"[{timestamp}] [{level}] {message}\n"
        
        # 启用编辑
        self.log_text.config(state=tk.NORMAL)
        
        # 检查行数限制
        if self.line_count >= self.max_lines:
            # 删除最早的行
            self.log_text.delete(1.0, 2.0)
            self.line_count -= 1
        
        # 添加新日志
        self.log_text.insert(tk.END, formatted_message, level)
        self.line_count += 1
        
        # 自动滚动到底部
        if self.auto_scroll:
            self.log_text.see(tk.END)
        
        # 禁用编辑
        self.log_text.config(state=tk.DISABLED)
    
    def add_log(self, level: str, message: str):
        """
        添加日志条目
        
        Args:
            level: 日志级别 (INFO, WARNING, ERROR, DEBUG)
            message: 日志消息
        """
        log_entry = {
            'level': level.upper(),
            'message': message,
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self.log_queue.put(log_entry)
    
    def clear_logs(self):
        """清除所有日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.line_count = 0
    
    def save_logs(self):
        """保存日志到文件"""
        from ..core.utils import GUIUtils
        
        filename = GUIUtils.save_file(
            title="保存日志",
            filetypes=[("文本文件", "*.txt"), ("日志文件", "*.log")],
            defaultextension=".txt"
        )
        
        if filename:
            try:
                content = self.log_text.get(1.0, tk.END)
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.add_log("INFO", f"日志已保存到: {filename}")
            except Exception as e:
                self.add_log("ERROR", f"保存日志失败: {e}")
    
    def destroy(self):
        """销毁组件"""
        # 停止日志处理线程
        self.log_queue.put(None)
        super().destroy()
