#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
状态指示器组件
提供直观的状态显示和用户反馈
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Optional, Callable
import threading
import time

from ..core.base_gui import BaseGUI
from ..core.component_factory import get_component_factory


class StatusIndicator(BaseGUI):
    """状态指示器组件"""
    
    def __init__(self, parent):
        """初始化状态指示器"""
        self.current_status = "idle"
        self.status_colors = {
            "idle": "#6c757d",      # 灰色 - 空闲
            "loading": "#007bff",   # 蓝色 - 加载中
            "running": "#ffc107",   # 黄色 - 运行中
            "success": "#28a745",   # 绿色 - 成功
            "warning": "#fd7e14",   # 橙色 - 警告
            "error": "#dc3545"      # 红色 - 错误
        }
        
        self.status_icons = {
            "idle": "⚪",
            "loading": "🔄",
            "running": "⚡",
            "success": "✅",
            "warning": "⚠️",
            "error": "❌"
        }
        
        self.status_messages = {
            "idle": "就绪",
            "loading": "加载中...",
            "running": "运行中...",
            "success": "完成",
            "warning": "警告",
            "error": "错误"
        }
        
        # 动画相关
        self.animation_running = False
        self.animation_thread = None
        
        super().__init__(parent)
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent)
        self.main_frame.pack(side='left', padx=5)
        
        # 状态指示器
        self.status_frame = factory.create_frame(self.main_frame)
        self.status_frame.pack(side='left')
        
        # 状态图标
        self.icon_var = tk.StringVar(value=self.status_icons["idle"])
        self.icon_label = factory.create_label(
            self.status_frame,
            textvariable=self.icon_var,
            font=('Arial', 12)
        )
        self.icon_label.pack(side='left', padx=(0, 5))
        
        # 状态文本
        self.status_var = tk.StringVar(value=self.status_messages["idle"])
        self.status_label = factory.create_label(
            self.status_frame,
            textvariable=self.status_var,
            font=('Arial', 9)
        )
        self.status_label.pack(side='left')
        
        # 设置初始颜色
        self._update_colors()
    
    def set_status(self, status: str, message: Optional[str] = None):
        """
        设置状态
        
        Args:
            status: 状态类型 (idle, loading, running, success, warning, error)
            message: 自定义消息
        """
        if status not in self.status_colors:
            status = "idle"
        
        self.current_status = status
        
        # 更新图标
        self.icon_var.set(self.status_icons[status])
        
        # 更新文本
        if message:
            self.status_var.set(message)
        else:
            self.status_var.set(self.status_messages[status])
        
        # 更新颜色
        self._update_colors()
        
        # 处理动画
        if status in ["loading", "running"]:
            self._start_animation()
        else:
            self._stop_animation()
    
    def _update_colors(self):
        """更新颜色"""
        color = self.status_colors[self.current_status]
        self.status_label.config(foreground=color)
        
        # 根据状态设置背景色（淡化版本）
        if self.current_status == "error":
            bg_color = "#ffe6e6"
        elif self.current_status == "warning":
            bg_color = "#fff3cd"
        elif self.current_status == "success":
            bg_color = "#e6ffe6"
        elif self.current_status in ["loading", "running"]:
            bg_color = "#e6f3ff"
        else:
            bg_color = self.main_frame.cget('bg')
        
        self.status_frame.config(bg=bg_color)
        self.icon_label.config(bg=bg_color)
        self.status_label.config(bg=bg_color)
    
    def _start_animation(self):
        """开始动画"""
        if not self.animation_running:
            self.animation_running = True
            self.animation_thread = threading.Thread(target=self._animate, daemon=True)
            self.animation_thread.start()
    
    def _stop_animation(self):
        """停止动画"""
        self.animation_running = False
    
    def _animate(self):
        """动画循环"""
        animation_chars = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
        char_index = 0
        
        while self.animation_running:
            if self.current_status in ["loading", "running"]:
                try:
                    # 更新动画字符
                    self.icon_var.set(animation_chars[char_index])
                    char_index = (char_index + 1) % len(animation_chars)
                    
                    time.sleep(0.1)
                except:
                    break
            else:
                break
    
    def pulse(self, duration: float = 1.0):
        """脉冲效果"""
        def pulse_animation():
            original_color = self.status_colors[self.current_status]
            steps = 20
            
            for i in range(steps):
                # 计算透明度
                alpha = 0.3 + 0.7 * (1 + math.cos(2 * math.pi * i / steps)) / 2
                
                # 这里简化处理，实际可以使用更复杂的颜色混合
                if i % 4 == 0:  # 每4步更新一次，减少闪烁
                    try:
                        if alpha > 0.7:
                            self.status_label.config(font=('Arial', 9, 'bold'))
                        else:
                            self.status_label.config(font=('Arial', 9))
                    except:
                        break
                
                time.sleep(duration / steps)
            
            # 恢复原始状态
            try:
                self.status_label.config(font=('Arial', 9))
            except:
                pass
        
        threading.Thread(target=pulse_animation, daemon=True).start()
    
    def show_temporary_message(self, message: str, status: str = "info", duration: float = 3.0):
        """
        显示临时消息
        
        Args:
            message: 消息内容
            status: 状态类型
            duration: 显示时长（秒）
        """
        # 保存当前状态
        original_status = self.current_status
        original_message = self.status_var.get()
        
        # 显示临时消息
        self.set_status(status, message)
        
        # 定时恢复
        def restore_status():
            time.sleep(duration)
            try:
                self.set_status(original_status, original_message)
            except:
                pass
        
        threading.Thread(target=restore_status, daemon=True).start()


class EnhancedProgressDialog:
    """增强的进度对话框"""
    
    def __init__(self, parent, title: str = "处理中", cancelable: bool = False):
        """
        初始化进度对话框
        
        Args:
            parent: 父窗口
            title: 对话框标题
            cancelable: 是否可取消
        """
        self.parent = parent
        self.title = title
        self.cancelable = cancelable
        self.cancelled = False
        self.dialog = None
        
        # 回调函数
        self.cancel_callback: Optional[Callable] = None
    
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("400x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = self.parent.winfo_rootx() + (self.parent.winfo_width() // 2) - 200
        y = self.parent.winfo_rooty() + (self.parent.winfo_height() // 2) - 100
        self.dialog.geometry(f"+{x}+{y}")
        
        # 创建内容
        self._create_content()
        
        # 如果不可取消，禁用关闭按钮
        if not self.cancelable:
            self.dialog.protocol("WM_DELETE_WINDOW", lambda: None)
    
    def _create_content(self):
        """创建对话框内容"""
        factory = get_component_factory()
        
        main_frame = factory.create_frame(self.dialog)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 状态指示器
        self.status_indicator = StatusIndicator(main_frame)
        self.status_indicator.main_frame.pack(pady=(0, 20))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            main_frame,
            variable=self.progress_var,
            maximum=100,
            mode='determinate'
        )
        self.progress_bar.pack(fill='x', pady=(0, 10))
        
        # 详细信息
        self.detail_var = tk.StringVar(value="准备中...")
        detail_label = factory.create_label(
            main_frame,
            textvariable=self.detail_var,
            style='info'
        )
        detail_label.pack(pady=(0, 20))
        
        # 取消按钮
        if self.cancelable:
            cancel_btn = factory.create_button(
                main_frame,
                text="取消",
                command=self._cancel,
                style='default'
            )
            cancel_btn.pack()
    
    def update_progress(self, value: float, status: str = "running", 
                       message: str = None, detail: str = None):
        """
        更新进度
        
        Args:
            value: 进度值 (0-100)
            status: 状态
            message: 状态消息
            detail: 详细信息
        """
        if self.dialog and self.dialog.winfo_exists():
            self.progress_var.set(value)
            self.status_indicator.set_status(status, message)
            
            if detail:
                self.detail_var.set(detail)
            
            self.dialog.update()
    
    def _cancel(self):
        """取消操作"""
        self.cancelled = True
        if self.cancel_callback:
            self.cancel_callback()
        self.close()
    
    def close(self):
        """关闭对话框"""
        if self.dialog and self.dialog.winfo_exists():
            self.dialog.destroy()
    
    def set_cancel_callback(self, callback: Callable):
        """设置取消回调"""
        self.cancel_callback = callback


# 全局状态管理
_global_status_indicator = None

def get_global_status_indicator(parent=None) -> Optional[StatusIndicator]:
    """获取全局状态指示器"""
    global _global_status_indicator
    if _global_status_indicator is None and parent:
        _global_status_indicator = StatusIndicator(parent)
    return _global_status_indicator
