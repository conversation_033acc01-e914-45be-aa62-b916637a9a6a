#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户指导组件
提供新手引导和操作提示功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Optional, Callable
import json
from pathlib import Path

from ..core.component_factory import get_component_factory


class UserGuideWidget:
    """用户指导组件"""
    
    def __init__(self, parent):
        """初始化用户指导组件"""
        self.parent = parent
        self.factory = get_component_factory()
        
        # 指导步骤
        self.guide_steps = {
            'data_management': [
                {
                    'title': '步骤1：加载数据',
                    'description': '点击"选择文件"按钮，选择您的CSV数据文件',
                    'tips': '支持的格式：CSV文件，确保数据包含特征列和目标列'
                },
                {
                    'title': '步骤2：数据预览',
                    'description': '查看数据预览，确认数据格式正确',
                    'tips': '检查是否有缺失值、异常值等数据质量问题'
                },
                {
                    'title': '步骤3：数据预处理',
                    'description': '选择目标列，配置预处理参数',
                    'tips': '目标列应该是您要预测的变量'
                }
            ],
            'model_training': [
                {
                    'title': '步骤1：选择模型',
                    'description': '在模型列表中选择要训练的算法',
                    'tips': '建议初学者从逻辑回归或随机森林开始'
                },
                {
                    'title': '步骤2：配置参数',
                    'description': '调整训练参数，或使用默认设置',
                    'tips': '默认参数通常能获得不错的结果'
                },
                {
                    'title': '步骤3：开始训练',
                    'description': '点击"开始训练"按钮，等待训练完成',
                    'tips': '训练时间取决于数据大小和模型复杂度'
                }
            ],
            'visualization': [
                {
                    'title': '步骤1：选择图表类型',
                    'description': '根据需要选择要生成的图表',
                    'tips': 'ROC曲线和混淆矩阵是评估模型的重要工具'
                },
                {
                    'title': '步骤2：生成图表',
                    'description': '点击"生成图表"按钮创建可视化',
                    'tips': '可以保存图表用于报告或演示'
                }
            ]
        }
        
        # 当前指导状态
        self.current_module = None
        self.current_step = 0
        self.guide_window = None
        
        # 用户偏好
        self.show_tips = True
        self.auto_guide = True
        
        self._load_user_preferences()
    
    def show_module_guide(self, module_name: str):
        """显示模块指导"""
        if module_name not in self.guide_steps:
            return
        
        if not self.auto_guide:
            return
        
        self.current_module = module_name
        self.current_step = 0
        self._show_guide_window()
    
    def _show_guide_window(self):
        """显示指导窗口"""
        if self.guide_window and self.guide_window.winfo_exists():
            self.guide_window.lift()
            return
        
        self.guide_window = tk.Toplevel(self.parent)
        self.guide_window.title("操作指导")
        self.guide_window.geometry("500x400")
        self.guide_window.resizable(False, False)
        
        # 设置窗口图标和样式
        self.guide_window.transient(self.parent)
        self.guide_window.grab_set()
        
        self._create_guide_content()
    
    def _create_guide_content(self):
        """创建指导内容"""
        main_frame = self.factory.create_frame(self.guide_window)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 标题
        title_frame = self.factory.create_frame(main_frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        module_names = {
            'data_management': '数据管理',
            'model_training': '模型训练',
            'visualization': '结果可视化'
        }
        
        title_text = f"{module_names.get(self.current_module, '操作')}指导"
        title_label = self.factory.create_label(
            title_frame, 
            text=title_text,
            style='title'
        )
        title_label.pack()
        
        # 步骤内容
        content_frame = self.factory.create_frame(main_frame)
        content_frame.pack(fill='both', expand=True)
        
        steps = self.guide_steps.get(self.current_module, [])
        if steps and self.current_step < len(steps):
            step = steps[self.current_step]
            
            # 步骤标题
            step_title = self.factory.create_label(
                content_frame,
                text=step['title'],
                style='subtitle'
            )
            step_title.pack(anchor='w', pady=(0, 10))
            
            # 步骤描述
            desc_text = self.factory.create_text(
                content_frame,
                height=6,
                wrap='word'
            )
            desc_text.pack(fill='both', expand=True, pady=(0, 10))
            desc_text.insert('1.0', step['description'])
            desc_text.config(state='disabled')
            
            # 提示信息
            if self.show_tips and 'tips' in step:
                tips_frame = self.factory.create_labelframe(content_frame, text="💡 提示")
                tips_frame.pack(fill='x', pady=(10, 0))
                
                tips_label = self.factory.create_label(
                    tips_frame,
                    text=step['tips'],
                    style='info'
                )
                tips_label.pack(padx=10, pady=10)
        
        # 控制按钮
        button_frame = self.factory.create_frame(main_frame)
        button_frame.pack(fill='x', pady=(20, 0))
        
        # 上一步按钮
        prev_btn = self.factory.create_button(
            button_frame,
            text="⬅️ 上一步",
            command=self._prev_step,
            style='default'
        )
        prev_btn.pack(side='left')
        
        if self.current_step == 0:
            prev_btn.config(state='disabled')
        
        # 下一步按钮
        next_btn = self.factory.create_button(
            button_frame,
            text="下一步 ➡️",
            command=self._next_step,
            style='primary'
        )
        next_btn.pack(side='right')
        
        steps = self.guide_steps.get(self.current_module, [])
        if self.current_step >= len(steps) - 1:
            next_btn.config(text="完成", command=self._finish_guide)
        
        # 跳过按钮
        skip_btn = self.factory.create_button(
            button_frame,
            text="跳过指导",
            command=self._skip_guide,
            style='default'
        )
        skip_btn.pack(side='right', padx=(0, 10))
        
        # 设置选项
        options_frame = self.factory.create_frame(main_frame)
        options_frame.pack(fill='x', pady=(10, 0))
        
        self.show_tips_var = tk.BooleanVar(value=self.show_tips)
        tips_cb = self.factory.create_checkbutton(
            options_frame,
            text="显示提示信息",
            variable=self.show_tips_var,
            command=self._toggle_tips
        )
        tips_cb.pack(side='left')
        
        self.auto_guide_var = tk.BooleanVar(value=self.auto_guide)
        auto_cb = self.factory.create_checkbutton(
            options_frame,
            text="自动显示指导",
            variable=self.auto_guide_var,
            command=self._toggle_auto_guide
        )
        auto_cb.pack(side='right')
    
    def _prev_step(self):
        """上一步"""
        if self.current_step > 0:
            self.current_step -= 1
            self._refresh_guide_content()
    
    def _next_step(self):
        """下一步"""
        steps = self.guide_steps.get(self.current_module, [])
        if self.current_step < len(steps) - 1:
            self.current_step += 1
            self._refresh_guide_content()
    
    def _finish_guide(self):
        """完成指导"""
        self.guide_window.destroy()
        messagebox.showinfo("完成", "指导完成！您现在可以开始使用该功能了。")
    
    def _skip_guide(self):
        """跳过指导"""
        self.guide_window.destroy()
    
    def _toggle_tips(self):
        """切换提示显示"""
        self.show_tips = self.show_tips_var.get()
        self._save_user_preferences()
        self._refresh_guide_content()
    
    def _toggle_auto_guide(self):
        """切换自动指导"""
        self.auto_guide = self.auto_guide_var.get()
        self._save_user_preferences()
    
    def _refresh_guide_content(self):
        """刷新指导内容"""
        if self.guide_window and self.guide_window.winfo_exists():
            # 清空现有内容
            for widget in self.guide_window.winfo_children():
                widget.destroy()
            
            # 重新创建内容
            self._create_guide_content()
    
    def _load_user_preferences(self):
        """加载用户偏好"""
        try:
            config_file = Path.cwd() / 'config' / 'user_guide.json'
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    prefs = json.load(f)
                    self.show_tips = prefs.get('show_tips', True)
                    self.auto_guide = prefs.get('auto_guide', True)
        except Exception:
            # 使用默认设置
            pass
    
    def _save_user_preferences(self):
        """保存用户偏好"""
        try:
            config_dir = Path.cwd() / 'config'
            config_dir.mkdir(exist_ok=True)
            
            config_file = config_dir / 'user_guide.json'
            prefs = {
                'show_tips': self.show_tips,
                'auto_guide': self.auto_guide
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(prefs, f, indent=2, ensure_ascii=False)
        except Exception:
            # 忽略保存错误
            pass
    
    def show_quick_tip(self, parent, message: str, title: str = "提示"):
        """显示快速提示"""
        if not self.show_tips:
            return
        
        tip_window = tk.Toplevel(parent)
        tip_window.title(title)
        tip_window.geometry("300x150")
        tip_window.resizable(False, False)
        tip_window.transient(parent)
        
        # 居中显示
        tip_window.update_idletasks()
        x = parent.winfo_rootx() + (parent.winfo_width() // 2) - (tip_window.winfo_width() // 2)
        y = parent.winfo_rooty() + (parent.winfo_height() // 2) - (tip_window.winfo_height() // 2)
        tip_window.geometry(f"+{x}+{y}")
        
        # 内容
        frame = self.factory.create_frame(tip_window)
        frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        label = self.factory.create_label(frame, text=message, style='info')
        label.pack(expand=True)
        
        btn = self.factory.create_button(
            frame,
            text="知道了",
            command=tip_window.destroy,
            style='primary'
        )
        btn.pack(pady=(10, 0))
        
        # 3秒后自动关闭
        tip_window.after(3000, tip_window.destroy)


# 全局用户指导实例
_user_guide = None

def get_user_guide(parent=None) -> UserGuideWidget:
    """获取全局用户指导实例"""
    global _user_guide
    if _user_guide is None and parent:
        _user_guide = UserGuideWidget(parent)
    return _user_guide
