#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI基础类模块
提供所有GUI模块的基础类和通用功能
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional, Dict, Any, Callable, Union
import logging
from abc import ABC, abstractmethod

from .event_manager import EventManager, get_event_manager
from .config_manager import GUIConfig, get_gui_config


class BaseGUI(ABC):
    """
    GUI基础抽象类
    所有GUI模块都应继承此类，提供统一的接口和通用功能
    """
    
    def __init__(self, parent: Optional[Union[tk.Tk, tk.Widget]] = None, 
                 event_manager: Optional[EventManager] = None,
                 config: Optional[GUIConfig] = None):
        """
        初始化基础GUI
        
        Args:
            parent: 父组件
            event_manager: 事件管理器
            config: 配置管理器
        """
        self.parent = parent
        self.event_manager = event_manager or get_event_manager()
        self.config = config or get_gui_config()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 组件容器
        self.components: Dict[str, tk.Widget] = {}
        self.variables: Dict[str, tk.Variable] = {}
        
        # 主容器
        self.main_frame: Optional[tk.Frame] = None
        
        # 初始化UI
        self._setup_ui()
        
        # 绑定事件
        self._bind_events()
    
    @abstractmethod
    def _setup_ui(self) -> None:
        """设置UI界面，子类必须实现"""
        pass
    
    def _bind_events(self) -> None:
        """绑定事件，子类可以重写"""
        pass
    
    def create_frame(self, parent: Optional[Union[tk.Tk, tk.Widget]] = None, **kwargs) -> tk.Frame:
        """
        创建框架组件
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            框架组件
        """
        if parent is None:
            parent = self.parent
        
        frame = tk.Frame(parent, **kwargs)
        return frame
    
    def create_label(self, parent: Optional[Union[tk.Tk, tk.Widget]] = None, 
                    text: str = "", **kwargs) -> tk.Label:
        """
        创建标签组件
        
        Args:
            parent: 父组件
            text: 标签文本
            **kwargs: 其他参数
            
        Returns:
            标签组件
        """
        if parent is None:
            parent = self.main_frame
        
        label = tk.Label(parent, text=text, **kwargs)
        return label
    
    def create_button(self, parent: Optional[Union[tk.Tk, tk.Widget]] = None,
                     text: str = "", command: Optional[Callable] = None,
                     **kwargs) -> tk.Button:
        """
        创建按钮组件
        
        Args:
            parent: 父组件
            text: 按钮文本
            command: 点击回调
            **kwargs: 其他参数
            
        Returns:
            按钮组件
        """
        if parent is None:
            parent = self.main_frame
        
        if command is not None:
            button = tk.Button(parent, text=text, command=command, **kwargs)
        else:
            button = tk.Button(parent, text=text, **kwargs)
        return button
    
    def create_entry(self, parent: Optional[Union[tk.Tk, tk.Widget]] = None,
                    textvariable: Optional[tk.StringVar] = None,
                    **kwargs) -> tk.Entry:
        """
        创建输入框组件
        
        Args:
            parent: 父组件
            textvariable: 文本变量
            **kwargs: 其他参数
            
        Returns:
            输入框组件
        """
        if parent is None:
            parent = self.main_frame
        
        if textvariable is not None:
            entry = tk.Entry(parent, textvariable=textvariable, **kwargs)
        else:
            entry = tk.Entry(parent, **kwargs)
        return entry
    
    def create_combobox(self, parent: Optional[Union[tk.Tk, tk.Widget]] = None,
                       values: Optional[list] = None, **kwargs) -> ttk.Combobox:
        """
        创建下拉框组件
        
        Args:
            parent: 父组件
            values: 选项列表
            **kwargs: 其他参数
            
        Returns:
            下拉框组件
        """
        if parent is None:
            parent = self.main_frame
        
        if values is None:
            values = []
        
        combobox = ttk.Combobox(parent, values=values, **kwargs)
        return combobox
    
    def create_text(self, parent: Optional[Union[tk.Tk, tk.Widget]] = None,
                   **kwargs) -> tk.Text:
        """
        创建文本框组件
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            文本框组件
        """
        if parent is None:
            parent = self.main_frame
        
        text = tk.Text(parent, **kwargs)
        return text
    
    def create_scrollbar(self, parent: Optional[Union[tk.Tk, tk.Widget]] = None,
                        **kwargs) -> tk.Scrollbar:
        """
        创建滚动条组件
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            滚动条组件
        """
        if parent is None:
            parent = self.main_frame
        
        scrollbar = tk.Scrollbar(parent, **kwargs)
        return scrollbar
    
    def register_component(self, name: str, component: tk.Widget) -> None:
        """
        注册组件到容器中
        
        Args:
            name: 组件名称
            component: 组件对象
        """
        self.components[name] = component
    
    def get_component(self, name: str) -> Optional[tk.Widget]:
        """
        获取已注册的组件
        
        Args:
            name: 组件名称
            
        Returns:
            组件对象或None
        """
        return self.components.get(name)
    
    def register_variable(self, name: str, variable: tk.Variable) -> None:
        """
        注册变量到容器中
        
        Args:
            name: 变量名称
            variable: 变量对象
        """
        self.variables[name] = variable
    
    def get_variable(self, name: str) -> Optional[tk.Variable]:
        """
        获取已注册的变量
        
        Args:
            name: 变量名称
            
        Returns:
            变量对象或None
        """
        return self.variables.get(name)
    
    def publish_event(self, event_type: str, data: Any = None) -> None:
        """
        发布事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        self.event_manager.publish(event_type, data)
    
    def subscribe_event(self, event_type: str, callback: Callable) -> None:
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        self.event_manager.subscribe(event_type, callback)
    
    def unsubscribe_event(self, event_type: str, callback: Callable) -> None:
        """
        取消订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        self.event_manager.unsubscribe(event_type, callback)
    
    def get_config_value(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径
            default: 默认值
            
        Returns:
            配置值
        """
        return self.config.get(key_path, default)
    
    def set_config_value(self, key_path: str, value: Any, save: bool = True) -> None:
        """
        设置配置值
        
        Args:
            key_path: 配置键路径
            value: 配置值
            save: 是否保存
        """
        self.config.set(key_path, value, save)
    
    def show_error(self, title: str, message: str) -> None:
        """
        显示错误消息
        
        Args:
            title: 标题
            message: 消息内容
        """
        from tkinter import messagebox
        messagebox.showerror(title, message)
    
    def show_info(self, title: str, message: str) -> None:
        """
        显示信息消息
        
        Args:
            title: 标题
            message: 消息内容
        """
        from tkinter import messagebox
        messagebox.showinfo(title, message)
    
    def show_warning(self, title: str, message: str) -> None:
        """
        显示警告消息
        
        Args:
            title: 标题
            message: 消息内容
        """
        from tkinter import messagebox
        messagebox.showwarning(title, message)
    
    def ask_yes_no(self, title: str, message: str) -> bool:
        """
        显示是否确认对话框
        
        Args:
            title: 标题
            message: 消息内容
            
        Returns:
            用户选择结果
        """
        from tkinter import messagebox
        return messagebox.askyesno(title, message)
    
    def destroy(self) -> None:
        """销毁组件"""
        if self.main_frame:
            self.main_frame.destroy()
        
        # 清理事件订阅
        # 注意：这里需要具体的事件类型列表，子类可以重写此方法
        pass
