#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
组件工厂模块
统一创建和配置GUI组件，确保样式一致性
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional, Any, Callable, Union
import logging

from .config_manager import GUIConfig, get_gui_config


class ComponentFactory:
    """
    GUI组件工厂
    负责创建标准化的GUI组件，确保样式和行为的一致性
    """
    
    def __init__(self, config: Optional[GUIConfig] = None):
        """
        初始化组件工厂
        
        Args:
            config: GUI配置管理器
        """
        self.config = config or get_gui_config()
        self.logger = logging.getLogger(__name__)
        
        # 缓存样式配置
        self._style_cache = {}
        self._init_styles()
    
    def _init_styles(self) -> None:
        """初始化样式配置"""
        theme_config = self.config.get_theme_config()
        
        self._style_cache = {
            'default_font': (
                theme_config.get('fonts', {}).get('default_family', 'Microsoft YaHei UI'),
                theme_config.get('fonts', {}).get('default_size', 9)
            ),
            'title_font': (
                theme_config.get('fonts', {}).get('default_family', 'Microsoft YaHei UI'),
                theme_config.get('fonts', {}).get('title_size', 12),
                'bold'
            ),
            'small_font': (
                theme_config.get('fonts', {}).get('default_family', 'Microsoft YaHei UI'),
                theme_config.get('fonts', {}).get('small_size', 8)
            ),
            'colors': theme_config.get('colors', {}),
            'padding': self.config.get('layout.padding', 10),
            'spacing': self.config.get('layout.spacing', 5)
        }
    
    def create_frame(self, parent: Union[tk.Tk, tk.Widget], style: str = 'default', **kwargs) -> tk.Frame:
        """
        创建框架
        
        Args:
            parent: 父组件
            style: 样式类型
            **kwargs: 其他参数
            
        Returns:
            框架组件
        """
        frame_kwargs = {
            'relief': tk.FLAT,
            'bd': 0
        }
        
        if style == 'card':
            frame_kwargs.update({
                'relief': tk.RAISED,
                'bd': 1,
                'bg': self._style_cache['colors'].get('surface', '#F5F5F5')
            })
        elif style == 'section':
            frame_kwargs.update({
                'relief': tk.GROOVE,
                'bd': 1
            })
        
        frame_kwargs.update(kwargs)
        return tk.Frame(parent, **frame_kwargs)
    
    def create_label(self, parent: Union[tk.Tk, tk.Widget], text: str = "", 
                    style: str = 'default', **kwargs) -> tk.Label:
        """
        创建标签
        
        Args:
            parent: 父组件
            text: 标签文本
            style: 样式类型
            **kwargs: 其他参数
            
        Returns:
            标签组件
        """
        label_kwargs = {
            'text': text,
            'font': self._style_cache['default_font'],
            'fg': self._style_cache['colors'].get('text_primary', '#212121')
        }
        
        if style == 'title':
            label_kwargs['font'] = self._style_cache['title_font']
        elif style == 'small':
            label_kwargs['font'] = self._style_cache['small_font']
        elif style == 'secondary':
            label_kwargs['fg'] = self._style_cache['colors'].get('text_secondary', '#757575')
        
        label_kwargs.update(kwargs)
        return tk.Label(parent, **label_kwargs)
    
    def create_button(self, parent: Union[tk.Tk, tk.Widget], text: str = "",
                     command: Optional[Callable] = None,
                     style: str = 'default', **kwargs) -> tk.Button:
        """
        创建按钮
        
        Args:
            parent: 父组件
            text: 按钮文本
            command: 点击回调
            style: 样式类型
            **kwargs: 其他参数
            
        Returns:
            按钮组件
        """
        button_kwargs = {
            'text': text,
            'command': command,
            'font': self._style_cache['default_font'],
            'relief': tk.RAISED,
            'bd': 1,
            'padx': 10,
            'pady': 5
        }
        
        if style == 'primary':
            button_kwargs.update({
                'bg': self._style_cache['colors'].get('primary', '#2E86AB'),
                'fg': 'white',
                'activebackground': '#1E5F7A',
                'activeforeground': 'white'
            })
        elif style == 'secondary':
            button_kwargs.update({
                'bg': self._style_cache['colors'].get('secondary', '#A23B72'),
                'fg': 'white',
                'activebackground': '#7A2B52',
                'activeforeground': 'white'
            })
        elif style == 'success':
            button_kwargs.update({
                'bg': self._style_cache['colors'].get('success', '#F18F01'),
                'fg': 'white',
                'activebackground': '#C1720A',
                'activeforeground': 'white'
            })
        elif style == 'warning':
            button_kwargs.update({
                'bg': self._style_cache['colors'].get('warning', '#C73E1D'),
                'fg': 'white',
                'activebackground': '#9A2E15',
                'activeforeground': 'white'
            })
        
        button_kwargs.update(kwargs)
        return tk.Button(parent, **button_kwargs)
    
    def create_entry(self, parent: Union[tk.Tk, tk.Widget], 
                    textvariable: Optional[tk.StringVar] = None,
                    **kwargs) -> tk.Entry:
        """
        创建输入框
        
        Args:
            parent: 父组件
            textvariable: 文本变量
            **kwargs: 其他参数
            
        Returns:
            输入框组件
        """
        entry_kwargs = {
            'font': self._style_cache['default_font'],
            'relief': tk.SUNKEN,
            'bd': 1
        }
        
        if textvariable:
            entry_kwargs['textvariable'] = textvariable
        
        entry_kwargs.update(kwargs)
        return tk.Entry(parent, **entry_kwargs)
    
    def create_text(self, parent: Union[tk.Tk, tk.Widget], **kwargs) -> tk.Text:
        """
        创建文本框
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            文本框组件
        """
        text_kwargs = {
            'font': self._style_cache['default_font'],
            'relief': tk.SUNKEN,
            'bd': 1,
            'wrap': tk.WORD
        }
        
        text_kwargs.update(kwargs)
        return tk.Text(parent, **text_kwargs)
    
    def create_combobox(self, parent: Union[tk.Tk, tk.Widget], values: Optional[list] = None,
                       **kwargs) -> ttk.Combobox:
        """
        创建下拉框
        
        Args:
            parent: 父组件
            values: 选项列表
            **kwargs: 其他参数
            
        Returns:
            下拉框组件
        """
        if values is None:
            values = []
        
        combobox_kwargs = {
            'values': values,
            'state': 'readonly'
        }
        
        combobox_kwargs.update(kwargs)
        return ttk.Combobox(parent, **combobox_kwargs)
    
    def create_progressbar(self, parent: Union[tk.Tk, tk.Widget], **kwargs) -> ttk.Progressbar:
        """
        创建进度条
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            进度条组件
        """
        progress_kwargs = {
            'length': kwargs.get('length', 200),
            'mode': kwargs.get('mode', 'determinate'),
            'maximum': kwargs.get('maximum', 100),
            'value': kwargs.get('value', 0)
            # 注意：ttk.Progressbar不支持height参数
        }
        
        # 移除None值
        progress_kwargs = {k: v for k, v in progress_kwargs.items() if v is not None}
        
        return ttk.Progressbar(parent, **progress_kwargs)
    
    def create_treeview(self, parent: Union[tk.Tk, tk.Widget], columns: Optional[list] = None,
                       **kwargs) -> ttk.Treeview:
        """
        创建树形视图
        
        Args:
            parent: 父组件
            columns: 列定义
            **kwargs: 其他参数
            
        Returns:
            树形视图组件
        """
        if columns is None:
            columns = []
        
        tree_kwargs = {
            'columns': columns,
            'show': 'tree headings' if columns else 'tree'
        }
        
        tree_kwargs.update(kwargs)
        return ttk.Treeview(parent, **tree_kwargs)
    
    def create_notebook(self, parent: Union[tk.Tk, tk.Widget], **kwargs) -> ttk.Notebook:
        """
        创建标签页容器
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            标签页容器组件
        """
        return ttk.Notebook(parent, **kwargs)
    
    def create_scrollbar(self, parent: Union[tk.Tk, tk.Widget], **kwargs) -> tk.Scrollbar:
        """
        创建滚动条
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            滚动条组件
        """
        scrollbar_kwargs = {
            'relief': tk.SUNKEN,
            'bd': 1
        }
        
        scrollbar_kwargs.update(kwargs)
        return tk.Scrollbar(parent, **scrollbar_kwargs)
    
    def create_checkbutton(self, parent: Union[tk.Tk, tk.Widget], text: str = "",
                          variable: Optional[tk.BooleanVar] = None,
                          **kwargs) -> tk.Checkbutton:
        """
        创建复选框
        
        Args:
            parent: 父组件
            text: 复选框文本
            variable: 布尔变量
            **kwargs: 其他参数
            
        Returns:
            复选框组件
        """
        check_kwargs = {
            'text': text,
            'font': self._style_cache['default_font']
        }
        
        if variable:
            check_kwargs['variable'] = variable
        
        check_kwargs.update(kwargs)
        return tk.Checkbutton(parent, **check_kwargs)
    
    def create_radiobutton(self, parent: Union[tk.Tk, tk.Widget], text: str = "",
                          variable: Optional[tk.Variable] = None,
                          value: Any = None, **kwargs) -> tk.Radiobutton:
        """
        创建单选框
        
        Args:
            parent: 父组件
            text: 单选框文本
            variable: 变量
            value: 选择值
            **kwargs: 其他参数
            
        Returns:
            单选框组件
        """
        radio_kwargs = {
            'text': text,
            'font': self._style_cache['default_font']
        }
        
        if variable:
            radio_kwargs['variable'] = variable
        if value is not None:
            radio_kwargs['value'] = value
        
        radio_kwargs.update(kwargs)
        return tk.Radiobutton(parent, **radio_kwargs)
    
    def create_labelframe(self, parent: Union[tk.Tk, tk.Widget], text: str = "", 
                         style: str = 'default', **kwargs) -> tk.LabelFrame:
        """
        创建标签框架
        
        Args:
            parent: 父组件
            text: 标签文本
            style: 样式类型
            **kwargs: 其他参数
            
        Returns:
            标签框架组件
        """
        labelframe_kwargs = {
            'text': text,
            'font': self._style_cache['default_font'],
            'relief': tk.GROOVE,
            'bd': 1,
            'padx': 5,
            'pady': 5
        }
        
        if style == 'section':
            labelframe_kwargs.update({
                'font': self._style_cache['title_font'],
                'fg': self._style_cache['colors'].get('primary', '#2E86AB')
            })
        
        labelframe_kwargs.update(kwargs)
        return tk.LabelFrame(parent, **labelframe_kwargs)
    
    def create_scrollable_frame(self, parent: Union[tk.Tk, tk.Widget], **kwargs) -> tk.Frame:
        """
        创建可滚动框架
        
        Args:
            parent: 父组件
            **kwargs: 其他参数
            
        Returns:
            可滚动框架组件
        """
        # 创建画布和滚动条
        canvas = tk.Canvas(parent, **kwargs)
        scrollbar = tk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind("<MouseWheel>", _on_mousewheel)
        
        # 保存引用（使用setattr避免类型检查错误）
        setattr(scrollable_frame, 'canvas', canvas)
        setattr(scrollable_frame, 'scrollbar', scrollbar)
        
        return scrollable_frame
    
    def create_checkbox(self, parent: Union[tk.Tk, tk.Widget], text: str = "",
                       variable: Optional[tk.BooleanVar] = None,
                       **kwargs) -> tk.Checkbutton:
        """
        创建复选框（别名方法）
        
        Args:
            parent: 父组件
            text: 复选框文本
            variable: 布尔变量
            **kwargs: 其他参数
            
        Returns:
            复选框组件
        """
        return self.create_checkbutton(parent, text, variable, **kwargs)
    
    def create_spinbox(self, parent: Union[tk.Tk, tk.Widget], from_: float = 0, to: float = 100,
                      increment: float = 1, **kwargs) -> tk.Spinbox:
        """
        创建数字选择框

        Args:
            parent: 父组件
            from_: 最小值
            to: 最大值
            increment: 步长
            **kwargs: 其他参数

        Returns:
            数字选择框组件
        """
        spinbox_kwargs = {
            'from_': from_,
            'to': to,
            'increment': increment,
            'font': self._style_cache['default_font'],
            'width': kwargs.get('width', 10),
            'relief': tk.SUNKEN,
            'bd': 1
        }

        spinbox_kwargs.update(kwargs)
        return tk.Spinbox(parent, **spinbox_kwargs)

    def create_scale(self, parent: Union[tk.Tk, tk.Widget], from_: float = 0, to: float = 100,
                    orient: str = tk.HORIZONTAL, resolution: float = 1, **kwargs) -> tk.Scale:
        """
        创建滑动条

        Args:
            parent: 父组件
            from_: 最小值
            to: 最大值
            orient: 方向 (tk.HORIZONTAL 或 tk.VERTICAL)
            resolution: 精度
            **kwargs: 其他参数

        Returns:
            滑动条组件
        """
        scale_kwargs = {
            'from_': from_,
            'to': to,
            'orient': orient,
            'resolution': resolution,
            'font': self._style_cache.get('font', ('Arial', 9)),
            'relief': 'flat',
            'bd': 1,
            'length': 200 if orient == tk.HORIZONTAL else 100
        }

        scale_kwargs.update(kwargs)
        return tk.Scale(parent, **scale_kwargs)


# 全局组件工厂实例
_global_factory = None


def get_component_factory() -> ComponentFactory:
    """获取全局组件工厂实例"""
    global _global_factory
    if _global_factory is None:
        _global_factory = ComponentFactory()
    return _global_factory
