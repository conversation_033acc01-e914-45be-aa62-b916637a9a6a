#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI配置管理模块
统一管理GUI的主题、样式、布局等配置
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging


class GUIConfig:
    """
    GUI配置管理器
    负责GUI相关的所有配置管理，包括主题、样式、布局等
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认路径
        """
        self.logger = logging.getLogger(__name__)
        
        # 设置配置文件路径
        if config_file is None:
            self.config_file = Path(__file__).parent.parent.parent / "config" / "gui_config.json"
        else:
            self.config_file = Path(config_file)
        
        # 默认配置
        self.default_config = {
            "window": {
                "title": "多模型集成机器学习平台",
                "size": [1400, 900],
                "min_size": [1200, 800],
                "resizable": True,
                "center_on_screen": True
            },
            "theme": {
                "name": "default",
                "colors": {
                    "primary": "#2E86AB",
                    "secondary": "#A23B72", 
                    "success": "#F18F01",
                    "warning": "#C73E1D",
                    "background": "#FFFFFF",
                    "surface": "#F5F5F5",
                    "text_primary": "#212121",
                    "text_secondary": "#757575"
                },
                "fonts": {
                    "default_family": "Microsoft YaHei UI",
                    "default_size": 9,
                    "title_size": 12,
                    "small_size": 8
                }
            },
            "layout": {
                "navigation_width": 200,
                "config_panel_width": 300,
                "status_bar_height": 25,
                "toolbar_height": 35,
                "padding": 10,
                "spacing": 5
            },
            "components": {
                "progress_bar": {
                    "height": 20,
                    "show_percentage": True
                },
                "data_table": {
                    "max_rows_display": 100,
                    "column_width": 100
                },
                "chart": {
                    "default_size": [600, 400],
                    "dpi": 100,
                    "style": "seaborn"
                }
            },
            "behavior": {
                "auto_save_interval": 300,  # 秒
                "max_log_lines": 1000,
                "confirm_on_exit": True,
                "remember_window_state": True
            }
        }
        
        # 当前配置
        self.config = self.default_config.copy()
        
        # 加载配置
        self.load_config()
    
    def load_config(self) -> None:
        """从文件加载配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self._merge_config(self.config, file_config)
                self.logger.info(f"配置已从文件加载: {self.config_file}")
            else:
                self.logger.info("配置文件不存在，使用默认配置")
                self.save_config()  # 保存默认配置到文件
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}，使用默认配置")
            self.config = self.default_config.copy()
    
    def save_config(self) -> None:
        """保存配置到文件"""
        try:
            # 确保配置目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            self.logger.info(f"配置已保存到文件: {self.config_file}")
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
    
    def _merge_config(self, base_config: Dict, new_config: Dict) -> None:
        """递归合并配置"""
        for key, value in new_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._merge_config(base_config[key], value)
            else:
                base_config[key] = value
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，用点号分隔，如 'window.size'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any, save: bool = True) -> None:
        """
        设置配置值
        
        Args:
            key_path: 配置键路径，用点号分隔
            value: 配置值
            save: 是否立即保存到文件
        """
        keys = key_path.split('.')
        config = self.config
        
        # 导航到目标位置
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置值
        config[keys[-1]] = value
        
        if save:
            self.save_config()
    
    def reset_to_default(self, key_path: str = None) -> None:
        """
        重置配置为默认值
        
        Args:
            key_path: 要重置的配置路径，如果为None则重置所有配置
        """
        if key_path is None:
            self.config = self.default_config.copy()
        else:
            default_value = self.get_default(key_path)
            if default_value is not None:
                self.set(key_path, default_value)
        
        self.save_config()
    
    def get_default(self, key_path: str) -> Any:
        """获取默认配置值"""
        keys = key_path.split('.')
        value = self.default_config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return None
    
    def get_window_config(self) -> Dict[str, Any]:
        """获取窗口配置"""
        return self.get('window', {})
    
    def get_theme_config(self) -> Dict[str, Any]:
        """获取主题配置"""
        return self.get('theme', {})
    
    def get_layout_config(self) -> Dict[str, Any]:
        """获取布局配置"""
        return self.get('layout', {})
    
    def get_component_config(self, component_name: str) -> Dict[str, Any]:
        """获取组件配置"""
        return self.get(f'components.{component_name}', {})
    
    def get_behavior_config(self) -> Dict[str, Any]:
        """获取行为配置"""
        return self.get('behavior', {})


# 全局配置管理器实例
_global_config = None


def get_gui_config() -> GUIConfig:
    """获取全局GUI配置管理器实例"""
    global _global_config
    if _global_config is None:
        _global_config = GUIConfig()
    return _global_config
