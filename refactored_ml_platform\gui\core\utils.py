#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI通用工具模块
提供GUI相关的通用工具函数
"""

import tkinter as tk
from tkinter import filedialog, messagebox
from typing import Optional, Tuple, List, Dict, Any
import os
from pathlib import Path
import logging


class GUIUtils:
    """GUI通用工具类"""
    
    @staticmethod
    def center_window(window: tk.Tk, width: int, height: int) -> None:
        """
        将窗口居中显示
        
        Args:
            window: 窗口对象
            width: 窗口宽度
            height: 窗口高度
        """
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    @staticmethod
    def browse_file(title: str = "选择文件", 
                   filetypes: List[Tuple[str, str]] = None,
                   initialdir: str = None) -> Optional[str]:
        """
        打开文件选择对话框
        
        Args:
            title: 对话框标题
            filetypes: 文件类型过滤器
            initialdir: 初始目录
            
        Returns:
            选择的文件路径，如果取消则返回None
        """
        if filetypes is None:
            filetypes = [("所有文件", "*.*")]
        
        if initialdir is None:
            initialdir = os.getcwd()
        
        filename = filedialog.askopenfilename(
            title=title,
            filetypes=filetypes,
            initialdir=initialdir
        )
        
        return filename if filename else None
    
    @staticmethod
    def browse_directory(title: str = "选择文件夹",
                        initialdir: str = None) -> Optional[str]:
        """
        打开文件夹选择对话框
        
        Args:
            title: 对话框标题
            initialdir: 初始目录
            
        Returns:
            选择的文件夹路径，如果取消则返回None
        """
        if initialdir is None:
            initialdir = os.getcwd()
        
        dirname = filedialog.askdirectory(
            title=title,
            initialdir=initialdir
        )
        
        return dirname if dirname else None
    
    @staticmethod
    def save_file(title: str = "保存文件",
                 filetypes: List[Tuple[str, str]] = None,
                 defaultextension: str = None,
                 initialdir: str = None) -> Optional[str]:
        """
        打开文件保存对话框
        
        Args:
            title: 对话框标题
            filetypes: 文件类型过滤器
            defaultextension: 默认扩展名
            initialdir: 初始目录
            
        Returns:
            保存的文件路径，如果取消则返回None
        """
        if filetypes is None:
            filetypes = [("所有文件", "*.*")]
        
        if initialdir is None:
            initialdir = os.getcwd()
        
        filename = filedialog.asksaveasfilename(
            title=title,
            filetypes=filetypes,
            defaultextension=defaultextension,
            initialdir=initialdir
        )
        
        return filename if filename else None
    
    @staticmethod
    def validate_file_path(file_path: str, 
                          extensions: List[str] = None) -> bool:
        """
        验证文件路径是否有效
        
        Args:
            file_path: 文件路径
            extensions: 允许的扩展名列表
            
        Returns:
            是否有效
        """
        if not file_path:
            return False
        
        path = Path(file_path)
        
        # 检查文件是否存在
        if not path.exists():
            return False
        
        # 检查是否是文件
        if not path.is_file():
            return False
        
        # 检查扩展名
        if extensions:
            if path.suffix.lower() not in [ext.lower() for ext in extensions]:
                return False
        
        return True
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小显示
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            格式化后的大小字符串
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    @staticmethod
    def truncate_text(text: str, max_length: int = 50) -> str:
        """
        截断文本并添加省略号
        
        Args:
            text: 原始文本
            max_length: 最大长度
            
        Returns:
            截断后的文本
        """
        if len(text) <= max_length:
            return text
        
        return text[:max_length-3] + "..."
    
    @staticmethod
    def bind_mousewheel(widget: tk.Widget, canvas: tk.Canvas) -> None:
        """
        为组件绑定鼠标滚轮事件
        
        Args:
            widget: 要绑定的组件
            canvas: 画布对象
        """
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        
        widget.bind("<MouseWheel>", on_mousewheel)
    
    @staticmethod
    def create_tooltip(widget: tk.Widget, text: str) -> None:
        """
        为组件创建工具提示
        
        Args:
            widget: 组件
            text: 提示文本
        """
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            
            label = tk.Label(tooltip, text=text, 
                           background="lightyellow",
                           relief=tk.SOLID, borderwidth=1,
                           font=("Microsoft YaHei UI", 8))
            label.pack()
            
            widget.tooltip = tooltip
        
        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip
        
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
    
    @staticmethod
    def safe_execute(func, *args, **kwargs) -> Tuple[bool, Any]:
        """
        安全执行函数，捕获异常
        
        Args:
            func: 要执行的函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            (是否成功, 结果或异常)
        """
        try:
            result = func(*args, **kwargs)
            return True, result
        except Exception as e:
            logging.getLogger(__name__).error(f"函数执行失败: {e}")
            return False, e
    
    @staticmethod
    def get_screen_size() -> Tuple[int, int]:
        """
        获取屏幕尺寸
        
        Returns:
            (宽度, 高度)
        """
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        width = root.winfo_screenwidth()
        height = root.winfo_screenheight()
        
        root.destroy()
        
        return width, height
    
    @staticmethod
    def validate_number_input(value: str, min_val: float = None, 
                            max_val: float = None) -> bool:
        """
        验证数字输入
        
        Args:
            value: 输入值
            min_val: 最小值
            max_val: 最大值
            
        Returns:
            是否有效
        """
        try:
            num = float(value)
            
            if min_val is not None and num < min_val:
                return False
            
            if max_val is not None and num > max_val:
                return False
            
            return True
        except ValueError:
            return False
    
    @staticmethod
    def create_scrollable_frame(parent: tk.Widget) -> Tuple[tk.Canvas, tk.Frame]:
        """
        创建可滚动的框架
        
        Args:
            parent: 父组件
            
        Returns:
            (画布, 可滚动框架)
        """
        # 创建画布和滚动条
        canvas = tk.Canvas(parent)
        scrollbar = tk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)
        
        # 配置画布
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.bind('<Configure>', 
                   lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        
        # 将框架添加到画布
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        
        # 布局
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        return canvas, scrollable_frame
