#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标签页布局管理器
提供统一的标签页布局管理功能
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, Callable

from ..core.base_gui import BaseGUI
from ..core.component_factory import get_component_factory


class TabLayoutManager(BaseGUI):
    """
    标签页布局管理器
    统一管理各种标签页的创建、切换和布局
    """
    
    def __init__(self, parent: tk.Widget):
        """初始化标签页管理器"""
        self.tabs = {}
        self.tab_configs = {}
        super().__init__(parent)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标签页容器
        self.notebook = factory.create_notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 绑定标签页切换事件
        self.notebook.bind('<<NotebookTabChanged>>', self._on_tab_changed)
        
        self.register_component('notebook', self.notebook)
    
    def add_tab(self, tab_id: str, title: str, content_widget: tk.Widget, 
                config: Optional[Dict[str, Any]] = None):
        """
        添加标签页
        
        Args:
            tab_id: 标签页ID
            title: 标签页标题
            content_widget: 标签页内容组件
            config: 标签页配置
        """
        if tab_id in self.tabs:
            self.logger.warning(f"标签页 {tab_id} 已存在，将被替换")
        
        # 添加到notebook
        self.notebook.add(content_widget, text=title)
        
        # 记录标签页信息
        tab_index = len(self.tabs)
        self.tabs[tab_id] = {
            'widget': content_widget,
            'title': title,
            'index': tab_index,
            'active': False
        }
        
        if config:
            self.tab_configs[tab_id] = config
        
        self.logger.info(f"已添加标签页: {tab_id} - {title}")
    
    def remove_tab(self, tab_id: str):
        """移除标签页"""
        if tab_id not in self.tabs:
            self.logger.warning(f"标签页 {tab_id} 不存在")
            return
        
        tab_info = self.tabs[tab_id]
        self.notebook.forget(tab_info['index'])
        
        # 更新其他标签页的索引
        for tid, info in self.tabs.items():
            if info['index'] > tab_info['index']:
                info['index'] -= 1
        
        del self.tabs[tab_id]
        if tab_id in self.tab_configs:
            del self.tab_configs[tab_id]
        
        self.logger.info(f"已移除标签页: {tab_id}")
    
    def switch_to_tab(self, tab_id: str):
        """切换到指定标签页"""
        if tab_id not in self.tabs:
            self.logger.warning(f"标签页 {tab_id} 不存在")
            return
        
        tab_index = self.tabs[tab_id]['index']
        self.notebook.select(tab_index)
        
        self.logger.info(f"已切换到标签页: {tab_id}")
    
    def get_current_tab(self) -> Optional[str]:
        """获取当前活动的标签页ID"""
        try:
            current_index = self.notebook.index(self.notebook.select())
            for tab_id, info in self.tabs.items():
                if info['index'] == current_index:
                    return tab_id
        except tk.TclError:
            pass
        return None
    
    def get_tab_widget(self, tab_id: str) -> Optional[tk.Widget]:
        """获取标签页的内容组件"""
        if tab_id in self.tabs:
            return self.tabs[tab_id]['widget']
        return None
    
    def update_tab_title(self, tab_id: str, new_title: str):
        """更新标签页标题"""
        if tab_id not in self.tabs:
            self.logger.warning(f"标签页 {tab_id} 不存在")
            return
        
        tab_index = self.tabs[tab_id]['index']
        self.notebook.tab(tab_index, text=new_title)
        self.tabs[tab_id]['title'] = new_title
        
        self.logger.info(f"已更新标签页标题: {tab_id} -> {new_title}")
    
    def set_tab_state(self, tab_id: str, state: str):
        """
        设置标签页状态
        
        Args:
            tab_id: 标签页ID
            state: 状态 ('normal', 'disabled', 'hidden')
        """
        if tab_id not in self.tabs:
            self.logger.warning(f"标签页 {tab_id} 不存在")
            return
        
        tab_index = self.tabs[tab_id]['index']
        
        if state == 'disabled':
            self.notebook.tab(tab_index, state='disabled')
        elif state == 'normal':
            self.notebook.tab(tab_index, state='normal')
        elif state == 'hidden':
            self.notebook.tab(tab_index, state='hidden')
        
        self.logger.info(f"已设置标签页状态: {tab_id} -> {state}")
    
    def get_all_tabs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有标签页信息"""
        return self.tabs.copy()
    
    def clear_all_tabs(self):
        """清空所有标签页"""
        for tab_id in list(self.tabs.keys()):
            self.remove_tab(tab_id)
        
        self.logger.info("已清空所有标签页")
    
    def _on_tab_changed(self, event):
        """标签页切换事件处理"""
        current_tab_id = self.get_current_tab()
        
        # 更新活动状态
        for tab_id, info in self.tabs.items():
            info['active'] = (tab_id == current_tab_id)
        
        if current_tab_id:
            self.logger.info(f"标签页已切换到: {current_tab_id}")
            
            # 发布标签页切换事件
            from ..core.event_manager import EventTypes
            self.publish_event(EventTypes.TAB_SWITCHED, {
                'tab_id': current_tab_id,
                'tab_title': self.tabs[current_tab_id]['title']
            })


class StandardTabLayout(BaseGUI):
    """
    标准标签页布局
    提供常用的标签页布局模式
    """
    
    def __init__(self, parent: tk.Widget, title: str = "标准标签页"):
        """初始化标准标签页布局"""
        self.title = title
        super().__init__(parent)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='card')
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题栏
        self._create_title_bar()
        
        # 内容区域
        self._create_content_area()
        
        # 操作栏
        self._create_action_bar()
    
    def _create_title_bar(self):
        """创建标题栏"""
        factory = get_component_factory()
        
        title_frame = factory.create_frame(self.main_frame, style='title_bar')
        title_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
        
        # 标题标签
        title_label = factory.create_label(title_frame, text=self.title, style='title')
        title_label.pack(side=tk.LEFT, pady=5)
        
        # 右侧操作按钮区域
        self.title_actions_frame = factory.create_frame(title_frame)
        self.title_actions_frame.pack(side=tk.RIGHT, pady=5)
        
        self.register_component('title_frame', title_frame)
        self.register_component('title_actions_frame', self.title_actions_frame)
    
    def _create_content_area(self):
        """创建内容区域"""
        factory = get_component_factory()
        
        # 内容框架
        self.content_frame = factory.create_frame(self.main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.register_component('content_frame', self.content_frame)
    
    def _create_action_bar(self):
        """创建操作栏"""
        factory = get_component_factory()
        
        action_frame = factory.create_frame(self.main_frame, style='action_bar')
        action_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # 左侧状态信息
        self.status_frame = factory.create_frame(action_frame)
        self.status_frame.pack(side=tk.LEFT, pady=5)
        
        # 右侧操作按钮
        self.actions_frame = factory.create_frame(action_frame)
        self.actions_frame.pack(side=tk.RIGHT, pady=5)
        
        self.register_component('action_frame', action_frame)
        self.register_component('status_frame', self.status_frame)
        self.register_component('actions_frame', self.actions_frame)
    
    def add_title_action(self, text: str, command: Callable, tooltip: str = ""):
        """在标题栏添加操作按钮"""
        factory = get_component_factory()
        
        btn = factory.create_button(
            self.title_actions_frame, 
            text=text, 
            command=command,
            style='small'
        )
        btn.pack(side=tk.LEFT, padx=2)
        
        if tooltip:
            from ..core.utils import GUIUtils
            GUIUtils.create_tooltip(btn, tooltip)
        
        return btn
    
    def add_action_button(self, text: str, command: Callable, style: str = 'default', tooltip: str = ""):
        """在操作栏添加按钮"""
        factory = get_component_factory()
        
        btn = factory.create_button(
            self.actions_frame,
            text=text,
            command=command,
            style=style
        )
        btn.pack(side=tk.LEFT, padx=2)
        
        if tooltip:
            from ..core.utils import GUIUtils
            GUIUtils.create_tooltip(btn, tooltip)
        
        return btn
    
    def add_status_info(self, text: str, style: str = 'default'):
        """在状态栏添加信息"""
        factory = get_component_factory()
        
        label = factory.create_label(
            self.status_frame,
            text=text,
            style=style
        )
        label.pack(side=tk.LEFT, padx=5)
        
        return label
    
    def update_title(self, new_title: str):
        """更新标题"""
        self.title = new_title
        # 这里需要找到标题标签并更新文本
        # 实际实现中可能需要保存标题标签的引用
    
    def get_content_frame(self) -> tk.Widget:
        """获取内容框架"""
        return self.content_frame
