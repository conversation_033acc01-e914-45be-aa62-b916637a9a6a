#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级分析工具GUI模块
提供各种高级统计分析工具的图形化界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import seaborn as sns
import threading
from typing import Dict, List, Optional, Any

try:
    from ....utils.statistical_analysis import get_statistical_analysis
    from ....core.model_manager import get_model_manager
    from ....utils.error_handler import get_error_handler
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))

    try:
        from utils.statistical_analysis import get_statistical_analysis
        from core.model_manager import get_model_manager
        from utils.error_handler import get_error_handler
    except ImportError:
        # 如果仍然失败，创建简化版本
        get_statistical_analysis = None
        get_model_manager = None
        get_error_handler = None


class AdvancedAnalysisGUI:
    """高级分析工具GUI"""
    
    def __init__(self, parent):
        """初始化高级分析GUI"""
        self.parent = parent
        try:
            self.statistical_analysis = get_statistical_analysis()
            self.model_manager = get_model_manager()
            self.error_handler = get_error_handler()
        except:
            # 如果导入失败，创建简化版本
            self.statistical_analysis = None
            self.model_manager = None
            self.error_handler = None

        # 数据存储
        self.model_results = {}
        self.analysis_results = None
        
        # GUI组件
        self.main_frame = None
        self.frame = None
        
        # 控制变量
        self.status_var = tk.StringVar(value="就绪")
        self.progress_var = tk.DoubleVar()
        
        # 创建界面
        if self.statistical_analysis:
            self._create_interface()
            self._refresh_models()
        else:
            self._create_simple_interface()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.frame = self.main_frame  # 保持兼容性
        
        # 标题
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(title_frame, text="高级统计分析工具", font=('Arial', 16, 'bold')).pack()
        ttk.Label(title_frame, text="提供多种统计检验和分析方法", 
                 foreground="gray").pack()
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self._create_mcnemar_tab()
        self._create_cochran_tab()
        self._create_friedman_tab()
        self._create_bootstrap_tab()

    def _create_simple_interface(self):
        """创建简化界面（当统计分析不可用时）"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 显示不可用消息
        message_frame = ttk.Frame(self.main_frame)
        message_frame.pack(expand=True, fill='both')

        ttk.Label(message_frame, text="高级分析工具暂时不可用",
                 font=('Arial', 16, 'bold')).pack(pady=20)

        ttk.Label(message_frame, text="原因：统计分析模块导入失败\n\n" +
                                    "功能说明：\n" +
                                    "• McNemar检验 - 比较两个分类器\n" +
                                    "• Cochran's Q检验 - 比较多个分类器\n" +
                                    "• Friedman检验 - 多算法多数据集比较\n" +
                                    "• Bootstrap置信区间\n" +
                                    "• Wilcoxon符号秩检验\n" +
                                    "• 置换检验\n\n" +
                                    "请检查系统配置或联系开发者",
                 justify='left').pack(pady=10)
    
    def _create_mcnemar_tab(self):
        """创建McNemar检验选项卡"""
        mcnemar_tab = ttk.Frame(self.notebook)
        self.notebook.add(mcnemar_tab, text="🔬 McNemar检验")
        
        # 说明
        info_frame = ttk.LabelFrame(mcnemar_tab, text="检验说明")
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        info_text = """McNemar检验用于比较两个分类器在同一数据集上的性能差异。
适用于配对样本的二分类问题，检验两个分类器的错误率是否存在显著差异。"""
        
        ttk.Label(info_frame, text=info_text, wraplength=600, justify='left').pack(padx=10, pady=10)
        
        # 模型选择
        selection_frame = ttk.LabelFrame(mcnemar_tab, text="选择模型")
        selection_frame.pack(fill=tk.X, padx=10, pady=10)
        
        model_frame = ttk.Frame(selection_frame)
        model_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(model_frame, text="模型1:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.mcnemar_model1_var = tk.StringVar()
        self.mcnemar_model1_combo = ttk.Combobox(model_frame, textvariable=self.mcnemar_model1_var,
                                               state="readonly", width=20)
        self.mcnemar_model1_combo.grid(row=0, column=1, padx=(0, 20))
        
        ttk.Label(model_frame, text="模型2:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.mcnemar_model2_var = tk.StringVar()
        self.mcnemar_model2_combo = ttk.Combobox(model_frame, textvariable=self.mcnemar_model2_var,
                                               state="readonly", width=20)
        self.mcnemar_model2_combo.grid(row=0, column=3)
        
        # 执行按钮
        control_frame = ttk.Frame(mcnemar_tab)
        control_frame.pack(fill=tk.X, padx=10, pady=20)
        
        ttk.Button(control_frame, text="🔄 刷新模型", 
                  command=self._refresh_models).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="🧪 执行McNemar检验", 
                  command=self._run_mcnemar_test, style="Accent.TButton").pack(side=tk.LEFT)
        
        # 结果显示
        results_frame = ttk.LabelFrame(mcnemar_tab, text="检验结果")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.mcnemar_results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Consolas', 10), height=10)
        mcnemar_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.mcnemar_results_text.yview)
        self.mcnemar_results_text.configure(yscrollcommand=mcnemar_scrollbar.set)
        
        self.mcnemar_results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        mcnemar_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def _create_cochran_tab(self):
        """创建Cochran's Q检验选项卡"""
        cochran_tab = ttk.Frame(self.notebook)
        self.notebook.add(cochran_tab, text="📊 Cochran's Q检验")
        
        # 说明
        info_frame = ttk.LabelFrame(cochran_tab, text="检验说明")
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        info_text = """Cochran's Q检验用于比较三个或更多分类器在同一数据集上的性能差异。
这是McNemar检验的扩展，适用于多个配对样本的比较。"""
        
        ttk.Label(info_frame, text=info_text, wraplength=600, justify='left').pack(padx=10, pady=10)
        
        # 模型选择
        selection_frame = ttk.LabelFrame(cochran_tab, text="选择模型")
        selection_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 模型列表
        list_frame = ttk.Frame(selection_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        ttk.Label(list_frame, text="选择要比较的模型（至少3个）:").pack(anchor=tk.W, pady=(0, 5))
        
        self.cochran_listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE, height=8)
        cochran_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.cochran_listbox.yview)
        self.cochran_listbox.configure(yscrollcommand=cochran_scrollbar.set)
        
        self.cochran_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        cochran_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 执行按钮
        control_frame = ttk.Frame(cochran_tab)
        control_frame.pack(fill=tk.X, padx=10, pady=20)
        
        ttk.Button(control_frame, text="🧪 执行Cochran's Q检验", 
                  command=self._run_cochran_test, style="Accent.TButton").pack(side=tk.LEFT)
        
        # 结果显示
        results_frame = ttk.LabelFrame(cochran_tab, text="检验结果")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.cochran_results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Consolas', 10), height=10)
        cochran_results_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.cochran_results_text.yview)
        self.cochran_results_text.configure(yscrollcommand=cochran_results_scrollbar.set)
        
        self.cochran_results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        cochran_results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def _create_friedman_tab(self):
        """创建Friedman检验选项卡"""
        friedman_tab = ttk.Frame(self.notebook)
        self.notebook.add(friedman_tab, text="📈 Friedman检验")
        
        # 说明
        info_frame = ttk.LabelFrame(friedman_tab, text="检验说明")
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        info_text = """Friedman检验用于比较多个算法在多个数据集上的性能。
这是一个非参数检验，不假设数据的正态分布。"""
        
        ttk.Label(info_frame, text=info_text, wraplength=600, justify='left').pack(padx=10, pady=10)
        
        # 数据输入
        data_frame = ttk.LabelFrame(friedman_tab, text="性能数据")
        data_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 文件选择
        file_frame = ttk.Frame(data_frame)
        file_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(file_frame, text="选择性能数据文件（CSV格式，行为数据集，列为算法）:").pack(anchor=tk.W)
        
        file_select_frame = ttk.Frame(file_frame)
        file_select_frame.pack(fill=tk.X, pady=5)
        
        self.friedman_file_var = tk.StringVar()
        ttk.Entry(file_select_frame, textvariable=self.friedman_file_var, width=50).pack(
            side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        ttk.Button(file_select_frame, text="浏览...", command=self._browse_friedman_file).pack(side=tk.RIGHT)
        
        # 或者使用当前模型数据
        ttk.Label(data_frame, text="或者使用当前加载的模型数据进行分析", 
                 foreground="gray").pack(padx=10, pady=5)
        
        # 执行按钮
        control_frame = ttk.Frame(friedman_tab)
        control_frame.pack(fill=tk.X, padx=10, pady=20)
        
        ttk.Button(control_frame, text="📁 从文件执行", 
                  command=self._run_friedman_from_file).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="🧪 使用当前模型", 
                  command=self._run_friedman_current_models, style="Accent.TButton").pack(side=tk.LEFT)
        
        # 结果显示
        results_frame = ttk.LabelFrame(friedman_tab, text="检验结果")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.friedman_results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Consolas', 10), height=10)
        friedman_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.friedman_results_text.yview)
        self.friedman_results_text.configure(yscrollcommand=friedman_scrollbar.set)
        
        self.friedman_results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        friedman_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def _create_bootstrap_tab(self):
        """创建Bootstrap置信区间选项卡"""
        bootstrap_tab = ttk.Frame(self.notebook)
        self.notebook.add(bootstrap_tab, text="🎯 Bootstrap分析")
        
        # 说明
        info_frame = ttk.LabelFrame(bootstrap_tab, text="分析说明")
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        info_text = """Bootstrap方法通过重采样估计统计量的分布和置信区间。
可以用于估计模型性能指标的不确定性。"""
        
        ttk.Label(info_frame, text=info_text, wraplength=600, justify='left').pack(padx=10, pady=10)
        
        # 参数设置
        params_frame = ttk.LabelFrame(bootstrap_tab, text="参数设置")
        params_frame.pack(fill=tk.X, padx=10, pady=10)
        
        param_grid = ttk.Frame(params_frame)
        param_grid.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(param_grid, text="模型:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.bootstrap_model_var = tk.StringVar()
        self.bootstrap_model_combo = ttk.Combobox(param_grid, textvariable=self.bootstrap_model_var,
                                                state="readonly", width=20)
        self.bootstrap_model_combo.grid(row=0, column=1, padx=(0, 20))
        
        ttk.Label(param_grid, text="置信水平:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.confidence_var = tk.DoubleVar(value=0.95)
        confidence_spinbox = tk.Spinbox(param_grid, textvariable=self.confidence_var,
                                      from_=0.8, to=0.99, increment=0.01, 
                                      format="%.2f", width=10)
        confidence_spinbox.grid(row=0, column=3, padx=(0, 20))
        
        ttk.Label(param_grid, text="Bootstrap次数:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.n_bootstrap_var = tk.IntVar(value=1000)
        bootstrap_spinbox = tk.Spinbox(param_grid, textvariable=self.n_bootstrap_var,
                                     from_=100, to=10000, increment=100, width=10)
        bootstrap_spinbox.grid(row=1, column=1, pady=(10, 0))
        
        # 执行按钮
        control_frame = ttk.Frame(bootstrap_tab)
        control_frame.pack(fill=tk.X, padx=10, pady=20)
        
        ttk.Button(control_frame, text="🧪 执行Bootstrap分析", 
                  command=self._run_bootstrap_analysis, style="Accent.TButton").pack(side=tk.LEFT)
        
        # 结果显示
        results_frame = ttk.LabelFrame(bootstrap_tab, text="分析结果")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.bootstrap_results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Consolas', 10), height=10)
        bootstrap_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.bootstrap_results_text.yview)
        self.bootstrap_results_text.configure(yscrollcommand=bootstrap_scrollbar.set)
        
        self.bootstrap_results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        bootstrap_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 状态栏
        status_frame = ttk.Frame(bootstrap_tab)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(
            side=tk.LEFT, padx=(5, 0))
        
        # 进度条
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var,
                                          mode='determinate', length=200)
        self.progress_bar.pack(side=tk.RIGHT)
    
    def _refresh_models(self):
        """刷新模型列表"""
        try:
            # 清空现有列表
            model_names = []

            # 从模型管理器获取
            if self.model_manager:
                manager_models = self.model_manager.list_models()
                for model_name in manager_models:
                    result = self.model_manager.get_model_result(model_name)
                    if result and 'y_true' in result and 'y_pred' in result:
                        self.model_results[model_name] = result
                        model_names.append(model_name)

            # 更新下拉框
            self.mcnemar_model1_combo['values'] = model_names
            self.mcnemar_model2_combo['values'] = model_names
            self.bootstrap_model_combo['values'] = model_names

            # 更新列表框
            self.cochran_listbox.delete(0, tk.END)
            for model_name in model_names:
                self.cochran_listbox.insert(tk.END, model_name)

            self.status_var.set(f"已加载 {len(model_names)} 个模型")

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(e, "刷新模型列表")
            self.status_var.set("刷新失败")

    def _run_mcnemar_test(self):
        """执行McNemar检验"""
        model1_name = self.mcnemar_model1_var.get()
        model2_name = self.mcnemar_model2_var.get()

        if not model1_name or not model2_name:
            messagebox.showwarning("警告", "请选择两个模型")
            return

        if model1_name == model2_name:
            messagebox.showwarning("警告", "请选择不同的模型")
            return

        try:
            # 获取模型结果
            result1 = self.model_results[model1_name]
            result2 = self.model_results[model2_name]

            y_true = result1['y_true']
            y_pred1 = result1['y_pred']
            y_pred2 = result2['y_pred']

            # 检查数据一致性
            if not np.array_equal(result1['y_true'], result2['y_true']):
                messagebox.showerror("错误", "两个模型的测试集不一致")
                return

            # 执行McNemar检验
            mcnemar_result = self.statistical_analysis.mcnemar_test(y_true, y_pred1, y_pred2)

            # 显示结果
            self._display_mcnemar_results(mcnemar_result, model1_name, model2_name)

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(e, "McNemar检验")
            messagebox.showerror("错误", f"McNemar检验失败: {e}")

    def _display_mcnemar_results(self, result: Dict[str, Any], model1: str, model2: str):
        """显示McNemar检验结果"""
        results_text = f"""McNemar检验结果
{'='*50}

比较模型: {model1} vs {model2}

统计量: {result['statistic']:.4f}
P值: {result['p_value']:.4f}
显著性: {'是' if result['is_significant'] else '否'} (α=0.05)

模型性能:
{model1} 准确率: {result['accuracy_1']:.4f}
{model2} 准确率: {result['accuracy_2']:.4f}
准确率差异: {result['accuracy_diff']:.4f}

列联表:
                    {model2}
                正确    错误
{model1} 正确   {result['contingency_table'][0,0]:4d}    {result['contingency_table'][0,1]:4d}
        错误   {result['contingency_table'][1,0]:4d}    {result['contingency_table'][1,1]:4d}

解释: {result['interpretation']}
"""

        self.mcnemar_results_text.delete(1.0, tk.END)
        self.mcnemar_results_text.insert(1.0, results_text)

    def _run_cochran_test(self):
        """执行Cochran's Q检验"""
        selected_indices = self.cochran_listbox.curselection()

        if len(selected_indices) < 3:
            messagebox.showwarning("警告", "请至少选择3个模型")
            return

        try:
            # 获取选中的模型
            selected_models = [self.cochran_listbox.get(i) for i in selected_indices]

            # 准备数据
            y_true = None
            y_preds = []

            for model_name in selected_models:
                result = self.model_results[model_name]
                if y_true is None:
                    y_true = result['y_true']
                elif not np.array_equal(y_true, result['y_true']):
                    messagebox.showerror("错误", f"模型{model_name}的测试集与其他模型不一致")
                    return

                y_preds.append(result['y_pred'])

            # 执行Cochran's Q检验
            cochran_result = self.statistical_analysis.cochran_q_test(y_true, *y_preds)

            # 显示结果
            self._display_cochran_results(cochran_result, selected_models)

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(e, "Cochran's Q检验")
            messagebox.showerror("错误", f"Cochran's Q检验失败: {e}")

    def _display_cochran_results(self, result: Dict[str, Any], models: List[str]):
        """显示Cochran's Q检验结果"""
        results_text = f"""Cochran's Q检验结果
{'='*50}

比较模型: {', '.join(models)}

Q统计量: {result['q_statistic']:.4f}
自由度: {result['degrees_of_freedom']}
P值: {result['p_value']:.4f}
显著性: {'是' if result['is_significant'] else '否'} (α=0.05)

各模型准确率:
"""

        for i, model in enumerate(models):
            results_text += f"{model}: {result['accuracies'][i]:.4f}\n"

        results_text += f"\n解释: {result['interpretation']}"

        self.cochran_results_text.delete(1.0, tk.END)
        self.cochran_results_text.insert(1.0, results_text)

    def _browse_friedman_file(self):
        """浏览Friedman检验数据文件"""
        filename = filedialog.askopenfilename(
            title="选择性能数据文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.friedman_file_var.set(filename)

    def _run_friedman_from_file(self):
        """从文件执行Friedman检验"""
        filename = self.friedman_file_var.get()
        if not filename:
            messagebox.showwarning("警告", "请选择数据文件")
            return

        try:
            # 读取数据
            if filename.endswith('.csv'):
                data = pd.read_csv(filename, index_col=0)
            elif filename.endswith('.xlsx'):
                data = pd.read_excel(filename, index_col=0)
            else:
                messagebox.showerror("错误", "不支持的文件格式")
                return

            # 执行Friedman检验
            friedman_result = self.statistical_analysis.friedman_test(data.values)

            # 显示结果
            self._display_friedman_results(friedman_result, data.columns.tolist(), data.index.tolist())

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(e, "Friedman检验")
            messagebox.showerror("错误", f"Friedman检验失败: {e}")

    def _run_friedman_current_models(self):
        """使用当前模型执行Friedman检验"""
        if len(self.model_results) < 3:
            messagebox.showwarning("警告", "至少需要3个模型进行Friedman检验")
            return

        try:
            # 构造性能矩阵（这里简化为使用准确率）
            model_names = list(self.model_results.keys())

            # 假设只有一个数据集，创建单行矩阵
            performance_matrix = np.array([[
                np.mean(self.model_results[model]['y_pred'] == self.model_results[model]['y_true'])
                for model in model_names
            ]])

            messagebox.showinfo("提示", "当前只有一个数据集，Friedman检验需要多个数据集。\n建议使用文件导入多数据集结果。")

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(e, "Friedman检验")
            messagebox.showerror("错误", f"Friedman检验失败: {e}")

    def _display_friedman_results(self, result: Dict[str, Any], algorithms: List[str], datasets: List[str]):
        """显示Friedman检验结果"""
        results_text = f"""Friedman检验结果
{'='*50}

算法数量: {len(algorithms)}
数据集数量: {len(datasets)}

Friedman统计量: {result['friedman_statistic']:.4f}
自由度: {result['degrees_of_freedom']}
P值: {result['p_value']:.4f}
显著性: {'是' if result['is_significant'] else '否'} (α=0.05)

平均排名:
"""

        for i, algorithm in enumerate(algorithms):
            results_text += f"{algorithm}: {result['mean_ranks'][i]:.2f}\n"

        results_text += f"\n临界差异 (CD): {result['critical_difference']:.4f}"
        results_text += f"\n\n解释: {result['interpretation']}"

        self.friedman_results_text.delete(1.0, tk.END)
        self.friedman_results_text.insert(1.0, results_text)

    def _run_bootstrap_analysis(self):
        """执行Bootstrap分析"""
        model_name = self.bootstrap_model_var.get()
        if not model_name:
            messagebox.showwarning("警告", "请选择模型")
            return

        confidence_level = self.confidence_var.get()
        n_bootstrap = self.n_bootstrap_var.get()

        # 在后台线程中执行
        threading.Thread(target=self._perform_bootstrap_analysis,
                        args=(model_name, confidence_level, n_bootstrap), daemon=True).start()

    def _perform_bootstrap_analysis(self, model_name: str, confidence_level: float, n_bootstrap: int):
        """执行Bootstrap分析"""
        try:
            self.status_var.set("正在执行Bootstrap分析...")
            self.progress_var.set(10)

            # 获取模型结果
            result = self.model_results[model_name]
            y_true = result['y_true']
            y_pred = result['y_pred']

            # 计算准确率的Bootstrap置信区间
            def accuracy_func(indices):
                return np.mean(y_pred[indices] == y_true[indices])

            self.progress_var.set(30)

            # 创建索引数组用于Bootstrap采样
            indices = np.arange(len(y_true))

            bootstrap_result = self.statistical_analysis.bootstrap_confidence_interval(
                indices, accuracy_func, confidence_level, n_bootstrap
            )

            self.progress_var.set(80)

            # 显示结果
            self._display_bootstrap_results(bootstrap_result, model_name, confidence_level)

            self.progress_var.set(100)
            self.status_var.set("Bootstrap分析完成")

            # 重置进度条
            self.parent.after(2000, lambda: self.progress_var.set(0))

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(e, "Bootstrap分析")
            self.status_var.set("Bootstrap分析失败")
            self.progress_var.set(0)
            messagebox.showerror("错误", f"Bootstrap分析失败: {e}")

    def _display_bootstrap_results(self, result: Dict[str, Any], model_name: str, confidence_level: float):
        """显示Bootstrap分析结果"""
        ci_lower, ci_upper = result['confidence_interval']

        results_text = f"""Bootstrap置信区间分析结果
{'='*50}

模型: {model_name}
置信水平: {confidence_level*100:.0f}%
Bootstrap采样次数: {len(result['bootstrap_statistics'])}

原始准确率: {result['original_statistic']:.4f}
Bootstrap均值: {result['mean_bootstrap']:.4f}
Bootstrap标准差: {result['std_bootstrap']:.4f}

{confidence_level*100:.0f}%置信区间: [{ci_lower:.4f}, {ci_upper:.4f}]

解释: {result['interpretation']}

这意味着我们有{confidence_level*100:.0f}%的信心认为，
该模型的真实准确率在{ci_lower:.4f}到{ci_upper:.4f}之间。
"""

        self.bootstrap_results_text.delete(1.0, tk.END)
        self.bootstrap_results_text.insert(1.0, results_text)

    def get_frame(self):
        """获取主框架"""
        return self.frame
