#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据加载器标签页模块
提供数据文件加载和基本信息显示功能
"""

import tkinter as tk
from tkinter import ttk
import pandas as pd
from typing import Optional, Dict, Any

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import EventTypes
from ...components.data_widgets import FileSelector, DataTableWidget


class DataLoaderTab(BaseGUI):
    """
    数据加载器标签页
    提供数据文件选择、加载和基本信息显示功能
    """
    
    def __init__(self, parent: tk.Widget):
        """初始化数据加载器标签页"""
        self.current_data = None
        self.file_path = None
        super().__init__(parent)
        
        # 绑定事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='card')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 标题
        title_label = factory.create_label(
            self.main_frame, 
            text="📁 数据加载", 
            style='title'
        )
        title_label.pack(pady=10)
        
        # 文件选择区域
        self._create_file_selection_area()
        
        # 数据信息区域
        self._create_data_info_area()
        
        # 数据预览区域
        self._create_data_preview_area()
    
    def _create_file_selection_area(self):
        """创建文件选择区域"""
        factory = get_component_factory()
        
        # 文件选择框架
        file_frame = factory.create_frame(self.main_frame, style='section')
        file_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 文件选择器
        self.file_selector = FileSelector(
            file_frame,
            title="选择数据文件",
            filetypes=[
                ("CSV文件", "*.csv"),
                ("Excel文件", "*.xlsx;*.xls"),
                ("所有文件", "*.*")
            ]
        )
        self.file_selector.main_frame.pack(fill=tk.X, pady=5)
        
        # 绑定文件选择事件
        self.file_selector.on_file_selected = self._on_file_selected
        
        # 加载按钮
        load_button = factory.create_button(
            file_frame,
            text="🔄 加载数据",
            command=self._load_data,
            style='primary'
        )
        load_button.pack(pady=10)
        
        self.register_component('file_frame', file_frame)
        self.register_component('load_button', load_button)
    
    def _create_data_info_area(self):
        """创建数据信息区域"""
        factory = get_component_factory()
        
        # 信息框架
        info_frame = factory.create_frame(self.main_frame, style='section')
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        info_title = factory.create_label(info_frame, text="📊 数据信息", style='subtitle')
        info_title.pack(anchor=tk.W, pady=(5, 10))
        
        # 信息显示区域
        self.info_text = factory.create_text(info_frame, height=6, state=tk.DISABLED)
        self.info_text.pack(fill=tk.X, pady=5)
        
        self.register_component('info_frame', info_frame)
        self.register_component('info_text', self.info_text)
    
    def _create_data_preview_area(self):
        """创建数据预览区域"""
        factory = get_component_factory()
        
        # 预览框架
        preview_frame = factory.create_frame(self.main_frame, style='section')
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        preview_title = factory.create_label(preview_frame, text="👁️ 数据预览", style='subtitle')
        preview_title.pack(anchor=tk.W, pady=(5, 10))
        
        # 数据表格
        self.data_table = DataTableWidget(preview_frame, max_rows=20)
        self.data_table.main_frame.pack(fill=tk.BOTH, expand=True)
        
        self.register_component('preview_frame', preview_frame)
    
    def _bind_events(self):
        """绑定事件"""
        # 这里可以订阅相关事件
        pass
    
    def _on_file_selected(self, file_path: str):
        """文件选择事件处理"""
        self.file_path = file_path
        self.logger.info(f"已选择文件: {file_path}")
        
        # 清空之前的信息
        self._clear_info()
    
    def _load_data(self):
        """加载数据"""
        if not self.file_path:
            self.show_warning("警告", "请先选择数据文件")
            return
        
        try:
            self.logger.info(f"开始加载数据: {self.file_path}")
            
            # 根据文件扩展名选择加载方法
            if self.file_path.lower().endswith('.csv'):
                self.current_data = pd.read_csv(self.file_path)
            elif self.file_path.lower().endswith(('.xlsx', '.xls')):
                self.current_data = pd.read_excel(self.file_path)
            else:
                # 尝试作为CSV加载
                self.current_data = pd.read_csv(self.file_path)
            
            # 更新显示
            self._update_data_info()
            self._update_data_preview()
            
            # 发布数据加载事件
            self.publish_event(EventTypes.DATA_LOADED, {
                'data': self.current_data,
                'file_path': self.file_path,
                'data_shape': self.current_data.shape,
                'columns': list(self.current_data.columns)
            })
            
            self.show_info("成功", f"数据加载完成！\n形状: {self.current_data.shape}")
            self.logger.info(f"数据加载完成，形状: {self.current_data.shape}")
            
        except Exception as e:
            error_msg = f"数据加载失败: {str(e)}"
            self.show_error("错误", error_msg)
            self.logger.error(error_msg)
    
    def _update_data_info(self):
        """更新数据信息显示"""
        if self.current_data is None:
            return
        
        info_text = f"""
文件路径: {self.file_path}
数据形状: {self.current_data.shape[0]} 行 × {self.current_data.shape[1]} 列
内存使用: {self.current_data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB

列信息:
"""
        
        # 添加列信息
        for i, col in enumerate(self.current_data.columns):
            dtype = str(self.current_data[col].dtype)
            null_count = self.current_data[col].isnull().sum()
            info_text += f"  {i+1}. {col} ({dtype}) - 缺失值: {null_count}\n"
        
        # 更新文本显示
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, info_text)
        self.info_text.config(state=tk.DISABLED)
    
    def _update_data_preview(self):
        """更新数据预览"""
        if self.current_data is None:
            return
        
        # 更新数据表格
        self.data_table.load_data(self.current_data)
    
    def _clear_info(self):
        """清空信息显示"""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        self.info_text.config(state=tk.DISABLED)
        
        self.data_table.clear_data()
    
    def get_current_data(self) -> Optional[pd.DataFrame]:
        """获取当前加载的数据"""
        return self.current_data
    
    def set_file_path(self, file_path: str):
        """设置文件路径"""
        self.file_selector.set_file_path(file_path)
        self.file_path = file_path
