#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理标签页模块
提供数据清洗、转换和预处理功能
"""

import tkinter as tk
from tkinter import ttk
import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, List

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import EventTypes


class DataPreprocessingTab(BaseGUI):
    """
    数据预处理标签页
    提供数据清洗、转换、缺失值处理、特征工程等功能
    """
    
    def __init__(self, parent: tk.Widget):
        """初始化数据预处理标签页"""
        self.current_data = None
        self.original_data = None
        self.preprocessing_history = []
        super().__init__(parent)
        
        # 绑定事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='card')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 标题
        title_label = factory.create_label(
            self.main_frame, 
            text="⚙️ 数据预处理", 
            style='title'
        )
        title_label.pack(pady=10)
        
        # 创建简化的预处理界面
        self._create_simple_preprocessing_ui()
    
    def _create_simple_preprocessing_ui(self):
        """创建简化的预处理界面"""
        factory = get_component_factory()
        
        # 操作按钮区域
        ops_frame = factory.create_frame(self.main_frame, style='section')
        ops_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ops_title = factory.create_label(ops_frame, text="🔧 预处理操作", style='subtitle')
        ops_title.pack(anchor=tk.W, pady=(5, 10))
        
        # 基本操作按钮
        button_frame = factory.create_frame(ops_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        # 第一行按钮
        row1 = factory.create_frame(button_frame)
        row1.pack(fill=tk.X, pady=2)
        
        factory.create_button(row1, text="删除重复行", command=self._remove_duplicates).pack(side=tk.LEFT, padx=5)
        factory.create_button(row1, text="删除缺失值行", command=self._drop_missing_rows).pack(side=tk.LEFT, padx=5)
        factory.create_button(row1, text="填充缺失值", command=self._fill_missing_values).pack(side=tk.LEFT, padx=5)
        
        # 第二行按钮
        row2 = factory.create_frame(button_frame)
        row2.pack(fill=tk.X, pady=2)
        
        factory.create_button(row2, text="标准化数据", command=self._standardize_data).pack(side=tk.LEFT, padx=5)
        factory.create_button(row2, text="归一化数据", command=self._normalize_data).pack(side=tk.LEFT, padx=5)
        factory.create_button(row2, text="重置数据", command=self._reset_data).pack(side=tk.LEFT, padx=5)
        
        # 状态显示区域
        status_frame = factory.create_frame(self.main_frame, style='section')
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        status_title = factory.create_label(status_frame, text="📊 处理状态", style='subtitle')
        status_title.pack(anchor=tk.W, pady=(5, 10))
        
        self.status_text = factory.create_text(status_frame, height=15, state=tk.DISABLED)
        self.status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.register_component('ops_frame', ops_frame)
        self.register_component('status_frame', status_frame)
        self.register_component('status_text', self.status_text)
    
    def _bind_events(self):
        """绑定事件"""
        self.subscribe_event(EventTypes.DATA_LOADED, self._on_data_loaded)
    
    def _on_data_loaded(self, event_data: Dict[str, Any]):
        """数据加载事件处理"""
        self.logger.info("收到数据加载事件，准备进行数据预处理")
    
    def _update_status(self, message: str):
        """更新状态显示"""
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, f"{pd.Timestamp.now().strftime('%H:%M:%S')} - {message}\n")
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)
    
    def _remove_duplicates(self):
        """删除重复行"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return
        
        try:
            before_count = len(self.current_data)
            self.current_data = self.current_data.drop_duplicates()
            after_count = len(self.current_data)
            removed_count = before_count - after_count
            
            message = f"删除重复行完成，删除了 {removed_count} 行，剩余 {after_count} 行"
            self._update_status(message)
            self.show_info("完成", message)
            
            # 发布数据预处理事件
            self.publish_event(EventTypes.DATA_PREPROCESSED, {
                'operation': 'remove_duplicates',
                'removed_count': removed_count,
                'current_shape': self.current_data.shape
            })
            
        except Exception as e:
            error_msg = f"删除重复行失败: {str(e)}"
            self._update_status(error_msg)
            self.show_error("错误", error_msg)
    
    def _drop_missing_rows(self):
        """删除包含缺失值的行"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return
        
        try:
            before_count = len(self.current_data)
            self.current_data = self.current_data.dropna()
            after_count = len(self.current_data)
            removed_count = before_count - after_count
            
            message = f"删除缺失值行完成，删除了 {removed_count} 行，剩余 {after_count} 行"
            self._update_status(message)
            self.show_info("完成", message)
            
            self.publish_event(EventTypes.DATA_PREPROCESSED, {
                'operation': 'drop_missing_rows',
                'removed_count': removed_count,
                'current_shape': self.current_data.shape
            })
            
        except Exception as e:
            error_msg = f"删除缺失值行失败: {str(e)}"
            self._update_status(error_msg)
            self.show_error("错误", error_msg)
    
    def _fill_missing_values(self):
        """填充缺失值（使用均值/众数）"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return
        
        try:
            # 数值列用均值填充
            numeric_cols = self.current_data.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                self.current_data[col].fillna(self.current_data[col].mean(), inplace=True)
            
            # 分类列用众数填充
            categorical_cols = self.current_data.select_dtypes(exclude=[np.number]).columns
            for col in categorical_cols:
                mode_value = self.current_data[col].mode()
                if not mode_value.empty:
                    self.current_data[col].fillna(mode_value[0], inplace=True)
            
            message = f"缺失值填充完成，数值列用均值填充，分类列用众数填充"
            self._update_status(message)
            self.show_info("完成", message)
            
            self.publish_event(EventTypes.DATA_PREPROCESSED, {
                'operation': 'fill_missing_values',
                'current_shape': self.current_data.shape
            })
            
        except Exception as e:
            error_msg = f"填充缺失值失败: {str(e)}"
            self._update_status(error_msg)
            self.show_error("错误", error_msg)
    
    def _standardize_data(self):
        """标准化数据"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return
        
        try:
            from sklearn.preprocessing import StandardScaler
            
            numeric_cols = self.current_data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) == 0:
                self.show_warning("警告", "没有数值列可以标准化")
                return
            
            scaler = StandardScaler()
            self.current_data[numeric_cols] = scaler.fit_transform(self.current_data[numeric_cols])
            
            message = f"数据标准化完成，处理了 {len(numeric_cols)} 个数值列"
            self._update_status(message)
            self.show_info("完成", message)
            
            self.publish_event(EventTypes.DATA_PREPROCESSED, {
                'operation': 'standardize_data',
                'processed_columns': len(numeric_cols),
                'current_shape': self.current_data.shape
            })
            
        except Exception as e:
            error_msg = f"数据标准化失败: {str(e)}"
            self._update_status(error_msg)
            self.show_error("错误", error_msg)
    
    def _normalize_data(self):
        """归一化数据"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return
        
        try:
            from sklearn.preprocessing import MinMaxScaler
            
            numeric_cols = self.current_data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) == 0:
                self.show_warning("警告", "没有数值列可以归一化")
                return
            
            scaler = MinMaxScaler()
            self.current_data[numeric_cols] = scaler.fit_transform(self.current_data[numeric_cols])
            
            message = f"数据归一化完成，处理了 {len(numeric_cols)} 个数值列"
            self._update_status(message)
            self.show_info("完成", message)
            
            self.publish_event(EventTypes.DATA_PREPROCESSED, {
                'operation': 'normalize_data',
                'processed_columns': len(numeric_cols),
                'current_shape': self.current_data.shape
            })
            
        except Exception as e:
            error_msg = f"数据归一化失败: {str(e)}"
            self._update_status(error_msg)
            self.show_error("错误", error_msg)
    
    def _reset_data(self):
        """重置数据到原始状态"""
        if self.original_data is None:
            self.show_warning("警告", "没有原始数据可以重置")
            return
        
        try:
            self.current_data = self.original_data.copy()
            
            message = f"数据已重置到原始状态，形状: {self.current_data.shape}"
            self._update_status(message)
            self.show_info("完成", message)
            
            # 清空状态显示
            self.status_text.config(state=tk.NORMAL)
            self.status_text.delete(1.0, tk.END)
            self.status_text.config(state=tk.DISABLED)
            
            self.publish_event(EventTypes.DATA_PREPROCESSED, {
                'operation': 'reset_data',
                'current_shape': self.current_data.shape
            })
            
        except Exception as e:
            error_msg = f"重置数据失败: {str(e)}"
            self._update_status(error_msg)
            self.show_error("错误", error_msg)
    
    def update_data(self, data: pd.DataFrame):
        """更新数据"""
        self.current_data = data.copy()
        if self.original_data is None:
            self.original_data = data.copy()
        
        message = f"数据已更新，形状: {data.shape}"
        self._update_status(message)
        self.logger.info(f"数据预处理模块已更新数据，形状: {data.shape}")
    
    def get_current_data(self) -> Optional[pd.DataFrame]:
        """获取当前处理后的数据"""
        return self.current_data
    
    def clear_data(self):
        """清空数据"""
        self.current_data = None
        self.original_data = None
        self.preprocessing_history = []
        
        # 清空状态显示
        self.status_text.config(state=tk.NORMAL)
        self.status_text.delete(1.0, tk.END)
        self.status_text.config(state=tk.DISABLED)
