#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预览标签页模块
提供数据的详细预览和统计信息显示功能
"""

import tkinter as tk
from tkinter import ttk
import pandas as pd
import numpy as np
from typing import Optional, Dict, Any

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import EventTypes
from ...components.data_widgets import DataPreviewWidget


class DataPreviewTab(BaseGUI):
    """
    数据预览标签页
    提供数据的详细预览、统计信息和基本分析功能
    """
    
    def __init__(self, parent: tk.Widget):
        """初始化数据预览标签页"""
        self.current_data = None
        super().__init__(parent)
        
        # 绑定事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='card')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 标题
        title_label = factory.create_label(
            self.main_frame, 
            text="👁️ 数据预览", 
            style='title'
        )
        title_label.pack(pady=10)
        
        # 创建标签页容器
        self.notebook = factory.create_notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建各个标签页
        self._create_basic_info_tab()
        self._create_statistics_tab()
        self._create_data_view_tab()
        self._create_column_analysis_tab()
    
    def _create_basic_info_tab(self):
        """创建基本信息标签页"""
        factory = get_component_factory()
        
        # 基本信息框架
        info_frame = factory.create_frame(self.notebook)
        self.notebook.add(info_frame, text="📋 基本信息")
        
        # 基本信息文本
        self.basic_info_text = factory.create_text(info_frame, state=tk.DISABLED)
        self.basic_info_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.register_component('info_frame', info_frame)
        self.register_component('basic_info_text', self.basic_info_text)
    
    def _create_statistics_tab(self):
        """创建统计信息标签页"""
        factory = get_component_factory()
        
        # 统计信息框架
        stats_frame = factory.create_frame(self.notebook)
        self.notebook.add(stats_frame, text="📊 统计信息")
        
        # 统计信息表格
        self.stats_tree = factory.create_treeview(
            stats_frame,
            columns=('count', 'mean', 'std', 'min', '25%', '50%', '75%', 'max'),
            show='tree headings'
        )
        
        # 设置列标题
        self.stats_tree.heading('#0', text='列名')
        for col in ('count', 'mean', 'std', 'min', '25%', '50%', '75%', 'max'):
            self.stats_tree.heading(col, text=col)
            self.stats_tree.column(col, width=80)
        
        self.stats_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.register_component('stats_frame', stats_frame)
        self.register_component('stats_tree', self.stats_tree)
    
    def _create_data_view_tab(self):
        """创建数据视图标签页"""
        factory = get_component_factory()
        
        # 数据视图框架
        view_frame = factory.create_frame(self.notebook)
        self.notebook.add(view_frame, text="🔍 数据视图")
        
        # 控制面板
        control_frame = factory.create_frame(view_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 显示行数控制
        factory.create_label(control_frame, text="显示行数:").pack(side=tk.LEFT, padx=5)
        
        self.rows_var = tk.StringVar(value="100")
        rows_combo = factory.create_combobox(
            control_frame,
            textvariable=self.rows_var,
            values=['50', '100', '200', '500', '全部'],
            state='readonly'
        )
        rows_combo.pack(side=tk.LEFT, padx=5)
        rows_combo.bind('<<ComboboxSelected>>', self._on_rows_changed)
        
        # 刷新按钮
        refresh_btn = factory.create_button(
            control_frame,
            text="🔄 刷新",
            command=self._refresh_data_view,
            style='small'
        )
        refresh_btn.pack(side=tk.LEFT, padx=10)
        
        # 数据预览组件
        self.data_preview = DataPreviewWidget(view_frame)
        self.data_preview.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.register_component('view_frame', view_frame)
        self.register_component('control_frame', control_frame)
    
    def _create_column_analysis_tab(self):
        """创建列分析标签页"""
        factory = get_component_factory()
        
        # 列分析框架
        analysis_frame = factory.create_frame(self.notebook)
        self.notebook.add(analysis_frame, text="📈 列分析")
        
        # 分割面板
        paned = ttk.PanedWindow(analysis_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧：列选择
        left_frame = factory.create_frame(paned)
        paned.add(left_frame, weight=1)
        
        factory.create_label(left_frame, text="选择列:", style='subtitle').pack(anchor=tk.W, pady=5)
        
        self.column_listbox = tk.Listbox(left_frame)
        self.column_listbox.pack(fill=tk.BOTH, expand=True)
        self.column_listbox.bind('<<ListboxSelect>>', self._on_column_selected)
        
        # 右侧：列详细信息
        right_frame = factory.create_frame(paned)
        paned.add(right_frame, weight=2)
        
        factory.create_label(right_frame, text="列详细信息:", style='subtitle').pack(anchor=tk.W, pady=5)
        
        self.column_info_text = factory.create_text(right_frame, state=tk.DISABLED)
        self.column_info_text.pack(fill=tk.BOTH, expand=True)
        
        self.register_component('analysis_frame', analysis_frame)
        self.register_component('column_listbox', self.column_listbox)
        self.register_component('column_info_text', self.column_info_text)
    
    def _bind_events(self):
        """绑定事件"""
        # 订阅数据加载事件
        self.subscribe_event(EventTypes.DATA_LOADED, self._on_data_loaded)
        self.subscribe_event(EventTypes.DATA_PREPROCESSED, self._on_data_updated)
    
    def _on_data_loaded(self, event_data: Dict[str, Any]):
        """数据加载事件处理"""
        # 这里可以从事件管理器获取数据，或者通过其他方式获取
        self.logger.info("收到数据加载事件，准备更新预览")
    
    def _on_data_updated(self, event_data: Dict[str, Any]):
        """数据更新事件处理"""
        self.logger.info("收到数据更新事件，准备刷新预览")
    
    def _on_rows_changed(self, event):
        """显示行数改变事件"""
        self._refresh_data_view()
    
    def _on_column_selected(self, event):
        """列选择事件"""
        selection = self.column_listbox.curselection()
        if selection and self.current_data is not None:
            col_index = selection[0]
            col_name = self.column_listbox.get(col_index)
            self._show_column_info(col_name)
    
    def _refresh_data_view(self):
        """刷新数据视图"""
        if self.current_data is None:
            return
        
        rows_to_show = self.rows_var.get()
        if rows_to_show == '全部':
            data_to_show = self.current_data
        else:
            n_rows = int(rows_to_show)
            data_to_show = self.current_data.head(n_rows)
        
        self.data_preview.load_data(data_to_show)
    
    def _show_column_info(self, column_name: str):
        """显示列的详细信息"""
        if self.current_data is None or column_name not in self.current_data.columns:
            return
        
        col_data = self.current_data[column_name]
        
        info_text = f"""列名: {column_name}
数据类型: {col_data.dtype}
非空值数量: {col_data.count()}
缺失值数量: {col_data.isnull().sum()}
缺失值比例: {col_data.isnull().sum() / len(col_data) * 100:.2f}%

"""
        
        if pd.api.types.is_numeric_dtype(col_data):
            # 数值型列的统计信息
            info_text += f"""数值统计:
最小值: {col_data.min()}
最大值: {col_data.max()}
平均值: {col_data.mean():.4f}
标准差: {col_data.std():.4f}
中位数: {col_data.median()}
25%分位数: {col_data.quantile(0.25)}
75%分位数: {col_data.quantile(0.75)}
"""
        else:
            # 非数值型列的统计信息
            info_text += f"""类别统计:
唯一值数量: {col_data.nunique()}
最频繁值: {col_data.mode().iloc[0] if not col_data.mode().empty else 'N/A'}
最频繁值出现次数: {col_data.value_counts().iloc[0] if not col_data.empty else 0}

前10个最频繁值:
"""
            value_counts = col_data.value_counts().head(10)
            for value, count in value_counts.items():
                info_text += f"  {value}: {count} ({count/len(col_data)*100:.1f}%)\n"
        
        # 更新显示
        self.column_info_text.config(state=tk.NORMAL)
        self.column_info_text.delete(1.0, tk.END)
        self.column_info_text.insert(1.0, info_text)
        self.column_info_text.config(state=tk.DISABLED)
    
    def update_data(self, data: pd.DataFrame):
        """更新数据"""
        self.current_data = data
        
        # 更新各个标签页
        self._update_basic_info()
        self._update_statistics()
        self._refresh_data_view()
        self._update_column_list()
        
        self.logger.info(f"数据预览已更新，数据形状: {data.shape}")
    
    def _update_basic_info(self):
        """更新基本信息"""
        if self.current_data is None:
            return
        
        info_text = f"""数据形状: {self.current_data.shape[0]} 行 × {self.current_data.shape[1]} 列
内存使用: {self.current_data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB
缺失值总数: {self.current_data.isnull().sum().sum()}

数据类型分布:
"""
        
        dtype_counts = self.current_data.dtypes.value_counts()
        for dtype, count in dtype_counts.items():
            info_text += f"  {dtype}: {count} 列\n"
        
        info_text += "\n列信息:\n"
        for i, col in enumerate(self.current_data.columns):
            dtype = str(self.current_data[col].dtype)
            null_count = self.current_data[col].isnull().sum()
            null_pct = null_count / len(self.current_data) * 100
            info_text += f"  {i+1:2d}. {col:20s} ({dtype:10s}) - 缺失: {null_count:4d} ({null_pct:5.1f}%)\n"
        
        # 更新显示
        self.basic_info_text.config(state=tk.NORMAL)
        self.basic_info_text.delete(1.0, tk.END)
        self.basic_info_text.insert(1.0, info_text)
        self.basic_info_text.config(state=tk.DISABLED)
    
    def _update_statistics(self):
        """更新统计信息"""
        if self.current_data is None:
            return
        
        # 清空现有数据
        for item in self.stats_tree.get_children():
            self.stats_tree.delete(item)
        
        # 获取数值列的统计信息
        numeric_data = self.current_data.select_dtypes(include=[np.number])
        if not numeric_data.empty:
            stats = numeric_data.describe()
            
            for col in stats.columns:
                values = [
                    f"{stats.loc['count', col]:.0f}",
                    f"{stats.loc['mean', col]:.4f}",
                    f"{stats.loc['std', col]:.4f}",
                    f"{stats.loc['min', col]:.4f}",
                    f"{stats.loc['25%', col]:.4f}",
                    f"{stats.loc['50%', col]:.4f}",
                    f"{stats.loc['75%', col]:.4f}",
                    f"{stats.loc['max', col]:.4f}"
                ]
                
                self.stats_tree.insert('', 'end', text=col, values=values)
    
    def _update_column_list(self):
        """更新列列表"""
        if self.current_data is None:
            return
        
        # 清空列表
        self.column_listbox.delete(0, tk.END)
        
        # 添加列名
        for col in self.current_data.columns:
            self.column_listbox.insert(tk.END, col)
    
    def clear_data(self):
        """清空数据"""
        self.current_data = None
        
        # 清空各个显示区域
        self.basic_info_text.config(state=tk.NORMAL)
        self.basic_info_text.delete(1.0, tk.END)
        self.basic_info_text.config(state=tk.DISABLED)
        
        for item in self.stats_tree.get_children():
            self.stats_tree.delete(item)
        
        self.data_preview.clear_data()
        
        self.column_listbox.delete(0, tk.END)
        
        self.column_info_text.config(state=tk.NORMAL)
        self.column_info_text.delete(1.0, tk.END)
        self.column_info_text.config(state=tk.DISABLED)
