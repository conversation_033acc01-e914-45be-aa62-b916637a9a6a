#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证标签页模块
提供数据质量检查和验证功能
"""

import tkinter as tk
from tkinter import ttk
import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, List

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import EventTypes


class DataValidationTab(BaseGUI):
    """
    数据验证标签页
    提供数据质量检查、异常值检测和数据验证功能
    """
    
    def __init__(self, parent: tk.Widget):
        """初始化数据验证标签页"""
        self.current_data = None
        self.validation_results = {}
        super().__init__(parent)
        
        # 绑定事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='card')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 标题
        title_label = factory.create_label(
            self.main_frame, 
            text="✅ 数据验证", 
            style='title'
        )
        title_label.pack(pady=10)
        
        # 创建控制面板
        self._create_control_panel()
        
        # 创建结果显示区域
        self._create_results_area()
    
    def _create_control_panel(self):
        """创建控制面板"""
        factory = get_component_factory()
        
        # 控制面板框架
        control_frame = factory.create_frame(self.main_frame, style='section')
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        control_title = factory.create_label(control_frame, text="🔧 验证控制", style='subtitle')
        control_title.pack(anchor=tk.W, pady=(5, 10))
        
        # 验证选项
        options_frame = factory.create_frame(control_frame)
        options_frame.pack(fill=tk.X, pady=5)
        
        # 验证选项复选框
        self.check_missing = tk.BooleanVar(value=True)
        self.check_duplicates = tk.BooleanVar(value=True)
        self.check_outliers = tk.BooleanVar(value=True)
        self.check_data_types = tk.BooleanVar(value=True)
        self.check_ranges = tk.BooleanVar(value=False)
        
        checks = [
            (self.check_missing, "检查缺失值"),
            (self.check_duplicates, "检查重复行"),
            (self.check_outliers, "检查异常值"),
            (self.check_data_types, "检查数据类型"),
            (self.check_ranges, "检查数值范围")
        ]
        
        for i, (var, text) in enumerate(checks):
            cb = factory.create_checkbutton(options_frame, text=text, variable=var)
            cb.grid(row=i//3, column=i%3, sticky=tk.W, padx=10, pady=2)
        
        # 执行按钮
        button_frame = factory.create_frame(control_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        validate_btn = factory.create_button(
            button_frame,
            text="🔍 开始验证",
            command=self._run_validation,
            style='primary'
        )
        validate_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = factory.create_button(
            button_frame,
            text="🗑️ 清空结果",
            command=self._clear_results,
            style='default'
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        self.register_component('control_frame', control_frame)
        self.register_component('validate_btn', validate_btn)
    
    def _create_results_area(self):
        """创建结果显示区域"""
        factory = get_component_factory()
        
        # 结果区域框架
        results_frame = factory.create_frame(self.main_frame, style='section')
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        results_title = factory.create_label(results_frame, text="📊 验证结果", style='subtitle')
        results_title.pack(anchor=tk.W, pady=(5, 10))
        
        # 创建标签页容器
        self.results_notebook = factory.create_notebook(results_frame)
        self.results_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个结果标签页
        self._create_summary_tab()
        self._create_details_tab()
        self._create_recommendations_tab()
        
        self.register_component('results_frame', results_frame)
    
    def _create_summary_tab(self):
        """创建摘要标签页"""
        factory = get_component_factory()
        
        # 摘要框架
        summary_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(summary_frame, text="📋 摘要")
        
        # 摘要文本
        self.summary_text = factory.create_text(summary_frame, state=tk.DISABLED)
        self.summary_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.register_component('summary_frame', summary_frame)
        self.register_component('summary_text', self.summary_text)
    
    def _create_details_tab(self):
        """创建详细信息标签页"""
        factory = get_component_factory()
        
        # 详细信息框架
        details_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(details_frame, text="🔍 详细信息")
        
        # 详细信息树形视图
        self.details_tree = factory.create_treeview(
            details_frame,
            columns=('type', 'severity', 'count', 'description'),
            show='tree headings'
        )
        
        # 设置列标题
        self.details_tree.heading('#0', text='问题')
        self.details_tree.heading('type', text='类型')
        self.details_tree.heading('severity', text='严重程度')
        self.details_tree.heading('count', text='数量')
        self.details_tree.heading('description', text='描述')
        
        # 设置列宽
        self.details_tree.column('#0', width=200)
        self.details_tree.column('type', width=100)
        self.details_tree.column('severity', width=100)
        self.details_tree.column('count', width=80)
        self.details_tree.column('description', width=300)
        
        self.details_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.register_component('details_frame', details_frame)
        self.register_component('details_tree', self.details_tree)
    
    def _create_recommendations_tab(self):
        """创建建议标签页"""
        factory = get_component_factory()
        
        # 建议框架
        recommendations_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(recommendations_frame, text="💡 建议")
        
        # 建议文本
        self.recommendations_text = factory.create_text(recommendations_frame, state=tk.DISABLED)
        self.recommendations_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.register_component('recommendations_frame', recommendations_frame)
        self.register_component('recommendations_text', self.recommendations_text)
    
    def _bind_events(self):
        """绑定事件"""
        # 订阅数据加载事件
        self.subscribe_event(EventTypes.DATA_LOADED, self._on_data_loaded)
        self.subscribe_event(EventTypes.DATA_PREPROCESSED, self._on_data_updated)
    
    def _on_data_loaded(self, event_data: Dict[str, Any]):
        """数据加载事件处理"""
        self.logger.info("收到数据加载事件，准备进行数据验证")
    
    def _on_data_updated(self, event_data: Dict[str, Any]):
        """数据更新事件处理"""
        self.logger.info("收到数据更新事件，清空验证结果")
        self._clear_results()
    
    def _run_validation(self):
        """运行数据验证"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return
        
        try:
            self.logger.info("开始数据验证")
            self.validation_results = {}
            
            # 执行各种验证
            if self.check_missing.get():
                self._check_missing_values()
            
            if self.check_duplicates.get():
                self._check_duplicate_rows()
            
            if self.check_outliers.get():
                self._check_outliers()
            
            if self.check_data_types.get():
                self._check_data_types()
            
            if self.check_ranges.get():
                self._check_value_ranges()
            
            # 更新显示
            self._update_results_display()
            
            self.logger.info("数据验证完成")
            self.show_info("完成", "数据验证已完成，请查看结果")
            
        except Exception as e:
            error_msg = f"数据验证失败: {str(e)}"
            self.show_error("错误", error_msg)
            self.logger.error(error_msg)
    
    def _check_missing_values(self):
        """检查缺失值"""
        missing_info = {}
        total_missing = 0
        
        for col in self.current_data.columns:
            missing_count = self.current_data[col].isnull().sum()
            if missing_count > 0:
                missing_pct = missing_count / len(self.current_data) * 100
                missing_info[col] = {
                    'count': missing_count,
                    'percentage': missing_pct
                }
                total_missing += missing_count
        
        self.validation_results['missing_values'] = {
            'total_missing': total_missing,
            'columns_with_missing': len(missing_info),
            'details': missing_info
        }
    
    def _check_duplicate_rows(self):
        """检查重复行"""
        duplicates = self.current_data.duplicated()
        duplicate_count = duplicates.sum()
        
        self.validation_results['duplicates'] = {
            'count': duplicate_count,
            'percentage': duplicate_count / len(self.current_data) * 100,
            'indices': self.current_data[duplicates].index.tolist()
        }
    
    def _check_outliers(self):
        """检查异常值（使用IQR方法）"""
        numeric_cols = self.current_data.select_dtypes(include=[np.number]).columns
        outlier_info = {}
        
        for col in numeric_cols:
            Q1 = self.current_data[col].quantile(0.25)
            Q3 = self.current_data[col].quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = self.current_data[
                (self.current_data[col] < lower_bound) | 
                (self.current_data[col] > upper_bound)
            ]
            
            if len(outliers) > 0:
                outlier_info[col] = {
                    'count': len(outliers),
                    'percentage': len(outliers) / len(self.current_data) * 100,
                    'lower_bound': lower_bound,
                    'upper_bound': upper_bound,
                    'indices': outliers.index.tolist()
                }
        
        self.validation_results['outliers'] = outlier_info
    
    def _check_data_types(self):
        """检查数据类型"""
        type_info = {}
        
        for col in self.current_data.columns:
            col_data = self.current_data[col]
            current_type = str(col_data.dtype)
            
            # 尝试推断更合适的数据类型
            suggested_type = self._suggest_data_type(col_data)
            
            type_info[col] = {
                'current_type': current_type,
                'suggested_type': suggested_type,
                'needs_conversion': current_type != suggested_type
            }
        
        self.validation_results['data_types'] = type_info
    
    def _check_value_ranges(self):
        """检查数值范围"""
        numeric_cols = self.current_data.select_dtypes(include=[np.number]).columns
        range_info = {}
        
        for col in numeric_cols:
            col_data = self.current_data[col]
            range_info[col] = {
                'min': col_data.min(),
                'max': col_data.max(),
                'range': col_data.max() - col_data.min(),
                'negative_count': (col_data < 0).sum(),
                'zero_count': (col_data == 0).sum(),
                'positive_count': (col_data > 0).sum()
            }
        
        self.validation_results['value_ranges'] = range_info
    
    def _suggest_data_type(self, series: pd.Series) -> str:
        """建议合适的数据类型"""
        # 简单的类型推断逻辑
        if pd.api.types.is_numeric_dtype(series):
            if series.dtype == 'float64' and series.isnull().sum() == 0:
                if (series % 1 == 0).all():
                    return 'int64'
            return str(series.dtype)
        
        # 尝试转换为数值类型
        try:
            pd.to_numeric(series, errors='raise')
            return 'numeric'
        except:
            pass
        
        # 尝试转换为日期类型
        try:
            pd.to_datetime(series, errors='raise')
            return 'datetime'
        except:
            pass
        
        # 检查是否为分类数据
        unique_ratio = series.nunique() / len(series)
        if unique_ratio < 0.5:
            return 'category'
        
        return 'object'
    
    def _update_results_display(self):
        """更新结果显示"""
        self._update_summary()
        self._update_details()
        self._update_recommendations()
    
    def _update_summary(self):
        """更新摘要"""
        summary_text = "数据验证摘要\n" + "="*50 + "\n\n"
        
        # 基本信息
        summary_text += f"数据形状: {self.current_data.shape[0]} 行 × {self.current_data.shape[1]} 列\n\n"
        
        # 缺失值摘要
        if 'missing_values' in self.validation_results:
            mv = self.validation_results['missing_values']
            summary_text += f"缺失值:\n"
            summary_text += f"  总缺失值: {mv['total_missing']}\n"
            summary_text += f"  有缺失值的列: {mv['columns_with_missing']}\n\n"
        
        # 重复行摘要
        if 'duplicates' in self.validation_results:
            dup = self.validation_results['duplicates']
            summary_text += f"重复行:\n"
            summary_text += f"  重复行数: {dup['count']} ({dup['percentage']:.2f}%)\n\n"
        
        # 异常值摘要
        if 'outliers' in self.validation_results:
            outliers = self.validation_results['outliers']
            total_outliers = sum(info['count'] for info in outliers.values())
            summary_text += f"异常值:\n"
            summary_text += f"  有异常值的列: {len(outliers)}\n"
            summary_text += f"  异常值总数: {total_outliers}\n\n"
        
        # 数据类型摘要
        if 'data_types' in self.validation_results:
            types = self.validation_results['data_types']
            needs_conversion = sum(1 for info in types.values() if info['needs_conversion'])
            summary_text += f"数据类型:\n"
            summary_text += f"  建议转换类型的列: {needs_conversion}\n\n"
        
        # 更新显示
        self.summary_text.config(state=tk.NORMAL)
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.insert(1.0, summary_text)
        self.summary_text.config(state=tk.DISABLED)
    
    def _update_details(self):
        """更新详细信息"""
        # 清空现有数据
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)
        
        # 添加缺失值详情
        if 'missing_values' in self.validation_results:
            mv = self.validation_results['missing_values']
            if mv['details']:
                missing_parent = self.details_tree.insert(
                    '', 'end', text='缺失值问题',
                    values=('数据质量', '中等', mv['total_missing'], '数据中存在缺失值')
                )
                
                for col, info in mv['details'].items():
                    self.details_tree.insert(
                        missing_parent, 'end', text=f'列: {col}',
                        values=('缺失值', '中等', info['count'], f"{info['percentage']:.2f}% 缺失")
                    )
        
        # 添加重复行详情
        if 'duplicates' in self.validation_results:
            dup = self.validation_results['duplicates']
            if dup['count'] > 0:
                self.details_tree.insert(
                    '', 'end', text='重复行',
                    values=('数据质量', '低', dup['count'], f"{dup['percentage']:.2f}% 重复")
                )
        
        # 添加异常值详情
        if 'outliers' in self.validation_results:
            outliers = self.validation_results['outliers']
            if outliers:
                outlier_parent = self.details_tree.insert(
                    '', 'end', text='异常值问题',
                    values=('数据质量', '中等', len(outliers), '检测到异常值')
                )
                
                for col, info in outliers.items():
                    self.details_tree.insert(
                        outlier_parent, 'end', text=f'列: {col}',
                        values=('异常值', '中等', info['count'], f"{info['percentage']:.2f}% 异常")
                    )
    
    def _update_recommendations(self):
        """更新建议"""
        recommendations = "数据质量改进建议\n" + "="*50 + "\n\n"
        
        # 缺失值处理建议
        if 'missing_values' in self.validation_results:
            mv = self.validation_results['missing_values']
            if mv['details']:
                recommendations += "缺失值处理建议:\n"
                for col, info in mv['details'].items():
                    if info['percentage'] > 50:
                        recommendations += f"  • {col}: 缺失值过多({info['percentage']:.1f}%)，建议删除该列\n"
                    elif info['percentage'] > 10:
                        recommendations += f"  • {col}: 缺失值较多({info['percentage']:.1f}%)，建议使用插值或填充\n"
                    else:
                        recommendations += f"  • {col}: 缺失值较少({info['percentage']:.1f}%)，可以删除或填充\n"
                recommendations += "\n"
        
        # 重复行处理建议
        if 'duplicates' in self.validation_results:
            dup = self.validation_results['duplicates']
            if dup['count'] > 0:
                recommendations += "重复行处理建议:\n"
                recommendations += f"  • 发现 {dup['count']} 行重复数据，建议删除重复行\n\n"
        
        # 异常值处理建议
        if 'outliers' in self.validation_results:
            outliers = self.validation_results['outliers']
            if outliers:
                recommendations += "异常值处理建议:\n"
                for col, info in outliers.items():
                    if info['percentage'] > 5:
                        recommendations += f"  • {col}: 异常值较多({info['percentage']:.1f}%)，建议检查数据来源\n"
                    else:
                        recommendations += f"  • {col}: 可以考虑删除或转换异常值\n"
                recommendations += "\n"
        
        # 数据类型转换建议
        if 'data_types' in self.validation_results:
            types = self.validation_results['data_types']
            conversion_needed = {k: v for k, v in types.items() if v['needs_conversion']}
            if conversion_needed:
                recommendations += "数据类型转换建议:\n"
                for col, info in conversion_needed.items():
                    recommendations += f"  • {col}: 从 {info['current_type']} 转换为 {info['suggested_type']}\n"
                recommendations += "\n"
        
        if not any(key in self.validation_results for key in ['missing_values', 'duplicates', 'outliers']):
            recommendations += "恭喜！数据质量良好，未发现明显问题。\n"
        
        # 更新显示
        self.recommendations_text.config(state=tk.NORMAL)
        self.recommendations_text.delete(1.0, tk.END)
        self.recommendations_text.insert(1.0, recommendations)
        self.recommendations_text.config(state=tk.DISABLED)
    
    def _clear_results(self):
        """清空结果"""
        self.validation_results = {}
        
        # 清空各个显示区域
        self.summary_text.config(state=tk.NORMAL)
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.config(state=tk.DISABLED)
        
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)
        
        self.recommendations_text.config(state=tk.NORMAL)
        self.recommendations_text.delete(1.0, tk.END)
        self.recommendations_text.config(state=tk.DISABLED)
    
    def update_data(self, data: pd.DataFrame):
        """更新数据"""
        self.current_data = data
        self._clear_results()
        self.logger.info(f"数据验证模块已更新数据，形状: {data.shape}")
    
    def get_validation_results(self) -> Dict[str, Any]:
        """获取验证结果"""
        return self.validation_results.copy()
