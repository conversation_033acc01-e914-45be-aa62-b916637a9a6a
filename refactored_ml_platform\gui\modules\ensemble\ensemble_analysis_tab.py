"""
集成分析标签页
提供集成学习模型的分析功能
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Any, Optional

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager


class EnsembleAnalysisTab(BaseGUI):
    """集成分析标签页"""
    
    def __init__(self, parent: tk.Widget):
        """初始化集成分析标签页"""
        self.ensemble_results = {}
        self.selected_ensemble = None
        
        super().__init__(parent)
        
        # 订阅集成训练完成事件
        event_manager = get_event_manager()
        event_manager.subscribe('ensemble_training_completed', self._on_ensemble_training_completed)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='main')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建上下分割
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.VERTICAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 上方控制面板
        top_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(top_frame, weight=1)
        
        # 下方分析面板
        bottom_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(bottom_frame, weight=3)
        
        # 设置控制面板
        self._setup_control_panel(top_frame)
        
        # 设置分析面板
        self._setup_analysis_panel(bottom_frame)
        
        self.register_component('main_frame', self.main_frame)
        self.register_component('paned_window', paned_window)
    
    def _setup_control_panel(self, parent):
        """设置控制面板"""
        factory = get_component_factory()
        
        # 集成模型选择
        selection_frame = factory.create_labelframe(parent, text="集成模型选择", style='section')
        selection_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 集成模型下拉框
        model_frame = factory.create_frame(selection_frame)
        model_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(model_frame, text="选择集成模型:").pack(side=tk.LEFT)
        
        self.ensemble_var = tk.StringVar()
        self.ensemble_combo = factory.create_combobox(
            model_frame,
            textvariable=self.ensemble_var,
            state="readonly"
        )
        self.ensemble_combo.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        self.ensemble_combo.bind('<<ComboboxSelected>>', self._on_ensemble_selected)
        
        # 分析选项
        options_frame = factory.create_labelframe(parent, text="分析选项", style='section')
        options_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 分析类型复选框
        self.analysis_vars = {}
        analysis_types = [
            ('diversity', '多样性分析'),
            ('contribution', '贡献度分析'),
            ('stability', '稳定性分析'),
            ('error_correlation', '错误相关性分析')
        ]
        
        for analysis_type, display_name in analysis_types:
            var = tk.BooleanVar(value=True)
            checkbox = factory.create_checkbox(
                options_frame,
                text=display_name,
                variable=var
            )
            checkbox.pack(anchor=tk.W, padx=5, pady=2)
            self.analysis_vars[analysis_type] = var
        
        # 分析按钮
        analyze_btn = factory.create_button(
            parent,
            text="🔍 开始分析",
            command=self._start_analysis,
            style='primary'
        )
        analyze_btn.pack(fill=tk.X, padx=5, pady=10)
        
        # 状态标签
        self.status_label = factory.create_label(
            parent,
            text="请先训练集成模型",
            style='info'
        )
        self.status_label.pack(padx=5, pady=5)
    
    def _setup_analysis_panel(self, parent):
        """设置分析面板"""
        factory = get_component_factory()
        
        # 分析结果标签页
        self.analysis_notebook = factory.create_notebook(parent)
        self.analysis_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 多样性分析标签页
        diversity_frame = factory.create_frame(self.analysis_notebook)
        self.analysis_notebook.add(diversity_frame, text="多样性分析")
        
        self.diversity_text = factory.create_text(
            diversity_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        diversity_scrollbar = factory.create_scrollbar(diversity_frame, orient=tk.VERTICAL)
        diversity_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.diversity_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.diversity_text.configure(yscrollcommand=diversity_scrollbar.set)
        diversity_scrollbar.configure(command=self.diversity_text.yview)
        
        # 贡献度分析标签页
        contribution_frame = factory.create_frame(self.analysis_notebook)
        self.analysis_notebook.add(contribution_frame, text="贡献度分析")
        
        # 贡献度表格
        contrib_columns = ('基础模型', '准确率贡献', '多样性贡献', '总体贡献', '重要性排名')
        self.contribution_tree = factory.create_treeview(
            contribution_frame,
            columns=contrib_columns,
            show='headings'
        )
        
        for col in contrib_columns:
            self.contribution_tree.heading(col, text=col)
            self.contribution_tree.column(col, width=120, anchor=tk.CENTER)
        
        contrib_scrollbar = factory.create_scrollbar(contribution_frame, orient=tk.VERTICAL)
        contrib_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.contribution_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.contribution_tree.configure(yscrollcommand=contrib_scrollbar.set)
        contrib_scrollbar.configure(command=self.contribution_tree.yview)
        
        # 稳定性分析标签页
        stability_frame = factory.create_frame(self.analysis_notebook)
        self.analysis_notebook.add(stability_frame, text="稳定性分析")
        
        self.stability_text = factory.create_text(
            stability_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        stability_scrollbar = factory.create_scrollbar(stability_frame, orient=tk.VERTICAL)
        stability_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.stability_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.stability_text.configure(yscrollcommand=stability_scrollbar.set)
        stability_scrollbar.configure(command=self.stability_text.yview)
        
        # 错误相关性分析标签页
        error_frame = factory.create_frame(self.analysis_notebook)
        self.analysis_notebook.add(error_frame, text="错误相关性")
        
        self.error_text = factory.create_text(
            error_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        error_scrollbar = factory.create_scrollbar(error_frame, orient=tk.VERTICAL)
        error_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.error_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.error_text.configure(yscrollcommand=error_scrollbar.set)
        error_scrollbar.configure(command=self.error_text.yview)
    
    def _on_ensemble_training_completed(self, event_data: Dict[str, Any]):
        """集成训练完成事件处理"""
        self.ensemble_results = event_data.get('results', {})
        
        # 更新集成模型下拉框
        ensemble_names = list(self.ensemble_results.keys())
        self.ensemble_combo['values'] = ensemble_names
        
        if ensemble_names:
            self.ensemble_combo.set(ensemble_names[0])
            self.selected_ensemble = ensemble_names[0]
            self.status_label.config(text=f"可分析 {len(ensemble_names)} 个集成模型")
        
        self.logger.info(f"接收到 {len(self.ensemble_results)} 个集成模型用于分析")
    
    def _on_ensemble_selected(self, event):
        """集成模型选择事件处理"""
        self.selected_ensemble = self.ensemble_var.get()
        self.logger.info(f"选择集成模型: {self.selected_ensemble}")
    
    def _start_analysis(self):
        """开始分析"""
        if not self.selected_ensemble or self.selected_ensemble not in self.ensemble_results:
            self.status_label.config(text="请选择有效的集成模型")
            return
        
        self.status_label.config(text="正在分析集成模型...")
        
        # 获取选中的分析类型
        selected_analyses = [
            analysis_type for analysis_type, var in self.analysis_vars.items() if var.get()
        ]
        
        if not selected_analyses:
            self.status_label.config(text="请至少选择一种分析类型")
            return
        
        # 执行分析
        ensemble_data = self.ensemble_results[self.selected_ensemble]
        
        if 'diversity' in selected_analyses:
            self._analyze_diversity(ensemble_data)
        
        if 'contribution' in selected_analyses:
            self._analyze_contribution(ensemble_data)
        
        if 'stability' in selected_analyses:
            self._analyze_stability(ensemble_data)
        
        if 'error_correlation' in selected_analyses:
            self._analyze_error_correlation(ensemble_data)
        
        self.status_label.config(text="分析完成")
    
    def _analyze_diversity(self, ensemble_data: Dict[str, Any]):
        """多样性分析"""
        import random
        
        base_models = ensemble_data.get('base_models', [])
        
        # 模拟多样性分析结果
        diversity_metrics = {
            'disagreement_measure': random.uniform(0.1, 0.4),
            'double_fault_measure': random.uniform(0.05, 0.2),
            'q_statistic': random.uniform(-0.5, 0.5),
            'correlation_coefficient': random.uniform(-0.3, 0.7),
            'kohavi_wolpert_variance': random.uniform(0.1, 0.3)
        }
        
        analysis_text = f"""
多样性分析结果
{'='*50}

基础模型: {', '.join([self._get_display_name(model) for model in base_models])}

多样性指标:
- 分歧度量 (Disagreement Measure): {diversity_metrics['disagreement_measure']:.4f}
  * 值越大表示模型间差异越大，多样性越好
  * 当前值: {'良好' if diversity_metrics['disagreement_measure'] > 0.2 else '一般'}

- 双重故障度量 (Double Fault Measure): {diversity_metrics['double_fault_measure']:.4f}
  * 值越小表示模型间互补性越好
  * 当前值: {'良好' if diversity_metrics['double_fault_measure'] < 0.15 else '一般'}

- Q统计量 (Q-Statistic): {diversity_metrics['q_statistic']:.4f}
  * 值接近0表示模型独立，负值表示互补
  * 当前值: {'互补性好' if diversity_metrics['q_statistic'] < 0 else '相关性较高'}

- 相关系数 (Correlation Coefficient): {diversity_metrics['correlation_coefficient']:.4f}
  * 值越小表示模型越独立
  * 当前值: {'独立性好' if abs(diversity_metrics['correlation_coefficient']) < 0.3 else '相关性较高'}

- Kohavi-Wolpert方差: {diversity_metrics['kohavi_wolpert_variance']:.4f}
  * 衡量模型预测的方差，值适中为佳
  * 当前值: {'适中' if 0.15 <= diversity_metrics['kohavi_wolpert_variance'] <= 0.25 else '需要调整'}

多样性评估:
"""
        
        # 综合评估
        good_metrics = sum([
            diversity_metrics['disagreement_measure'] > 0.2,
            diversity_metrics['double_fault_measure'] < 0.15,
            diversity_metrics['q_statistic'] < 0,
            abs(diversity_metrics['correlation_coefficient']) < 0.3,
            0.15 <= diversity_metrics['kohavi_wolpert_variance'] <= 0.25
        ])
        
        if good_metrics >= 4:
            analysis_text += "✅ 集成模型具有良好的多样性，各基础模型互补性强"
        elif good_metrics >= 3:
            analysis_text += "⚠️ 集成模型多样性中等，可以考虑调整基础模型组合"
        else:
            analysis_text += "❌ 集成模型多样性较差，建议重新选择基础模型"
        
        analysis_text += f"""

建议:
- 如果多样性不足，可以尝试使用不同类型的基础模型
- 考虑使用不同的特征子集训练基础模型
- 调整基础模型的超参数以增加差异性
        """
        
        self._update_text_widget(self.diversity_text, analysis_text.strip())
    
    def _analyze_contribution(self, ensemble_data: Dict[str, Any]):
        """贡献度分析"""
        import random
        
        base_models = ensemble_data.get('base_models', [])
        
        # 清空贡献度表格
        for item in self.contribution_tree.get_children():
            self.contribution_tree.delete(item)
        
        # 模拟贡献度分析
        contributions = []
        for model_name in base_models:
            contrib_data = {
                'model': model_name,
                'accuracy_contrib': random.uniform(0.15, 0.35),
                'diversity_contrib': random.uniform(0.10, 0.30),
                'total_contrib': 0
            }
            contrib_data['total_contrib'] = (contrib_data['accuracy_contrib'] + contrib_data['diversity_contrib']) / 2
            contributions.append(contrib_data)
        
        # 按总贡献度排序
        contributions.sort(key=lambda x: x['total_contrib'], reverse=True)
        
        # 添加到表格
        for rank, contrib in enumerate(contributions, 1):
            display_name = self._get_display_name(contrib['model'])
            values = (
                display_name,
                f"{contrib['accuracy_contrib']:.4f}",
                f"{contrib['diversity_contrib']:.4f}",
                f"{contrib['total_contrib']:.4f}",
                rank
            )
            self.contribution_tree.insert('', tk.END, values=values)
    
    def _analyze_stability(self, ensemble_data: Dict[str, Any]):
        """稳定性分析"""
        import random
        
        base_models = ensemble_data.get('base_models', [])
        
        # 模拟稳定性分析结果
        stability_metrics = {
            'prediction_variance': random.uniform(0.01, 0.05),
            'bootstrap_stability': random.uniform(0.85, 0.98),
            'cross_validation_std': random.uniform(0.005, 0.02),
            'model_agreement': random.uniform(0.75, 0.95)
        }
        
        analysis_text = f"""
稳定性分析结果
{'='*50}

基础模型: {', '.join([self._get_display_name(model) for model in base_models])}

稳定性指标:
- 预测方差 (Prediction Variance): {stability_metrics['prediction_variance']:.6f}
  * 值越小表示预测越稳定
  * 当前值: {'稳定' if stability_metrics['prediction_variance'] < 0.03 else '不够稳定'}

- Bootstrap稳定性: {stability_metrics['bootstrap_stability']:.4f}
  * 值越高表示模型对数据扰动越不敏感
  * 当前值: {'很稳定' if stability_metrics['bootstrap_stability'] > 0.9 else '较稳定'}

- 交叉验证标准差: {stability_metrics['cross_validation_std']:.6f}
  * 值越小表示性能越一致
  * 当前值: {'一致性好' if stability_metrics['cross_validation_std'] < 0.015 else '一致性一般'}

- 模型一致性 (Model Agreement): {stability_metrics['model_agreement']:.4f}
  * 值越高表示基础模型预测越一致
  * 当前值: {'一致性高' if stability_metrics['model_agreement'] > 0.85 else '一致性中等'}

稳定性评估:
"""
        
        # 综合评估
        stable_metrics = sum([
            stability_metrics['prediction_variance'] < 0.03,
            stability_metrics['bootstrap_stability'] > 0.9,
            stability_metrics['cross_validation_std'] < 0.015,
            stability_metrics['model_agreement'] > 0.85
        ])
        
        if stable_metrics >= 3:
            analysis_text += "✅ 集成模型具有良好的稳定性，预测结果可靠"
        elif stable_metrics >= 2:
            analysis_text += "⚠️ 集成模型稳定性中等，可以进一步优化"
        else:
            analysis_text += "❌ 集成模型稳定性较差，建议调整模型配置"
        
        analysis_text += f"""

建议:
- 如果稳定性不足，可以增加基础模型数量
- 使用更稳定的基础模型算法
- 调整集成策略，如使用加权投票
        """
        
        self._update_text_widget(self.stability_text, analysis_text.strip())
    
    def _analyze_error_correlation(self, ensemble_data: Dict[str, Any]):
        """错误相关性分析"""
        import random
        
        base_models = ensemble_data.get('base_models', [])
        
        # 模拟错误相关性分析
        error_analysis = f"""
错误相关性分析结果
{'='*50}

基础模型: {', '.join([self._get_display_name(model) for model in base_models])}

错误模式分析:
"""
        
        # 模拟两两模型间的错误相关性
        for i, model1 in enumerate(base_models):
            for j, model2 in enumerate(base_models[i+1:], i+1):
                correlation = random.uniform(-0.2, 0.6)
                display_name1 = self._get_display_name(model1)
                display_name2 = self._get_display_name(model2)
                
                if correlation < 0.1:
                    relationship = "互补性强"
                elif correlation < 0.3:
                    relationship = "轻微相关"
                else:
                    relationship = "相关性较高"
                
                error_analysis += f"\n- {display_name1} vs {display_name2}: {correlation:.3f} ({relationship})"
        
        error_analysis += f"""

错误类型分布:
- 假阳性错误: {random.uniform(0.02, 0.08):.3f}
- 假阴性错误: {random.uniform(0.01, 0.06):.3f}
- 边界样本错误: {random.uniform(0.03, 0.10):.3f}
- 噪声样本错误: {random.uniform(0.01, 0.05):.3f}

集成效果:
- 错误减少率: {random.uniform(0.15, 0.35):.3f}
- 鲁棒性提升: {random.uniform(0.10, 0.25):.3f}

建议:
- 相关性高的模型对可能需要调整
- 关注错误集中的样本类型，进行针对性优化
- 考虑使用错误纠正策略
        """
        
        self._update_text_widget(self.error_text, error_analysis.strip())
    
    def _get_display_name(self, model_name: str) -> str:
        """获取模型显示名称"""
        display_names = {
            'DecisionTree': '决策树',
            'RandomForest': '随机森林',
            'XGBoost': 'XGBoost',
            'LightGBM': 'LightGBM',
            'CatBoost': 'CatBoost',
            'Logistic': '逻辑回归',
            'SVM': '支持向量机',
            'KNN': 'K近邻',
            'NaiveBayes': '朴素贝叶斯',
            'NeuralNet': '神经网络'
        }
        return display_names.get(model_name, model_name)
    
    def _update_text_widget(self, text_widget, content: str):
        """更新文本控件内容"""
        text_widget.config(state=tk.NORMAL)
        text_widget.delete(1.0, tk.END)
        text_widget.insert(1.0, content)
        text_widget.config(state=tk.DISABLED)
