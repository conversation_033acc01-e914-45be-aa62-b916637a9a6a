#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成学习管理器
负责集成学习的配置、训练和结果分析
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, List
import threading
import time
import pandas as pd
import numpy as np

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager, EventTypes
from ...components.progress_widgets import ProgressWidget

# 导入算法模块
try:
    from algorithms import MODEL_TRAINERS
    from algorithms.model_ensemble import run_ensemble_pipeline, EnsembleModel
    from algorithms.multi_data_ensemble import run_multi_data_ensemble_pipeline, MultiDataEnsemble
    from algorithms.hyperparameter_tuning import tune_model
    HAS_ALGORITHMS = True
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))

    try:
        from algorithms import MODEL_TRAINERS
        from algorithms.model_ensemble import run_ensemble_pipeline, EnsembleModel
        from algorithms.multi_data_ensemble import run_multi_data_ensemble_pipeline, MultiDataEnsemble
        from algorithms.hyperparameter_tuning import tune_model
        HAS_ALGORITHMS = True
    except ImportError:
        MODEL_TRAINERS = {}
        HAS_ALGORITHMS = False

# 导入线程安全GUI工具
try:
    from utils.thread_safe_gui import ThreadSafeGUI
except ImportError:
    from utils.thread_safe_gui import ThreadSafeGUI


class EnsembleManager(BaseGUI):
    """集成学习管理器类"""
    
    def __init__(self, parent):
        """初始化集成学习管理器"""
        self.current_data = None
        self.preprocessed_data = None
        self.training_results = None
        self.ensemble_results = {}
        self.multi_data_results = {}
        self.selected_models = []
        self.data_sources = {}  # 多数据源映射

        # 集成配置
        self.ensemble_config = {
            'method': 'voting',
            'voting_type': 'soft',
            'cv_folds': 5,
            'test_size': 0.2,
            'random_state': 42,
            'enable_hyperparameter_tuning': False,
            'tune_budget': 30,
            'enable_shap': True,
            'n_jobs': -1
        }

        # 多数据源集成配置
        self.multi_data_config = {
            'ensemble_methods': ['voting', 'stacking', 'weighted'],
            'data_strategies': ['unified', 'original', 'combined'],
            'feature_selection': True,
            'feature_selection_method': 'weighted',
            'k_features': None
        }

        self.is_training = False
        self.training_thread = None

        # 初始化线程安全GUI更新器
        if parent:
            self.gui_updater = ThreadSafeGUI(parent)
        else:
            self.gui_updater = None

        super().__init__(parent)

        # 订阅相关事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        if self.parent:
            self.main_frame = factory.create_frame(self.parent)
            self.main_frame.pack(fill='both', expand=True, padx=5, pady=5)
        else:
            self.main_frame = None
            return
        
        # 创建主要内容区域
        self._create_main_content()
    
    def _create_main_content(self):
        """创建主要内容区域"""
        factory = get_component_factory()
        
        # 创建水平分割面板
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill='both', expand=True)
        
        # 左侧配置面板
        self._create_config_panel(paned_window)
        
        # 右侧结果面板
        self._create_results_panel(paned_window)
        
        self.register_component('paned_window', paned_window)
    
    def _create_config_panel(self, parent):
        """创建左侧配置面板"""
        factory = get_component_factory()
        
        # 配置面板框架
        config_frame = factory.create_frame(parent)
        config_frame.pack(fill='both', expand=True)
        
        # 创建滚动区域
        canvas = tk.Canvas(config_frame, width=350)
        scrollbar = ttk.Scrollbar(config_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = factory.create_frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 状态信息
        self._create_status_section(scrollable_frame)
        
        # 模型选择
        self._create_model_selection_section(scrollable_frame)
        
        # 集成配置
        self._create_ensemble_config_section(scrollable_frame)

        # 多数据源集成配置
        self._create_multi_data_section(scrollable_frame)

        # 控制按钮
        self._create_control_buttons_section(scrollable_frame)
        
        # 将配置面板添加到分割窗口
        parent.add(config_frame, weight=1)
        
        self.register_component('config_frame', config_frame)
    
    def _create_status_section(self, parent):
        """创建状态信息区域"""
        factory = get_component_factory()
        
        status_frame = factory.create_labelframe(parent, text="🔗 集成状态")
        status_frame.pack(fill='x', padx=10, pady=5)
        
        self.status_label = factory.create_label(status_frame, text="等待模型训练完成...", style='info')
        self.status_label.pack(padx=10, pady=10)
        
        # 进度条
        self.progress_widget = ProgressWidget(status_frame, show_percentage=True)
        if self.progress_widget.main_frame:
            self.progress_widget.main_frame.pack(fill='x', padx=10, pady=(0, 10))
    
    def _create_model_selection_section(self, parent):
        """创建模型选择区域"""
        factory = get_component_factory()
        
        model_frame = factory.create_labelframe(parent, text="🎯 基模型选择")
        model_frame.pack(fill='x', padx=10, pady=5)
        
        # 说明文本
        info_label = factory.create_label(
            model_frame, 
            text="选择要用于集成的基模型（需要先完成模型训练）:",
            style='secondary'
        )
        info_label.pack(padx=10, pady=5, anchor='w')
        
        # 模型复选框容器
        self.model_checkboxes_frame = factory.create_frame(model_frame)
        self.model_checkboxes_frame.pack(fill='x', padx=10, pady=5)
        
        # 模型变量字典
        self.model_vars = {}
        
        # 占位符文本
        self.no_models_label = factory.create_label(
            self.model_checkboxes_frame,
            text="请先完成模型训练以选择基模型",
            style='secondary'
        )
        self.no_models_label.pack(pady=10)
        
        # 全选/取消全选按钮
        button_frame = factory.create_frame(model_frame)
        button_frame.pack(fill='x', padx=10, pady=5)
        
        self.select_all_btn = factory.create_button(button_frame, text="全选", 
                                                  command=self._select_all_models, style='small')
        self.select_all_btn.pack(side='left', padx=(0, 5))
        self.select_all_btn.config(state='disabled')
        
        self.deselect_all_btn = factory.create_button(button_frame, text="取消全选", 
                                                    command=self._deselect_all_models, style='small')
        self.deselect_all_btn.pack(side='left')
        self.deselect_all_btn.config(state='disabled')
    
    def _create_ensemble_config_section(self, parent):
        """创建集成配置区域"""
        factory = get_component_factory()
        
        config_frame = factory.create_labelframe(parent, text="⚙️ 集成配置")
        config_frame.pack(fill='x', padx=10, pady=5)
        
        # 集成方法
        method_frame = factory.create_frame(config_frame)
        method_frame.pack(fill='x', padx=10, pady=5)
        
        factory.create_label(method_frame, text="集成方法:").pack(side='left')
        self.ensemble_method_var = tk.StringVar(value='voting')
        method_combo = ttk.Combobox(method_frame, textvariable=self.ensemble_method_var,
                                  values=['voting', 'bagging', 'stacking', 'weighted'],
                                  state='readonly', width=15)
        method_combo.pack(side='right')
        method_combo.bind('<<ComboboxSelected>>', self._on_method_changed)
        
        # 投票类型（仅对voting方法有效）
        voting_frame = factory.create_frame(config_frame)
        voting_frame.pack(fill='x', padx=10, pady=5)
        
        factory.create_label(voting_frame, text="投票类型:").pack(side='left')
        self.voting_type_var = tk.StringVar(value='soft')
        voting_combo = ttk.Combobox(voting_frame, textvariable=self.voting_type_var,
                                  values=['soft', 'hard'], state='readonly', width=15)
        voting_combo.pack(side='right')
        
        # 交叉验证折数
        cv_frame = factory.create_frame(config_frame)
        cv_frame.pack(fill='x', padx=10, pady=5)
        
        factory.create_label(cv_frame, text="交叉验证折数:").pack(side='left')
        self.cv_folds_var = tk.IntVar(value=5)
        cv_spin = tk.Spinbox(cv_frame, from_=3, to=10, width=10, textvariable=self.cv_folds_var)
        cv_spin.pack(side='right')
        
        # 随机种子
        seed_frame = factory.create_frame(config_frame)
        seed_frame.pack(fill='x', padx=10, pady=5)

        factory.create_label(seed_frame, text="随机种子:").pack(side='left')
        self.random_state_var = tk.IntVar(value=42)
        seed_entry = factory.create_entry(seed_frame, textvariable=self.random_state_var, width=10)
        seed_entry.pack(side='right')

        # 高级选项
        advanced_frame = factory.create_labelframe(config_frame, text="🔧 高级选项")
        advanced_frame.pack(fill='x', padx=5, pady=5)

        # 超参数调优
        tune_frame = factory.create_frame(advanced_frame)
        tune_frame.pack(fill='x', padx=10, pady=5)

        self.enable_tuning_var = tk.BooleanVar(value=False)
        tune_cb = factory.create_checkbutton(tune_frame, text="启用超参数调优",
                                           variable=self.enable_tuning_var,
                                           command=self._toggle_tuning_options)
        tune_cb.pack(side='left')

        # 调优预算
        self.tune_budget_frame = factory.create_frame(advanced_frame)
        self.tune_budget_frame.pack(fill='x', padx=10, pady=5)

        factory.create_label(self.tune_budget_frame, text="调优预算:").pack(side='left')
        self.tune_budget_var = tk.IntVar(value=30)
        budget_spin = tk.Spinbox(self.tune_budget_frame, from_=10, to=100, width=10,
                               textvariable=self.tune_budget_var)
        budget_spin.pack(side='right')

        # SHAP分析
        shap_frame = factory.create_frame(advanced_frame)
        shap_frame.pack(fill='x', padx=10, pady=5)

        self.enable_shap_var = tk.BooleanVar(value=True)
        shap_cb = factory.create_checkbutton(shap_frame, text="启用SHAP可解释性分析",
                                           variable=self.enable_shap_var)
        shap_cb.pack(side='left')

        # 并行作业数
        jobs_frame = factory.create_frame(advanced_frame)
        jobs_frame.pack(fill='x', padx=10, pady=5)

        factory.create_label(jobs_frame, text="并行作业数:").pack(side='left')
        self.n_jobs_var = tk.IntVar(value=-1)
        jobs_spin = tk.Spinbox(jobs_frame, from_=-1, to=16, width=10,
                             textvariable=self.n_jobs_var)
        jobs_spin.pack(side='right')

        # 初始状态下禁用调优选项
        for widget in self.tune_budget_frame.winfo_children():
            widget.config(state='disabled')

    def _create_multi_data_section(self, parent):
        """创建多数据源集成配置区域"""
        factory = get_component_factory()

        multi_data_frame = factory.create_labelframe(parent, text="🌐 多数据源集成")
        multi_data_frame.pack(fill='x', padx=10, pady=5)

        # 启用多数据源集成
        self.enable_multi_data_var = tk.BooleanVar(value=False)
        multi_data_cb = factory.create_checkbutton(multi_data_frame, text="启用多数据源集成",
                                                 variable=self.enable_multi_data_var,
                                                 command=self._toggle_multi_data_options)
        multi_data_cb.pack(anchor='w', padx=10, pady=5)

        # 多数据源选项框架
        self.multi_data_options_frame = factory.create_frame(multi_data_frame)
        self.multi_data_options_frame.pack(fill='x', padx=10, pady=5)

        # 数据策略
        strategy_frame = factory.create_frame(self.multi_data_options_frame)
        strategy_frame.pack(fill='x', pady=5)

        factory.create_label(strategy_frame, text="数据策略:").pack(side='left')
        self.data_strategy_var = tk.StringVar(value='unified')
        strategy_combo = ttk.Combobox(strategy_frame, textvariable=self.data_strategy_var,
                                    values=['unified', 'original', 'combined'],
                                    state='readonly', width=15)
        strategy_combo.pack(side='right')

        # 特征选择
        feature_frame = factory.create_frame(self.multi_data_options_frame)
        feature_frame.pack(fill='x', pady=5)

        self.enable_feature_selection_var = tk.BooleanVar(value=True)
        feature_cb = factory.create_checkbutton(feature_frame, text="启用特征选择",
                                               variable=self.enable_feature_selection_var)
        feature_cb.pack(side='left')

        # 数据源管理按钮
        data_source_frame = factory.create_frame(self.multi_data_options_frame)
        data_source_frame.pack(fill='x', pady=5)

        manage_data_btn = factory.create_button(data_source_frame, text="管理数据源",
                                              command=self._manage_data_sources, style='small')
        manage_data_btn.pack(side='left')

        # 初始状态下禁用多数据源选项
        for widget in self.multi_data_options_frame.winfo_children():
            for child in widget.winfo_children():
                if hasattr(child, 'config'):
                    child.config(state='disabled')
    
    def _create_control_buttons_section(self, parent):
        """创建控制按钮区域"""
        factory = get_component_factory()
        
        button_frame = factory.create_frame(parent)
        button_frame.pack(fill='x', padx=10, pady=10)
        
        # 开始集成按钮
        self.ensemble_button = factory.create_button(
            button_frame, 
            text="🚀 开始集成学习", 
            command=self._start_ensemble,
            style='primary'
        )
        self.ensemble_button.pack(fill='x', pady=(0, 5))
        self.ensemble_button.config(state='disabled')
        
        # 停止集成按钮
        self.stop_button = factory.create_button(
            button_frame, 
            text="⏹️ 停止集成", 
            command=self._stop_ensemble,
            style='danger'
        )
        self.stop_button.pack(fill='x', pady=(0, 5))
        self.stop_button.config(state='disabled')
        
        # 清空结果按钮
        clear_button = factory.create_button(
            button_frame, 
            text="🗑️ 清空结果", 
            command=self.clear_results,
            style='secondary'
        )
        clear_button.pack(fill='x')

    def _create_results_panel(self, parent):
        """创建右侧结果面板"""
        factory = get_component_factory()

        # 结果面板框架
        results_frame = factory.create_frame(parent)
        results_frame.pack(fill='both', expand=True)

        # 创建标签页
        self.results_notebook = factory.create_notebook(results_frame)
        self.results_notebook.pack(fill='both', expand=True, padx=5, pady=5)

        # 集成日志标签页
        self._create_ensemble_log_tab()

        # 集成结果标签页
        self._create_ensemble_results_tab()

        # 性能比较标签页
        self._create_performance_comparison_tab()

        # 将结果面板添加到分割窗口
        parent.add(results_frame, weight=2)

        self.register_component('results_frame', results_frame)

    def _create_ensemble_log_tab(self):
        """创建集成日志标签页"""
        factory = get_component_factory()

        log_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(log_frame, text="📋 集成日志")

        # 日志文本区域
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, font=('Consolas', 9))
        log_scrollbar = ttk.Scrollbar(log_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.config(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side='left', fill='both', expand=True, padx=(5, 0), pady=5)
        log_scrollbar.pack(side='right', fill='y', padx=(0, 5), pady=5)

        self.log_text.insert('1.0', "集成学习日志将在这里显示...\n")
        self.log_text.config(state='disabled')

    def _create_ensemble_results_tab(self):
        """创建集成结果标签页"""
        factory = get_component_factory()

        results_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(results_frame, text="📊 集成结果")

        # 结果表格
        columns = ('集成方法', '准确率', 'F1分数', 'AUC', '训练时间', '基模型数量')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)

        # 设置列标题
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=120, anchor='center')

        # 滚动条
        results_scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.config(yscrollcommand=results_scrollbar.set)

        self.results_tree.pack(side='left', fill='both', expand=True, padx=(5, 0), pady=5)
        results_scrollbar.pack(side='right', fill='y', padx=(0, 5), pady=5)

    def _create_performance_comparison_tab(self):
        """创建性能比较标签页"""
        factory = get_component_factory()

        comparison_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(comparison_frame, text="🔍 性能比较")

        # 比较图表区域（占位符）
        comparison_label = factory.create_label(
            comparison_frame,
            text="性能比较图表将在集成完成后显示\n\n包括：\n• 基模型 vs 集成模型性能对比\n• ROC曲线比较\n• 特征重要性分析\n• 集成权重分析",
            style='secondary'
        )
        comparison_label.pack(expand=True)

    def _select_all_models(self):
        """全选所有模型"""
        for var in self.model_vars.values():
            var.set(True)

    def _deselect_all_models(self):
        """取消选择所有模型"""
        for var in self.model_vars.values():
            var.set(False)

    def _on_method_changed(self, event):
        """集成方法改变事件处理"""
        method = self.ensemble_method_var.get()
        # 可以根据不同的集成方法启用/禁用相关配置
        pass

    def _start_ensemble(self):
        """开始集成学习"""
        if not self.training_results:
            self.show_warning("警告", "请先完成模型训练！")
            return

        # 获取选中的模型
        selected_models = [name for name, var in self.model_vars.items() if var.get()]
        if len(selected_models) < 2:
            self.show_warning("警告", "请至少选择2个模型进行集成！")
            return

        # 更新集成配置
        self.ensemble_config.update({
            'method': self.ensemble_method_var.get(),
            'voting_type': self.voting_type_var.get(),
            'cv_folds': self.cv_folds_var.get(),
            'random_state': self.random_state_var.get()
        })

        # 更新UI状态
        self.is_training = True
        self.status_label.config(text="正在进行集成学习...")
        self.ensemble_button.config(state='disabled')
        self.stop_button.config(state='normal')

        # 清空之前的结果
        self._clear_results_display()

        # 开始集成
        self._run_ensemble(selected_models)

    def _stop_ensemble(self):
        """停止集成学习"""
        self.is_training = False
        self.status_label.config(text="集成学习已停止")
        self.ensemble_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self._log_message("集成学习已被用户停止")

    def _run_ensemble(self, selected_models):
        """运行集成学习过程"""
        def ensemble():
            try:
                self._log_message(f"开始集成学习，选中模型: {', '.join(selected_models)}")
                self._log_message(f"集成配置: {self.ensemble_config}")

                # 模拟集成学习过程
                if HAS_ALGORITHMS and self.preprocessed_data:
                    # 使用真实的集成学习算法
                    self._log_message("使用真实集成学习算法...")
                    # 这里可以调用实际的集成学习函数
                    # result = run_ensemble_pipeline(...)
                else:
                    # 模拟集成学习
                    self._log_message("模拟集成学习过程...")
                    time.sleep(3)  # 模拟训练时间

                # 模拟集成结果
                ensemble_result = {
                    'method': self.ensemble_config['method'],
                    'base_models': selected_models,
                    'metrics': {
                        'accuracy': np.random.uniform(0.85, 0.95),
                        'f1': np.random.uniform(0.82, 0.92),
                        'auc': np.random.uniform(0.88, 0.98)
                    },
                    'training_time': np.random.uniform(5, 15),
                    'base_model_count': len(selected_models)
                }

                # 在主线程中更新UI
                if self.is_training and self.main_frame:
                    self.main_frame.after(0, lambda: self._ensemble_completed(ensemble_result))

            except Exception as e:
                if self.main_frame:
                    self.main_frame.after(0, lambda: self._ensemble_failed(str(e)))

        # 在后台线程中运行集成
        ensemble_thread = threading.Thread(target=ensemble)
        ensemble_thread.daemon = True
        ensemble_thread.start()

    def _log_message(self, message):
        """添加日志消息"""
        if hasattr(self, 'log_text'):
            timestamp = time.strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            self.log_text.config(state='normal')
            self.log_text.insert('end', log_entry)
            self.log_text.see('end')
            self.log_text.config(state='disabled')

    def _clear_results_display(self):
        """清空结果显示"""
        # 清空结果表格
        if hasattr(self, 'results_tree'):
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

        # 重置进度条
        if hasattr(self, 'progress_widget'):
            self.progress_widget.set_progress(0)

    def _ensemble_completed(self, result):
        """集成学习完成处理"""
        self.ensemble_results[result['method']] = result
        self.is_training = False

        # 更新UI状态
        self.status_label.config(text=f"✅ 集成学习完成！方法: {result['method']}")
        self.ensemble_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress_widget.set_progress(100)

        # 更新结果表格
        self._update_results_table(result)

        # 记录完成信息
        metrics = result['metrics']
        self._log_message(f"集成学习完成 - "
                        f"准确率: {metrics['accuracy']:.3f}, "
                        f"F1: {metrics['f1']:.3f}, "
                        f"AUC: {metrics['auc']:.3f}")

        # 发布集成完成事件
        event_manager = get_event_manager()
        event_manager.publish(EventTypes.MODEL_TRAINED, {
            'results': {f"Ensemble_{result['method']}": result},
            'type': 'ensemble'
        })

    def _ensemble_failed(self, error_message):
        """集成学习失败处理"""
        self.is_training = False
        self.status_label.config(text=f"❌ 集成学习失败: {error_message}")
        self.ensemble_button.config(state='normal')
        self.stop_button.config(state='disabled')

        self._log_message(f"集成学习失败: {error_message}")
        self.show_error("集成学习失败", f"集成学习过程中出现错误:\n{error_message}")

    def _update_results_table(self, result):
        """更新结果表格"""
        if not hasattr(self, 'results_tree'):
            return

        metrics = result['metrics']
        values = (
            result['method'],
            f"{metrics['accuracy']:.3f}",
            f"{metrics['f1']:.3f}",
            f"{metrics['auc']:.3f}",
            f"{result['training_time']:.2f}s",
            str(result['base_model_count'])
        )

        self.results_tree.insert('', 'end', values=values)

    def _bind_events(self):
        """绑定事件"""
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.DATA_LOADED, self._on_data_loaded)
        event_manager.subscribe(EventTypes.DATA_PREPROCESSED, self._on_data_preprocessed)
        event_manager.subscribe(EventTypes.MODEL_TRAINED, self._on_model_trained)

    def _on_data_loaded(self, event_data):
        """数据加载事件处理"""
        try:
            if event_data and 'data' in event_data:
                self.current_data = event_data['data']
                self.preprocessed_data = None  # 重置预处理数据

                # 更新状态
                rows, cols = self.current_data.shape
                self._log_message(f"数据已加载: {rows} 行, {cols} 列")
                self.logger.info(f"集成学习模块已加载数据: {self.current_data.shape}")
        except Exception as e:
            self.logger.error(f"处理数据加载事件时出错: {e}")
            self._log_message(f"数据加载失败: {e}")

    def _on_data_preprocessed(self, event_data):
        """数据预处理事件处理"""
        try:
            if event_data and all(key in event_data for key in ['X_train', 'X_test', 'y_train', 'y_test']):
                # 保存预处理后的数据
                self.preprocessed_data = event_data

                train_size = event_data['X_train'].shape[0]
                test_size = event_data['X_test'].shape[0]
                feature_count = event_data['X_train'].shape[1]

                self._log_message(f"数据已预处理: 训练集 {train_size} 行, 测试集 {test_size} 行, 特征 {feature_count} 个")
                self.logger.info(f"集成学习模块已接收预处理数据")

            elif event_data and 'data' in event_data:
                # 如果只有原始数据
                self.current_data = event_data['data']
                self._log_message(f"数据已预处理: {self.current_data.shape[0]} 行, {self.current_data.shape[1]} 列")
        except Exception as e:
            self.logger.error(f"处理数据预处理事件时出错: {e}")
            self._log_message(f"数据预处理失败: {e}")

    def _on_model_trained(self, event_data):
        """模型训练完成事件处理"""
        try:
            if event_data and 'results' in event_data:
                # 检查是否是集成学习结果
                if event_data.get('type') == 'ensemble':
                    return  # 忽略集成学习结果，避免循环

                self.training_results = event_data['results']

                # 更新模型选择界面
                self._update_model_selection()

                # 更新状态
                model_count = len(self.training_results)
                self.status_label.config(text=f"✅ 已完成 {model_count} 个模型训练，可以开始集成学习")

                # 启用集成按钮
                self.ensemble_button.config(state='normal')

                self._log_message(f"接收到 {model_count} 个训练完成的模型")
                self.logger.info(f"集成学习模块已接收训练结果: {model_count} 个模型")
        except Exception as e:
            self.logger.error(f"处理模型训练事件时出错: {e}")
            self._log_message(f"处理模型训练结果失败: {e}")

    def _update_model_selection(self):
        """更新模型选择界面"""
        if not self.training_results:
            return

        # 清空现有的复选框
        for widget in self.model_checkboxes_frame.winfo_children():
            widget.destroy()

        # 创建新的复选框
        self.model_vars = {}
        for model_name, result in self.training_results.items():
            var = tk.BooleanVar()
            self.model_vars[model_name] = var

            # 获取模型性能信息
            metrics = result.get('metrics', {})
            accuracy = metrics.get('accuracy', 0)
            f1 = metrics.get('f1', 0)

            # 创建复选框，显示模型名称和性能
            cb_text = f"{model_name} (准确率: {accuracy:.3f}, F1: {f1:.3f})"
            cb = tk.Checkbutton(self.model_checkboxes_frame, text=cb_text, variable=var)
            cb.pack(anchor='w', padx=10, pady=2)

        # 启用全选/取消全选按钮
        self.select_all_btn.config(state='normal')
        self.deselect_all_btn.config(state='normal')

        # 默认选择性能较好的模型
        sorted_models = sorted(self.training_results.items(),
                             key=lambda x: x[1].get('metrics', {}).get('accuracy', 0),
                             reverse=True)

        # 选择前3个性能最好的模型
        for i, (model_name, _) in enumerate(sorted_models[:3]):
            if model_name in self.model_vars:
                self.model_vars[model_name].set(True)

    def clear_results(self):
        """清空所有结果"""
        self.ensemble_results = {}
        self.training_results = None

        # 清空结果显示
        self._clear_results_display()

        # 清空日志
        if hasattr(self, 'log_text'):
            self.log_text.config(state='normal')
            self.log_text.delete('1.0', 'end')
            self.log_text.insert('1.0', "集成学习日志将在这里显示...\n")
            self.log_text.config(state='disabled')

        # 重置模型选择
        if hasattr(self, 'model_checkboxes_frame'):
            for widget in self.model_checkboxes_frame.winfo_children():
                widget.destroy()

            factory = get_component_factory()
            self.no_models_label = factory.create_label(
                self.model_checkboxes_frame,
                text="请先完成模型训练以选择基模型",
                style='secondary'
            )
            self.no_models_label.pack(pady=10)

        # 重置状态
        if hasattr(self, 'status_label'):
            self.status_label.config(text="等待模型训练完成...")

        # 禁用按钮
        if hasattr(self, 'ensemble_button'):
            self.ensemble_button.config(state='disabled')
        if hasattr(self, 'select_all_btn'):
            self.select_all_btn.config(state='disabled')
        if hasattr(self, 'deselect_all_btn'):
            self.deselect_all_btn.config(state='disabled')

        self._log_message("所有结果已清空")

    def get_ensemble_results(self) -> Dict[str, Any]:
        """获取集成学习结果"""
        return self.ensemble_results.copy()

    def is_ensemble_active(self) -> bool:
        """检查是否正在进行集成学习"""
        return self.is_training

    def _toggle_tuning_options(self):
        """切换超参数调优选项的启用状态"""
        enabled = self.enable_tuning_var.get()
        state = 'normal' if enabled else 'disabled'

        for widget in self.tune_budget_frame.winfo_children():
            if hasattr(widget, 'config'):
                widget.config(state=state)

    def _toggle_multi_data_options(self):
        """切换多数据源选项的启用状态"""
        enabled = self.enable_multi_data_var.get()
        state = 'normal' if enabled else 'disabled'

        for widget in self.multi_data_options_frame.winfo_children():
            for child in widget.winfo_children():
                if hasattr(child, 'config'):
                    child.config(state=state)

    def _manage_data_sources(self):
        """管理数据源对话框"""
        from tkinter import messagebox

        # 这里可以实现一个完整的数据源管理对话框
        # 目前先显示一个简单的提示
        messagebox.showinfo("数据源管理", "数据源管理功能正在开发中\n\n将支持:\n• 添加多个数据源\n• 配置模型-数据源映射\n• 数据源预览和验证")

    def _start_ensemble(self):
        """开始集成学习"""
        if self.is_training:
            self.show_warning("警告", "集成学习正在进行中，请等待完成")
            return

        # 检查是否有选择的模型
        selected_models = self._get_selected_models()
        if len(selected_models) < 2:
            self.show_warning("警告", "请至少选择2个模型进行集成学习")
            return

        # 检查数据是否准备好
        if not self._is_data_ready():
            self.show_warning("警告", "请先完成数据预处理")
            return

        # 更新UI状态
        self.is_training = True
        self.ensemble_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.progress_widget.reset()

        # 清空之前的结果
        self.ensemble_results = {}
        self._clear_results_display()

        # 记录开始集成
        self._log_message(f"开始集成学习，选择的模型: {', '.join(selected_models)}")
        self.status_label.config(text="🚀 正在进行集成学习...")

        # 在后台线程中进行集成学习
        self.training_thread = threading.Thread(
            target=self._ensemble_training_thread,
            args=(selected_models,),
            daemon=True
        )
        self.training_thread.start()

    def _stop_ensemble(self):
        """停止集成学习"""
        if self.is_training:
            self.is_training = False
            self._log_message("用户停止了集成学习")
            self.status_label.config(text="⏹️ 集成学习已停止")

            # 恢复UI状态
            self.ensemble_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def _get_selected_models(self) -> List[str]:
        """获取选择的模型列表"""
        selected = []
        for model_name, var in self.model_vars.items():
            if var.get():
                selected.append(model_name)
        return selected

    def _is_data_ready(self) -> bool:
        """检查数据是否准备好"""
        if self.preprocessed_data:
            # 检查预处理数据是否完整
            required_keys = ['X_train', 'X_test', 'y_train', 'y_test']
            return all(key in self.preprocessed_data for key in required_keys)
        elif self.current_data is not None:
            # 如果有原始数据，可以进行集成（需要内部处理数据分割）
            return True
        return False

    def _ensemble_training_thread(self, selected_models: List[str]):
        """集成学习的后台线程"""
        try:
            # 准备数据
            if not self._prepare_ensemble_data():
                return

            # 检查是否启用多数据源集成
            if self.enable_multi_data_var.get() and self.data_sources:
                self._run_multi_data_ensemble(selected_models)
            else:
                self._run_single_data_ensemble(selected_models)

        except Exception as e:
            error_msg = f"集成学习过程出错: {str(e)}"
            if self.gui_updater:
                self.gui_updater.safe_log_message(self._log_message, f"❌ {error_msg}")
                self.gui_updater.safe_show_message("错误", error_msg, "error")
            self.logger.error(error_msg)
        finally:
            # 恢复UI状态
            self.is_training = False
            if self.gui_updater:
                self.gui_updater.safe_enable_widget(self.ensemble_button, True)
                self.gui_updater.safe_enable_widget(self.stop_button, False)
