"""
集成优化标签页
提供集成学习模型的优化功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Dict, List, Any, Optional

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager


class EnsembleOptimizationTab(BaseGUI):
    """集成优化标签页"""
    
    def __init__(self, parent: tk.Widget):
        """初始化集成优化标签页"""
        self.ensemble_results = {}
        self.optimization_results = {}
        self.selected_ensemble = None
        
        # 优化策略
        self.optimization_strategies = {
            'weight_optimization': '权重优化',
            'model_selection': '模型选择优化',
            'threshold_optimization': '阈值优化',
            'ensemble_pruning': '集成剪枝',
            'dynamic_selection': '动态选择优化'
        }
        
        super().__init__(parent)
        
        # 订阅集成训练完成事件
        event_manager = get_event_manager()
        event_manager.subscribe('ensemble_training_completed', self._on_ensemble_training_completed)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='main')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分割
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧控制面板
        left_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(left_frame, weight=1)
        
        # 右侧结果面板
        right_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(right_frame, weight=2)
        
        # 设置控制面板
        self._setup_control_panel(left_frame)
        
        # 设置结果面板
        self._setup_results_panel(right_frame)
        
        self.register_component('main_frame', self.main_frame)
        self.register_component('paned_window', paned_window)
    
    def _setup_control_panel(self, parent):
        """设置控制面板"""
        factory = get_component_factory()
        
        # 集成模型选择
        selection_frame = factory.create_labelframe(parent, text="集成模型选择", style='section')
        selection_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(selection_frame, text="选择集成模型:").pack(anchor=tk.W, padx=5, pady=2)
        
        self.ensemble_var = tk.StringVar()
        self.ensemble_combo = factory.create_combobox(
            selection_frame,
            textvariable=self.ensemble_var,
            state="readonly"
        )
        self.ensemble_combo.pack(fill=tk.X, padx=5, pady=5)
        self.ensemble_combo.bind('<<ComboboxSelected>>', self._on_ensemble_selected)
        
        # 优化策略选择
        strategy_frame = factory.create_labelframe(parent, text="优化策略", style='section')
        strategy_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.strategy_vars = {}
        for strategy, display_name in self.optimization_strategies.items():
            var = tk.BooleanVar(value=True)
            checkbox = factory.create_checkbox(
                strategy_frame,
                text=display_name,
                variable=var
            )
            checkbox.pack(anchor=tk.W, padx=5, pady=2)
            self.strategy_vars[strategy] = var
        
        # 优化参数
        params_frame = factory.create_labelframe(parent, text="优化参数", style='section')
        params_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 优化目标
        target_frame = factory.create_frame(params_frame)
        target_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(target_frame, text="优化目标:").pack(anchor=tk.W)
        self.optimization_target_var = tk.StringVar(value="accuracy")
        target_combo = factory.create_combobox(
            target_frame,
            textvariable=self.optimization_target_var,
            values=["accuracy", "precision", "recall", "f1_score", "auc"],
            state="readonly"
        )
        target_combo.pack(fill=tk.X, pady=(2, 0))
        
        # 优化算法
        algorithm_frame = factory.create_frame(params_frame)
        algorithm_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(algorithm_frame, text="优化算法:").pack(anchor=tk.W)
        self.optimization_algorithm_var = tk.StringVar(value="genetic")
        algorithm_combo = factory.create_combobox(
            algorithm_frame,
            textvariable=self.optimization_algorithm_var,
            values=["genetic", "particle_swarm", "simulated_annealing", "grid_search"],
            state="readonly"
        )
        algorithm_combo.pack(fill=tk.X, pady=(2, 0))
        
        # 迭代次数
        iterations_frame = factory.create_frame(params_frame)
        iterations_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(iterations_frame, text="最大迭代次数:").pack(side=tk.LEFT)
        self.max_iterations_var = tk.IntVar(value=100)
        iterations_spinbox = factory.create_spinbox(
            iterations_frame,
            from_=10,
            to=1000,
            increment=10,
            textvariable=self.max_iterations_var,
            width=8
        )
        iterations_spinbox.pack(side=tk.RIGHT)
        
        # 收敛阈值
        threshold_frame = factory.create_frame(params_frame)
        threshold_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(threshold_frame, text="收敛阈值:").pack(side=tk.LEFT)
        self.convergence_threshold_var = tk.DoubleVar(value=0.001)
        threshold_spinbox = factory.create_spinbox(
            threshold_frame,
            from_=0.0001,
            to=0.01,
            increment=0.0001,
            textvariable=self.convergence_threshold_var,
            width=8
        )
        threshold_spinbox.pack(side=tk.RIGHT)
        
        # 优化按钮
        optimize_btn = factory.create_button(
            parent,
            text="🔧 开始优化",
            command=self._start_optimization,
            style='primary'
        )
        optimize_btn.pack(fill=tk.X, padx=5, pady=10)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = factory.create_progressbar(
            parent,
            variable=self.progress_var,
            maximum=100
        )
        self.progress_bar.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # 状态标签
        self.status_label = factory.create_label(
            parent,
            text="请先训练集成模型",
            style='info'
        )
        self.status_label.pack(padx=5, pady=5)
    
    def _setup_results_panel(self, parent):
        """设置结果面板"""
        factory = get_component_factory()
        
        # 优化结果标签页
        self.results_notebook = factory.create_notebook(parent)
        self.results_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 优化历史标签页
        history_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(history_frame, text="优化历史")
        
        # 优化历史表格
        history_columns = ('迭代次数', '优化策略', '目标值', '改进幅度', '状态')
        self.history_tree = factory.create_treeview(
            history_frame,
            columns=history_columns,
            show='headings'
        )
        
        for col in history_columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=100, anchor=tk.CENTER)
        
        history_scrollbar = factory.create_scrollbar(history_frame, orient=tk.VERTICAL)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        history_scrollbar.configure(command=self.history_tree.yview)
        
        # 最优结果标签页
        best_results_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(best_results_frame, text="最优结果")
        
        # 最优结果表格
        best_columns = ('优化策略', '优化前', '优化后', '改进幅度', '置信度')
        self.best_results_tree = factory.create_treeview(
            best_results_frame,
            columns=best_columns,
            show='headings'
        )
        
        for col in best_columns:
            self.best_results_tree.heading(col, text=col)
            self.best_results_tree.column(col, width=120, anchor=tk.CENTER)
        
        best_scrollbar = factory.create_scrollbar(best_results_frame, orient=tk.VERTICAL)
        best_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.best_results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.best_results_tree.configure(yscrollcommand=best_scrollbar.set)
        best_scrollbar.configure(command=self.best_results_tree.yview)
        
        # 优化建议标签页
        suggestions_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(suggestions_frame, text="优化建议")
        
        self.suggestions_text = factory.create_text(
            suggestions_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        suggestions_scrollbar = factory.create_scrollbar(suggestions_frame, orient=tk.VERTICAL)
        suggestions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.suggestions_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.suggestions_text.configure(yscrollcommand=suggestions_scrollbar.set)
        suggestions_scrollbar.configure(command=self.suggestions_text.yview)
        
        # 参数配置标签页
        config_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(config_frame, text="最优配置")
        
        self.config_text = factory.create_text(
            config_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        config_scrollbar = factory.create_scrollbar(config_frame, orient=tk.VERTICAL)
        config_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.config_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.config_text.configure(yscrollcommand=config_scrollbar.set)
        config_scrollbar.configure(command=self.config_text.yview)
    
    def _on_ensemble_training_completed(self, event_data: Dict[str, Any]):
        """集成训练完成事件处理"""
        self.ensemble_results = event_data.get('results', {})
        
        # 更新集成模型下拉框
        ensemble_names = list(self.ensemble_results.keys())
        self.ensemble_combo['values'] = ensemble_names
        
        if ensemble_names:
            self.ensemble_combo.set(ensemble_names[0])
            self.selected_ensemble = ensemble_names[0]
            self.status_label.config(text=f"可优化 {len(ensemble_names)} 个集成模型")
        
        self.logger.info(f"接收到 {len(self.ensemble_results)} 个集成模型用于优化")
    
    def _on_ensemble_selected(self, event):
        """集成模型选择事件处理"""
        self.selected_ensemble = self.ensemble_var.get()
        self.logger.info(f"选择集成模型进行优化: {self.selected_ensemble}")
    
    def _start_optimization(self):
        """开始优化"""
        if not self.selected_ensemble or self.selected_ensemble not in self.ensemble_results:
            messagebox.showwarning("警告", "请选择有效的集成模型")
            return
        
        # 获取选中的优化策略
        selected_strategies = [
            strategy for strategy, var in self.strategy_vars.items() if var.get()
        ]
        
        if not selected_strategies:
            messagebox.showwarning("警告", "请至少选择一种优化策略")
            return
        
        self.status_label.config(text="正在优化集成模型...")
        self.progress_var.set(0)
        
        # 清空之前的结果
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        for item in self.best_results_tree.get_children():
            self.best_results_tree.delete(item)
        
        # 在后台线程中优化
        optimization_thread = threading.Thread(
            target=self._optimize_ensemble,
            args=(selected_strategies,),
            daemon=True
        )
        optimization_thread.start()
    
    def _optimize_ensemble(self, strategies: List[str]):
        """优化集成模型（后台线程）"""
        try:
            import time
            import random
            
            ensemble_data = self.ensemble_results[self.selected_ensemble]
            original_performance = ensemble_data.get('accuracy', 0.9)
            
            optimization_target = self.optimization_target_var.get()
            max_iterations = self.max_iterations_var.get()
            convergence_threshold = self.convergence_threshold_var.get()
            
            self.optimization_results = {
                'original_performance': original_performance,
                'strategies': {},
                'best_configuration': {},
                'optimization_history': []
            }
            
            total_strategies = len(strategies)
            
            for i, strategy in enumerate(strategies):
                try:
                    self.logger.info(f"开始优化策略: {strategy}")
                    
                    # 更新状态
                    strategy_display = self.optimization_strategies[strategy]
                    self._update_status(f"正在执行 {strategy_display}...")
                    
                    # 模拟优化过程
                    best_performance = original_performance
                    iterations_without_improvement = 0
                    
                    for iteration in range(max_iterations):
                        # 模拟一次优化迭代
                        time.sleep(0.1)  # 减少睡眠时间以加快演示
                        
                        # 模拟性能改进
                        if random.random() < 0.3:  # 30%概率有改进
                            improvement = random.uniform(0.001, 0.01)
                            current_performance = min(best_performance + improvement, 0.999)
                            
                            if current_performance > best_performance:
                                improvement_amount = current_performance - best_performance
                                best_performance = current_performance
                                iterations_without_improvement = 0
                                
                                # 添加到历史记录
                                history_entry = {
                                    'iteration': iteration + 1,
                                    'strategy': strategy,
                                    'performance': current_performance,
                                    'improvement': improvement_amount,
                                    'status': '改进'
                                }
                                self.optimization_results['optimization_history'].append(history_entry)
                                self._add_history_entry(history_entry)
                            else:
                                iterations_without_improvement += 1
                        else:
                            iterations_without_improvement += 1
                        
                        # 检查收敛条件
                        if iterations_without_improvement > 20:
                            break
                        
                        # 更新进度
                        strategy_progress = ((i * max_iterations + iteration + 1) / (total_strategies * max_iterations)) * 100
                        self._update_progress(strategy_progress)
                    
                    # 保存策略结果
                    improvement = best_performance - original_performance
                    self.optimization_results['strategies'][strategy] = {
                        'original': original_performance,
                        'optimized': best_performance,
                        'improvement': improvement,
                        'confidence': random.uniform(0.8, 0.95)
                    }
                    
                    # 添加到最优结果表格
                    self._add_best_result(strategy, self.optimization_results['strategies'][strategy])
                    
                    self.logger.info(f"策略 {strategy} 优化完成，改进: {improvement:.4f}")
                
                except Exception as e:
                    self.logger.error(f"优化策略 {strategy} 时出错: {e}")
            
            # 生成最优配置和建议
            self._generate_optimization_suggestions()
            self._generate_best_configuration()
            
            # 优化完成
            self._optimization_completed()
            
        except Exception as e:
            self.logger.error(f"集成优化过程出错: {e}")
            self._optimization_failed(str(e))
    
    def _add_history_entry(self, entry: Dict[str, Any]):
        """添加历史记录条目"""
        def update_history():
            strategy_display = self.optimization_strategies[entry['strategy']]
            values = (
                entry['iteration'],
                strategy_display,
                f"{entry['performance']:.4f}",
                f"+{entry['improvement']:.4f}",
                entry['status']
            )
            self.history_tree.insert('', tk.END, values=values)
            # 滚动到最新条目
            self.history_tree.see(self.history_tree.get_children()[-1])
        
        self.parent.after(0, update_history)
    
    def _add_best_result(self, strategy: str, result: Dict[str, Any]):
        """添加最优结果"""
        def update_best_results():
            strategy_display = self.optimization_strategies[strategy]
            values = (
                strategy_display,
                f"{result['original']:.4f}",
                f"{result['optimized']:.4f}",
                f"+{result['improvement']:.4f}",
                f"{result['confidence']:.2%}"
            )
            self.best_results_tree.insert('', tk.END, values=values)
        
        self.parent.after(0, update_best_results)
    
    def _generate_optimization_suggestions(self):
        """生成优化建议"""
        strategies_results = self.optimization_results['strategies']
        
        # 按改进幅度排序
        sorted_strategies = sorted(
            strategies_results.items(),
            key=lambda x: x[1]['improvement'],
            reverse=True
        )
        
        suggestions = f"""
集成优化建议
{'='*50}

优化目标: {self.optimization_target_var.get().upper()}
优化算法: {self.optimization_algorithm_var.get()}

策略效果排名:
"""
        
        for rank, (strategy, result) in enumerate(sorted_strategies, 1):
            strategy_display = self.optimization_strategies[strategy]
            suggestions += f"{rank}. {strategy_display}: +{result['improvement']:.4f} (置信度: {result['confidence']:.2%})\n"
        
        suggestions += f"""

具体建议:
"""
        
        # 根据最佳策略给出建议
        if sorted_strategies:
            best_strategy, best_result = sorted_strategies[0]
            best_strategy_display = self.optimization_strategies[best_strategy]
            
            if best_result['improvement'] > 0.01:
                suggestions += f"✅ {best_strategy_display} 效果显著，建议优先采用\n"
            elif best_result['improvement'] > 0.005:
                suggestions += f"⚠️ {best_strategy_display} 有一定效果，可以考虑采用\n"
            else:
                suggestions += f"❌ 当前优化策略效果有限，建议尝试其他方法\n"
        
        suggestions += f"""
- 如果权重优化效果好，可以进一步调整权重分配
- 如果模型选择优化有效，考虑移除贡献度低的模型
- 如果阈值优化改进明显，可以针对不同类别设置不同阈值
- 建议结合多种优化策略以获得最佳效果
- 定期重新优化以适应数据分布的变化

注意事项:
- 优化后的模型需要在独立测试集上验证
- 避免过度优化导致过拟合
- 保持模型的可解释性和稳定性
        """
        
        self._update_text_widget(self.suggestions_text, suggestions.strip())
    
    def _generate_best_configuration(self):
        """生成最优配置"""
        strategies_results = self.optimization_results['strategies']
        
        config_text = f"""
最优集成配置
{'='*50}

基础配置:
- 集成方法: {self.selected_ensemble}
- 优化目标: {self.optimization_target_var.get()}
- 优化算法: {self.optimization_algorithm_var.get()}

优化结果:
"""
        
        total_improvement = sum(result['improvement'] for result in strategies_results.values())
        original_performance = self.optimization_results['original_performance']
        final_performance = original_performance + total_improvement
        
        config_text += f"- 原始性能: {original_performance:.4f}\n"
        config_text += f"- 优化后性能: {final_performance:.4f}\n"
        config_text += f"- 总体改进: +{total_improvement:.4f} ({(total_improvement/original_performance)*100:.2f}%)\n"
        
        config_text += f"""

推荐配置参数:
"""
        
        # 根据优化结果推荐参数
        for strategy, result in strategies_results.items():
            if result['improvement'] > 0.005:  # 只显示有效的优化策略
                strategy_display = self.optimization_strategies[strategy]
                config_text += f"- {strategy_display}: 启用 (改进: +{result['improvement']:.4f})\n"
        
        config_text += f"""

部署建议:
- 使用优化后的参数重新训练集成模型
- 在生产环境中监控模型性能
- 定期评估和更新优化策略
- 保存当前最优配置作为基线
        """
        
        self._update_text_widget(self.config_text, config_text.strip())
    
    def _update_status(self, message: str):
        """更新状态信息"""
        def update():
            self.status_label.config(text=message)
        
        self.parent.after(0, update)
    
    def _update_progress(self, value: float):
        """更新进度条"""
        def update():
            self.progress_var.set(value)
        
        self.parent.after(0, update)
    
    def _optimization_completed(self):
        """优化完成处理"""
        def complete():
            self.status_label.config(text="集成优化完成！")
            self.progress_var.set(100)
            
            # 发布集成优化完成事件
            event_manager = get_event_manager()
            event_manager.publish('ensemble_optimization_completed', {
                'results': self.optimization_results,
                'ensemble_model': self.selected_ensemble
            })
        
        self.parent.after(0, complete)
    
    def _optimization_failed(self, error_message: str):
        """优化失败处理"""
        def fail():
            self.status_label.config(text=f"集成优化失败: {error_message}")
            self.progress_var.set(0)
            messagebox.showerror("优化失败", f"集成优化过程中出现错误:\n{error_message}")
        
        self.parent.after(0, fail)
    
    def _update_text_widget(self, text_widget, content: str):
        """更新文本控件内容"""
        text_widget.config(state=tk.NORMAL)
        text_widget.delete(1.0, tk.END)
        text_widget.insert(1.0, content)
        text_widget.config(state=tk.DISABLED)
    
    def get_optimization_results(self) -> Dict[str, Any]:
        """获取优化结果"""
        return self.optimization_results.copy()
