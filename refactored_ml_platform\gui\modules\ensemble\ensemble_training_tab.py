"""
集成训练标签页
提供集成学习模型的训练功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Dict, List, Any, Optional

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager


class EnsembleTrainingTab(BaseGUI):
    """集成训练标签页"""
    
    def __init__(self, parent: tk.Widget):
        """初始化集成训练标签页"""
        self.trained_models = {}
        self.selected_base_models = []
        self.ensemble_results = {}
        self.current_ensemble_method = "voting"
        
        # 集成方法配置
        self.ensemble_methods = {
            "voting": "投票集成",
            "bagging": "Bagging集成",
            "boosting": "Boosting集成",
            "stacking": "Stacking集成"
        }
        
        super().__init__(parent)
        
        # 订阅模型训练完成事件
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.MODEL_TRAINED, self._on_models_trained)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='main')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分割
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧控制面板
        left_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(left_frame, weight=1)
        
        # 右侧结果面板
        right_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(right_frame, weight=2)
        
        # 设置控制面板
        self._setup_control_panel(left_frame)
        
        # 设置结果面板
        self._setup_results_panel(right_frame)
        
        self.register_component('main_frame', self.main_frame)
        self.register_component('paned_window', paned_window)
    
    def _setup_control_panel(self, parent):
        """设置控制面板"""
        factory = get_component_factory()
        
        # 基础模型选择
        base_models_frame = factory.create_labelframe(parent, text="基础模型选择", style='section')
        base_models_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 基础模型复选框
        self.base_model_vars = {}
        self.base_models_scroll_frame = factory.create_scrollable_frame(base_models_frame)
        self.base_models_scroll_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 集成方法选择
        method_frame = factory.create_labelframe(parent, text="集成方法", style='section')
        method_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.ensemble_method_var = tk.StringVar(value="voting")
        for method, display_name in self.ensemble_methods.items():
            radio = factory.create_radiobutton(
                method_frame,
                text=display_name,
                variable=self.ensemble_method_var,
                value=method,
                command=self._on_ensemble_method_changed
            )
            radio.pack(anchor=tk.W, padx=5, pady=2)
        
        # 训练按钮
        self.train_button = factory.create_button(
            parent,
            text="🚀 开始集成训练",
            command=self._start_ensemble_training,
            style='primary'
        )
        self.train_button.pack(fill=tk.X, padx=5, pady=10)
        self.train_button.config(state=tk.DISABLED)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = factory.create_progressbar(
            parent,
            variable=self.progress_var,
            maximum=100
        )
        self.progress_bar.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # 状态标签
        self.status_label = factory.create_label(
            parent,
            text="请选择基础模型进行集成训练",
            style='info'
        )
        self.status_label.pack(padx=5, pady=5)
    
    def _setup_results_panel(self, parent):
        """设置结果面板"""
        factory = get_component_factory()
        
        # 结果标签页
        self.results_notebook = factory.create_notebook(parent)
        self.results_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 集成结果标签页
        results_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(results_frame, text="集成结果")
        
        # 结果表格
        columns = ('集成方法', '准确率', '精确率', '召回率', 'F1分数', 'AUC')
        self.results_tree = factory.create_treeview(
            results_frame,
            columns=columns,
            show='headings'
        )
        
        # 设置列
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=100, anchor=tk.CENTER)
        
        # 滚动条
        results_scrollbar = factory.create_scrollbar(results_frame, orient=tk.VERTICAL)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.results_tree.configure(yscrollcommand=results_scrollbar.set)
        results_scrollbar.configure(command=self.results_tree.yview)
        
        # 详细信息标签页
        details_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(details_frame, text="详细信息")
        
        # 详细信息文本框
        self.details_text = factory.create_text(
            details_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        details_scrollbar = factory.create_scrollbar(details_frame, orient=tk.VERTICAL)
        details_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.details_text.configure(yscrollcommand=details_scrollbar.set)
        details_scrollbar.configure(command=self.details_text.yview)
        
        # 绑定选择事件
        self.results_tree.bind('<<TreeviewSelect>>', self._on_result_select)
    
    def _on_models_trained(self, event_data: Dict[str, Any]):
        """模型训练完成事件处理"""
        self.trained_models = event_data.get('results', {})
        
        # 清空现有的基础模型选择控件
        for widget in self.base_models_scroll_frame.scrollable_frame.winfo_children():
            widget.destroy()
        self.base_model_vars = {}
        
        # 创建新的基础模型复选框
        factory = get_component_factory()
        for model_name in self.trained_models.keys():
            var = tk.BooleanVar()
            display_name = self._get_display_name(model_name)
            checkbox = factory.create_checkbox(
                self.base_models_scroll_frame.scrollable_frame,
                text=display_name,
                variable=var,
                command=self._on_base_model_selection_changed
            )
            checkbox.pack(anchor=tk.W, pady=2)
            self.base_model_vars[model_name] = var
        
        self.logger.info(f"接收到 {len(self.trained_models)} 个基础模型")
        self.status_label.config(text=f"可用基础模型: {len(self.trained_models)} 个")
    
    def _get_display_name(self, model_name: str) -> str:
        """获取模型显示名称"""
        display_names = {
            'DecisionTree': '决策树',
            'RandomForest': '随机森林',
            'XGBoost': 'XGBoost',
            'LightGBM': 'LightGBM',
            'CatBoost': 'CatBoost',
            'Logistic': '逻辑回归',
            'SVM': '支持向量机',
            'KNN': 'K近邻',
            'NaiveBayes': '朴素贝叶斯',
            'NeuralNet': '神经网络'
        }
        return display_names.get(model_name, model_name)
    
    def _on_base_model_selection_changed(self):
        """基础模型选择变化处理"""
        self.selected_base_models = [
            model for model, var in self.base_model_vars.items() if var.get()
        ]
        
        # 更新训练按钮状态
        if len(self.selected_base_models) >= 2:
            self.train_button.config(state=tk.NORMAL)
            self.status_label.config(text=f"已选择 {len(self.selected_base_models)} 个基础模型")
        else:
            self.train_button.config(state=tk.DISABLED)
            self.status_label.config(text="请至少选择2个基础模型进行集成")
    
    def _on_ensemble_method_changed(self):
        """集成方法变化处理"""
        self.current_ensemble_method = self.ensemble_method_var.get()
        self.logger.info(f"集成方法变更为: {self.ensemble_methods[self.current_ensemble_method]}")
    
    def _start_ensemble_training(self):
        """开始集成训练"""
        if len(self.selected_base_models) < 2:
            messagebox.showwarning("警告", "请至少选择2个基础模型进行集成")
            return
        
        # 禁用训练按钮
        self.train_button.config(state=tk.DISABLED)
        self.status_label.config(text="正在进行集成训练...")
        self.progress_var.set(0)
        
        # 在后台线程中训练
        training_thread = threading.Thread(
            target=self._train_ensemble_models,
            daemon=True
        )
        training_thread.start()
    
    def _train_ensemble_models(self):
        """训练集成模型（后台线程）"""
        try:
            import time
            import random
            
            # 模拟集成训练过程
            time.sleep(random.uniform(2, 5))
            
            # 模拟训练结果
            result = {
                'method': self.current_ensemble_method,
                'base_models': self.selected_base_models.copy(),
                'accuracy': random.uniform(0.85, 0.98),
                'precision': random.uniform(0.85, 0.98),
                'recall': random.uniform(0.85, 0.98),
                'f1_score': random.uniform(0.85, 0.98),
                'auc': random.uniform(0.85, 0.98)
            }
            
            self.ensemble_results[self.current_ensemble_method] = result
            
            # 更新结果显示
            self._add_result_to_tree(self.current_ensemble_method, result)
            
            # 训练完成
            self._training_completed()
            
        except Exception as e:
            self.logger.error(f"集成训练过程出错: {e}")
            self._training_failed(str(e))
    
    def _add_result_to_tree(self, method: str, result: Dict[str, Any]):
        """添加结果到树形视图"""
        def update_tree():
            method_display = self.ensemble_methods.get(method, method)
            values = (
                method_display,
                f"{result['accuracy']:.4f}",
                f"{result['precision']:.4f}",
                f"{result['recall']:.4f}",
                f"{result['f1_score']:.4f}",
                f"{result['auc']:.4f}"
            )
            self.results_tree.insert('', tk.END, values=values, tags=(method,))
        
        self.parent.after(0, update_tree)
    
    def _training_completed(self):
        """训练完成处理"""
        def complete():
            self.train_button.config(state=tk.NORMAL)
            self.status_label.config(text="集成训练完成！")
            self.progress_var.set(100)
            
            # 发布集成训练完成事件
            event_manager = get_event_manager()
            event_manager.publish('ensemble_training_completed', {
                'results': self.ensemble_results,
                'base_models': self.selected_base_models
            })
        
        self.parent.after(0, complete)
    
    def _training_failed(self, error_message: str):
        """训练失败处理"""
        def fail():
            self.train_button.config(state=tk.NORMAL)
            self.status_label.config(text=f"集成训练失败: {error_message}")
            self.progress_var.set(0)
            messagebox.showerror("训练失败", f"集成训练过程中出现错误:\n{error_message}")
        
        self.parent.after(0, fail)
    
    def _on_result_select(self, event):
        """结果选择事件处理"""
        selection = self.results_tree.selection()
        if not selection:
            return
        
        item = selection[0]
        tags = self.results_tree.item(item, 'tags')
        if not tags:
            return
        
        method = tags[0]
        if method in self.ensemble_results:
            self._show_ensemble_details(method, self.ensemble_results[method])
    
    def _show_ensemble_details(self, method: str, result: Dict[str, Any]):
        """显示集成详细信息"""
        method_display = self.ensemble_methods.get(method, method)
        
        details = f"""
集成方法: {method_display}
{'='*50}

基础模型:
{', '.join([self._get_display_name(model) for model in result['base_models']])}

性能指标:
- 准确率: {result['accuracy']:.4f}
- 精确率: {result['precision']:.4f}
- 召回率: {result['recall']:.4f}
- F1分数: {result['f1_score']:.4f}
- AUC: {result['auc']:.4f}

集成优势:
- 通过结合多个基础模型，提高了预测的稳定性和准确性
- 减少了单一模型的过拟合风险
- 利用了不同模型的互补优势
        """
        
        self._update_details(details.strip())
    
    def _update_details(self, text: str):
        """更新详细信息"""
        self.details_text.config(state=tk.NORMAL)
        self.details_text.delete(1.0, tk.END)
        self.details_text.insert(1.0, text)
        self.details_text.config(state=tk.DISABLED)
    
    def _clear_details(self):
        """清空详细信息"""
        self._update_details("选择一个集成方法查看详细信息...")
    
    def get_ensemble_results(self) -> Dict[str, Any]:
        """获取集成结果"""
        return self.ensemble_results.copy()
