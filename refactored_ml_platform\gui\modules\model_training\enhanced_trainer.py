#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的模型训练器
集成算法模块，提供完整的模型训练功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
import numpy as np
import threading
import time
from typing import Dict, List, Optional, Any

try:
    from ...core.event_manager import get_event_manager
    from core.model_manager import get_model_manager
    from utils.error_handler import get_error_handler
    from algorithms import (
        MODEL_TRAINERS, MODEL_NAMES, MODEL_DISPLAY_NAMES,
        tune_model, DataPreprocessor, load_and_preprocess_data
    )
    HAS_ALGORITHMS = True
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))

    from gui.core.event_manager import get_event_manager
    from core.model_manager import get_model_manager
    from utils.error_handler import get_error_handler

    try:
        from algorithms import (
            MODEL_TRAINERS, MODEL_NAMES, MODEL_DISPLAY_NAMES,
            tune_model, DataPreprocessor, load_and_preprocess_data
        )
        HAS_ALGORITHMS = True
    except ImportError:
        MODEL_TRAINERS = {}
        MODEL_NAMES = []
        MODEL_DISPLAY_NAMES = {}
        HAS_ALGORITHMS = False


class EnhancedModelTrainer:
    """增强的模型训练器"""
    
    def __init__(self, parent):
        """初始化增强模型训练器"""
        self.parent = parent
        self.event_manager = get_event_manager()
        self.model_manager = get_model_manager()
        self.error_handler = get_error_handler()
        
        # 数据存储
        self.current_data = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.preprocessor = None
        
        # 训练结果
        self.training_results = {}
        self.training_history = []
        
        # GUI组件
        self.frame = None
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="就绪")
        
        # 控制变量
        self.selected_models = {}
        self.training_params = {}
        self.enable_tuning = tk.BooleanVar(value=False)
        self.enable_preprocessing = tk.BooleanVar(value=True)
        self.test_size_var = tk.DoubleVar(value=0.2)
        self.cv_folds_var = tk.IntVar(value=5)
        
        # 创建界面
        self._create_interface()
        
        # 注册事件监听
        self._register_events()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.frame = ttk.Frame(self.parent)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个选项卡
        self._create_model_selection_tab()
        self._create_training_config_tab()
        self._create_training_monitor_tab()
        self._create_results_tab()
    
    def _create_model_selection_tab(self):
        """创建模型选择选项卡"""
        model_tab = ttk.Frame(self.notebook)
        self.notebook.add(model_tab, text="🤖 模型选择")
        
        # 可用模型列表
        models_frame = ttk.LabelFrame(model_tab, text="可用模型")
        models_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建模型选择复选框
        self.model_checkboxes = {}
        
        if HAS_ALGORITHMS and MODEL_NAMES:
            available_models = MODEL_NAMES
        else:
            available_models = self.model_manager.get_available_models()
        
        # 按行排列模型选择框
        for i, model_name in enumerate(available_models):
            row = i // 3
            col = i % 3
            
            var = tk.BooleanVar()
            self.selected_models[model_name] = var
            
            display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name) if HAS_ALGORITHMS else model_name
            
            checkbox = ttk.Checkbutton(
                models_frame, 
                text=f"{display_name} ({model_name})",
                variable=var
            )
            checkbox.grid(row=row, column=col, sticky=tk.W, padx=10, pady=5)
            
            self.model_checkboxes[model_name] = checkbox
        
        # 快速选择按钮
        quick_select_frame = ttk.Frame(models_frame)
        quick_select_frame.grid(row=len(available_models)//3 + 1, column=0, columnspan=3, pady=10)
        
        ttk.Button(quick_select_frame, text="全选", command=self._select_all_models).pack(side=tk.LEFT, padx=5)
        ttk.Button(quick_select_frame, text="全不选", command=self._deselect_all_models).pack(side=tk.LEFT, padx=5)
        ttk.Button(quick_select_frame, text="推荐模型", command=self._select_recommended_models).pack(side=tk.LEFT, padx=5)
    
    def _create_training_config_tab(self):
        """创建训练配置选项卡"""
        config_tab = ttk.Frame(self.notebook)
        self.notebook.add(config_tab, text="⚙️ 训练配置")
        
        # 数据配置
        data_config_frame = ttk.LabelFrame(config_tab, text="数据配置")
        data_config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 测试集比例
        test_size_frame = ttk.Frame(data_config_frame)
        test_size_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(test_size_frame, text="测试集比例:").pack(side=tk.LEFT)
        test_size_scale = ttk.Scale(
            test_size_frame, from_=0.1, to=0.5, 
            variable=self.test_size_var, orient=tk.HORIZONTAL
        )
        test_size_scale.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True)
        ttk.Label(test_size_frame, textvariable=self.test_size_var).pack(side=tk.RIGHT)
        
        # 交叉验证折数
        cv_frame = ttk.Frame(data_config_frame)
        cv_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(cv_frame, text="交叉验证折数:").pack(side=tk.LEFT)
        cv_spinbox = tk.Spinbox(cv_frame, textvariable=self.cv_folds_var, from_=3, to=10, width=10)
        cv_spinbox.pack(side=tk.LEFT, padx=10)
        
        # 预处理选项
        preprocessing_frame = ttk.LabelFrame(config_tab, text="预处理选项")
        preprocessing_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Checkbutton(
            preprocessing_frame, 
            text="启用数据预处理（标准化、特征选择等）",
            variable=self.enable_preprocessing
        ).pack(anchor=tk.W, padx=10, pady=5)
        
        # 超参数调优选项
        tuning_frame = ttk.LabelFrame(config_tab, text="超参数调优")
        tuning_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Checkbutton(
            tuning_frame, 
            text="启用超参数调优（将显著增加训练时间）",
            variable=self.enable_tuning
        ).pack(anchor=tk.W, padx=10, pady=5)
        
        # 调优参数
        tuning_params_frame = ttk.Frame(tuning_frame)
        tuning_params_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(tuning_params_frame, text="调优试验次数:").pack(side=tk.LEFT)
        self.tuning_trials_var = tk.IntVar(value=50)
        tuning_trials_spinbox = tk.Spinbox(
            tuning_params_frame, textvariable=self.tuning_trials_var, 
            from_=10, to=200, width=10
        )
        tuning_trials_spinbox.pack(side=tk.LEFT, padx=10)
        
        # 开始训练按钮
        train_button_frame = ttk.Frame(config_tab)
        train_button_frame.pack(fill=tk.X, padx=10, pady=20)
        
        self.train_button = ttk.Button(
            train_button_frame, 
            text="🚀 开始训练", 
            command=self._start_training,
            style="Accent.TButton"
        )
        self.train_button.pack(side=tk.LEFT, padx=10)
        
        self.stop_button = ttk.Button(
            train_button_frame, 
            text="⏹️ 停止训练", 
            command=self._stop_training,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=10)
    
    def _create_training_monitor_tab(self):
        """创建训练监控选项卡"""
        monitor_tab = ttk.Frame(self.notebook)
        self.notebook.add(monitor_tab, text="📊 训练监控")
        
        # 训练状态
        status_frame = ttk.LabelFrame(monitor_tab, text="训练状态")
        status_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 状态标签
        ttk.Label(status_frame, text="当前状态:").pack(side=tk.LEFT, padx=10, pady=10)
        status_label = ttk.Label(status_frame, textvariable=self.status_var, foreground="blue")
        status_label.pack(side=tk.LEFT, padx=10, pady=10)
        
        # 进度条
        progress_frame = ttk.LabelFrame(monitor_tab, text="训练进度")
        progress_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.progress_bar = ttk.Progressbar(
            progress_frame, 
            variable=self.progress_var, 
            mode='determinate',
            length=400
        )
        self.progress_bar.pack(padx=10, pady=10)
        
        # 训练日志
        log_frame = ttk.LabelFrame(monitor_tab, text="训练日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.log_text = tk.Text(log_frame, height=15, wrap=tk.WORD, font=('Consolas', 9))
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 清空日志按钮
        ttk.Button(log_frame, text="清空日志", command=self._clear_log).pack(pady=5)
    
    def _create_results_tab(self):
        """创建结果选项卡"""
        results_tab = ttk.Frame(self.notebook)
        self.notebook.add(results_tab, text="📈 训练结果")
        
        # 结果摘要
        summary_frame = ttk.LabelFrame(results_tab, text="结果摘要")
        summary_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 创建结果表格
        columns = ('模型', '准确率', 'AUC', 'F1分数', '训练时间')
        self.results_tree = ttk.Treeview(summary_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=100, anchor=tk.CENTER)
        
        # 滚动条
        results_scrollbar = ttk.Scrollbar(summary_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=results_scrollbar.set)
        
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 操作按钮
        buttons_frame = ttk.Frame(results_tab)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="📊 查看详细结果", command=self._view_detailed_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="💾 保存结果", command=self._save_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="📈 生成报告", command=self._generate_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="🔄 重新训练", command=self._retrain_selected).pack(side=tk.LEFT, padx=5)
    
    def _register_events(self):
        """注册事件监听"""
        self.event_manager.subscribe('data_loaded', self._on_data_loaded)
        self.event_manager.subscribe('data_preprocessed', self._on_data_preprocessed)
    
    def _on_data_loaded(self, event_data):
        """数据加载完成事件处理"""
        self.current_data = event_data
        self._log_message("数据加载完成，可以开始训练")
        self.status_var.set("数据已加载，等待训练")
    
    def _on_data_preprocessed(self, event_data):
        """数据预处理完成事件处理"""
        self.X_train = event_data.get('X_train')
        self.X_test = event_data.get('X_test')
        self.y_train = event_data.get('y_train')
        self.y_test = event_data.get('y_test')
        self.preprocessor = event_data.get('preprocessor')
        
        self._log_message("数据预处理完成")
        self.status_var.set("数据预处理完成，可以开始训练")
    
    def _select_all_models(self):
        """选择所有模型"""
        for var in self.selected_models.values():
            var.set(True)
    
    def _deselect_all_models(self):
        """取消选择所有模型"""
        for var in self.selected_models.values():
            var.set(False)
    
    def _select_recommended_models(self):
        """选择推荐模型"""
        recommended = ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
        
        # 先取消所有选择
        self._deselect_all_models()
        
        # 选择推荐模型
        for model_name in recommended:
            if model_name in self.selected_models:
                self.selected_models[model_name].set(True)
    
    def _start_training(self):
        """开始训练"""
        # 检查是否有选择的模型
        selected = [name for name, var in self.selected_models.items() if var.get()]
        
        if not selected:
            messagebox.showwarning("警告", "请至少选择一个模型进行训练")
            return
        
        if self.current_data is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
        
        # 禁用训练按钮，启用停止按钮
        self.train_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        # 清空之前的结果
        self.training_results.clear()
        self._clear_results_tree()
        
        # 在后台线程中进行训练
        self.training_thread = threading.Thread(
            target=self._perform_training, 
            args=(selected,), 
            daemon=True
        )
        self.training_thread.start()
    
    def _stop_training(self):
        """停止训练"""
        self.status_var.set("正在停止训练...")
        # 这里可以添加停止训练的逻辑
        self._training_completed()
    
    def _perform_training(self, selected_models: List[str]):
        """执行训练"""
        try:
            self.status_var.set("开始训练...")
            self.progress_var.set(0)
            
            total_models = len(selected_models)
            
            for i, model_name in enumerate(selected_models):
                self.status_var.set(f"正在训练 {model_name}...")
                self._log_message(f"开始训练模型: {model_name}")
                
                try:
                    # 训练模型
                    if HAS_ALGORITHMS:
                        # 使用算法模块训练
                        result = self.model_manager.train_model(
                            model_name, self.X_train, self.y_train, 
                            self.X_test, self.y_test
                        )
                    else:
                        # 使用原有方法训练
                        result = self.model_manager.train_model(
                            model_name, self.X_train, self.y_train, 
                            self.X_test, self.y_test
                        )
                    
                    # 保存结果
                    self.training_results[model_name] = result
                    
                    # 更新结果表格
                    self._update_results_tree(model_name, result)
                    
                    # 记录日志
                    accuracy = result.get('accuracy', 0)
                    training_time = result.get('training_time', 0)
                    self._log_message(f"模型 {model_name} 训练完成 - 准确率: {accuracy:.4f}, 用时: {training_time:.2f}秒")
                    
                    # 发布事件
                    self.event_manager.publish('model_trained', {
                        'model_name': model_name,
                        'model': result.get('model'),
                        'result': result
                    })
                    
                except Exception as e:
                    self._log_message(f"模型 {model_name} 训练失败: {e}")
                    continue
                
                # 更新进度
                progress = (i + 1) / total_models * 100
                self.progress_var.set(progress)
            
            self.status_var.set("训练完成")
            self._log_message("所有模型训练完成")
            
        except Exception as e:
            self.status_var.set("训练失败")
            self._log_message(f"训练过程出错: {e}")
        
        finally:
            self._training_completed()
    
    def _training_completed(self):
        """训练完成后的清理工作"""
        # 恢复按钮状态
        self.train_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        # 重置进度条
        self.progress_var.set(0)
    
    def _update_results_tree(self, model_name: str, result: Dict[str, Any]):
        """更新结果表格"""
        accuracy = result.get('accuracy', 0)
        auc = result.get('auc', 0)
        f1 = result.get('f1_score', 0)
        training_time = result.get('training_time', 0)
        
        self.results_tree.insert('', 'end', values=(
            model_name,
            f"{accuracy:.4f}",
            f"{auc:.4f}",
            f"{f1:.4f}",
            f"{training_time:.2f}s"
        ))
    
    def _clear_results_tree(self):
        """清空结果表格"""
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
    
    def _log_message(self, message: str):
        """记录日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
    
    def _clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def _view_detailed_results(self):
        """查看详细结果"""
        selected_item = self.results_tree.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请选择一个模型查看详细结果")
            return
        
        # 获取选中的模型名称
        item = self.results_tree.item(selected_item[0])
        model_name = item['values'][0]
        
        if model_name in self.training_results:
            result = self.training_results[model_name]
            self._show_detailed_result_window(model_name, result)
    
    def _show_detailed_result_window(self, model_name: str, result: Dict[str, Any]):
        """显示详细结果窗口"""
        detail_window = tk.Toplevel(self.parent)
        detail_window.title(f"详细结果 - {model_name}")
        detail_window.geometry("600x400")
        
        # 创建文本框显示详细信息
        text_widget = tk.Text(detail_window, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(detail_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        # 格式化结果信息
        detail_text = f"模型: {model_name}\n"
        detail_text += "=" * 50 + "\n\n"
        
        for key, value in result.items():
            if key != 'model':  # 跳过模型对象
                detail_text += f"{key}: {value}\n"
        
        text_widget.insert(1.0, detail_text)
        text_widget.config(state=tk.DISABLED)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def _save_results(self):
        """保存结果"""
        if not self.training_results:
            messagebox.showwarning("警告", "没有可保存的结果")
            return
        
        # 这里可以添加保存结果的逻辑
        messagebox.showinfo("提示", "结果保存功能待实现")
    
    def _generate_report(self):
        """生成报告"""
        if not self.training_results:
            messagebox.showwarning("警告", "没有可生成报告的结果")
            return
        
        # 这里可以添加生成报告的逻辑
        messagebox.showinfo("提示", "报告生成功能待实现")
    
    def _retrain_selected(self):
        """重新训练选中的模型"""
        selected_item = self.results_tree.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请选择要重新训练的模型")
            return
        
        # 获取选中的模型名称
        item = self.results_tree.item(selected_item[0])
        model_name = item['values'][0]
        
        # 重新训练该模型
        self._start_single_model_training(model_name)
    
    def _start_single_model_training(self, model_name: str):
        """训练单个模型"""
        # 在后台线程中训练单个模型
        threading.Thread(
            target=self._perform_training, 
            args=([model_name],), 
            daemon=True
        ).start()
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
