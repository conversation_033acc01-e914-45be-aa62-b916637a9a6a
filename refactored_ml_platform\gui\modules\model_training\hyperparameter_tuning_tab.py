"""
超参数调优标签页
提供模型超参数优化功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Dict, List, Any, Optional
import json

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager


class HyperparameterTuningTab(BaseGUI):
    """超参数调优标签页"""
    
    def __init__(self, parent: tk.Widget):
        """初始化超参数调优标签页"""
        self.trained_models = {}
        self.tuning_results = {}
        self.selected_model = None
        
        # 预定义的超参数配置
        self.hyperparameter_configs = {
            'RandomForest': {
                'n_estimators': {'type': 'int', 'range': [50, 200], 'default': 100},
                'max_depth': {'type': 'int', 'range': [3, 20], 'default': 10},
                'min_samples_split': {'type': 'int', 'range': [2, 20], 'default': 2},
                'min_samples_leaf': {'type': 'int', 'range': [1, 10], 'default': 1}
            },
            'XGBoost': {
                'n_estimators': {'type': 'int', 'range': [50, 200], 'default': 100},
                'max_depth': {'type': 'int', 'range': [3, 10], 'default': 6},
                'learning_rate': {'type': 'float', 'range': [0.01, 0.3], 'default': 0.1},
                'subsample': {'type': 'float', 'range': [0.6, 1.0], 'default': 1.0}
            },
            'SVM': {
                'C': {'type': 'float', 'range': [0.1, 100], 'default': 1.0},
                'gamma': {'type': 'choice', 'choices': ['scale', 'auto'], 'default': 'scale'},
                'kernel': {'type': 'choice', 'choices': ['rbf', 'linear', 'poly'], 'default': 'rbf'}
            },
            'KNN': {
                'n_neighbors': {'type': 'int', 'range': [3, 20], 'default': 5},
                'weights': {'type': 'choice', 'choices': ['uniform', 'distance'], 'default': 'uniform'},
                'algorithm': {'type': 'choice', 'choices': ['auto', 'ball_tree', 'kd_tree', 'brute'], 'default': 'auto'}
            }
        }
        
        super().__init__(parent)
        
        # 订阅模型训练完成事件
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.MODEL_TRAINED, self._on_models_trained)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='main')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建上下分割
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.VERTICAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 上方控制面板
        top_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(top_frame, weight=1)
        
        # 下方结果面板
        bottom_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(bottom_frame, weight=2)
        
        # 设置控制面板
        self._setup_control_panel(top_frame)
        
        # 设置结果面板
        self._setup_results_panel(bottom_frame)
        
        self.register_component('main_frame', self.main_frame)
        self.register_component('paned_window', paned_window)
    
    def _setup_control_panel(self, parent):
        """设置控制面板"""
        factory = get_component_factory()
        
        # 创建左右分割
        control_paned = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        control_paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧模型选择
        left_frame = factory.create_frame(control_paned)
        control_paned.add(left_frame, weight=1)
        
        # 右侧参数配置
        right_frame = factory.create_frame(control_paned)
        control_paned.add(right_frame, weight=2)
        
        # 模型选择
        model_frame = factory.create_labelframe(left_frame, text="模型选择", style='section')
        model_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.model_listbox = tk.Listbox(model_frame, height=8)
        self.model_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.model_listbox.bind('<<ListboxSelect>>', self._on_model_select)
        
        # 调优设置
        settings_frame = factory.create_labelframe(left_frame, text="调优设置", style='section')
        settings_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 搜索方法
        method_frame = factory.create_frame(settings_frame)
        method_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(method_frame, text="搜索方法:").pack(anchor=tk.W)
        self.search_method_var = tk.StringVar(value="grid")
        method_combo = factory.create_combobox(
            method_frame,
            textvariable=self.search_method_var,
            values=["grid", "random", "bayesian"],
            state="readonly"
        )
        method_combo.pack(fill=tk.X, pady=(2, 0))
        
        # 交叉验证折数
        cv_frame = factory.create_frame(settings_frame)
        cv_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(cv_frame, text="交叉验证折数:").pack(side=tk.LEFT)
        self.cv_folds_var = tk.IntVar(value=5)
        cv_spinbox = factory.create_spinbox(
            cv_frame,
            from_=3,
            to=10,
            increment=1,
            textvariable=self.cv_folds_var,
            width=8
        )
        cv_spinbox.pack(side=tk.RIGHT)
        
        # 搜索次数（随机搜索）
        iter_frame = factory.create_frame(settings_frame)
        iter_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(iter_frame, text="搜索次数:").pack(side=tk.LEFT)
        self.n_iter_var = tk.IntVar(value=50)
        iter_spinbox = factory.create_spinbox(
            iter_frame,
            from_=10,
            to=200,
            increment=10,
            textvariable=self.n_iter_var,
            width=8
        )
        iter_spinbox.pack(side=tk.RIGHT)
        
        # 开始调优按钮
        self.tune_button = factory.create_button(
            settings_frame,
            text="🔧 开始调优",
            command=self._start_tuning,
            style='primary'
        )
        self.tune_button.pack(fill=tk.X, padx=5, pady=10)
        self.tune_button.config(state=tk.DISABLED)
        
        # 超参数配置面板
        params_frame = factory.create_labelframe(right_frame, text="超参数配置", style='section')
        params_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建滚动框架
        self.params_scroll_frame = factory.create_scrollable_frame(params_frame)
        self.params_scroll_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 参数控件字典
        self.param_widgets = {}
        
        # 状态和进度
        status_frame = factory.create_frame(right_frame)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = factory.create_progressbar(
            status_frame,
            variable=self.progress_var,
            maximum=100
        )
        self.progress_bar.pack(fill=tk.X)
        
        self.status_label = factory.create_label(
            status_frame,
            text="请选择模型进行超参数调优",
            style='info'
        )
        self.status_label.pack(pady=(5, 0))
    
    def _setup_results_panel(self, parent):
        """设置结果面板"""
        factory = get_component_factory()
        
        # 结果标签页
        self.results_notebook = factory.create_notebook(parent)
        self.results_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 最佳参数标签页
        best_params_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(best_params_frame, text="最佳参数")
        
        self.best_params_text = factory.create_text(
            best_params_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        best_scrollbar = factory.create_scrollbar(best_params_frame, orient=tk.VERTICAL)
        best_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.best_params_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.best_params_text.configure(yscrollcommand=best_scrollbar.set)
        best_scrollbar.configure(command=self.best_params_text.yview)
        
        # 调优历史标签页
        history_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(history_frame, text="调优历史")
        
        # 历史结果表格
        columns = ('试验', '参数', '得分', '排名')
        self.history_tree = factory.create_treeview(
            history_frame,
            columns=columns,
            show='headings'
        )
        
        for col in columns:
            self.history_tree.heading(col, text=col)
            if col == '参数':
                self.history_tree.column(col, width=300)
            else:
                self.history_tree.column(col, width=80, anchor=tk.CENTER)
        
        history_scrollbar = factory.create_scrollbar(history_frame, orient=tk.VERTICAL)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        history_scrollbar.configure(command=self.history_tree.yview)
    
    def _on_models_trained(self, event_data: Dict[str, Any]):
        """模型训练完成事件处理"""
        self.trained_models = event_data.get('results', {})
        
        # 更新模型列表
        self.model_listbox.delete(0, tk.END)
        for model_name in self.trained_models.keys():
            display_name = self._get_display_name(model_name)
            self.model_listbox.insert(tk.END, display_name)
        
        self.logger.info(f"接收到 {len(self.trained_models)} 个训练完成的模型")
    
    def _get_display_name(self, model_name: str) -> str:
        """获取模型显示名称"""
        display_names = {
            'DecisionTree': '决策树',
            'RandomForest': '随机森林',
            'XGBoost': 'XGBoost',
            'LightGBM': 'LightGBM',
            'CatBoost': 'CatBoost',
            'Logistic': '逻辑回归',
            'SVM': '支持向量机',
            'KNN': 'K近邻',
            'NaiveBayes': '朴素贝叶斯',
            'NeuralNet': '神经网络'
        }
        return display_names.get(model_name, model_name)
    
    def _on_model_select(self, event):
        """模型选择事件处理"""
        selection = self.model_listbox.curselection()
        if not selection:
            return
        
        index = selection[0]
        model_names = list(self.trained_models.keys())
        if index < len(model_names):
            self.selected_model = model_names[index]
            self._setup_hyperparameter_widgets()
            self.tune_button.config(state=tk.NORMAL)
            self.status_label.config(text=f"已选择模型: {self._get_display_name(self.selected_model)}")
    
    def _setup_hyperparameter_widgets(self):
        """设置超参数配置控件"""
        # 清空现有控件
        for widget in self.params_scroll_frame.scrollable_frame.winfo_children():
            widget.destroy()
        self.param_widgets = {}
        
        if self.selected_model not in self.hyperparameter_configs:
            factory = get_component_factory()
            no_config_label = factory.create_label(
                self.params_scroll_frame.scrollable_frame,
                text=f"暂无 {self._get_display_name(self.selected_model)} 的超参数配置",
                style='info'
            )
            no_config_label.pack(pady=20)
            return
        
        config = self.hyperparameter_configs[self.selected_model]
        factory = get_component_factory()
        
        for param_name, param_config in config.items():
            # 参数框架
            param_frame = factory.create_frame(self.params_scroll_frame.scrollable_frame)
            param_frame.pack(fill=tk.X, padx=5, pady=5)
            
            # 参数标签
            label = factory.create_label(param_frame, text=f"{param_name}:")
            label.pack(anchor=tk.W)
            
            param_type = param_config['type']
            
            if param_type == 'int':
                # 整数范围
                range_frame = factory.create_frame(param_frame)
                range_frame.pack(fill=tk.X, pady=(2, 0))
                
                factory.create_label(range_frame, text="最小值:").pack(side=tk.LEFT)
                min_var = tk.IntVar(value=param_config['range'][0])
                min_spinbox = factory.create_spinbox(
                    range_frame,
                    from_=1,
                    to=1000,
                    textvariable=min_var,
                    width=8
                )
                min_spinbox.pack(side=tk.LEFT, padx=(5, 10))
                
                factory.create_label(range_frame, text="最大值:").pack(side=tk.LEFT)
                max_var = tk.IntVar(value=param_config['range'][1])
                max_spinbox = factory.create_spinbox(
                    range_frame,
                    from_=1,
                    to=1000,
                    textvariable=max_var,
                    width=8
                )
                max_spinbox.pack(side=tk.LEFT, padx=5)
                
                self.param_widgets[param_name] = {
                    'type': 'int',
                    'min_var': min_var,
                    'max_var': max_var
                }
            
            elif param_type == 'float':
                # 浮点数范围
                range_frame = factory.create_frame(param_frame)
                range_frame.pack(fill=tk.X, pady=(2, 0))
                
                factory.create_label(range_frame, text="最小值:").pack(side=tk.LEFT)
                min_var = tk.DoubleVar(value=param_config['range'][0])
                min_entry = factory.create_entry(range_frame, textvariable=min_var, width=10)
                min_entry.pack(side=tk.LEFT, padx=(5, 10))
                
                factory.create_label(range_frame, text="最大值:").pack(side=tk.LEFT)
                max_var = tk.DoubleVar(value=param_config['range'][1])
                max_entry = factory.create_entry(range_frame, textvariable=max_var, width=10)
                max_entry.pack(side=tk.LEFT, padx=5)
                
                self.param_widgets[param_name] = {
                    'type': 'float',
                    'min_var': min_var,
                    'max_var': max_var
                }
            
            elif param_type == 'choice':
                # 选择列表
                choices_frame = factory.create_frame(param_frame)
                choices_frame.pack(fill=tk.X, pady=(2, 0))
                
                choices_var = {}
                for choice in param_config['choices']:
                    var = tk.BooleanVar(value=True)  # 默认全选
                    checkbox = factory.create_checkbox(
                        choices_frame,
                        text=choice,
                        variable=var
                    )
                    checkbox.pack(anchor=tk.W)
                    choices_var[choice] = var
                
                self.param_widgets[param_name] = {
                    'type': 'choice',
                    'choices_var': choices_var
                }
    
    def _start_tuning(self):
        """开始超参数调优"""
        if not self.selected_model:
            messagebox.showwarning("警告", "请先选择要调优的模型")
            return
        
        # 获取参数配置
        param_grid = self._build_parameter_grid()
        if not param_grid:
            messagebox.showwarning("警告", "请配置至少一个超参数")
            return
        
        # 禁用调优按钮
        self.tune_button.config(state=tk.DISABLED)
        self.status_label.config(text="正在进行超参数调优...")
        self.progress_var.set(0)
        
        # 清空结果
        self._clear_results()
        
        # 在后台线程中进行调优
        tuning_thread = threading.Thread(
            target=self._tune_hyperparameters,
            args=(param_grid,),
            daemon=True
        )
        tuning_thread.start()
    
    def _build_parameter_grid(self) -> Dict[str, Any]:
        """构建参数网格"""
        param_grid = {}
        
        for param_name, widget_info in self.param_widgets.items():
            param_type = widget_info['type']
            
            if param_type == 'int':
                min_val = widget_info['min_var'].get()
                max_val = widget_info['max_var'].get()
                if min_val < max_val:
                    # 生成整数范围
                    step = max(1, (max_val - min_val) // 10)
                    param_grid[param_name] = list(range(min_val, max_val + 1, step))
            
            elif param_type == 'float':
                min_val = widget_info['min_var'].get()
                max_val = widget_info['max_var'].get()
                if min_val < max_val:
                    # 生成浮点数范围
                    import numpy as np
                    param_grid[param_name] = np.linspace(min_val, max_val, 10).tolist()
            
            elif param_type == 'choice':
                choices = [
                    choice for choice, var in widget_info['choices_var'].items()
                    if var.get()
                ]
                if choices:
                    param_grid[param_name] = choices
        
        return param_grid
    
    def _tune_hyperparameters(self, param_grid: Dict[str, Any]):
        """执行超参数调优（后台线程）"""
        try:
            # 模拟调优过程
            import time
            import random
            import itertools
            
            # 生成所有参数组合
            keys = list(param_grid.keys())
            values = list(param_grid.values())
            combinations = list(itertools.product(*values))
            
            # 限制组合数量
            if len(combinations) > 50:
                combinations = random.sample(combinations, 50)
            
            results = []
            total_combinations = len(combinations)
            
            for i, combination in enumerate(combinations):
                # 模拟调优时间
                time.sleep(random.uniform(0.1, 0.5))
                
                # 创建参数字典
                params = dict(zip(keys, combination))
                
                # 模拟评估得分
                score = random.uniform(0.7, 0.95)
                
                result = {
                    'params': params,
                    'score': score,
                    'rank': i + 1
                }
                results.append(result)
                
                # 更新进度和结果
                progress = ((i + 1) / total_combinations) * 100
                self._update_progress(progress)
                self._add_result_to_history(i + 1, params, score, i + 1)
            
            # 找到最佳结果
            best_result = max(results, key=lambda x: x['score'])
            self.tuning_results[self.selected_model] = {
                'best_params': best_result['params'],
                'best_score': best_result['score'],
                'all_results': results
            }
            
            # 显示最佳参数
            self._show_best_params(best_result)
            
            # 调优完成
            self._tuning_completed()
            
        except Exception as e:
            self.logger.error(f"超参数调优失败: {e}")
            self._tuning_failed(str(e))
    
    def _add_result_to_history(self, trial: int, params: Dict[str, Any], score: float, rank: int):
        """添加结果到历史记录"""
        def update_history():
            params_str = ", ".join([f"{k}={v}" for k, v in params.items()])
            values = (trial, params_str, f"{score:.4f}", rank)
            self.history_tree.insert('', tk.END, values=values)
        
        self.parent.after(0, update_history)
    
    def _show_best_params(self, best_result: Dict[str, Any]):
        """显示最佳参数"""
        def update_best_params():
            model_display_name = self._get_display_name(self.selected_model)
            
            text = f"""
{model_display_name} 最佳超参数配置
{'='*50}

最佳得分: {best_result['score']:.4f}

最佳参数:
"""
            for param, value in best_result['params'].items():
                text += f"  {param}: {value}\n"
            
            text += f"""
建议:
- 使用以上参数重新训练模型可能获得更好的性能
- 可以在这些参数附近进一步细化搜索
- 建议结合交叉验证结果进行最终决策
            """
            
            self.best_params_text.config(state=tk.NORMAL)
            self.best_params_text.delete(1.0, tk.END)
            self.best_params_text.insert(1.0, text.strip())
            self.best_params_text.config(state=tk.DISABLED)
        
        self.parent.after(0, update_best_params)
    
    def _update_progress(self, value: float):
        """更新进度条"""
        def update():
            self.progress_var.set(value)
        
        self.parent.after(0, update)
    
    def _tuning_completed(self):
        """调优完成处理"""
        def complete():
            self.tune_button.config(state=tk.NORMAL)
            self.status_label.config(text="超参数调优完成！")
            self.progress_var.set(100)
            
            # 发布调优完成事件
            event_manager = get_event_manager()
            event_manager.publish('hyperparameter_tuning_completed', {
                'model': self.selected_model,
                'results': self.tuning_results.get(self.selected_model, {})
            })
        
        self.parent.after(0, complete)
    
    def _tuning_failed(self, error_message: str):
        """调优失败处理"""
        def fail():
            self.tune_button.config(state=tk.NORMAL)
            self.status_label.config(text=f"调优失败: {error_message}")
            self.progress_var.set(0)
            messagebox.showerror("调优失败", f"超参数调优过程中出现错误:\n{error_message}")
        
        self.parent.after(0, fail)
    
    def _clear_results(self):
        """清空结果"""
        # 清空历史记录
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        # 清空最佳参数
        self.best_params_text.config(state=tk.NORMAL)
        self.best_params_text.delete(1.0, tk.END)
        self.best_params_text.insert(1.0, "调优结果将在这里显示...")
        self.best_params_text.config(state=tk.DISABLED)
    
    def get_tuning_results(self) -> Dict[str, Any]:
        """获取调优结果"""
        return self.tuning_results.copy()
    
    def get_best_params(self, model_name: str) -> Optional[Dict[str, Any]]:
        """获取指定模型的最佳参数"""
        if model_name in self.tuning_results:
            return self.tuning_results[model_name]['best_params']
        return None
