"""
模型比较标签页
提供多个模型的性能比较和分析功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager
from ...components.chart_widgets import ChartWidget


class ModelComparisonTab(BaseGUI):
    """模型比较标签页"""
    
    def __init__(self, parent: tk.Widget):
        """初始化模型比较标签页"""
        self.trained_models = {}
        self.comparison_data = None
        self.selected_models = []
        
        # 比较指标
        self.metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'auc', 'training_time']
        self.metric_display_names = {
            'accuracy': '准确率',
            'precision': '精确率',
            'recall': '召回率',
            'f1_score': 'F1分数',
            'auc': 'AUC',
            'training_time': '训练时间(秒)'
        }
        
        super().__init__(parent)
        
        # 订阅模型训练完成事件
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.MODEL_TRAINED, self._on_models_trained)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='main')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建上下分割
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.VERTICAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 上方控制面板
        top_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(top_frame, weight=1)
        
        # 下方比较结果面板
        bottom_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(bottom_frame, weight=3)
        
        # 设置控制面板
        self._setup_control_panel(top_frame)
        
        # 设置比较结果面板
        self._setup_comparison_panel(bottom_frame)
        
        self.register_component('main_frame', self.main_frame)
        self.register_component('paned_window', paned_window)
    
    def _setup_control_panel(self, parent):
        """设置控制面板"""
        factory = get_component_factory()
        
        # 创建左右分割
        control_paned = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        control_paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧模型选择
        left_frame = factory.create_frame(control_paned)
        control_paned.add(left_frame, weight=1)
        
        # 右侧比较设置
        right_frame = factory.create_frame(control_paned)
        control_paned.add(right_frame, weight=1)
        
        # 模型选择
        model_frame = factory.create_labelframe(left_frame, text="模型选择", style='section')
        model_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 全选/取消全选按钮
        select_frame = factory.create_frame(model_frame)
        select_frame.pack(fill=tk.X, padx=5, pady=5)
        
        select_all_btn = factory.create_button(
            select_frame,
            text="全选",
            command=self._select_all_models,
            style='secondary'
        )
        select_all_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        deselect_all_btn = factory.create_button(
            select_frame,
            text="取消全选",
            command=self._deselect_all_models,
            style='secondary'
        )
        deselect_all_btn.pack(side=tk.LEFT)
        
        # 模型复选框
        self.model_vars = {}
        models_scroll_frame = factory.create_scrollable_frame(model_frame)
        models_scroll_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 比较设置
        settings_frame = factory.create_labelframe(right_frame, text="比较设置", style='section')
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 主要指标选择
        primary_metric_frame = factory.create_frame(settings_frame)
        primary_metric_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(primary_metric_frame, text="主要指标:").pack(anchor=tk.W)
        self.primary_metric_var = tk.StringVar(value="f1_score")
        metric_combo = factory.create_combobox(
            primary_metric_frame,
            textvariable=self.primary_metric_var,
            values=list(self.metric_display_names.values()),
            state="readonly"
        )
        metric_combo.pack(fill=tk.X, pady=(2, 0))
        
        # 排序方式
        sort_frame = factory.create_frame(settings_frame)
        sort_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(sort_frame, text="排序方式:").pack(anchor=tk.W)
        self.sort_order_var = tk.StringVar(value="降序")
        sort_combo = factory.create_combobox(
            sort_frame,
            textvariable=self.sort_order_var,
            values=["降序", "升序"],
            state="readonly"
        )
        sort_combo.pack(fill=tk.X, pady=(2, 0))
        
        # 显示选项
        display_frame = factory.create_labelframe(settings_frame, text="显示选项", style='subsection')
        display_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.show_chart_var = tk.BooleanVar(value=True)
        chart_check = factory.create_checkbox(
            display_frame,
            text="显示性能图表",
            variable=self.show_chart_var
        )
        chart_check.pack(anchor=tk.W, padx=5, pady=2)
        
        self.show_ranking_var = tk.BooleanVar(value=True)
        ranking_check = factory.create_checkbox(
            display_frame,
            text="显示排名信息",
            variable=self.show_ranking_var
        )
        ranking_check.pack(anchor=tk.W, padx=5, pady=2)
        
        self.show_details_var = tk.BooleanVar(value=False)
        details_check = factory.create_checkbox(
            display_frame,
            text="显示详细统计",
            variable=self.show_details_var
        )
        details_check.pack(anchor=tk.W, padx=5, pady=2)
        
        # 比较按钮
        self.compare_button = factory.create_button(
            settings_frame,
            text="📊 开始比较",
            command=self._start_comparison,
            style='primary'
        )
        self.compare_button.pack(fill=tk.X, padx=5, pady=10)
        self.compare_button.config(state=tk.DISABLED)
        
        # 状态标签
        self.status_label = factory.create_label(
            settings_frame,
            text="请选择要比较的模型",
            style='info'
        )
        self.status_label.pack(padx=5, pady=(0, 5))
    
    def _setup_comparison_panel(self, parent):
        """设置比较结果面板"""
        factory = get_component_factory()
        
        # 比较结果标签页
        self.comparison_notebook = factory.create_notebook(parent)
        self.comparison_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 性能比较表格标签页
        table_frame = factory.create_frame(self.comparison_notebook)
        self.comparison_notebook.add(table_frame, text="性能比较表")
        
        # 比较表格
        self.comparison_tree = None
        self._setup_comparison_table(table_frame)
        
        # 可视化图表标签页
        chart_frame = factory.create_frame(self.comparison_notebook)
        self.comparison_notebook.add(chart_frame, text="可视化图表")
        
        # 图表容器
        self.chart_widget = ChartWidget(chart_frame, chart_type="comparison")
        
        # 排名分析标签页
        ranking_frame = factory.create_frame(self.comparison_notebook)
        self.comparison_notebook.add(ranking_frame, text="排名分析")
        
        # 排名信息文本
        self.ranking_text = factory.create_text(
            ranking_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        ranking_scrollbar = factory.create_scrollbar(ranking_frame, orient=tk.VERTICAL)
        ranking_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.ranking_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.ranking_text.configure(yscrollcommand=ranking_scrollbar.set)
        ranking_scrollbar.configure(command=self.ranking_text.yview)
        
        # 详细统计标签页
        stats_frame = factory.create_frame(self.comparison_notebook)
        self.comparison_notebook.add(stats_frame, text="详细统计")
        
        # 统计信息文本
        self.stats_text = factory.create_text(
            stats_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        stats_scrollbar = factory.create_scrollbar(stats_frame, orient=tk.VERTICAL)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        stats_scrollbar.configure(command=self.stats_text.yview)
    
    def _setup_comparison_table(self, parent):
        """设置比较表格"""
        factory = get_component_factory()
        
        # 表格列
        columns = ['模型', '排名'] + [self.metric_display_names[m] for m in self.metrics]
        
        self.comparison_tree = factory.create_treeview(
            parent,
            columns=columns,
            show='headings'
        )
        
        # 设置列
        for col in columns:
            self.comparison_tree.heading(col, text=col)
            if col in ['模型']:
                self.comparison_tree.column(col, width=120, anchor=tk.W)
            elif col in ['排名']:
                self.comparison_tree.column(col, width=60, anchor=tk.CENTER)
            else:
                self.comparison_tree.column(col, width=100, anchor=tk.CENTER)
        
        # 滚动条
        table_scrollbar = factory.create_scrollbar(parent, orient=tk.VERTICAL)
        table_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.comparison_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.comparison_tree.configure(yscrollcommand=table_scrollbar.set)
        table_scrollbar.configure(command=self.comparison_tree.yview)
        
        # 绑定选择事件
        self.comparison_tree.bind('<<TreeviewSelect>>', self._on_model_select_in_table)
    
    def _on_models_trained(self, event_data: Dict[str, Any]):
        """模型训练完成事件处理"""
        self.trained_models = event_data.get('results', {})
        
        # 清空现有的模型选择控件
        for widget in self.model_vars:
            widget.destroy() if hasattr(widget, 'destroy') else None
        self.model_vars = {}
        
        # 重新创建模型复选框
        factory = get_component_factory()
        models_scroll_frame = None
        
        # 找到模型滚动框架
        for child in self.main_frame.winfo_children():
            if isinstance(child, ttk.PanedWindow):
                for pane_child in child.winfo_children():
                    for frame_child in pane_child.winfo_children():
                        if isinstance(frame_child, ttk.LabelFrame) and "模型选择" in str(frame_child):
                            for scroll_child in frame_child.winfo_children():
                                if hasattr(scroll_child, 'scrollable_frame'):
                                    models_scroll_frame = scroll_child
                                    break
        
        if models_scroll_frame:
            # 清空现有控件
            for widget in models_scroll_frame.scrollable_frame.winfo_children():
                widget.destroy()
            
            # 创建新的复选框
            for model_name in self.trained_models.keys():
                var = tk.BooleanVar()
                display_name = self._get_display_name(model_name)
                checkbox = factory.create_checkbox(
                    models_scroll_frame.scrollable_frame,
                    text=display_name,
                    variable=var,
                    command=self._on_model_selection_changed
                )
                checkbox.pack(anchor=tk.W, pady=2)
                self.model_vars[model_name] = var
        
        self.logger.info(f"接收到 {len(self.trained_models)} 个训练完成的模型")
        self.status_label.config(text=f"可比较 {len(self.trained_models)} 个模型")
    
    def _get_display_name(self, model_name: str) -> str:
        """获取模型显示名称"""
        display_names = {
            'DecisionTree': '决策树',
            'RandomForest': '随机森林',
            'XGBoost': 'XGBoost',
            'LightGBM': 'LightGBM',
            'CatBoost': 'CatBoost',
            'Logistic': '逻辑回归',
            'SVM': '支持向量机',
            'KNN': 'K近邻',
            'NaiveBayes': '朴素贝叶斯',
            'NeuralNet': '神经网络'
        }
        return display_names.get(model_name, model_name)
    
    def _select_all_models(self):
        """选择所有模型"""
        for var in self.model_vars.values():
            var.set(True)
        self._on_model_selection_changed()
    
    def _deselect_all_models(self):
        """取消选择所有模型"""
        for var in self.model_vars.values():
            var.set(False)
        self._on_model_selection_changed()
    
    def _on_model_selection_changed(self):
        """模型选择变化处理"""
        self.selected_models = [
            model for model, var in self.model_vars.items() if var.get()
        ]
        
        # 更新比较按钮状态
        if len(self.selected_models) >= 2:
            self.compare_button.config(state=tk.NORMAL)
            self.status_label.config(text=f"已选择 {len(self.selected_models)} 个模型")
        else:
            self.compare_button.config(state=tk.DISABLED)
            self.status_label.config(text="请至少选择2个模型进行比较")
    
    def _start_comparison(self):
        """开始模型比较"""
        if len(self.selected_models) < 2:
            messagebox.showwarning("警告", "请至少选择2个模型进行比较")
            return
        
        try:
            # 准备比较数据
            self._prepare_comparison_data()
            
            # 更新比较表格
            self._update_comparison_table()
            
            # 更新图表（如果启用）
            if self.show_chart_var.get():
                self._update_comparison_chart()
            
            # 更新排名分析（如果启用）
            if self.show_ranking_var.get():
                self._update_ranking_analysis()
            
            # 更新详细统计（如果启用）
            if self.show_details_var.get():
                self._update_detailed_statistics()
            
            self.status_label.config(text="模型比较完成")
            
        except Exception as e:
            self.logger.error(f"模型比较失败: {e}")
            messagebox.showerror("比较失败", f"模型比较过程中出现错误:\n{e}")
    
    def _prepare_comparison_data(self):
        """准备比较数据"""
        comparison_data = []
        
        for model_name in self.selected_models:
            if model_name in self.trained_models:
                model_result = self.trained_models[model_name]
                
                # 提取性能指标
                data_row = {
                    'model_name': model_name,
                    'display_name': self._get_display_name(model_name)
                }
                
                for metric in self.metrics:
                    data_row[metric] = model_result.get(metric, 0.0)
                
                comparison_data.append(data_row)
        
        # 转换为DataFrame
        self.comparison_data = pd.DataFrame(comparison_data)
        
        # 根据主要指标排序
        primary_metric_key = None
        for key, display_name in self.metric_display_names.items():
            if display_name == self.primary_metric_var.get():
                primary_metric_key = key
                break
        
        if primary_metric_key:
            ascending = self.sort_order_var.get() == "升序"
            self.comparison_data = self.comparison_data.sort_values(
                by=primary_metric_key,
                ascending=ascending
            ).reset_index(drop=True)
            
            # 添加排名
            self.comparison_data['rank'] = range(1, len(self.comparison_data) + 1)
    
    def _update_comparison_table(self):
        """更新比较表格"""
        # 清空现有数据
        for item in self.comparison_tree.get_children():
            self.comparison_tree.delete(item)
        
        # 添加新数据
        for _, row in self.comparison_data.iterrows():
            values = [row['display_name'], row['rank']]
            
            for metric in self.metrics:
                if metric == 'training_time':
                    values.append(f"{row[metric]:.2f}")
                else:
                    values.append(f"{row[metric]:.4f}")
            
            # 根据排名设置标签
            tag = f"rank_{row['rank']}"
            self.comparison_tree.insert('', tk.END, values=values, tags=(tag, row['model_name']))
            
            # 为前三名设置特殊颜色
            if row['rank'] == 1:
                self.comparison_tree.set_children('', self.comparison_tree.get_children()[-1:])
                # 这里可以设置颜色，但需要配置tag的样式
    
    def _update_comparison_chart(self):
        """更新比较图表"""
        if self.comparison_data is not None:
            # 这里应该创建实际的图表
            # 目前使用占位符
            chart_data = {
                'models': self.comparison_data['display_name'].tolist(),
                'metrics': {metric: self.comparison_data[metric].tolist() for metric in self.metrics}
            }
            self.chart_widget.load_data(chart_data)
    
    def _update_ranking_analysis(self):
        """更新排名分析"""
        if self.comparison_data is None:
            return
        
        analysis_text = f"""
模型性能排名分析
{'='*50}

基于 {self.primary_metric_var.get()} 的排名结果：

"""
        
        for _, row in self.comparison_data.iterrows():
            primary_metric_key = None
            for key, display_name in self.metric_display_names.items():
                if display_name == self.primary_metric_var.get():
                    primary_metric_key = key
                    break
            
            primary_score = row[primary_metric_key] if primary_metric_key else 0
            
            analysis_text += f"""
第 {row['rank']} 名: {row['display_name']}
  {self.primary_metric_var.get()}: {primary_score:.4f}
  综合表现: {'优秀' if primary_score > 0.9 else '良好' if primary_score > 0.8 else '一般'}
"""
        
        analysis_text += f"""

排名总结:
- 最佳模型: {self.comparison_data.iloc[0]['display_name']}
- 平均性能: {self.comparison_data[primary_metric_key].mean():.4f}
- 性能标准差: {self.comparison_data[primary_metric_key].std():.4f}
- 性能差异: {'较大' if self.comparison_data[primary_metric_key].std() > 0.05 else '较小'}

建议:
- 优先考虑排名前三的模型
- 结合业务需求选择最适合的模型
- 可以考虑集成学习结合多个优秀模型
        """
        
        self.ranking_text.config(state=tk.NORMAL)
        self.ranking_text.delete(1.0, tk.END)
        self.ranking_text.insert(1.0, analysis_text.strip())
        self.ranking_text.config(state=tk.DISABLED)
    
    def _update_detailed_statistics(self):
        """更新详细统计"""
        if self.comparison_data is None:
            return
        
        stats_text = f"""
详细统计信息
{'='*50}

参与比较的模型数量: {len(self.comparison_data)}
比较指标数量: {len(self.metrics)}

各指标统计:
"""
        
        for metric in self.metrics:
            metric_name = self.metric_display_names[metric]
            values = self.comparison_data[metric]
            
            stats_text += f"""
{metric_name}:
  最大值: {values.max():.4f} ({self.comparison_data.loc[values.idxmax(), 'display_name']})
  最小值: {values.min():.4f} ({self.comparison_data.loc[values.idxmin(), 'display_name']})
  平均值: {values.mean():.4f}
  中位数: {values.median():.4f}
  标准差: {values.std():.4f}
  变异系数: {(values.std() / values.mean() * 100):.2f}%
"""
        
        # 相关性分析
        stats_text += f"""

指标相关性分析:
"""
        
        # 计算相关性矩阵
        correlation_matrix = self.comparison_data[self.metrics].corr()
        
        for i, metric1 in enumerate(self.metrics):
            for j, metric2 in enumerate(self.metrics):
                if i < j:  # 只显示上三角
                    corr_value = correlation_matrix.loc[metric1, metric2]
                    corr_strength = '强' if abs(corr_value) > 0.7 else '中等' if abs(corr_value) > 0.4 else '弱'
                    stats_text += f"  {self.metric_display_names[metric1]} vs {self.metric_display_names[metric2]}: {corr_value:.3f} ({corr_strength}相关)\n"
        
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text.strip())
        self.stats_text.config(state=tk.DISABLED)
    
    def _on_model_select_in_table(self, event):
        """表格中模型选择事件处理"""
        selection = self.comparison_tree.selection()
        if not selection:
            return
        
        item = selection[0]
        tags = self.comparison_tree.item(item, 'tags')
        if len(tags) >= 2:
            model_name = tags[1]  # 第二个tag是模型名称
            self.logger.info(f"选中模型: {model_name}")
            # 这里可以添加更多的选择处理逻辑
    
    def get_comparison_results(self) -> Optional[pd.DataFrame]:
        """获取比较结果"""
        return self.comparison_data.copy() if self.comparison_data is not None else None
    
    def export_comparison_results(self, file_path: str):
        """导出比较结果"""
        if self.comparison_data is not None:
            try:
                self.comparison_data.to_csv(file_path, index=False, encoding='utf-8-sig')
                messagebox.showinfo("导出成功", f"比较结果已导出到:\n{file_path}")
            except Exception as e:
                messagebox.showerror("导出失败", f"导出过程中出现错误:\n{e}")
        else:
            messagebox.showwarning("警告", "没有可导出的比较结果")
