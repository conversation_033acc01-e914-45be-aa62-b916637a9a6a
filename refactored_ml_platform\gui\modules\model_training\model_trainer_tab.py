"""
模型训练标签页
提供机器学习模型的训练功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
from pathlib import Path

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager


class ModelTrainerTab(BaseGUI):
    """模型训练标签页"""
    
    def __init__(self, parent: tk.Widget):
        """初始化模型训练标签页"""
        self.data = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.feature_names = None
        self.training_results = {}
        self.selected_models = []
        
        # 可用的模型列表
        self.available_models = [
            'DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
            'Logistic', 'SVM', 'KNN', 'NaiveBayes', 'NeuralNet'
        ]
        
        # 模型显示名称
        self.model_display_names = {
            'DecisionTree': '决策树',
            'RandomForest': '随机森林',
            'XGBoost': 'XGBoost',
            'LightGBM': 'LightGBM',
            'CatBoost': 'CatBoost',
            'Logistic': '逻辑回归',
            'SVM': '支持向量机',
            'KNN': 'K近邻',
            'NaiveBayes': '朴素贝叶斯',
            'NeuralNet': '神经网络'
        }
        
        super().__init__(parent)
        
        # 订阅数据更新事件
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.DATA_LOADED, self._on_data_loaded)
        event_manager.subscribe(EventTypes.DATA_PREPROCESSED, self._on_data_preprocessed)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分割
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧控制面板
        left_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(left_frame, weight=1)
        
        # 右侧结果面板
        right_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(right_frame, weight=2)
        
        # 设置左侧面板
        self._setup_control_panel(left_frame)
        
        # 设置右侧面板
        self._setup_results_panel(right_frame)
        
        self.register_component('main_frame', self.main_frame)
        self.register_component('paned_window', paned_window)
    
    def _setup_control_panel(self, parent):
        """设置控制面板"""
        factory = get_component_factory()
        
        # 数据状态
        status_frame = factory.create_labelframe(parent, text="数据状态", style='section')
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.data_status_label = factory.create_label(
            status_frame, 
            text="❌ 未加载数据", 
            style='info'
        )
        self.data_status_label.pack(anchor=tk.W, padx=5, pady=5)
        
        # 模型选择
        model_frame = factory.create_labelframe(parent, text="模型选择", style='section')
        model_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 全选/取消全选按钮
        select_frame = factory.create_frame(model_frame)
        select_frame.pack(fill=tk.X, padx=5, pady=5)
        
        select_all_btn = factory.create_button(
            select_frame,
            text="全选",
            command=self._select_all_models,
            style='secondary'
        )
        select_all_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        deselect_all_btn = factory.create_button(
            select_frame,
            text="取消全选",
            command=self._deselect_all_models,
            style='secondary'
        )
        deselect_all_btn.pack(side=tk.LEFT)
        
        # 模型复选框
        self.model_vars = {}
        models_scroll_frame = factory.create_scrollable_frame(model_frame)
        models_scroll_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        for model in self.available_models:
            var = tk.BooleanVar()
            checkbox = factory.create_checkbox(
                models_scroll_frame.scrollable_frame,
                text=self.model_display_names.get(model, model),
                variable=var,
                command=self._on_model_selection_changed
            )
            checkbox.pack(anchor=tk.W, pady=2)
            self.model_vars[model] = var
        
        # 训练参数
        params_frame = factory.create_labelframe(parent, text="训练参数", style='section')
        params_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 测试集比例
        test_size_frame = factory.create_frame(params_frame)
        test_size_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(test_size_frame, text="测试集比例:").pack(side=tk.LEFT)
        self.test_size_var = tk.DoubleVar(value=0.2)
        test_size_spinbox = factory.create_spinbox(
            test_size_frame,
            from_=0.1,
            to=0.5,
            increment=0.05,
            textvariable=self.test_size_var,
            width=10
        )
        test_size_spinbox.pack(side=tk.RIGHT)
        
        # 随机种子
        seed_frame = factory.create_frame(params_frame)
        seed_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(seed_frame, text="随机种子:").pack(side=tk.LEFT)
        self.random_seed_var = tk.IntVar(value=42)
        seed_spinbox = factory.create_spinbox(
            seed_frame,
            from_=1,
            to=9999,
            increment=1,
            textvariable=self.random_seed_var,
            width=10
        )
        seed_spinbox.pack(side=tk.RIGHT)
        
        # 交叉验证折数
        cv_frame = factory.create_frame(params_frame)
        cv_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(cv_frame, text="交叉验证折数:").pack(side=tk.LEFT)
        self.cv_folds_var = tk.IntVar(value=5)
        cv_spinbox = factory.create_spinbox(
            cv_frame,
            from_=3,
            to=10,
            increment=1,
            textvariable=self.cv_folds_var,
            width=10
        )
        cv_spinbox.pack(side=tk.RIGHT)
        
        # 训练按钮
        train_frame = factory.create_frame(parent)
        train_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.train_button = factory.create_button(
            train_frame,
            text="🚀 开始训练",
            command=self._start_training,
            style='primary'
        )
        self.train_button.pack(fill=tk.X)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = factory.create_progressbar(
            train_frame,
            variable=self.progress_var,
            maximum=100
        )
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
        
        # 状态标签
        self.status_label = factory.create_label(
            train_frame,
            text="等待开始训练...",
            style='info'
        )
        self.status_label.pack(pady=(5, 0))
    
    def _setup_results_panel(self, parent):
        """设置结果面板"""
        factory = get_component_factory()
        
        # 结果标签页
        self.results_notebook = factory.create_notebook(parent)
        self.results_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 训练结果标签页
        results_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(results_frame, text="训练结果")
        
        # 结果表格
        columns = ['模型', '准确率', '精确率', '召回率', 'F1分数', 'AUC', '训练时间']
        self.results_tree = factory.create_treeview(
            results_frame,
            columns=columns,
            show='headings'
        )
        
        # 设置列
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=100, anchor=tk.CENTER)
        
        # 滚动条
        results_scrollbar = factory.create_scrollbar(results_frame, orient=tk.VERTICAL)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.results_tree.configure(yscrollcommand=results_scrollbar.set)
        results_scrollbar.configure(command=self.results_tree.yview)
        
        # 详细信息标签页
        details_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(details_frame, text="详细信息")
        
        # 详细信息文本框
        self.details_text = factory.create_text(
            details_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        details_scrollbar = factory.create_scrollbar(details_frame, orient=tk.VERTICAL)
        details_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.details_text.configure(yscrollcommand=details_scrollbar.set)
        details_scrollbar.configure(command=self.details_text.yview)
        
        # 绑定选择事件
        self.results_tree.bind('<<TreeviewSelect>>', self._on_result_select)
    
    def _on_data_loaded(self, event_data: Dict[str, Any]):
        """数据加载事件处理"""
        self.data = event_data.get('data')
        if self.data is not None:
            self.data_status_label.config(
                text=f"✅ 已加载数据 ({self.data.shape[0]} 行, {self.data.shape[1]} 列)"
            )
            self.logger.info(f"接收到数据: {self.data.shape}")
        else:
            self.data_status_label.config(text="❌ 数据加载失败")
    
    def _on_data_preprocessed(self, event_data: Dict[str, Any]):
        """数据预处理事件处理"""
        self.X_train = event_data.get('X_train')
        self.X_test = event_data.get('X_test')
        self.y_train = event_data.get('y_train')
        self.y_test = event_data.get('y_test')
        self.feature_names = event_data.get('feature_names')
        
        if all(x is not None for x in [self.X_train, self.X_test, self.y_train, self.y_test]):
            self.data_status_label.config(
                text=f"✅ 数据已预处理 (训练集: {self.X_train.shape[0]}, 测试集: {self.X_test.shape[0]})"
            )
            self.logger.info("接收到预处理后的数据")
        else:
            self.data_status_label.config(text="❌ 数据预处理失败")
    
    def _select_all_models(self):
        """选择所有模型"""
        for var in self.model_vars.values():
            var.set(True)
        self._on_model_selection_changed()
    
    def _deselect_all_models(self):
        """取消选择所有模型"""
        for var in self.model_vars.values():
            var.set(False)
        self._on_model_selection_changed()
    
    def _on_model_selection_changed(self):
        """模型选择变化处理"""
        self.selected_models = [
            model for model, var in self.model_vars.items() if var.get()
        ]
        
        # 更新训练按钮状态
        if self.selected_models and self._is_data_ready():
            self.train_button.config(state=tk.NORMAL)
        else:
            self.train_button.config(state=tk.DISABLED)
    
    def _is_data_ready(self) -> bool:
        """检查数据是否准备就绪"""
        if self.data is not None:
            return True
        return all(x is not None for x in [self.X_train, self.X_test, self.y_train, self.y_test])
    
    def _start_training(self):
        """开始训练"""
        if not self.selected_models:
            messagebox.showwarning("警告", "请至少选择一个模型进行训练")
            return
        
        if not self._is_data_ready():
            messagebox.showwarning("警告", "请先加载并预处理数据")
            return
        
        # 禁用训练按钮
        self.train_button.config(state=tk.DISABLED)
        self.status_label.config(text="正在训练模型...")
        self.progress_var.set(0)
        
        # 清空之前的结果
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        self._clear_details()
        
        # 在后台线程中训练
        training_thread = threading.Thread(
            target=self._train_models,
            daemon=True
        )
        training_thread.start()
    
    def _train_models(self):
        """训练模型（后台线程）"""
        try:
            # 准备数据
            if self.data is not None:
                # 如果有原始数据，需要进行数据分割
                self._prepare_data_from_raw()
            
            total_models = len(self.selected_models)
            self.training_results = {}
            
            for i, model_name in enumerate(self.selected_models):
                try:
                    self.logger.info(f"开始训练模型: {model_name}")
                    
                    # 更新状态
                    self._update_status(f"正在训练 {self.model_display_names.get(model_name, model_name)}...")
                    
                    # 训练模型
                    result = self._train_single_model(model_name)
                    
                    if result:
                        self.training_results[model_name] = result
                        
                        # 更新结果显示
                        self._add_result_to_tree(model_name, result)
                        
                        self.logger.info(f"模型 {model_name} 训练完成")
                    else:
                        self.logger.error(f"模型 {model_name} 训练失败")
                
                except Exception as e:
                    self.logger.error(f"训练模型 {model_name} 时出错: {e}")
                
                # 更新进度
                progress = ((i + 1) / total_models) * 100
                self._update_progress(progress)
            
            # 训练完成
            self._training_completed()
            
        except Exception as e:
            self.logger.error(f"训练过程出错: {e}")
            self._training_failed(str(e))
    
    def _prepare_data_from_raw(self):
        """从原始数据准备训练数据"""
        try:
            from sklearn.model_selection import train_test_split
            
            # 假设最后一列是目标变量
            X = self.data.iloc[:, :-1]
            y = self.data.iloc[:, -1]
            
            # 数据分割
            self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
                X, y,
                test_size=self.test_size_var.get(),
                random_state=self.random_seed_var.get(),
                stratify=y if len(y.unique()) > 1 else None
            )
            
            self.feature_names = list(X.columns) if hasattr(X, 'columns') else None
            
        except Exception as e:
            self.logger.error(f"数据准备失败: {e}")
            raise
    
    def _train_single_model(self, model_name: str) -> Optional[Dict[str, Any]]:
        """训练单个模型"""
        try:
            # 这里应该调用实际的模型训练函数
            # 为了演示，我们创建一个模拟的训练结果
            import time
            import random
            
            # 模拟训练时间
            time.sleep(random.uniform(1, 3))
            
            # 模拟训练结果
            result = {
                'model_name': model_name,
                'accuracy': random.uniform(0.7, 0.95),
                'precision': random.uniform(0.7, 0.95),
                'recall': random.uniform(0.7, 0.95),
                'f1_score': random.uniform(0.7, 0.95),
                'auc': random.uniform(0.7, 0.95),
                'training_time': random.uniform(1, 3),
                'model': None,  # 实际模型对象
                'confusion_matrix': [[50, 5], [3, 42]],  # 示例混淆矩阵
                'classification_report': "示例分类报告"
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"训练模型 {model_name} 失败: {e}")
            return None
    
    def _add_result_to_tree(self, model_name: str, result: Dict[str, Any]):
        """添加结果到树形视图"""
        def update_tree():
            display_name = self.model_display_names.get(model_name, model_name)
            values = (
                display_name,
                f"{result['accuracy']:.4f}",
                f"{result['precision']:.4f}",
                f"{result['recall']:.4f}",
                f"{result['f1_score']:.4f}",
                f"{result['auc']:.4f}",
                f"{result['training_time']:.2f}s"
            )
            self.results_tree.insert('', tk.END, values=values, tags=(model_name,))
        
        # 在主线程中更新UI
        self.parent.after(0, update_tree)
    
    def _update_status(self, message: str):
        """更新状态信息"""
        def update():
            self.status_label.config(text=message)
        
        self.parent.after(0, update)
    
    def _update_progress(self, value: float):
        """更新进度条"""
        def update():
            self.progress_var.set(value)
        
        self.parent.after(0, update)
    
    def _training_completed(self):
        """训练完成处理"""
        def complete():
            self.train_button.config(state=tk.NORMAL)
            self.status_label.config(text=f"训练完成！成功训练 {len(self.training_results)} 个模型")
            self.progress_var.set(100)
            
            # 发布训练完成事件
            event_manager = get_event_manager()
            event_manager.publish(EventTypes.MODEL_TRAINED, {
                'results': self.training_results,
                'selected_models': self.selected_models
            })
        
        self.parent.after(0, complete)
    
    def _training_failed(self, error_message: str):
        """训练失败处理"""
        def fail():
            self.train_button.config(state=tk.NORMAL)
            self.status_label.config(text=f"训练失败: {error_message}")
            self.progress_var.set(0)
            messagebox.showerror("训练失败", f"模型训练过程中出现错误:\n{error_message}")
        
        self.parent.after(0, fail)
    
    def _on_result_select(self, event):
        """结果选择事件处理"""
        selection = self.results_tree.selection()
        if not selection:
            return
        
        item = selection[0]
        tags = self.results_tree.item(item, 'tags')
        if not tags:
            return
        
        model_name = tags[0]
        if model_name in self.training_results:
            self._show_model_details(model_name, self.training_results[model_name])
    
    def _show_model_details(self, model_name: str, result: Dict[str, Any]):
        """显示模型详细信息"""
        display_name = self.model_display_names.get(model_name, model_name)
        
        details = f"""
模型: {display_name}
{'='*50}

性能指标:
- 准确率: {result['accuracy']:.4f}
- 精确率: {result['precision']:.4f}
- 召回率: {result['recall']:.4f}
- F1分数: {result['f1_score']:.4f}
- AUC: {result['auc']:.4f}
- 训练时间: {result['training_time']:.2f} 秒

混淆矩阵:
{result.get('confusion_matrix', '暂无数据')}

分类报告:
{result.get('classification_report', '暂无数据')}
        """
        
        self._update_details(details.strip())
    
    def _update_details(self, text: str):
        """更新详细信息"""
        self.details_text.config(state=tk.NORMAL)
        self.details_text.delete(1.0, tk.END)
        self.details_text.insert(1.0, text)
        self.details_text.config(state=tk.DISABLED)
    
    def _clear_details(self):
        """清空详细信息"""
        self._update_details("选择一个模型查看详细信息...")
    
    def get_training_results(self) -> Dict[str, Any]:
        """获取训练结果"""
        return self.training_results.copy()
    
    def clear_results(self):
        """清空结果"""
        self.training_results = {}
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self._clear_details()
        self.progress_var.set(0)
        self.status_label.config(text="等待开始训练...")
