#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多数据源集成学习GUI模块
提供完整的多数据源集成学习界面，包括模型映射配置、多种集成策略、结果可视化等功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import numpy as np
from pathlib import Path
import threading
import json
from typing import List, Dict, Any, Optional, Tuple
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import logging

try:
    from ...algorithms.multi_data_ensemble import MultiDataEnsemble
    from ...utils.error_handler import get_error_handler
    from ..components.progress_widget import ProgressWidget
    from ..components.data_table import DataTableWidget
    from ..core.event_manager import get_event_manager
except ImportError:
    # 处理相对导入问题
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent
    sys.path.insert(0, str(project_root))
    
    from algorithms.multi_data_ensemble import MultiDataEnsemble
    from utils.error_handler import get_error_handler
    from gui.components.progress_widget import ProgressWidget
    from gui.components.data_table import DataTableWidget
    from gui.core.event_manager import get_event_manager


class MultiDataEnsembleModule:
    """多数据源集成学习模块"""
    
    def __init__(self, parent):
        """初始化多数据源集成学习模块"""
        self.parent = parent
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        self.event_manager = get_event_manager()
        
        # 核心组件
        self.ensemble_learner = None
        
        # 数据相关
        self.model_data_mapping = {}
        self.target_data_path = ""
        self.ensemble_results = {}
        
        # GUI组件
        self.main_frame = None
        self.canvas_frame = None
        self.current_figure = None
        self.canvas = None
        self.toolbar = None
        
        # 控制变量
        self.ensemble_methods_vars = {}
        self.data_strategies_vars = {}
        self.selection_strategy_var = tk.StringVar(value="performance_diversity")
        self.max_models_var = tk.IntVar(value=5)
        self.diversity_threshold_var = tk.DoubleVar(value=0.1)
        self.feature_selection_var = tk.BooleanVar(value=True)
        self.feature_selection_method_var = tk.StringVar(value="weighted")
        self.feature_k_var = tk.IntVar(value=10)
        self.enable_shap_var = tk.BooleanVar(value=True)
        
        # 可用选项
        self.ensemble_methods = ['voting', 'stacking', 'weighted', 'bagging', 'boosting']
        self.data_strategies = ['unified', 'original', 'combined']
        self.selection_strategies = ['performance', 'diversity', 'performance_diversity']
        self.feature_selection_methods = ['combined', 'union', 'intersection', 'weighted', 'meta_model']
        
        # 分析状态
        self.is_training = False
        self.training_completed = False
        
        self.logger.info("多数据源集成学习模块初始化完成")
    
    def create_interface(self, parent_notebook):
        """创建多数据源集成学习界面"""
        # 创建主选项卡
        self.main_frame = ttk.Frame(parent_notebook)
        parent_notebook.add(self.main_frame, text="🔗 多数据源集成")
        
        # 创建左右分割面板
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧控制面板
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)
        
        # 右侧结果显示面板
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=2)
        
        # 创建左侧控制界面
        self._create_control_panel(left_frame)
        
        # 创建右侧结果显示界面
        self._create_result_panel(right_frame)
        
        return self.main_frame
    
    def _create_control_panel(self, parent):
        """创建控制面板"""
        # 数据源配置区域
        data_frame = ttk.LabelFrame(parent, text="📊 数据源配置")
        data_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 模型-数据映射配置
        mapping_frame = ttk.Frame(data_frame)
        mapping_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(mapping_frame, text="模型-数据映射:").pack(anchor=tk.W)
        
        # 映射列表
        self.mapping_listbox = tk.Listbox(mapping_frame, height=6)
        mapping_scrollbar = ttk.Scrollbar(mapping_frame, orient=tk.VERTICAL, command=self.mapping_listbox.yview)
        self.mapping_listbox.configure(yscrollcommand=mapping_scrollbar.set)
        
        self.mapping_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, pady=5)
        mapping_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 映射操作按钮
        mapping_btn_frame = ttk.Frame(data_frame)
        mapping_btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(mapping_btn_frame, text="➕ 添加映射", 
                  command=self._add_mapping).pack(side=tk.LEFT, padx=2)
        ttk.Button(mapping_btn_frame, text="✏️ 编辑映射", 
                  command=self._edit_mapping).pack(side=tk.LEFT, padx=2)
        ttk.Button(mapping_btn_frame, text="🗑️ 删除映射", 
                  command=self._remove_mapping).pack(side=tk.LEFT, padx=2)
        ttk.Button(mapping_btn_frame, text="📁 导入配置", 
                  command=self._import_config).pack(side=tk.LEFT, padx=2)
        ttk.Button(mapping_btn_frame, text="💾 导出配置", 
                  command=self._export_config).pack(side=tk.LEFT, padx=2)
        
        # 目标数据路径
        target_frame = ttk.Frame(data_frame)
        target_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(target_frame, text="目标数据路径:").pack(anchor=tk.W)
        
        target_path_frame = ttk.Frame(target_frame)
        target_path_frame.pack(fill=tk.X, pady=2)
        
        self.target_path_var = tk.StringVar()
        ttk.Entry(target_path_frame, textvariable=self.target_path_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(target_path_frame, text="浏览", command=self._browse_target_data).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 集成方法选择区域
        method_frame = ttk.LabelFrame(parent, text="🎯 集成方法")
        method_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(method_frame, text="选择集成方法:").pack(anchor=tk.W, padx=5, pady=5)
        
        # 集成方法复选框
        for method in self.ensemble_methods:
            var = tk.BooleanVar(value=method in ['voting', 'stacking'])
            self.ensemble_methods_vars[method] = var
            ttk.Checkbutton(method_frame, text=method.title(), variable=var).pack(anchor=tk.W, padx=20, pady=2)
        
        # 数据策略选择区域
        strategy_frame = ttk.LabelFrame(parent, text="📋 数据策略")
        strategy_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(strategy_frame, text="选择数据策略:").pack(anchor=tk.W, padx=5, pady=5)
        
        # 数据策略复选框
        for strategy in self.data_strategies:
            var = tk.BooleanVar(value=strategy in ['unified', 'combined'])
            self.data_strategies_vars[strategy] = var
            ttk.Checkbutton(strategy_frame, text=strategy.title(), variable=var).pack(anchor=tk.W, padx=20, pady=2)
        
        # 智能模型选择区域
        selection_frame = ttk.LabelFrame(parent, text="🧠 智能模型选择")
        selection_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 选择策略
        ttk.Label(selection_frame, text="选择策略:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        strategy_combo = ttk.Combobox(selection_frame, textvariable=self.selection_strategy_var,
                                    values=self.selection_strategies, state="readonly", width=20)
        strategy_combo.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
        
        # 最大模型数
        ttk.Label(selection_frame, text="最大模型数:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Spinbox(selection_frame, from_=3, to=10, textvariable=self.max_models_var, width=10).grid(
            row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 多样性阈值
        ttk.Label(selection_frame, text="多样性阈值:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(selection_frame, from_=0.0, to=1.0, variable=self.diversity_threshold_var, 
                 orient=tk.HORIZONTAL, length=150).grid(row=2, column=1, sticky="ew", padx=5, pady=5)
        
        selection_frame.columnconfigure(1, weight=1)
        
        # 特征选择区域
        feature_frame = ttk.LabelFrame(parent, text="🔍 特征选择")
        feature_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 启用特征选择
        ttk.Checkbutton(feature_frame, text="启用特征选择", 
                       variable=self.feature_selection_var).pack(anchor=tk.W, padx=5, pady=5)
        
        # 特征选择方法
        ttk.Label(feature_frame, text="选择方法:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        method_combo = ttk.Combobox(feature_frame, textvariable=self.feature_selection_method_var,
                                  values=self.feature_selection_methods, state="readonly", width=15)
        method_combo.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
        
        # 特征数量
        ttk.Label(feature_frame, text="特征数量:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Spinbox(feature_frame, from_=5, to=50, textvariable=self.feature_k_var, width=10).grid(
            row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        feature_frame.columnconfigure(1, weight=1)
        
        # 其他选项
        options_frame = ttk.LabelFrame(parent, text="⚙️ 其他选项")
        options_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Checkbutton(options_frame, text="启用SHAP分析", 
                       variable=self.enable_shap_var).pack(anchor=tk.W, padx=5, pady=5)
        
        # 执行按钮区域
        execute_frame = ttk.LabelFrame(parent, text="🚀 执行集成学习")
        execute_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(execute_frame, text="🔄 加载数据源", 
                  command=self._load_data_sources).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(execute_frame, text="🤖 训练基础模型", 
                  command=self._train_base_models).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(execute_frame, text="🧠 智能模型选择", 
                  command=self._intelligent_model_selection).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(execute_frame, text="🔗 创建集成模型", 
                  command=self._create_ensemble_models).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(execute_frame, text="📊 评估集成模型", 
                  command=self._evaluate_ensemble_models).pack(fill=tk.X, padx=5, pady=2)
        
        # 进度条
        self.progress_widget = ProgressWidget(parent)
        self.progress_widget.pack(fill=tk.X, padx=5, pady=5)
    
    def _create_result_panel(self, parent):
        """创建结果显示面板"""
        # 创建notebook用于显示不同类型的结果
        self.result_notebook = ttk.Notebook(parent)
        self.result_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 图表显示选项卡
        self._create_chart_tab()
        
        # 结果表格选项卡
        self._create_table_tab()
        
        # 模型信息选项卡
        self._create_info_tab()
    
    def _create_chart_tab(self):
        """创建图表显示选项卡"""
        chart_frame = ttk.Frame(self.result_notebook)
        self.result_notebook.add(chart_frame, text="📈 可视化结果")
        
        # 图表控制工具栏
        toolbar_frame = ttk.Frame(chart_frame)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar_frame, text="📊 性能对比", 
                  command=self._plot_performance_comparison).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="📈 ROC曲线", 
                  command=self._plot_roc_curves).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="🎯 混淆矩阵", 
                  command=self._plot_confusion_matrices).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="🕸️ 雷达图", 
                  command=self._plot_radar_chart).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="💾 保存图表", 
                  command=self._save_current_chart).pack(side=tk.LEFT, padx=2)
        
        # 图表显示区域
        self.canvas_frame = ttk.Frame(chart_frame)
        self.canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def _create_table_tab(self):
        """创建结果表格选项卡"""
        table_frame = ttk.Frame(self.result_notebook)
        self.result_notebook.add(table_frame, text="📋 结果表格")
        
        # 创建数据表组件
        self.data_table = DataTableWidget(table_frame)
        self.data_table.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def _create_info_tab(self):
        """创建模型信息选项卡"""
        info_frame = ttk.Frame(self.result_notebook)
        self.result_notebook.add(info_frame, text="ℹ️ 模型信息")
        
        # 模型信息显示
        self.info_text = tk.Text(info_frame, wrap=tk.WORD, font=('Consolas', 10))
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

    def _add_mapping(self):
        """添加模型-数据映射"""
        dialog = MappingDialog(self.parent, "添加映射")
        result = dialog.show()

        if result:
            model_name, data_path = result
            if model_name and data_path:
                self.model_data_mapping[model_name] = data_path
                self._update_mapping_display()

    def _edit_mapping(self):
        """编辑模型-数据映射"""
        selection = self.mapping_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要编辑的映射")
            return

        idx = selection[0]
        mapping_text = self.mapping_listbox.get(idx)
        model_name = mapping_text.split(" -> ")[0]

        if model_name in self.model_data_mapping:
            dialog = MappingDialog(self.parent, "编辑映射", model_name, self.model_data_mapping[model_name])
            result = dialog.show()

            if result:
                new_model_name, new_data_path = result
                if new_model_name and new_data_path:
                    # 删除旧映射
                    if new_model_name != model_name:
                        del self.model_data_mapping[model_name]
                    # 添加新映射
                    self.model_data_mapping[new_model_name] = new_data_path
                    self._update_mapping_display()

    def _remove_mapping(self):
        """删除模型-数据映射"""
        selection = self.mapping_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的映射")
            return

        idx = selection[0]
        mapping_text = self.mapping_listbox.get(idx)
        model_name = mapping_text.split(" -> ")[0]

        if messagebox.askyesno("确认", f"确定要删除映射 '{model_name}' 吗？"):
            if model_name in self.model_data_mapping:
                del self.model_data_mapping[model_name]
                self._update_mapping_display()

    def _update_mapping_display(self):
        """更新映射显示"""
        self.mapping_listbox.delete(0, tk.END)
        for model_name, data_path in self.model_data_mapping.items():
            display_path = str(Path(data_path).name) if len(data_path) > 50 else data_path
            self.mapping_listbox.insert(tk.END, f"{model_name} -> {display_path}")

    def _import_config(self):
        """导入配置文件"""
        file_path = filedialog.askopenfilename(
            title="导入配置文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                if 'model_data_mapping' in config:
                    self.model_data_mapping = config['model_data_mapping']
                    self._update_mapping_display()

                if 'target_data_path' in config:
                    self.target_path_var.set(config['target_data_path'])

                messagebox.showinfo("成功", "配置文件导入成功")

            except Exception as e:
                messagebox.showerror("错误", f"导入配置文件失败: {e}")

    def _export_config(self):
        """导出配置文件"""
        if not self.model_data_mapping:
            messagebox.showwarning("警告", "没有配置可导出")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出配置文件",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                config = {
                    'model_data_mapping': self.model_data_mapping,
                    'target_data_path': self.target_path_var.get()
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("成功", f"配置文件已导出到: {file_path}")

            except Exception as e:
                messagebox.showerror("错误", f"导出配置文件失败: {e}")

    def _browse_target_data(self):
        """浏览目标数据文件"""
        file_path = filedialog.askopenfilename(
            title="选择目标数据文件",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Excel files", "*.xlsx *.xls"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.target_path_var.set(file_path)

    def _load_data_sources(self):
        """加载数据源"""
        if not self.model_data_mapping:
            messagebox.showwarning("警告", "请先配置模型-数据映射")
            return

        def load_task():
            try:
                self.progress_widget.start("正在加载数据源...")

                # 创建多数据源集成学习器
                self.ensemble_learner = MultiDataEnsemble(
                    model_data_mapping=self.model_data_mapping,
                    feature_selection=self.feature_selection_var.get(),
                    feature_selection_method=self.feature_selection_method_var.get(),
                    k=self.feature_k_var.get() if self.feature_selection_var.get() else None
                )

                # 加载数据
                datasets = self.ensemble_learner.load_and_prepare_data()

                if not datasets:
                    raise ValueError("没有成功加载任何数据源")

                # 更新界面显示
                self.parent.after(0, lambda: self._update_data_info(datasets))

                self.progress_widget.complete(f"成功加载 {len(datasets)} 个数据源")

            except Exception as e:
                self.logger.error(f"加载数据源失败: {e}")
                self.progress_widget.error(f"加载失败: {e}")
                self.parent.after(0, lambda: messagebox.showerror("错误", f"加载数据源失败: {e}"))

        # 在后台线程中执行
        threading.Thread(target=load_task, daemon=True).start()

    def _update_data_info(self, datasets):
        """更新数据信息显示"""
        info_text = "=== 数据源加载信息 ===\n\n"

        for dataset_name, dataset in datasets.items():
            info_text += f"数据源: {dataset_name}\n"
            info_text += f"  训练集形状: {dataset['X_train'].shape}\n"
            info_text += f"  测试集形状: {dataset['X_test'].shape}\n"
            info_text += f"  特征数量: {dataset['X_train'].shape[1]}\n"

            if hasattr(dataset['X_train'], 'columns'):
                feature_names = list(dataset['X_train'].columns)[:5]
                info_text += f"  特征示例: {', '.join(feature_names)}{'...' if len(dataset['X_train'].columns) > 5 else ''}\n"

            info_text += "\n"

        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, info_text)

    def _train_base_models(self):
        """训练基础模型"""
        if self.ensemble_learner is None:
            messagebox.showwarning("警告", "请先加载数据源")
            return

        def train_task():
            try:
                self.is_training = True
                self.progress_widget.start("正在训练基础模型...")

                # 训练基础模型
                self.ensemble_learner.train_base_models(
                    enable_shap=self.enable_shap_var.get()
                )

                self.training_completed = True

                # 更新界面显示
                self.parent.after(0, self._update_training_info)

                self.progress_widget.complete("基础模型训练完成")

            except Exception as e:
                self.logger.error(f"训练基础模型失败: {e}")
                self.progress_widget.error(f"训练失败: {e}")
                self.parent.after(0, lambda: messagebox.showerror("错误", f"训练基础模型失败: {e}"))
            finally:
                self.is_training = False

        # 在后台线程中执行
        threading.Thread(target=train_task, daemon=True).start()

    def _update_training_info(self):
        """更新训练信息显示"""
        if not self.ensemble_learner or not self.ensemble_learner.trained_models:
            return

        info_text = "=== 基础模型训练信息 ===\n\n"

        total_models = 0
        for dataset_name, models in self.ensemble_learner.trained_models.items():
            info_text += f"数据源: {dataset_name}\n"
            info_text += f"  训练模型数: {len(models)}\n"

            # 显示模型性能
            if dataset_name in self.ensemble_learner.model_performance:
                performance = self.ensemble_learner.model_performance[dataset_name]
                for model_key, metrics in performance.items():
                    model_name = model_key.split('_')[-1]
                    accuracy = metrics.get('accuracy', 0)
                    auc = metrics.get('auc', 0)
                    info_text += f"    {model_name}: 准确率={accuracy:.4f}, AUC={auc:.4f}\n"

            info_text += "\n"
            total_models += len(models)

        info_text += f"总计训练模型数: {total_models}\n"

        # 更新显示
        current_text = self.info_text.get(1.0, tk.END)
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, current_text + "\n" + info_text)

    def _intelligent_model_selection(self):
        """智能模型选择"""
        if not self.training_completed:
            messagebox.showwarning("警告", "请先完成基础模型训练")
            return

        try:
            self.progress_widget.start("正在进行智能模型选择...")

            # 执行智能模型选择
            selected_models = self.ensemble_learner.intelligent_model_selection(
                selection_strategy=self.selection_strategy_var.get(),
                max_models=self.max_models_var.get(),
                diversity_threshold=self.diversity_threshold_var.get()
            )

            # 更新显示
            self._update_selection_info(selected_models)

            self.progress_widget.complete(f"智能选择了 {len(selected_models)} 个基模型")

        except Exception as e:
            self.logger.error(f"智能模型选择失败: {e}")
            self.progress_widget.error(f"选择失败: {e}")
            messagebox.showerror("错误", f"智能模型选择失败: {e}")

    def _update_selection_info(self, selected_models):
        """更新模型选择信息显示"""
        info_text = "\n=== 智能模型选择结果 ===\n\n"
        info_text += f"选择策略: {self.selection_strategy_var.get()}\n"
        info_text += f"最大模型数: {self.max_models_var.get()}\n"
        info_text += f"多样性阈值: {self.diversity_threshold_var.get():.2f}\n\n"
        info_text += f"选中的基模型 ({len(selected_models)} 个):\n"

        for i, model_key in enumerate(selected_models, 1):
            info_text += f"  {i}. {model_key}\n"

        # 追加到现有文本
        current_text = self.info_text.get(1.0, tk.END)
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, current_text + info_text)

    def _create_ensemble_models(self):
        """创建集成模型"""
        if not self.ensemble_learner or not self.ensemble_learner.selected_base_models:
            messagebox.showwarning("警告", "请先完成智能模型选择")
            return

        # 获取选中的集成方法和数据策略
        selected_methods = [method for method, var in self.ensemble_methods_vars.items() if var.get()]
        selected_strategies = [strategy for strategy, var in self.data_strategies_vars.items() if var.get()]

        if not selected_methods:
            messagebox.showwarning("警告", "请至少选择一种集成方法")
            return

        if not selected_strategies:
            messagebox.showwarning("警告", "请至少选择一种数据策略")
            return

        def create_task():
            try:
                self.progress_widget.start("正在创建集成模型...")

                # 这里需要实现集成模型创建逻辑
                # 由于时间限制，这里提供基本框架
                ensemble_results = {}

                for method in selected_methods:
                    for strategy in selected_strategies:
                        key = f"{method}_{strategy}"
                        # 创建集成模型的具体实现
                        ensemble_results[key] = {
                            'method': method,
                            'strategy': strategy,
                            'status': 'created'
                        }

                self.ensemble_results = ensemble_results

                # 更新界面显示
                self.parent.after(0, lambda: self._update_ensemble_info(ensemble_results))

                self.progress_widget.complete(f"成功创建 {len(ensemble_results)} 个集成模型")

            except Exception as e:
                self.logger.error(f"创建集成模型失败: {e}")
                self.progress_widget.error(f"创建失败: {e}")
                self.parent.after(0, lambda: messagebox.showerror("错误", f"创建集成模型失败: {e}"))

        # 在后台线程中执行
        threading.Thread(target=create_task, daemon=True).start()

    def _update_ensemble_info(self, ensemble_results):
        """更新集成模型信息显示"""
        info_text = "\n=== 集成模型创建结果 ===\n\n"
        info_text += f"创建的集成模型 ({len(ensemble_results)} 个):\n"

        for key, result in ensemble_results.items():
            method = result['method']
            strategy = result['strategy']
            status = result['status']
            info_text += f"  {key}: {method.title()} + {strategy.title()} ({status})\n"

        # 追加到现有文本
        current_text = self.info_text.get(1.0, tk.END)
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, current_text + info_text)

    def _evaluate_ensemble_models(self):
        """评估集成模型"""
        if not self.ensemble_results:
            messagebox.showwarning("警告", "请先创建集成模型")
            return

        messagebox.showinfo("提示", "集成模型评估功能正在开发中...")

    def _plot_performance_comparison(self):
        """绘制性能对比图"""
        if not self.ensemble_results:
            messagebox.showwarning("警告", "没有可显示的结果")
            return

        messagebox.showinfo("提示", "性能对比图功能正在开发中...")

    def _plot_roc_curves(self):
        """绘制ROC曲线"""
        messagebox.showinfo("提示", "ROC曲线功能正在开发中...")

    def _plot_confusion_matrices(self):
        """绘制混淆矩阵"""
        messagebox.showinfo("提示", "混淆矩阵功能正在开发中...")

    def _plot_radar_chart(self):
        """绘制雷达图"""
        messagebox.showinfo("提示", "雷达图功能正在开发中...")

    def _save_current_chart(self):
        """保存当前图表"""
        if self.current_figure is None:
            messagebox.showwarning("警告", "没有可保存的图表")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存图表",
            defaultextension=".png",
            filetypes=[
                ("PNG files", "*.png"),
                ("PDF files", "*.pdf"),
                ("SVG files", "*.svg"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                self.current_figure.savefig(file_path, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"图表已保存到: {file_path}")
                self.logger.info(f"图表已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存图表失败: {e}")
                self.logger.error(f"保存图表失败: {e}")


class MappingDialog:
    """模型-数据映射对话框"""

    def __init__(self, parent, title, model_name="", data_path=""):
        """初始化对话框"""
        self.parent = parent
        self.result = None

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x200")
        self.dialog.resizable(False, False)

        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self._center_dialog()

        # 创建界面
        self._create_interface(model_name, data_path)

    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()

        # 获取对话框大小
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()

        # 获取父窗口位置和大小
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        # 计算居中位置
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2

        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

    def _create_interface(self, model_name, data_path):
        """创建对话框界面"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 模型名称
        ttk.Label(main_frame, text="模型名称:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.model_name_var = tk.StringVar(value=model_name)
        ttk.Entry(main_frame, textvariable=self.model_name_var, width=40).grid(
            row=0, column=1, columnspan=2, sticky="ew", padx=(10, 0), pady=5)

        # 数据路径
        ttk.Label(main_frame, text="数据路径:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.data_path_var = tk.StringVar(value=data_path)
        ttk.Entry(main_frame, textvariable=self.data_path_var, width=30).grid(
            row=1, column=1, sticky="ew", padx=(10, 5), pady=5)
        ttk.Button(main_frame, text="浏览", command=self._browse_file).grid(
            row=1, column=2, padx=(0, 0), pady=5)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=20)

        ttk.Button(button_frame, text="确定", command=self._ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self._cancel_clicked).pack(side=tk.LEFT, padx=5)

        main_frame.columnconfigure(1, weight=1)

        # 绑定回车键
        self.dialog.bind('<Return>', lambda e: self._ok_clicked())
        self.dialog.bind('<Escape>', lambda e: self._cancel_clicked())

    def _browse_file(self):
        """浏览文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Excel files", "*.xlsx *.xls"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.data_path_var.set(file_path)

    def _ok_clicked(self):
        """确定按钮点击"""
        model_name = self.model_name_var.get().strip()
        data_path = self.data_path_var.get().strip()

        if not model_name:
            messagebox.showwarning("警告", "请输入模型名称")
            return

        if not data_path:
            messagebox.showwarning("警告", "请选择数据文件")
            return

        self.result = (model_name, data_path)
        self.dialog.destroy()

    def _cancel_clicked(self):
        """取消按钮点击"""
        self.result = None
        self.dialog.destroy()

    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result
