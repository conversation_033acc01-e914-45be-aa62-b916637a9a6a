#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成GUI模块
提供性能报告生成的图形化界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import webbrowser
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

try:
    from ...core.event_manager import get_event_manager
    from ....utils.report_generator import get_report_generator
    from ....core.model_manager import get_model_manager
    from ....core.session_manager import get_session_manager
    from ....utils.error_handler import get_error_handler
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))

    try:
        from gui.core.event_manager import get_event_manager
        from utils.report_generator import get_report_generator
        from core.model_manager import get_model_manager
        from core.session_manager import get_session_manager
        from utils.error_handler import get_error_handler
    except ImportError:
        # 如果仍然失败，创建简化版本
        get_event_manager = None
        get_report_generator = None
        get_model_manager = None
        get_session_manager = None
        get_error_handler = None


class ReportGeneratorGUI:
    """报告生成GUI"""
    
    def __init__(self, parent):
        """初始化报告生成GUI"""
        self.parent = parent
        try:
            self.report_generator = get_report_generator()
            self.event_manager = get_event_manager()
            self.model_manager = get_model_manager()
            self.session_manager = get_session_manager()
            self.error_handler = get_error_handler()
        except:
            # 如果导入失败，创建简化版本
            self.report_generator = None
            self.event_manager = None
            self.model_manager = None
            self.session_manager = None
            self.error_handler = None

        # 数据存储
        self.model_results = {}
        self.selected_models = {}

        # GUI组件
        self.main_frame = None
        self.frame = None
        
        # 控制变量
        self.report_type_var = tk.StringVar(value="single_model")
        self.model_var = tk.StringVar()
        self.session_var = tk.StringVar()
        self.output_path_var = tk.StringVar()
        self.status_var = tk.StringVar(value="就绪")
        
        # 创建界面
        if self.report_generator:
            self._create_interface()

            # 注册事件监听
            if self.event_manager:
                self._register_events()

            # 初始化数据
            self._refresh_data()
        else:
            self._create_simple_interface()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.frame = self.main_frame  # 保持兼容性
        
        # 标题
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(title_frame, text="性能报告生成器", font=('Arial', 16, 'bold')).pack()
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self._create_report_config_tab()
        self._create_preview_tab()

    def _create_simple_interface(self):
        """创建简化界面（当报告生成器不可用时）"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 显示不可用消息
        message_frame = ttk.Frame(self.main_frame)
        message_frame.pack(expand=True, fill='both')

        ttk.Label(message_frame, text="报告生成功能暂时不可用",
                 font=('Arial', 16, 'bold')).pack(pady=20)

        ttk.Label(message_frame, text="原因：报告生成器模块导入失败\n\n" +
                                    "功能说明：\n" +
                                    "• 生成详细的模型性能报告\n" +
                                    "• 支持单模型和多模型比较报告\n" +
                                    "• 导出HTML格式的专业报告\n" +
                                    "• 包含图表和统计分析\n\n" +
                                    "请检查系统配置或联系开发者",
                 justify='left').pack(pady=10)
    
    def _create_report_config_tab(self):
        """创建报告配置选项卡"""
        config_tab = ttk.Frame(self.notebook)
        self.notebook.add(config_tab, text="📝 报告配置")
        
        # 报告类型选择
        type_frame = ttk.LabelFrame(config_tab, text="报告类型")
        type_frame.pack(fill=tk.X, padx=10, pady=10)
        
        report_types = [
            ("单模型报告", "single_model"),
            ("模型比较报告", "model_comparison"),
            ("会话报告", "session_report"),
            ("训练报告", "training_report"),
            ("验证报告", "validation_report"),
            ("综合报告", "comprehensive_report")
        ]
        
        # 使用两行布局
        for i, (text, value) in enumerate(report_types):
            row = i // 3
            col = i % 3
            ttk.Radiobutton(type_frame, text=text, variable=self.report_type_var,
                           value=value, command=self._on_report_type_change).grid(
                row=row, column=col, padx=20, pady=10, sticky=tk.W)
        
        # 配置选项
        self._create_config_options(config_tab)
        
        # 输出设置
        self._create_output_settings(config_tab)
        
        # 生成按钮
        self._create_generation_controls(config_tab)
    
    def _create_config_options(self, parent):
        """创建配置选项"""
        self.config_frame = ttk.LabelFrame(parent, text="配置选项")
        self.config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 单模型配置
        self.single_model_frame = ttk.Frame(self.config_frame)
        
        ttk.Label(self.single_model_frame, text="选择模型:").pack(side=tk.LEFT, padx=(10, 5))
        self.model_combo = ttk.Combobox(self.single_model_frame, textvariable=self.model_var,
                                       state="readonly", width=20)
        self.model_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # 模型比较配置
        self.comparison_frame = ttk.Frame(self.config_frame)
        
        # 左侧：可用模型
        left_frame = ttk.Frame(self.comparison_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 5))
        
        ttk.Label(left_frame, text="可用模型:").pack(anchor=tk.W)
        self.available_listbox = tk.Listbox(left_frame, selectmode=tk.MULTIPLE, height=8)
        self.available_listbox.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # 中间：操作按钮
        middle_frame = ttk.Frame(self.comparison_frame)
        middle_frame.pack(side=tk.LEFT, padx=10)
        
        ttk.Button(middle_frame, text="➡️", command=self._add_selected_models).pack(pady=2)
        ttk.Button(middle_frame, text="⬅️", command=self._remove_selected_models).pack(pady=2)
        
        # 右侧：选中模型
        right_frame = ttk.Frame(self.comparison_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 10))
        
        ttk.Label(right_frame, text="选中模型:").pack(anchor=tk.W)
        self.selected_listbox = tk.Listbox(right_frame, selectmode=tk.MULTIPLE, height=8)
        self.selected_listbox.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # 会话报告配置
        self.session_frame = ttk.Frame(self.config_frame)
        
        ttk.Label(self.session_frame, text="选择会话:").pack(side=tk.LEFT, padx=(10, 5))
        self.session_combo = ttk.Combobox(self.session_frame, textvariable=self.session_var,
                                         state="readonly", width=30)
        self.session_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # 默认显示单模型配置
        self._show_config_frame("single_model")
    
    def _create_output_settings(self, parent):
        """创建输出设置"""
        output_frame = ttk.LabelFrame(parent, text="输出设置")
        output_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 输出路径
        path_frame = ttk.Frame(output_frame)
        path_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(path_frame, text="输出路径:").pack(side=tk.LEFT)
        ttk.Entry(path_frame, textvariable=self.output_path_var, width=50).pack(
            side=tk.LEFT, padx=10, fill=tk.X, expand=True)
        ttk.Button(path_frame, text="浏览...", command=self._browse_output_path).pack(side=tk.RIGHT)
        
        # 输出选项
        options_frame = ttk.Frame(output_frame)
        options_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.auto_open_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="生成后自动打开报告", 
                       variable=self.auto_open_var).pack(side=tk.LEFT)
        
        self.include_charts_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="包含图表", 
                       variable=self.include_charts_var).pack(side=tk.LEFT, padx=(20, 0))
    
    def _create_generation_controls(self, parent):
        """创建生成控制"""
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, padx=10, pady=20)
        
        # 生成按钮
        ttk.Button(control_frame, text="📊 生成报告",
                  command=self._generate_report, style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(control_frame, text="🔄 刷新数据",
                  command=self._refresh_data).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(control_frame, text="👁️ 预览报告",
                  command=self._preview_report).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(control_frame, text="🤖 自动生成所有报告",
                  command=self._auto_generate_all_reports).pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var,
                                          mode='determinate', length=200)
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 状态栏
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(
            side=tk.LEFT, padx=(5, 0))
    
    def _create_preview_tab(self):
        """创建预览选项卡"""
        preview_tab = ttk.Frame(self.notebook)
        self.notebook.add(preview_tab, text="👁️ 报告预览")
        
        # 预览控制
        control_frame = ttk.Frame(preview_tab)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(control_frame, text="🔄 刷新预览", 
                  command=self._refresh_preview).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="🌐 在浏览器中打开", 
                  command=self._open_in_browser).pack(side=tk.LEFT, padx=(0, 10))
        
        # 预览区域
        preview_frame = ttk.LabelFrame(preview_tab, text="报告预览")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 使用Text控件显示HTML预览（简化版）
        self.preview_text = tk.Text(preview_frame, wrap=tk.WORD, font=('Consolas', 10))
        preview_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, 
                                        command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scrollbar.set)
        
        self.preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 初始预览内容
        self.preview_text.insert(1.0, "请配置报告参数后点击'刷新预览'查看报告内容")
        
        # 存储生成的HTML内容
        self.current_html = ""
    
    def _register_events(self):
        """注册事件监听"""
        self.event_manager.subscribe('model_trained', self._on_model_trained)
        self.event_manager.subscribe('session_created', self._on_session_created)
    
    def _on_model_trained(self, event_data):
        """模型训练完成事件处理"""
        self._refresh_data()
    
    def _on_session_created(self, event_data):
        """会话创建事件处理"""
        self._refresh_data()
    
    def _refresh_data(self):
        """刷新数据"""
        try:
            # 刷新模型列表
            self._refresh_models()
            
            # 刷新会话列表
            self._refresh_sessions()
            
            self.status_var.set("数据刷新完成")
            
        except Exception as e:
            self.error_handler.handle_error(e, "刷新数据")
    
    def _refresh_models(self):
        """刷新模型列表"""
        # 获取所有可用模型
        all_models = set()
        
        # 从模型管理器获取
        manager_models = self.model_manager.list_models()
        for model_name in manager_models:
            result = self.model_manager.get_model_result(model_name)
            if result:
                self.model_results[model_name] = result
                all_models.add(model_name)
        
        # 更新下拉框和列表框
        model_list = sorted(all_models)
        self.model_combo['values'] = model_list
        
        # 更新可用模型列表框
        self.available_listbox.delete(0, tk.END)
        for model_name in model_list:
            self.available_listbox.insert(tk.END, model_name)
        
        if model_list and not self.model_var.get():
            self.model_var.set(model_list[0])
    
    def _refresh_sessions(self):
        """刷新会话列表"""
        sessions = self.session_manager.list_sessions()
        session_list = [f"{s['session_name']} ({s['session_id']})" for s in sessions]
        
        self.session_combo['values'] = session_list
        
        if session_list and not self.session_var.get():
            self.session_var.set(session_list[0])
    
    def _on_report_type_change(self):
        """报告类型改变事件处理"""
        report_type = self.report_type_var.get()
        self._show_config_frame(report_type)
    
    def _show_config_frame(self, report_type: str):
        """显示对应的配置框架"""
        # 隐藏所有配置框架
        self.single_model_frame.pack_forget()
        self.comparison_frame.pack_forget()
        self.session_frame.pack_forget()
        
        # 显示对应的配置框架
        if report_type == "single_model":
            self.single_model_frame.pack(fill=tk.X, padx=10, pady=10)
        elif report_type == "model_comparison":
            self.comparison_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        elif report_type == "session_report":
            self.session_frame.pack(fill=tk.X, padx=10, pady=10)
        elif report_type in ["training_report", "validation_report", "comprehensive_report"]:
            # 这些报告类型使用会话数据
            self.session_frame.pack(fill=tk.X, padx=10, pady=10)
    
    def _add_selected_models(self):
        """添加选中的模型"""
        selected_indices = self.available_listbox.curselection()
        for index in selected_indices:
            model_name = self.available_listbox.get(index)
            if model_name not in [self.selected_listbox.get(i) for i in range(self.selected_listbox.size())]:
                self.selected_listbox.insert(tk.END, model_name)
    
    def _remove_selected_models(self):
        """移除选中的模型"""
        selected_indices = list(self.selected_listbox.curselection())
        selected_indices.reverse()
        for index in selected_indices:
            self.selected_listbox.delete(index)
    
    def _browse_output_path(self):
        """浏览输出路径"""
        filename = filedialog.asksaveasfilename(
            title="选择报告保存位置",
            defaultextension=".html",
            filetypes=[("HTML文件", "*.html"), ("所有文件", "*.*")]
        )
        if filename:
            self.output_path_var.set(filename)
    
    def _generate_report(self):
        """生成报告"""
        report_type = self.report_type_var.get()
        output_path = self.output_path_var.get()
        
        if not output_path:
            messagebox.showwarning("警告", "请选择输出路径")
            return
        
        # 在后台线程中生成报告
        threading.Thread(target=self._perform_report_generation, 
                        args=(report_type, output_path), daemon=True).start()
    
    def _perform_report_generation(self, report_type: str, output_path: str):
        """执行报告生成"""
        try:
            self.status_var.set("正在生成报告...")
            self.progress_var.set(20)
            
            if report_type == "single_model":
                self._generate_single_model_report(output_path)
            elif report_type == "model_comparison":
                self._generate_comparison_report(output_path)
            elif report_type == "session_report":
                self._generate_session_report(output_path)
            elif report_type == "training_report":
                self._generate_training_report(output_path)
            elif report_type == "validation_report":
                self._generate_validation_report(output_path)
            elif report_type == "comprehensive_report":
                self._generate_comprehensive_report(output_path)
            
            self.progress_var.set(100)
            self.status_var.set("报告生成完成")
            
            # 自动打开报告
            if self.auto_open_var.get():
                webbrowser.open(f"file://{Path(output_path).absolute()}")
            
            messagebox.showinfo("成功", f"报告已生成并保存到:\n{output_path}")
            
            # 重置进度条
            self.parent.after(2000, lambda: self.progress_var.set(0))
            
        except Exception as e:
            self.error_handler.handle_error(e, "生成报告")
            self.status_var.set("报告生成失败")
            self.progress_var.set(0)
            messagebox.showerror("错误", f"报告生成失败: {e}")
    
    def _generate_single_model_report(self, output_path: str):
        """生成单模型报告"""
        model_name = self.model_var.get()
        if not model_name:
            raise ValueError("请选择模型")
        
        if model_name not in self.model_results:
            raise ValueError(f"无法获取模型 {model_name} 的结果")
        
        model_result = self.model_results[model_name]
        
        self.progress_var.set(50)
        
        html_content = self.report_generator.generate_single_model_report(
            model_name, model_result, output_path
        )
        
        self.current_html = html_content
    
    def _generate_comparison_report(self, output_path: str):
        """生成模型比较报告"""
        selected_models = [self.selected_listbox.get(i) for i in range(self.selected_listbox.size())]
        
        if len(selected_models) < 2:
            raise ValueError("请至少选择2个模型进行比较")
        
        comparison_data = {}
        for model_name in selected_models:
            if model_name in self.model_results:
                comparison_data[model_name] = self.model_results[model_name]
        
        if len(comparison_data) < 2:
            raise ValueError("无法获取足够的模型数据")
        
        self.progress_var.set(50)
        
        html_content = self.report_generator.generate_comparison_report(
            comparison_data, output_path
        )
        
        self.current_html = html_content
    
    def _generate_session_report(self, output_path: str):
        """生成会话报告"""
        session_info = self.session_var.get()
        if not session_info:
            raise ValueError("请选择会话")
        
        # 解析会话ID
        session_id = session_info.split('(')[-1].rstrip(')')
        session = self.session_manager.load_session(session_id)
        
        if not session:
            raise ValueError(f"无法加载会话: {session_id}")
        
        session_data = session.get_summary()
        
        self.progress_var.set(50)
        
        html_content = self.report_generator.generate_session_report(
            session_data, output_path
        )
        
        self.current_html = html_content

    def _generate_training_report(self, output_path: str):
        """生成训练报告"""
        session_info = self.session_var.get()
        if not session_info:
            raise ValueError("请选择会话")

        # 解析会话ID
        session_id = session_info.split('(')[-1].rstrip(')')
        session = self.session_manager.load_session(session_id)

        if not session:
            raise ValueError(f"无法加载会话: {session_id}")

        # 构造训练数据
        training_data = {
            'task_name': session.session_name,
            'start_time': session.created_time,
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_time': 0,  # 需要从会话中计算
            'model_count': len(session.results),
            'dataset_size': '未知',
            'parameters': session.config if hasattr(session, 'config') else {},
            'training_logs': []
        }

        self.progress_var.set(50)

        html_content = self.report_generator.generate_training_report(
            training_data, output_path
        )

        self.current_html = html_content

    def _generate_validation_report(self, output_path: str):
        """生成验证报告"""
        session_info = self.session_var.get()
        if not session_info:
            raise ValueError("请选择会话")

        # 解析会话ID
        session_id = session_info.split('(')[-1].rstrip(')')
        session = self.session_manager.load_session(session_id)

        if not session:
            raise ValueError(f"无法加载会话: {session_id}")

        # 构造验证数据
        validation_data = {
            'task_name': session.session_name,
            'validation_type': '交叉验证',
            'validation_dataset': '测试集',
            'sample_count': 0,  # 需要从会话中获取
            'validation_time': 0,
            'results': {}
        }

        # 从会话结果中提取验证指标
        if session.results:
            first_result = list(session.results.values())[0]
            validation_data['results'] = {
                'accuracy': first_result.get('accuracy', 0),
                'precision': first_result.get('precision', 0),
                'recall': first_result.get('recall', 0),
                'f1_score': first_result.get('f1_score', 0),
                'auc': first_result.get('auc', 0)
            }

        self.progress_var.set(50)

        html_content = self.report_generator.generate_validation_report(
            validation_data, output_path
        )

        self.current_html = html_content

    def _generate_comprehensive_report(self, output_path: str):
        """生成综合报告"""
        session_info = self.session_var.get()
        if not session_info:
            raise ValueError("请选择会话")

        # 解析会话ID
        session_id = session_info.split('(')[-1].rstrip(')')
        session = self.session_manager.load_session(session_id)

        if not session:
            raise ValueError(f"无法加载会话: {session_id}")

        # 构造项目数据
        project_data = {
            'project_name': session.session_name,
            'created_time': session.created_time,
            'dataset_name': '未知',
            'problem_type': '分类',
            'model_results': session.results,
            'total_training_time': 0,
            'session_data': session.get_summary(),
            'training_data': {
                'task_name': session.session_name,
                'model_count': len(session.results)
            },
            'validation_data': {
                'task_name': session.session_name,
                'results': {}
            }
        }

        self.progress_var.set(50)

        html_content = self.report_generator.generate_comprehensive_report(
            project_data, output_path
        )

        self.current_html = html_content

    def _auto_generate_all_reports(self):
        """自动生成所有报告"""
        output_dir = filedialog.askdirectory(title="选择报告输出目录")
        if not output_dir:
            return

        # 在后台线程中生成所有报告
        threading.Thread(target=self._perform_auto_generation,
                        args=(output_dir,), daemon=True).start()

    def _perform_auto_generation(self, output_dir: str):
        """执行自动生成所有报告"""
        try:
            self.status_var.set("正在自动生成所有报告...")
            self.progress_var.set(10)

            # 收集所有数据
            all_data = {
                'model_results': self.model_results,
                'project_name': '机器学习项目'
            }

            # 如果有选中的会话，添加会话数据
            session_info = self.session_var.get()
            if session_info:
                session_id = session_info.split('(')[-1].rstrip(')')
                session = self.session_manager.load_session(session_id)
                if session:
                    all_data['session_data'] = session.get_summary()
                    all_data['training_data'] = {
                        'task_name': session.session_name,
                        'model_count': len(session.results)
                    }
                    all_data['validation_data'] = {
                        'task_name': session.session_name,
                        'results': {}
                    }

            self.progress_var.set(30)

            # 自动生成所有报告
            generated_reports = self.report_generator.auto_generate_reports(all_data, output_dir)

            self.progress_var.set(100)
            self.status_var.set(f"成功生成 {len(generated_reports)} 个报告")

            messagebox.showinfo("成功",
                              f"成功生成 {len(generated_reports)} 个报告:\n" +
                              "\n".join([Path(p).name for p in generated_reports]))

            # 打开输出目录
            import subprocess
            import platform
            if platform.system() == "Windows":
                subprocess.run(["explorer", output_dir])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", output_dir])
            else:  # Linux
                subprocess.run(["xdg-open", output_dir])

            # 重置进度条
            self.parent.after(3000, lambda: self.progress_var.set(0))

        except Exception as e:
            self.error_handler.handle_error(e, "自动生成报告")
            self.status_var.set("自动生成报告失败")
            self.progress_var.set(0)
            messagebox.showerror("错误", f"自动生成报告失败: {e}")

    def _preview_report(self):
        """预览报告"""
        self._refresh_preview()
        self.notebook.select(1)  # 切换到预览选项卡
    
    def _refresh_preview(self):
        """刷新预览"""
        try:
            report_type = self.report_type_var.get()
            
            if report_type == "single_model":
                model_name = self.model_var.get()
                if model_name and model_name in self.model_results:
                    html_content = self.report_generator.generate_single_model_report(
                        model_name, self.model_results[model_name]
                    )
                    self.current_html = html_content
                    self._show_html_preview(html_content)
                else:
                    self.preview_text.delete(1.0, tk.END)
                    self.preview_text.insert(1.0, "请选择有效的模型")
            
            elif report_type == "model_comparison":
                selected_models = [self.selected_listbox.get(i) for i in range(self.selected_listbox.size())]
                if len(selected_models) >= 2:
                    comparison_data = {name: self.model_results[name] 
                                    for name in selected_models if name in self.model_results}
                    if len(comparison_data) >= 2:
                        html_content = self.report_generator.generate_comparison_report(comparison_data)
                        self.current_html = html_content
                        self._show_html_preview(html_content)
                    else:
                        self.preview_text.delete(1.0, tk.END)
                        self.preview_text.insert(1.0, "无法获取足够的模型数据")
                else:
                    self.preview_text.delete(1.0, tk.END)
                    self.preview_text.insert(1.0, "请至少选择2个模型进行比较")
            
            elif report_type == "session_report":
                session_info = self.session_var.get()
                if session_info:
                    session_id = session_info.split('(')[-1].rstrip(')')
                    session = self.session_manager.load_session(session_id)
                    if session:
                        session_data = session.get_summary()
                        html_content = self.report_generator.generate_session_report(session_data)
                        self.current_html = html_content
                        self._show_html_preview(html_content)
                    else:
                        self.preview_text.delete(1.0, tk.END)
                        self.preview_text.insert(1.0, "无法加载会话数据")
                else:
                    self.preview_text.delete(1.0, tk.END)
                    self.preview_text.insert(1.0, "请选择会话")

            elif report_type in ["training_report", "validation_report", "comprehensive_report"]:
                session_info = self.session_var.get()
                if session_info:
                    session_id = session_info.split('(')[-1].rstrip(')')
                    session = self.session_manager.load_session(session_id)
                    if session:
                        if report_type == "training_report":
                            training_data = {
                                'task_name': session.session_name,
                                'start_time': session.created_time,
                                'model_count': len(session.results),
                                'parameters': {}
                            }
                            html_content = self.report_generator.generate_training_report(training_data)
                        elif report_type == "validation_report":
                            validation_data = {
                                'task_name': session.session_name,
                                'validation_type': '交叉验证',
                                'results': {}
                            }
                            html_content = self.report_generator.generate_validation_report(validation_data)
                        else:  # comprehensive_report
                            project_data = {
                                'project_name': session.session_name,
                                'model_results': session.results,
                                'created_time': session.created_time
                            }
                            html_content = self.report_generator.generate_comprehensive_report(project_data)

                        self.current_html = html_content
                        self._show_html_preview(html_content)
                    else:
                        self.preview_text.delete(1.0, tk.END)
                        self.preview_text.insert(1.0, "无法加载会话数据")
                else:
                    self.preview_text.delete(1.0, tk.END)
                    self.preview_text.insert(1.0, "请选择会话")
            
        except Exception as e:
            self.error_handler.handle_error(e, "刷新预览")
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(1.0, f"预览生成失败: {e}")
    
    def _show_html_preview(self, html_content: str):
        """显示HTML预览（简化版）"""
        # 提取文本内容进行预览
        import re
        
        # 移除HTML标签，保留文本内容
        text_content = re.sub(r'<[^>]+>', '', html_content)
        text_content = re.sub(r'\s+', ' ', text_content).strip()
        
        # 格式化显示
        formatted_content = "=== 报告预览 ===\n\n"
        formatted_content += text_content[:2000]  # 限制预览长度
        if len(text_content) > 2000:
            formatted_content += "\n\n... (内容已截断，请生成完整报告查看)"
        
        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(1.0, formatted_content)
    
    def _open_in_browser(self):
        """在浏览器中打开预览"""
        if not self.current_html:
            messagebox.showwarning("警告", "请先生成预览")
            return
        
        # 创建临时文件
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
            f.write(self.current_html)
            temp_path = f.name
        
        # 在浏览器中打开
        webbrowser.open(f"file://{Path(temp_path).absolute()}")
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
