#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告调度器GUI模块
提供自动化报告调度的图形化界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from datetime import datetime, time
from typing import Dict, List, Optional, Any

try:
    from ....utils.report_scheduler import get_report_scheduler
    from ....core.model_manager import get_model_manager
    from ....core.session_manager import get_session_manager
    from ....utils.error_handler import get_error_handler
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))

    try:
        from utils.report_scheduler import get_report_scheduler
        from core.model_manager import get_model_manager
        from core.session_manager import get_session_manager
        from utils.error_handler import get_error_handler
    except ImportError:
        # 如果仍然失败，创建简化版本
        get_report_scheduler = None
        get_model_manager = None
        get_session_manager = None
        get_error_handler = None


class ReportSchedulerGUI:
    """报告调度器GUI"""
    
    def __init__(self, parent):
        """初始化报告调度器GUI"""
        self.parent = parent
        try:
            self.scheduler = get_report_scheduler()
            self.model_manager = get_model_manager()
            self.session_manager = get_session_manager()
            self.error_handler = get_error_handler()
        except:
            # 如果导入失败，创建简化版本
            self.scheduler = None
            self.model_manager = None
            self.session_manager = None
            self.error_handler = None

        # GUI组件
        self.main_frame = None
        self.frame = None
        
        # 控制变量
        self.scheduler_status_var = tk.StringVar(value="未启动")
        self.task_count_var = tk.StringVar(value="0")
        
        # 创建界面
        if self.scheduler:
            self._create_interface()
            self._refresh_status()
        else:
            self._create_simple_interface()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.frame = self.main_frame  # 保持兼容性
        
        # 标题
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(title_frame, text="自动化报告调度器", font=('Arial', 16, 'bold')).pack()
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self._create_status_tab()
        self._create_schedule_tab()
        self._create_tasks_tab()

    def _create_simple_interface(self):
        """创建简化界面（当调度器不可用时）"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 显示不可用消息
        message_frame = ttk.Frame(self.main_frame)
        message_frame.pack(expand=True, fill='both')

        ttk.Label(message_frame, text="报告调度器功能暂时不可用",
                 font=('Arial', 16, 'bold')).pack(pady=20)

        ttk.Label(message_frame, text="原因：报告调度器模块导入失败\n\n" +
                                    "功能说明：\n" +
                                    "• 自动定时生成报告\n" +
                                    "• 支持每日、每周调度\n" +
                                    "• 训练完成自动报告\n" +
                                    "• 任务管理和监控\n\n" +
                                    "请检查系统配置或联系开发者",
                 justify='left').pack(pady=10)
    
    def _create_status_tab(self):
        """创建状态选项卡"""
        status_tab = ttk.Frame(self.notebook)
        self.notebook.add(status_tab, text="📊 调度器状态")
        
        # 状态信息
        status_frame = ttk.LabelFrame(status_tab, text="调度器状态")
        status_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 状态显示
        info_frame = ttk.Frame(status_frame)
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(info_frame, text="运行状态:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(info_frame, textvariable=self.scheduler_status_var, 
                 foreground="green").grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(info_frame, text="任务数量:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(info_frame, textvariable=self.task_count_var).grid(row=1, column=1, sticky=tk.W)
        
        # 控制按钮
        control_frame = ttk.Frame(status_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(control_frame, text="🚀 启动调度器", 
                  command=self._start_scheduler).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="⏹️ 停止调度器", 
                  command=self._stop_scheduler).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="🔄 刷新状态", 
                  command=self._refresh_status).pack(side=tk.LEFT, padx=(0, 10))
    
    def _create_schedule_tab(self):
        """创建调度设置选项卡"""
        schedule_tab = ttk.Frame(self.notebook)
        self.notebook.add(schedule_tab, text="⏰ 调度设置")
        
        # 每日报告设置
        daily_frame = ttk.LabelFrame(schedule_tab, text="每日报告")
        daily_frame.pack(fill=tk.X, padx=10, pady=10)
        
        daily_config_frame = ttk.Frame(daily_frame)
        daily_config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(daily_config_frame, text="时间:").grid(row=0, column=0, sticky=tk.W)
        self.daily_time_var = tk.StringVar(value="09:00")
        ttk.Entry(daily_config_frame, textvariable=self.daily_time_var, width=10).grid(
            row=0, column=1, padx=10, sticky=tk.W)
        
        ttk.Button(daily_config_frame, text="添加每日报告任务", 
                  command=self._add_daily_task).grid(row=0, column=2, padx=10)
        
        # 每周报告设置
        weekly_frame = ttk.LabelFrame(schedule_tab, text="每周报告")
        weekly_frame.pack(fill=tk.X, padx=10, pady=10)
        
        weekly_config_frame = ttk.Frame(weekly_frame)
        weekly_config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(weekly_config_frame, text="星期:").grid(row=0, column=0, sticky=tk.W)
        self.weekly_day_var = tk.StringVar(value="monday")
        day_combo = ttk.Combobox(weekly_config_frame, textvariable=self.weekly_day_var,
                                values=["monday", "tuesday", "wednesday", "thursday", 
                                       "friday", "saturday", "sunday"], state="readonly", width=10)
        day_combo.grid(row=0, column=1, padx=10, sticky=tk.W)
        
        ttk.Label(weekly_config_frame, text="时间:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.weekly_time_var = tk.StringVar(value="10:00")
        ttk.Entry(weekly_config_frame, textvariable=self.weekly_time_var, width=10).grid(
            row=0, column=3, padx=10, sticky=tk.W)
        
        ttk.Button(weekly_config_frame, text="添加每周报告任务", 
                  command=self._add_weekly_task).grid(row=0, column=4, padx=10)
        
        # 报告类型选择
        report_types_frame = ttk.LabelFrame(schedule_tab, text="报告类型")
        report_types_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.report_type_vars = {}
        report_types = [
            ("综合报告", "comprehensive_report"),
            ("模型比较", "model_comparison"),
            ("训练报告", "training_report"),
            ("验证报告", "validation_report")
        ]
        
        types_config_frame = ttk.Frame(report_types_frame)
        types_config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        for i, (text, value) in enumerate(report_types):
            var = tk.BooleanVar(value=True if value == "comprehensive_report" else False)
            self.report_type_vars[value] = var
            ttk.Checkbutton(types_config_frame, text=text, variable=var).grid(
                row=i//2, column=i%2, sticky=tk.W, padx=20, pady=5)
    
    def _create_tasks_tab(self):
        """创建任务管理选项卡"""
        tasks_tab = ttk.Frame(self.notebook)
        self.notebook.add(tasks_tab, text="📋 任务管理")
        
        # 任务列表
        list_frame = ttk.LabelFrame(tasks_tab, text="当前任务")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建Treeview
        columns = ("任务ID", "类型", "时间", "状态")
        self.tasks_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)
        
        for col in columns:
            self.tasks_tree.heading(col, text=col)
            self.tasks_tree.column(col, width=150)
        
        # 滚动条
        tasks_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tasks_tree.yview)
        self.tasks_tree.configure(yscrollcommand=tasks_scrollbar.set)
        
        self.tasks_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        tasks_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 任务操作按钮
        task_control_frame = ttk.Frame(tasks_tab)
        task_control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(task_control_frame, text="🔄 刷新任务列表", 
                  command=self._refresh_tasks).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(task_control_frame, text="❌ 删除选中任务", 
                  command=self._delete_selected_task).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(task_control_frame, text="▶️ 立即执行", 
                  command=self._execute_task_now).pack(side=tk.LEFT, padx=(0, 10))
    
    def _start_scheduler(self):
        """启动调度器"""
        try:
            self.scheduler.start()
            self._refresh_status()
            messagebox.showinfo("成功", "调度器已启动")
        except Exception as e:
            messagebox.showerror("错误", f"启动调度器失败: {e}")
    
    def _stop_scheduler(self):
        """停止调度器"""
        try:
            self.scheduler.stop()
            self._refresh_status()
            messagebox.showinfo("成功", "调度器已停止")
        except Exception as e:
            messagebox.showerror("错误", f"停止调度器失败: {e}")
    
    def _refresh_status(self):
        """刷新状态"""
        try:
            status = self.scheduler.get_task_status()
            self.scheduler_status_var.set("运行中" if status['is_running'] else "已停止")
            self.task_count_var.set(str(status['task_count']))
        except Exception as e:
            self.error_handler.handle_error(e, "刷新调度器状态")
    
    def _get_selected_report_types(self) -> List[str]:
        """获取选中的报告类型"""
        selected_types = []
        for report_type, var in self.report_type_vars.items():
            if var.get():
                selected_types.append(report_type)
        return selected_types
    
    def _get_data_provider(self):
        """获取数据提供函数"""
        def data_provider():
            # 收集所有可用数据
            data = {}
            
            # 获取模型结果
            if self.model_manager:
                model_names = self.model_manager.list_models()
                model_results = {}
                for name in model_names:
                    result = self.model_manager.get_model_result(name)
                    if result:
                        model_results[name] = result
                data['model_results'] = model_results
            
            # 获取会话数据
            if self.session_manager:
                sessions = self.session_manager.list_sessions()
                if sessions:
                    latest_session = sessions[-1]  # 获取最新会话
                    session = self.session_manager.load_session(latest_session['session_id'])
                    if session:
                        data['session_data'] = session.get_summary()
                        data['training_data'] = {
                            'task_name': session.session_name,
                            'model_count': len(session.results),
                            'start_time': session.created_time
                        }
                        data['validation_data'] = {
                            'task_name': session.session_name,
                            'results': {}
                        }
            
            data['project_name'] = '机器学习项目'
            return data
        
        return data_provider
    
    def _add_daily_task(self):
        """添加每日任务"""
        try:
            time_str = self.daily_time_var.get()
            report_types = self._get_selected_report_types()
            data_provider = self._get_data_provider()
            
            if not report_types:
                messagebox.showwarning("警告", "请至少选择一种报告类型")
                return
            
            task_id = self.scheduler.schedule_daily_report(time_str, data_provider, report_types)
            messagebox.showinfo("成功", f"已添加每日报告任务: {task_id}")
            self._refresh_tasks()
            self._refresh_status()
            
        except Exception as e:
            messagebox.showerror("错误", f"添加每日任务失败: {e}")
    
    def _add_weekly_task(self):
        """添加每周任务"""
        try:
            day = self.weekly_day_var.get()
            time_str = self.weekly_time_var.get()
            report_types = self._get_selected_report_types()
            data_provider = self._get_data_provider()
            
            if not report_types:
                messagebox.showwarning("警告", "请至少选择一种报告类型")
                return
            
            task_id = self.scheduler.schedule_weekly_report(day, time_str, data_provider, report_types)
            messagebox.showinfo("成功", f"已添加每周报告任务: {task_id}")
            self._refresh_tasks()
            self._refresh_status()
            
        except Exception as e:
            messagebox.showerror("错误", f"添加每周任务失败: {e}")
    
    def _refresh_tasks(self):
        """刷新任务列表"""
        try:
            # 清空现有项目
            for item in self.tasks_tree.get_children():
                self.tasks_tree.delete(item)
            
            # 获取任务列表
            tasks = self.scheduler.list_tasks()
            
            for task_id, task_info in tasks.items():
                task_type = task_info.get('type', '未知')
                time_info = task_info.get('time', task_info.get('day', ''))
                status = "活跃"
                
                self.tasks_tree.insert("", tk.END, values=(task_id, task_type, time_info, status))
                
        except Exception as e:
            self.error_handler.handle_error(e, "刷新任务列表")
    
    def _delete_selected_task(self):
        """删除选中的任务"""
        selected_item = self.tasks_tree.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请选择要删除的任务")
            return
        
        task_id = self.tasks_tree.item(selected_item[0])['values'][0]
        
        if messagebox.askyesno("确认", f"确定要删除任务 {task_id} 吗？"):
            try:
                self.scheduler.remove_task(task_id)
                self._refresh_tasks()
                self._refresh_status()
                messagebox.showinfo("成功", "任务已删除")
            except Exception as e:
                messagebox.showerror("错误", f"删除任务失败: {e}")
    
    def _execute_task_now(self):
        """立即执行任务"""
        selected_item = self.tasks_tree.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请选择要执行的任务")
            return
        
        task_id = self.tasks_tree.item(selected_item[0])['values'][0]
        
        try:
            # 这里需要实现立即执行任务的逻辑
            messagebox.showinfo("提示", f"任务 {task_id} 已加入执行队列")
        except Exception as e:
            messagebox.showerror("错误", f"执行任务失败: {e}")
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
