#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话管理GUI模块
提供完整的训练会话管理界面，包括会话创建、保存、恢复、管理等功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import pandas as pd
import numpy as np
from pathlib import Path
import threading
import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import logging

try:
    from ...core.session_manager import get_session_manager, TrainingSession
    from ...utils.error_handler import get_error_handler
    from ..components.progress_widget import ProgressWidget
    from ..components.data_table import DataTableWidget
    from ..core.event_manager import get_event_manager
except ImportError:
    # 处理相对导入问题
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent
    sys.path.insert(0, str(project_root))
    
    from core.session_manager import get_session_manager, TrainingSession
    from utils.error_handler import get_error_handler
    from gui.components.progress_widget import ProgressWidget
    from gui.components.data_table import DataTableWidget
    from gui.core.event_manager import get_event_manager


class SessionManagementModule:
    """会话管理模块"""
    
    def __init__(self, parent):
        """初始化会话管理模块"""
        self.parent = parent
        self.logger = logging.getLogger(__name__)
        self.session_manager = get_session_manager()
        self.error_handler = get_error_handler()
        self.event_manager = get_event_manager()
        
        # GUI组件
        self.main_frame = None
        self.sessions_tree = None
        self.detail_text = None
        self.current_session_label = None
        
        # 控制变量
        self.selected_session_id = None
        self.search_var = tk.StringVar()
        self.filter_var = tk.StringVar(value="all")
        self.status_var = tk.StringVar(value="就绪")
        
        # 会话数据
        self.sessions_data = {}
        
        self.logger.info("会话管理模块初始化完成")
    
    def create_interface(self, parent_notebook):
        """创建会话管理界面"""
        # 创建主选项卡
        self.main_frame = ttk.Frame(parent_notebook)
        parent_notebook.add(self.main_frame, text="📁 会话管理")
        
        # 创建上下分割面板
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.VERTICAL)
        paned_window.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 上部：控制面板和会话列表
        top_frame = ttk.Frame(paned_window)
        paned_window.add(top_frame, weight=2)
        
        # 下部：会话详情
        bottom_frame = ttk.Frame(paned_window)
        paned_window.add(bottom_frame, weight=1)
        
        # 创建控制面板和会话列表
        self._create_control_and_list_panel(top_frame)
        
        # 创建会话详情面板
        self._create_detail_panel(bottom_frame)
        
        # 初始加载
        self._refresh_sessions()
        
        return self.main_frame
    
    def _create_control_and_list_panel(self, parent):
        """创建控制面板和会话列表"""
        # 左右分割
        paned_window = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：控制面板
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)
        
        # 右侧：会话列表
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=2)
        
        # 创建控制面板
        self._create_control_panel(left_frame)
        
        # 创建会话列表
        self._create_session_list(right_frame)
    
    def _create_control_panel(self, parent):
        """创建控制面板"""
        # 当前会话信息
        current_frame = ttk.LabelFrame(parent, text="📍 当前会话")
        current_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.current_session_label = ttk.Label(current_frame, text="无活动会话", 
                                             font=('Arial', 10, 'bold'))
        self.current_session_label.pack(padx=10, pady=10)
        
        # 会话操作区域
        operations_frame = ttk.LabelFrame(parent, text="🛠️ 会话操作")
        operations_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 创建会话
        ttk.Button(operations_frame, text="➕ 新建会话", 
                  command=self._create_new_session).pack(fill=tk.X, padx=5, pady=2)
        
        # 激活会话
        ttk.Button(operations_frame, text="🎯 激活会话", 
                  command=self._activate_session).pack(fill=tk.X, padx=5, pady=2)
        
        # 恢复会话
        ttk.Button(operations_frame, text="🔄 恢复到缓存", 
                  command=self._restore_session).pack(fill=tk.X, padx=5, pady=2)
        
        # 导出会话
        ttk.Button(operations_frame, text="📤 导出会话", 
                  command=self._export_session).pack(fill=tk.X, padx=5, pady=2)
        
        # 删除会话
        ttk.Button(operations_frame, text="🗑️ 删除会话", 
                  command=self._delete_session).pack(fill=tk.X, padx=5, pady=2)
        
        # 搜索和过滤区域
        search_frame = ttk.LabelFrame(parent, text="🔍 搜索过滤")
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 搜索框
        ttk.Label(search_frame, text="搜索:").pack(anchor=tk.W, padx=5, pady=2)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.pack(fill=tk.X, padx=5, pady=2)
        search_entry.bind('<KeyRelease>', self._on_search_change)
        
        # 状态过滤
        ttk.Label(search_frame, text="状态过滤:").pack(anchor=tk.W, padx=5, pady=2)
        filter_combo = ttk.Combobox(search_frame, textvariable=self.filter_var,
                                  values=["all", "created", "running", "completed", "failed"],
                                  state="readonly")
        filter_combo.pack(fill=tk.X, padx=5, pady=2)
        filter_combo.bind('<<ComboboxSelected>>', self._on_filter_change)
        
        # 刷新按钮
        ttk.Button(search_frame, text="🔄 刷新列表", 
                  command=self._refresh_sessions).pack(fill=tk.X, padx=5, pady=5)
        
        # 统计信息区域
        stats_frame = ttk.LabelFrame(parent, text="📊 统计信息")
        stats_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.stats_text = tk.Text(stats_frame, height=6, wrap=tk.WORD, font=('Consolas', 9))
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 进度条
        self.progress_widget = ProgressWidget(parent)
        self.progress_widget.pack(fill=tk.X, padx=5, pady=5)
    
    def _create_session_list(self, parent):
        """创建会话列表"""
        list_frame = ttk.LabelFrame(parent, text="📋 会话列表")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建Treeview
        columns = ('会话ID', '会话名称', '状态', '创建时间', '最后修改', '模型数', '文件数')
        self.sessions_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题和宽度
        column_widths = {
            '会话ID': 120,
            '会话名称': 200,
            '状态': 80,
            '创建时间': 150,
            '最后修改': 150,
            '模型数': 60,
            '文件数': 60
        }
        
        for col in columns:
            self.sessions_tree.heading(col, text=col, command=lambda c=col: self._sort_by_column(c))
            self.sessions_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)
        
        # 滚动条
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.sessions_tree.yview)
        self.sessions_tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.sessions_tree.xview)
        self.sessions_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.sessions_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定事件
        self.sessions_tree.bind('<<TreeviewSelect>>', self._on_session_select)
        self.sessions_tree.bind('<Double-1>', self._on_session_double_click)
        self.sessions_tree.bind('<Button-3>', self._on_session_right_click)
        
        # 右键菜单
        self.context_menu = tk.Menu(self.sessions_tree, tearoff=0)
        self.context_menu.add_command(label="激活会话", command=self._activate_session)
        self.context_menu.add_command(label="恢复到缓存", command=self._restore_session)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="重命名", command=self._rename_session)
        self.context_menu.add_command(label="编辑描述", command=self._edit_description)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="导出会话", command=self._export_session)
        self.context_menu.add_command(label="删除会话", command=self._delete_session)
    
    def _create_detail_panel(self, parent):
        """创建会话详情面板"""
        detail_frame = ttk.LabelFrame(parent, text="📄 会话详情")
        detail_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建notebook用于显示不同类型的详情
        self.detail_notebook = ttk.Notebook(detail_frame)
        self.detail_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 基本信息选项卡
        info_frame = ttk.Frame(self.detail_notebook)
        self.detail_notebook.add(info_frame, text="ℹ️ 基本信息")
        
        self.detail_text = tk.Text(info_frame, wrap=tk.WORD, font=('Consolas', 10))
        detail_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.detail_text.yview)
        self.detail_text.configure(yscrollcommand=detail_scrollbar.set)
        
        self.detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 文件列表选项卡
        files_frame = ttk.Frame(self.detail_notebook)
        self.detail_notebook.add(files_frame, text="📁 文件列表")
        
        self.files_table = DataTableWidget(files_frame)
        self.files_table.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 模型列表选项卡
        models_frame = ttk.Frame(self.detail_notebook)
        self.detail_notebook.add(models_frame, text="🤖 模型列表")
        
        self.models_table = DataTableWidget(models_frame)
        self.models_table.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def _refresh_sessions(self):
        """刷新会话列表"""
        try:
            self.progress_widget.start("正在加载会话列表...")

            # 获取所有会话
            sessions = self.session_manager.list_sessions()
            self.sessions_data = {}

            # 清空现有列表
            for item in self.sessions_tree.get_children():
                self.sessions_tree.delete(item)

            # 添加会话到列表
            for session_id, session_info in sessions.items():
                try:
                    # 获取会话详细信息
                    session = self.session_manager.load_session(session_id)
                    if session:
                        self.sessions_data[session_id] = session

                        # 计算统计信息
                        model_count = len(session.metadata.get('trained_models', []))
                        file_count = self._count_session_files(session)

                        # 格式化时间
                        created_time = session.created_time.strftime('%Y-%m-%d %H:%M:%S')
                        modified_time = session.last_modified.strftime('%Y-%m-%d %H:%M:%S')

                        # 添加到树形控件
                        self.sessions_tree.insert('', tk.END, values=(
                            session_id,
                            session.session_name,
                            session.metadata.get('status', 'unknown'),
                            created_time,
                            modified_time,
                            model_count,
                            file_count
                        ))

                except Exception as e:
                    self.logger.warning(f"加载会话 {session_id} 失败: {e}")
                    continue

            # 更新当前会话显示
            self._update_current_session_display()

            # 更新统计信息
            self._update_statistics()

            self.progress_widget.complete(f"已加载 {len(self.sessions_data)} 个会话")

        except Exception as e:
            self.logger.error(f"刷新会话列表失败: {e}")
            self.progress_widget.error(f"刷新失败: {e}")
            messagebox.showerror("错误", f"刷新会话列表失败: {e}")

    def _count_session_files(self, session):
        """统计会话文件数量"""
        try:
            if not session.session_path.exists():
                return 0

            file_count = 0
            for path in session.session_path.rglob('*'):
                if path.is_file():
                    file_count += 1

            return file_count
        except:
            return 0

    def _update_current_session_display(self):
        """更新当前会话显示"""
        current_session = self.session_manager.get_current_session()
        if current_session:
            text = f"🎯 {current_session.session_name}\n({current_session.session_id})"
        else:
            text = "无活动会话"

        self.current_session_label.config(text=text)

    def _update_statistics(self):
        """更新统计信息"""
        if not self.sessions_data:
            stats_text = "暂无会话数据"
        else:
            total_sessions = len(self.sessions_data)

            # 按状态统计
            status_counts = {}
            total_models = 0
            total_files = 0

            for session in self.sessions_data.values():
                status = session.metadata.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
                total_models += len(session.metadata.get('trained_models', []))
                total_files += self._count_session_files(session)

            stats_text = f"""会话统计信息:
总会话数: {total_sessions}
总模型数: {total_models}
总文件数: {total_files}

按状态分布:"""

            for status, count in status_counts.items():
                stats_text += f"\n  {status}: {count}"

        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)

    def _on_session_select(self, event):
        """会话选择事件"""
        selection = self.sessions_tree.selection()
        if selection:
            item = selection[0]
            values = self.sessions_tree.item(item, 'values')
            if values:
                self.selected_session_id = values[0]
                self._show_session_details(self.selected_session_id)

    def _show_session_details(self, session_id):
        """显示会话详情"""
        if session_id not in self.sessions_data:
            return

        session = self.sessions_data[session_id]

        # 基本信息
        detail_text = f"""会话详细信息:

会话ID: {session.session_id}
会话名称: {session.session_name}
描述: {session.description}
状态: {session.metadata.get('status', 'unknown')}
创建时间: {session.created_time.strftime('%Y-%m-%d %H:%M:%S')}
最后修改: {session.last_modified.strftime('%Y-%m-%d %H:%M:%S')}
会话路径: {session.session_path}

训练模型:"""

        trained_models = session.metadata.get('trained_models', [])
        if trained_models:
            for i, model_info in enumerate(trained_models, 1):
                detail_text += f"\n  {i}. {model_info}"
        else:
            detail_text += "\n  无训练模型"

        detail_text += "\n\n集成结果:"
        ensemble_results = session.metadata.get('ensemble_results', [])
        if ensemble_results:
            for i, result_info in enumerate(ensemble_results, 1):
                detail_text += f"\n  {i}. {result_info}"
        else:
            detail_text += "\n  无集成结果"

        # 更新详情显示
        self.detail_text.delete(1.0, tk.END)
        self.detail_text.insert(1.0, detail_text)

        # 更新文件列表
        self._update_files_table(session)

        # 更新模型列表
        self._update_models_table(session)

    def _update_files_table(self, session):
        """更新文件列表表格"""
        try:
            files_data = []

            if session.session_path.exists():
                for path in session.session_path.rglob('*'):
                    if path.is_file():
                        relative_path = path.relative_to(session.session_path)
                        file_size = path.stat().st_size
                        modified_time = datetime.fromtimestamp(path.stat().st_mtime)

                        files_data.append({
                            '文件名': path.name,
                            '相对路径': str(relative_path),
                            '文件大小': f"{file_size:,} bytes",
                            '修改时间': modified_time.strftime('%Y-%m-%d %H:%M:%S'),
                            '文件类型': path.suffix
                        })

            if files_data:
                df = pd.DataFrame(files_data)
                self.files_table.update_data(df)
            else:
                self.files_table.clear()

        except Exception as e:
            self.logger.error(f"更新文件列表失败: {e}")

    def _update_models_table(self, session):
        """更新模型列表表格"""
        try:
            models_data = []

            trained_models = session.metadata.get('trained_models', [])
            for model_info in trained_models:
                if isinstance(model_info, dict):
                    models_data.append({
                        '模型名称': model_info.get('name', 'Unknown'),
                        '模型类型': model_info.get('type', 'Unknown'),
                        '准确率': f"{model_info.get('accuracy', 0):.4f}",
                        'AUC': f"{model_info.get('auc', 0):.4f}",
                        '训练时间': model_info.get('training_time', 'Unknown'),
                        '文件路径': model_info.get('file_path', 'Unknown')
                    })
                else:
                    # 兼容字符串格式
                    models_data.append({
                        '模型名称': str(model_info),
                        '模型类型': 'Unknown',
                        '准确率': 'Unknown',
                        'AUC': 'Unknown',
                        '训练时间': 'Unknown',
                        '文件路径': 'Unknown'
                    })

            if models_data:
                df = pd.DataFrame(models_data)
                self.models_table.update_data(df)
            else:
                self.models_table.clear()

        except Exception as e:
            self.logger.error(f"更新模型列表失败: {e}")

    def _create_new_session(self):
        """创建新会话"""
        dialog = SessionCreateDialog(self.parent)
        result = dialog.show()

        if result:
            session_name, description = result
            try:
                self.progress_widget.start("正在创建新会话...")

                session = self.session_manager.create_session(session_name, description)

                if session:
                    self._refresh_sessions()
                    self.progress_widget.complete(f"会话 '{session_name}' 创建成功")
                    messagebox.showinfo("成功", f"会话 '{session_name}' 创建成功")
                else:
                    raise ValueError("会话创建失败")

            except Exception as e:
                self.logger.error(f"创建会话失败: {e}")
                self.progress_widget.error(f"创建失败: {e}")
                messagebox.showerror("错误", f"创建会话失败: {e}")

    def _activate_session(self):
        """激活选中的会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return

        try:
            success = self.session_manager.set_current_session(self.selected_session_id)
            if success:
                self._update_current_session_display()
                messagebox.showinfo("成功", f"会话 '{self.selected_session_id}' 已激活")
            else:
                messagebox.showerror("错误", "激活会话失败")

        except Exception as e:
            self.logger.error(f"激活会话失败: {e}")
            messagebox.showerror("错误", f"激活会话失败: {e}")

    def _restore_session(self):
        """恢复会话到缓存"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return

        if not messagebox.askyesno("确认", "恢复会话将覆盖当前缓存数据，是否继续？"):
            return

        def restore_task():
            try:
                self.progress_widget.start("正在恢复会话数据...")

                # 这里需要实现具体的恢复逻辑
                # 由于时间限制，提供基本框架
                session = self.sessions_data.get(self.selected_session_id)
                if session:
                    # 恢复模型文件
                    # 恢复结果文件
                    # 恢复配置文件
                    # 更新缓存
                    pass

                self.progress_widget.complete("会话数据恢复完成")
                self.parent.after(0, lambda: messagebox.showinfo("成功", "会话数据已恢复到缓存"))

            except Exception as e:
                self.logger.error(f"恢复会话失败: {e}")
                self.progress_widget.error(f"恢复失败: {e}")
                self.parent.after(0, lambda: messagebox.showerror("错误", f"恢复会话失败: {e}"))

        # 在后台线程中执行
        threading.Thread(target=restore_task, daemon=True).start()

    def _export_session(self):
        """导出会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return

        # 选择导出目录
        export_dir = filedialog.askdirectory(title="选择导出目录")
        if not export_dir:
            return

        def export_task():
            try:
                self.progress_widget.start("正在导出会话...")

                session = self.sessions_data.get(self.selected_session_id)
                if session and session.session_path.exists():
                    import shutil

                    # 创建导出目录
                    export_path = Path(export_dir) / f"session_{self.selected_session_id}"
                    export_path.mkdir(exist_ok=True)

                    # 复制会话目录
                    shutil.copytree(session.session_path, export_path / "data", dirs_exist_ok=True)

                    # 生成导出报告
                    report_data = {
                        'session_id': session.session_id,
                        'session_name': session.session_name,
                        'description': session.description,
                        'export_time': datetime.now().isoformat(),
                        'metadata': session.metadata
                    }

                    with open(export_path / "export_info.json", 'w', encoding='utf-8') as f:
                        json.dump(report_data, f, indent=2, ensure_ascii=False)

                self.progress_widget.complete("会话导出完成")
                self.parent.after(0, lambda: messagebox.showinfo("成功", f"会话已导出到: {export_path}"))

            except Exception as e:
                self.logger.error(f"导出会话失败: {e}")
                self.progress_widget.error(f"导出失败: {e}")
                self.parent.after(0, lambda: messagebox.showerror("错误", f"导出会话失败: {e}"))

        # 在后台线程中执行
        threading.Thread(target=export_task, daemon=True).start()

    def _delete_session(self):
        """删除会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return

        session_name = self.sessions_data.get(self.selected_session_id, {}).session_name or self.selected_session_id

        if not messagebox.askyesno("确认删除",
                                 f"确定要删除会话 '{session_name}' 吗？\n\n此操作不可撤销！"):
            return

        try:
            success = self.session_manager.delete_session(self.selected_session_id)
            if success:
                self._refresh_sessions()
                self.selected_session_id = None
                self.detail_text.delete(1.0, tk.END)
                self.files_table.clear()
                self.models_table.clear()
                messagebox.showinfo("成功", f"会话 '{session_name}' 已删除")
            else:
                messagebox.showerror("错误", "删除会话失败")

        except Exception as e:
            self.logger.error(f"删除会话失败: {e}")
            messagebox.showerror("错误", f"删除会话失败: {e}")

    def _rename_session(self):
        """重命名会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return

        session = self.sessions_data.get(self.selected_session_id)
        if not session:
            return

        new_name = simpledialog.askstring("重命名会话", "请输入新的会话名称:",
                                        initialvalue=session.session_name)
        if new_name and new_name != session.session_name:
            try:
                session.session_name = new_name
                session.metadata['session_name'] = new_name
                session._save_metadata()

                self._refresh_sessions()
                messagebox.showinfo("成功", "会话重命名成功")

            except Exception as e:
                self.logger.error(f"重命名会话失败: {e}")
                messagebox.showerror("错误", f"重命名会话失败: {e}")

    def _edit_description(self):
        """编辑会话描述"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return

        session = self.sessions_data.get(self.selected_session_id)
        if not session:
            return

        # 创建多行文本编辑对话框
        dialog = DescriptionEditDialog(self.parent, session.description)
        new_description = dialog.show()

        if new_description is not None and new_description != session.description:
            try:
                session.description = new_description
                session.metadata['description'] = new_description
                session._save_metadata()

                self._show_session_details(self.selected_session_id)
                messagebox.showinfo("成功", "会话描述更新成功")

            except Exception as e:
                self.logger.error(f"更新会话描述失败: {e}")
                messagebox.showerror("错误", f"更新会话描述失败: {e}")

    def _on_session_double_click(self, event):
        """会话双击事件"""
        self._activate_session()

    def _on_session_right_click(self, event):
        """会话右键点击事件"""
        # 选择点击的项目
        item = self.sessions_tree.identify_row(event.y)
        if item:
            self.sessions_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def _on_search_change(self, event):
        """搜索内容变化"""
        self._apply_filters()

    def _on_filter_change(self, event):
        """过滤条件变化"""
        self._apply_filters()

    def _apply_filters(self):
        """应用搜索和过滤条件"""
        search_text = self.search_var.get().lower()
        filter_status = self.filter_var.get()

        # 清空现有显示
        for item in self.sessions_tree.get_children():
            self.sessions_tree.delete(item)

        # 重新添加符合条件的会话
        for session_id, session in self.sessions_data.items():
            # 搜索过滤
            if search_text and search_text not in session.session_name.lower() and search_text not in session_id.lower():
                continue

            # 状态过滤
            session_status = session.metadata.get('status', 'unknown')
            if filter_status != "all" and session_status != filter_status:
                continue

            # 添加到显示
            model_count = len(session.metadata.get('trained_models', []))
            file_count = self._count_session_files(session)
            created_time = session.created_time.strftime('%Y-%m-%d %H:%M:%S')
            modified_time = session.last_modified.strftime('%Y-%m-%d %H:%M:%S')

            self.sessions_tree.insert('', tk.END, values=(
                session_id,
                session.session_name,
                session_status,
                created_time,
                modified_time,
                model_count,
                file_count
            ))

    def _sort_by_column(self, column):
        """按列排序"""
        # 获取所有项目
        items = [(self.sessions_tree.set(item, column), item) for item in self.sessions_tree.get_children('')]

        # 排序
        items.sort()

        # 重新排列
        for index, (val, item) in enumerate(items):
            self.sessions_tree.move(item, '', index)


class SessionCreateDialog:
    """会话创建对话框"""

    def __init__(self, parent):
        """初始化对话框"""
        self.parent = parent
        self.result = None

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("创建新会话")
        self.dialog.geometry("500x300")
        self.dialog.resizable(False, False)

        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self._center_dialog()

        # 创建界面
        self._create_interface()

    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()

        # 获取对话框大小
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()

        # 获取父窗口位置和大小
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        # 计算居中位置
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2

        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

    def _create_interface(self):
        """创建对话框界面"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 会话名称
        ttk.Label(main_frame, text="会话名称:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar(value=f"训练会话_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        ttk.Entry(main_frame, textvariable=self.name_var, width=50).grid(
            row=0, column=1, sticky="ew", padx=(10, 0), pady=5)

        # 会话描述
        ttk.Label(main_frame, text="会话描述:").grid(row=1, column=0, sticky=tk.NW, pady=5)
        self.description_text = tk.Text(main_frame, width=40, height=8, wrap=tk.WORD)
        self.description_text.grid(row=1, column=1, sticky="ew", padx=(10, 0), pady=5)

        # 滚动条
        desc_scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.description_text.yview)
        self.description_text.configure(yscrollcommand=desc_scrollbar.set)
        desc_scrollbar.grid(row=1, column=2, sticky="ns", pady=5)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=20)

        ttk.Button(button_frame, text="创建", command=self._ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self._cancel_clicked).pack(side=tk.LEFT, padx=5)

        main_frame.columnconfigure(1, weight=1)

        # 绑定回车键
        self.dialog.bind('<Return>', lambda e: self._ok_clicked())
        self.dialog.bind('<Escape>', lambda e: self._cancel_clicked())

    def _ok_clicked(self):
        """确定按钮点击"""
        session_name = self.name_var.get().strip()
        description = self.description_text.get(1.0, tk.END).strip()

        if not session_name:
            messagebox.showwarning("警告", "请输入会话名称")
            return

        self.result = (session_name, description)
        self.dialog.destroy()

    def _cancel_clicked(self):
        """取消按钮点击"""
        self.result = None
        self.dialog.destroy()

    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result


class DescriptionEditDialog:
    """描述编辑对话框"""

    def __init__(self, parent, initial_text=""):
        """初始化对话框"""
        self.parent = parent
        self.result = None

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("编辑会话描述")
        self.dialog.geometry("500x300")
        self.dialog.resizable(True, True)

        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self._center_dialog()

        # 创建界面
        self._create_interface(initial_text)

    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()

        # 获取对话框大小
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()

        # 获取父窗口位置和大小
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        # 计算居中位置
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2

        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

    def _create_interface(self, initial_text):
        """创建对话框界面"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 描述文本
        ttk.Label(main_frame, text="会话描述:").pack(anchor=tk.W, pady=(0, 5))

        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.description_text = tk.Text(text_frame, wrap=tk.WORD, font=('Arial', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.description_text.yview)
        self.description_text.configure(yscrollcommand=scrollbar.set)

        self.description_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 设置初始文本
        if initial_text:
            self.description_text.insert(1.0, initial_text)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="保存", command=self._ok_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=self._cancel_clicked).pack(side=tk.RIGHT)

        # 绑定快捷键
        self.dialog.bind('<Control-Return>', lambda e: self._ok_clicked())
        self.dialog.bind('<Escape>', lambda e: self._cancel_clicked())

    def _ok_clicked(self):
        """确定按钮点击"""
        description = self.description_text.get(1.0, tk.END).strip()
        self.result = description
        self.dialog.destroy()

    def _cancel_clicked(self):
        """取消按钮点击"""
        self.result = None
        self.dialog.destroy()

    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result
