"""
导出导入标签页
提供项目数据的导出和导入功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import pickle
from datetime import datetime
from typing import Dict, List, Any, Optional

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager


class ExportImportTab(BaseGUI):
    """导出导入标签页"""
    
    def __init__(self, parent: tk.Widget):
        """初始化导出导入标签页"""
        self.export_data = {}
        self.import_data = {}
        
        # 导出格式
        self.export_formats = {
            'json': 'JSON格式',
            'pickle': 'Pickle格式',
            'csv': 'CSV格式',
            'excel': 'Excel格式'
        }
        
        # 导出内容类型
        self.export_types = {
            'session': '会话数据',
            'models': '模型结果',
            'data': '数据集信息',
            'history': '操作历史',
            'config': '配置信息',
            'all': '全部内容'
        }
        
        super().__init__(parent)
        
        # 订阅事件以收集导出数据
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.DATA_LOADED, self._collect_data_info)
        event_manager.subscribe(EventTypes.MODEL_TRAINED, self._collect_models_info)
        event_manager.subscribe('ensemble_training_completed', self._collect_ensemble_info)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='main')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分割
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧导出面板
        left_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(left_frame, weight=1)
        
        # 右侧导入面板
        right_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(right_frame, weight=1)
        
        # 设置导出面板
        self._setup_export_panel(left_frame)
        
        # 设置导入面板
        self._setup_import_panel(right_frame)
        
        self.register_component('main_frame', self.main_frame)
        self.register_component('paned_window', paned_window)
    
    def _setup_export_panel(self, parent):
        """设置导出面板"""
        factory = get_component_factory()
        
        # 导出标题
        title_label = factory.create_label(parent, text="数据导出", style='title')
        title_label.pack(pady=(0, 10))
        
        # 导出内容选择
        content_frame = factory.create_labelframe(parent, text="导出内容", style='section')
        content_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.export_type_vars = {}
        for export_type, display_name in self.export_types.items():
            var = tk.BooleanVar(value=True if export_type == 'all' else False)
            checkbox = factory.create_checkbox(
                content_frame,
                text=display_name,
                variable=var,
                command=self._on_export_type_changed
            )
            checkbox.pack(anchor=tk.W, padx=5, pady=2)
            self.export_type_vars[export_type] = var
        
        # 导出格式选择
        format_frame = factory.create_labelframe(parent, text="导出格式", style='section')
        format_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.export_format_var = tk.StringVar(value="json")
        for format_key, display_name in self.export_formats.items():
            radio = factory.create_radiobutton(
                format_frame,
                text=display_name,
                variable=self.export_format_var,
                value=format_key
            )
            radio.pack(anchor=tk.W, padx=5, pady=2)
        
        # 导出选项
        options_frame = factory.create_labelframe(parent, text="导出选项", style='section')
        options_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.include_metadata_var = tk.BooleanVar(value=True)
        metadata_check = factory.create_checkbox(
            options_frame,
            text="包含元数据",
            variable=self.include_metadata_var
        )
        metadata_check.pack(anchor=tk.W, padx=5, pady=2)
        
        self.compress_data_var = tk.BooleanVar(value=False)
        compress_check = factory.create_checkbox(
            options_frame,
            text="压缩数据",
            variable=self.compress_data_var
        )
        compress_check.pack(anchor=tk.W, padx=5, pady=2)
        
        # 导出按钮
        export_btn = factory.create_button(
            parent,
            text="📤 导出数据",
            command=self._export_data,
            style='primary'
        )
        export_btn.pack(fill=tk.X, padx=5, pady=10)
        
        # 导出状态
        self.export_status_label = factory.create_label(
            parent,
            text="准备导出",
            style='info'
        )
        self.export_status_label.pack(padx=5, pady=5)
    
    def _setup_import_panel(self, parent):
        """设置导入面板"""
        factory = get_component_factory()
        
        # 导入标题
        title_label = factory.create_label(parent, text="数据导入", style='title')
        title_label.pack(pady=(0, 10))
        
        # 文件选择
        file_frame = factory.create_labelframe(parent, text="选择文件", style='section')
        file_frame.pack(fill=tk.X, padx=5, pady=5)
        
        file_select_frame = factory.create_frame(file_frame)
        file_select_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.import_file_var = tk.StringVar()
        file_entry = factory.create_entry(file_select_frame, textvariable=self.import_file_var)
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        browse_btn = factory.create_button(
            file_select_frame,
            text="浏览...",
            command=self._browse_import_file,
            style='secondary'
        )
        browse_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 导入选项
        import_options_frame = factory.create_labelframe(parent, text="导入选项", style='section')
        import_options_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.overwrite_existing_var = tk.BooleanVar(value=False)
        overwrite_check = factory.create_checkbox(
            import_options_frame,
            text="覆盖现有数据",
            variable=self.overwrite_existing_var
        )
        overwrite_check.pack(anchor=tk.W, padx=5, pady=2)
        
        self.validate_data_var = tk.BooleanVar(value=True)
        validate_check = factory.create_checkbox(
            import_options_frame,
            text="验证数据完整性",
            variable=self.validate_data_var
        )
        validate_check.pack(anchor=tk.W, padx=5, pady=2)
        
        self.backup_before_import_var = tk.BooleanVar(value=True)
        backup_check = factory.create_checkbox(
            import_options_frame,
            text="导入前备份",
            variable=self.backup_before_import_var
        )
        backup_check.pack(anchor=tk.W, padx=5, pady=2)
        
        # 导入预览
        preview_frame = factory.create_labelframe(parent, text="导入预览", style='section')
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.preview_text = factory.create_text(
            preview_frame,
            wrap=tk.WORD,
            state=tk.DISABLED,
            height=8
        )
        preview_scrollbar = factory.create_scrollbar(preview_frame, orient=tk.VERTICAL)
        preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.preview_text.configure(yscrollcommand=preview_scrollbar.set)
        preview_scrollbar.configure(command=self.preview_text.yview)
        
        # 导入按钮
        import_buttons_frame = factory.create_frame(parent)
        import_buttons_frame.pack(fill=tk.X, padx=5, pady=10)
        
        preview_btn = factory.create_button(
            import_buttons_frame,
            text="👁️ 预览",
            command=self._preview_import_data,
            style='secondary'
        )
        preview_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        import_btn = factory.create_button(
            import_buttons_frame,
            text="📥 导入数据",
            command=self._import_data,
            style='primary'
        )
        import_btn.pack(side=tk.LEFT)
        
        # 导入状态
        self.import_status_label = factory.create_label(
            parent,
            text="准备导入",
            style='info'
        )
        self.import_status_label.pack(padx=5, pady=5)
    
    def _on_export_type_changed(self):
        """导出类型变化处理"""
        # 如果选择了"全部内容"，取消其他选项
        if self.export_type_vars['all'].get():
            for export_type, var in self.export_type_vars.items():
                if export_type != 'all':
                    var.set(False)
        else:
            # 如果取消了"全部内容"，检查是否有其他选项被选中
            any_selected = any(var.get() for export_type, var in self.export_type_vars.items() if export_type != 'all')
            if not any_selected:
                self.export_type_vars['all'].set(True)
    
    def _export_data(self):
        """导出数据"""
        # 检查导出内容选择
        selected_types = []
        if self.export_type_vars['all'].get():
            selected_types = list(self.export_types.keys())[:-1]  # 除了'all'
        else:
            selected_types = [export_type for export_type, var in self.export_type_vars.items() 
                            if var.get() and export_type != 'all']
        
        if not selected_types:
            messagebox.showwarning("警告", "请选择要导出的内容")
            return
        
        # 选择保存位置
        export_format = self.export_format_var.get()
        file_extensions = {
            'json': '.json',
            'pickle': '.pkl',
            'csv': '.csv',
            'excel': '.xlsx'
        }
        
        file_path = filedialog.asksaveasfilename(
            title="导出数据",
            defaultextension=file_extensions.get(export_format, '.json'),
            filetypes=[(f"{self.export_formats[export_format]} files", f"*{file_extensions[export_format]}"), 
                      ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            self.export_status_label.config(text="正在导出数据...")
            
            # 准备导出数据
            export_data = self._prepare_export_data(selected_types)
            
            # 根据格式导出
            if export_format == 'json':
                self._export_json(file_path, export_data)
            elif export_format == 'pickle':
                self._export_pickle(file_path, export_data)
            elif export_format == 'csv':
                self._export_csv(file_path, export_data)
            elif export_format == 'excel':
                self._export_excel(file_path, export_data)
            
            self.export_status_label.config(text=f"导出完成: {os.path.basename(file_path)}")
            messagebox.showinfo("导出成功", f"数据已成功导出到:\n{file_path}")
            
        except Exception as e:
            self.export_status_label.config(text=f"导出失败: {str(e)}")
            messagebox.showerror("导出失败", f"导出数据时出错:\n{str(e)}")
            self.logger.error(f"导出数据失败: {e}")
    
    def _prepare_export_data(self, selected_types: List[str]) -> Dict[str, Any]:
        """准备导出数据"""
        export_data = {}
        
        # 添加元数据
        if self.include_metadata_var.get():
            export_data['metadata'] = {
                'export_time': datetime.now().isoformat(),
                'export_types': selected_types,
                'export_format': self.export_format_var.get(),
                'version': '1.0'
            }
        
        # 添加各类数据
        for data_type in selected_types:
            if data_type in self.export_data:
                export_data[data_type] = self.export_data[data_type]
        
        return export_data
    
    def _export_json(self, file_path: str, data: Dict[str, Any]):
        """导出JSON格式"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
    
    def _export_pickle(self, file_path: str, data: Dict[str, Any]):
        """导出Pickle格式"""
        with open(file_path, 'wb') as f:
            pickle.dump(data, f)
    
    def _export_csv(self, file_path: str, data: Dict[str, Any]):
        """导出CSV格式"""
        # 简化的CSV导出，主要导出表格数据
        import csv
        
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入标题行
            writer.writerow(['数据类型', '键', '值'])
            
            # 写入数据
            for data_type, type_data in data.items():
                if isinstance(type_data, dict):
                    for key, value in type_data.items():
                        writer.writerow([data_type, key, str(value)])
                else:
                    writer.writerow([data_type, '', str(type_data)])
    
    def _export_excel(self, file_path: str, data: Dict[str, Any]):
        """导出Excel格式"""
        try:
            import pandas as pd
            
            # 创建Excel写入器
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                for data_type, type_data in data.items():
                    if isinstance(type_data, dict):
                        # 将字典转换为DataFrame
                        df = pd.DataFrame(list(type_data.items()), columns=['键', '值'])
                        df.to_excel(writer, sheet_name=data_type, index=False)
                    else:
                        # 简单数据
                        df = pd.DataFrame([type_data], columns=[data_type])
                        df.to_excel(writer, sheet_name=data_type, index=False)
        
        except ImportError:
            # 如果没有pandas，回退到JSON格式
            json_path = file_path.replace('.xlsx', '.json')
            self._export_json(json_path, data)
            messagebox.showwarning("格式回退", f"未安装pandas，已导出为JSON格式:\n{json_path}")
    
    def _browse_import_file(self):
        """浏览导入文件"""
        file_path = filedialog.askopenfilename(
            title="选择导入文件",
            filetypes=[
                ("JSON files", "*.json"),
                ("Pickle files", "*.pkl"),
                ("CSV files", "*.csv"),
                ("Excel files", "*.xlsx"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.import_file_var.set(file_path)
    
    def _preview_import_data(self):
        """预览导入数据"""
        file_path = self.import_file_var.get()
        if not file_path or not os.path.exists(file_path):
            messagebox.showwarning("警告", "请选择有效的导入文件")
            return
        
        try:
            # 根据文件扩展名确定格式
            _, ext = os.path.splitext(file_path)
            ext = ext.lower()
            
            preview_text = ""
            
            if ext == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                preview_text = json.dumps(data, indent=2, ensure_ascii=False, default=str)[:2000]
            
            elif ext == '.pkl':
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
                preview_text = str(data)[:2000]
            
            elif ext == '.csv':
                with open(file_path, 'r', encoding='utf-8') as f:
                    preview_text = f.read(2000)
            
            else:
                preview_text = "不支持的文件格式预览"
            
            # 显示预览
            self.preview_text.config(state=tk.NORMAL)
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(1.0, preview_text)
            if len(preview_text) >= 2000:
                self.preview_text.insert(tk.END, "\n\n... (预览已截断)")
            self.preview_text.config(state=tk.DISABLED)
            
            self.import_status_label.config(text="预览加载完成")
            
        except Exception as e:
            messagebox.showerror("预览失败", f"预览文件时出错:\n{str(e)}")
            self.import_status_label.config(text=f"预览失败: {str(e)}")
    
    def _import_data(self):
        """导入数据"""
        file_path = self.import_file_var.get()
        if not file_path or not os.path.exists(file_path):
            messagebox.showwarning("警告", "请选择有效的导入文件")
            return
        
        # 确认导入
        result = messagebox.askyesno("确认导入", f"确定要导入文件 '{os.path.basename(file_path)}' 吗？")
        if not result:
            return
        
        try:
            self.import_status_label.config(text="正在导入数据...")
            
            # 备份现有数据
            if self.backup_before_import_var.get():
                self._backup_current_data()
            
            # 根据文件扩展名导入
            _, ext = os.path.splitext(file_path)
            ext = ext.lower()
            
            imported_data = None
            
            if ext == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_data = json.load(f)
            
            elif ext == '.pkl':
                with open(file_path, 'rb') as f:
                    imported_data = pickle.load(f)
            
            else:
                raise ValueError(f"不支持的文件格式: {ext}")
            
            # 验证数据
            if self.validate_data_var.get():
                if not self._validate_import_data(imported_data):
                    raise ValueError("导入数据验证失败")
            
            # 处理导入数据
            self._process_imported_data(imported_data)
            
            # 发布导入完成事件
            event_manager = get_event_manager()
            event_manager.publish('data_imported', {'imported_data': imported_data})
            
            self.import_status_label.config(text="导入完成")
            messagebox.showinfo("导入成功", "数据已成功导入")
            
        except Exception as e:
            self.import_status_label.config(text=f"导入失败: {str(e)}")
            messagebox.showerror("导入失败", f"导入数据时出错:\n{str(e)}")
            self.logger.error(f"导入数据失败: {e}")
    
    def _backup_current_data(self):
        """备份当前数据"""
        try:
            backup_data = self.export_data.copy()
            backup_file = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"数据已备份到: {backup_file}")
            
        except Exception as e:
            self.logger.error(f"备份数据失败: {e}")
    
    def _validate_import_data(self, data: Dict[str, Any]) -> bool:
        """验证导入数据"""
        try:
            # 基本结构验证
            if not isinstance(data, dict):
                return False
            
            # 检查必要的键
            required_keys = ['metadata']
            for key in required_keys:
                if key not in data:
                    self.logger.warning(f"导入数据缺少必要键: {key}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证导入数据时出错: {e}")
            return False
    
    def _process_imported_data(self, data: Dict[str, Any]):
        """处理导入数据"""
        # 根据覆盖选项处理数据
        if self.overwrite_existing_var.get():
            self.export_data = data.copy()
        else:
            # 合并数据
            for key, value in data.items():
                if key not in self.export_data:
                    self.export_data[key] = value
                elif isinstance(value, dict) and isinstance(self.export_data[key], dict):
                    self.export_data[key].update(value)
    
    def _collect_data_info(self, event_data: Dict[str, Any]):
        """收集数据信息"""
        self.export_data['data'] = event_data
    
    def _collect_models_info(self, event_data: Dict[str, Any]):
        """收集模型信息"""
        self.export_data['models'] = event_data
    
    def _collect_ensemble_info(self, event_data: Dict[str, Any]):
        """收集集成信息"""
        self.export_data['ensemble'] = event_data
    
    def get_export_data(self) -> Dict[str, Any]:
        """获取导出数据"""
        return self.export_data.copy()
    
    def set_export_data(self, data: Dict[str, Any]):
        """设置导出数据"""
        self.export_data = data.copy()
