"""
项目历史标签页
提供项目历史记录和版本管理功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from typing import Dict, List, Any, Optional

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager


class ProjectHistoryTab(BaseGUI):
    """项目历史标签页"""
    
    def __init__(self, parent: tk.Widget):
        """初始化项目历史标签页"""
        self.history_records = []
        self.selected_record = None
        
        super().__init__(parent)
        
        # 订阅各种事件以记录历史
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.DATA_LOADED, self._record_data_event)
        event_manager.subscribe(EventTypes.MODEL_TRAINED, self._record_model_event)
        event_manager.subscribe('ensemble_training_completed', self._record_ensemble_event)
        event_manager.subscribe('session_loaded', self._record_session_event)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='main')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建上下分割
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.VERTICAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 上方历史列表面板
        top_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(top_frame, weight=2)
        
        # 下方详细信息面板
        bottom_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(bottom_frame, weight=1)
        
        # 设置历史列表面板
        self._setup_history_panel(top_frame)
        
        # 设置详细信息面板
        self._setup_details_panel(bottom_frame)
        
        self.register_component('main_frame', self.main_frame)
        self.register_component('paned_window', paned_window)
    
    def _setup_history_panel(self, parent):
        """设置历史列表面板"""
        factory = get_component_factory()
        
        # 工具栏
        toolbar_frame = factory.create_frame(parent)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 筛选选项
        filter_frame = factory.create_frame(toolbar_frame)
        filter_frame.pack(side=tk.LEFT)
        
        factory.create_label(filter_frame, text="筛选类型:").pack(side=tk.LEFT)
        
        self.filter_var = tk.StringVar(value="all")
        filter_combo = factory.create_combobox(
            filter_frame,
            textvariable=self.filter_var,
            values=["all", "data", "model", "ensemble", "session"],
            state="readonly",
            width=10
        )
        filter_combo.pack(side=tk.LEFT, padx=(5, 0))
        filter_combo.bind('<<ComboboxSelected>>', self._apply_filter)
        
        # 操作按钮
        buttons_frame = factory.create_frame(toolbar_frame)
        buttons_frame.pack(side=tk.RIGHT)
        
        refresh_btn = factory.create_button(
            buttons_frame,
            text="🔄 刷新",
            command=self._refresh_history,
            style='secondary'
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        clear_btn = factory.create_button(
            buttons_frame,
            text="🗑️ 清空历史",
            command=self._clear_history,
            style='danger'
        )
        clear_btn.pack(side=tk.LEFT)
        
        # 历史记录表格
        history_frame = factory.create_labelframe(parent, text="操作历史", style='section')
        history_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        columns = ('时间', '操作类型', '操作描述', '状态', '详情')
        self.history_tree = factory.create_treeview(
            history_frame,
            columns=columns,
            show='headings'
        )
        
        # 设置列
        column_widths = {'时间': 120, '操作类型': 100, '操作描述': 300, '状态': 80, '详情': 150}
        
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=column_widths.get(col, 100), anchor=tk.W)
        
        # 滚动条
        history_scrollbar = factory.create_scrollbar(history_frame, orient=tk.VERTICAL)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        history_scrollbar.configure(command=self.history_tree.yview)
        
        # 绑定选择事件
        self.history_tree.bind('<<TreeviewSelect>>', self._on_history_select)
    
    def _setup_details_panel(self, parent):
        """设置详细信息面板"""
        factory = get_component_factory()
        
        # 详细信息标签页
        self.details_notebook = factory.create_notebook(parent)
        self.details_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 基本信息标签页
        basic_frame = factory.create_frame(self.details_notebook)
        self.details_notebook.add(basic_frame, text="基本信息")
        
        self.basic_text = factory.create_text(
            basic_frame,
            wrap=tk.WORD,
            state=tk.DISABLED,
            height=8
        )
        basic_scrollbar = factory.create_scrollbar(basic_frame, orient=tk.VERTICAL)
        basic_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.basic_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.basic_text.configure(yscrollcommand=basic_scrollbar.set)
        basic_scrollbar.configure(command=self.basic_text.yview)
        
        # 详细数据标签页
        data_frame = factory.create_frame(self.details_notebook)
        self.details_notebook.add(data_frame, text="详细数据")
        
        self.data_text = factory.create_text(
            data_frame,
            wrap=tk.WORD,
            state=tk.DISABLED,
            height=8
        )
        data_scrollbar = factory.create_scrollbar(data_frame, orient=tk.VERTICAL)
        data_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.data_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.data_text.configure(yscrollcommand=data_scrollbar.set)
        data_scrollbar.configure(command=self.data_text.yview)
    
    def _record_data_event(self, event_data: Dict[str, Any]):
        """记录数据事件"""
        record = {
            'timestamp': datetime.now(),
            'type': 'data',
            'description': f"数据加载: {event_data.get('file_path', '未知文件')}",
            'status': '成功',
            'details': f"数据集: {event_data.get('dataset_name', '未知')}, 行数: {event_data.get('rows', 0)}, 列数: {event_data.get('columns', 0)}",
            'data': event_data
        }
        self._add_history_record(record)
    
    def _record_model_event(self, event_data: Dict[str, Any]):
        """记录模型事件"""
        results = event_data.get('results', {})
        model_count = len(results)
        
        record = {
            'timestamp': datetime.now(),
            'type': 'model',
            'description': f"模型训练完成: 训练了 {model_count} 个模型",
            'status': '成功',
            'details': f"模型列表: {', '.join(results.keys())}",
            'data': event_data
        }
        self._add_history_record(record)
    
    def _record_ensemble_event(self, event_data: Dict[str, Any]):
        """记录集成事件"""
        results = event_data.get('results', {})
        ensemble_count = len(results)
        
        record = {
            'timestamp': datetime.now(),
            'type': 'ensemble',
            'description': f"集成训练完成: 训练了 {ensemble_count} 个集成模型",
            'status': '成功',
            'details': f"集成方法: {', '.join(results.keys())}",
            'data': event_data
        }
        self._add_history_record(record)
    
    def _record_session_event(self, event_data: Dict[str, Any]):
        """记录会话事件"""
        session_data = event_data.get('session_data', {})
        session_name = session_data.get('name', '未知会话')
        
        record = {
            'timestamp': datetime.now(),
            'type': 'session',
            'description': f"会话加载: {session_name}",
            'status': '成功',
            'details': f"会话ID: {session_data.get('session_id', '未知')}",
            'data': event_data
        }
        self._add_history_record(record)
    
    def _add_history_record(self, record: Dict[str, Any]):
        """添加历史记录"""
        self.history_records.insert(0, record)  # 最新记录在前
        
        # 限制历史记录数量
        if len(self.history_records) > 1000:
            self.history_records = self.history_records[:1000]
        
        # 更新显示
        self._refresh_history_display()
        
        self.logger.info(f"添加历史记录: {record['description']}")
    
    def _refresh_history_display(self):
        """刷新历史记录显示"""
        # 清空现有记录
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        # 应用筛选
        filter_type = self.filter_var.get()
        filtered_records = self.history_records
        
        if filter_type != "all":
            filtered_records = [r for r in self.history_records if r['type'] == filter_type]
        
        # 添加记录到表格
        for i, record in enumerate(filtered_records):
            time_str = record['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            type_display = {
                'data': '数据操作',
                'model': '模型训练',
                'ensemble': '集成学习',
                'session': '会话管理'
            }.get(record['type'], record['type'])
            
            values = (
                time_str,
                type_display,
                record['description'],
                record['status'],
                record['details'][:50] + '...' if len(record['details']) > 50 else record['details']
            )
            
            self.history_tree.insert('', tk.END, values=values, tags=(str(i),))
    
    def _apply_filter(self, event=None):
        """应用筛选"""
        self._refresh_history_display()
        filter_type = self.filter_var.get()
        filter_display = {
            'all': '全部',
            'data': '数据操作',
            'model': '模型训练',
            'ensemble': '集成学习',
            'session': '会话管理'
        }.get(filter_type, filter_type)
        
        self.logger.info(f"应用筛选: {filter_display}")
    
    def _refresh_history(self):
        """刷新历史记录"""
        self._refresh_history_display()
        self.logger.info("历史记录已刷新")
    
    def _clear_history(self):
        """清空历史记录"""
        result = messagebox.askyesno("确认清空", "确定要清空所有历史记录吗？此操作不可撤销。")
        if result:
            self.history_records.clear()
            self._refresh_history_display()
            self._clear_details()
            self.logger.info("历史记录已清空")
    
    def _on_history_select(self, event):
        """历史记录选择事件"""
        selection = self.history_tree.selection()
        if not selection:
            return
        
        item = selection[0]
        tags = self.history_tree.item(item, 'tags')
        if not tags:
            return
        
        try:
            record_index = int(tags[0])
            filter_type = self.filter_var.get()
            
            if filter_type == "all":
                filtered_records = self.history_records
            else:
                filtered_records = [r for r in self.history_records if r['type'] == filter_type]
            
            if 0 <= record_index < len(filtered_records):
                self.selected_record = filtered_records[record_index]
                self._show_record_details(self.selected_record)
        
        except (ValueError, IndexError) as e:
            self.logger.error(f"选择历史记录时出错: {e}")
    
    def _show_record_details(self, record: Dict[str, Any]):
        """显示记录详细信息"""
        # 基本信息
        basic_info = f"""
操作时间: {record['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}
操作类型: {record['type']}
操作描述: {record['description']}
执行状态: {record['status']}
详细信息: {record['details']}
        """.strip()
        
        self._update_text_widget(self.basic_text, basic_info)
        
        # 详细数据
        import json
        try:
            data_json = json.dumps(record.get('data', {}), indent=2, ensure_ascii=False, default=str)
            self._update_text_widget(self.data_text, data_json)
        except Exception as e:
            error_msg = f"无法显示详细数据: {str(e)}"
            self._update_text_widget(self.data_text, error_msg)
    
    def _clear_details(self):
        """清空详细信息"""
        self._update_text_widget(self.basic_text, "请选择一条历史记录查看详细信息")
        self._update_text_widget(self.data_text, "")
    
    def _update_text_widget(self, text_widget, content: str):
        """更新文本控件内容"""
        text_widget.config(state=tk.NORMAL)
        text_widget.delete(1.0, tk.END)
        text_widget.insert(1.0, content)
        text_widget.config(state=tk.DISABLED)
    
    def get_history_records(self) -> List[Dict[str, Any]]:
        """获取历史记录"""
        return self.history_records.copy()
    
    def export_history(self, file_path: str):
        """导出历史记录"""
        try:
            import json
            
            # 准备导出数据
            export_data = []
            for record in self.history_records:
                export_record = record.copy()
                export_record['timestamp'] = record['timestamp'].isoformat()
                export_data.append(export_record)
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"历史记录已导出到: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出历史记录失败: {e}")
            return False
