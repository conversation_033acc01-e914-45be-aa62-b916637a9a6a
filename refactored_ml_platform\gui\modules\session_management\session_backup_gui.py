#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话备份和恢复GUI模块
提供会话数据的备份和恢复功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
from typing import Dict, List, Optional, Any
import threading
import json
import os
import shutil
import zipfile
from pathlib import Path

try:
    from ....core.session_manager import get_session_manager
    from ....utils.error_handler import get_error_handler
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))

    try:
        from core.session_manager import get_session_manager
        from utils.error_handler import get_error_handler
    except ImportError:
        # 如果仍然失败，创建简化版本
        get_session_manager = None
        get_error_handler = None


class SessionBackupGUI:
    """会话备份和恢复GUI"""
    
    def __init__(self, parent):
        """初始化会话备份GUI"""
        self.parent = parent
        try:
            self.session_manager = get_session_manager()
            self.error_handler = get_error_handler()
        except:
            # 如果导入失败，创建简化版本
            self.session_manager = None
            self.error_handler = None

        # GUI组件
        self.main_frame = None
        self.frame = None
        
        # 控制变量
        self.status_var = tk.StringVar(value="就绪")
        self.progress_var = tk.DoubleVar()
        
        # 创建界面
        if self.session_manager:
            self._create_interface()
        else:
            self._create_simple_interface()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.frame = self.main_frame  # 保持兼容性
        
        # 标题
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(title_frame, text="会话备份与恢复", font=('Arial', 16, 'bold')).pack()
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self._create_backup_tab()
        self._create_restore_tab()
        self._create_schedule_tab()

    def _create_simple_interface(self):
        """创建简化界面（当会话管理器不可用时）"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 显示不可用消息
        message_frame = ttk.Frame(self.main_frame)
        message_frame.pack(expand=True, fill='both')

        ttk.Label(message_frame, text="会话备份功能暂时不可用",
                 font=('Arial', 16, 'bold')).pack(pady=20)

        ttk.Label(message_frame, text="原因：会话管理器模块导入失败\n\n" +
                                    "功能说明：\n" +
                                    "• 创建会话数据的完整备份\n" +
                                    "• 从备份文件恢复会话\n" +
                                    "• 定时自动备份\n" +
                                    "• 备份文件管理\n\n" +
                                    "请检查系统配置或联系开发者",
                 justify='left').pack(pady=10)
    
    def _create_backup_tab(self):
        """创建备份选项卡"""
        backup_tab = ttk.Frame(self.notebook)
        self.notebook.add(backup_tab, text="💾 创建备份")
        
        # 备份选项
        options_frame = ttk.LabelFrame(backup_tab, text="备份选项")
        options_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 备份类型
        type_frame = ttk.Frame(options_frame)
        type_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.backup_type_var = tk.StringVar(value="all")
        ttk.Radiobutton(type_frame, text="备份所有会话", variable=self.backup_type_var, 
                       value="all").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(type_frame, text="备份选定会话", variable=self.backup_type_var, 
                       value="selected").pack(side=tk.LEFT)
        
        # 会话选择
        self.session_selection_frame = ttk.LabelFrame(backup_tab, text="选择会话")
        self.session_selection_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 会话列表
        self.backup_sessions_tree = ttk.Treeview(self.session_selection_frame, 
                                               columns=('名称', '状态', '大小'), 
                                               show='headings', height=8, selectmode='extended')
        
        for col in ['名称', '状态', '大小']:
            self.backup_sessions_tree.heading(col, text=col)
            self.backup_sessions_tree.column(col, width=150)
        
        backup_scrollbar = ttk.Scrollbar(self.session_selection_frame, orient=tk.VERTICAL, 
                                       command=self.backup_sessions_tree.yview)
        self.backup_sessions_tree.configure(yscrollcommand=backup_scrollbar.set)
        
        self.backup_sessions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        backup_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 备份设置
        settings_frame = ttk.LabelFrame(backup_tab, text="备份设置")
        settings_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 输出路径
        path_frame = ttk.Frame(settings_frame)
        path_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(path_frame, text="备份路径:").pack(side=tk.LEFT)
        self.backup_path_var = tk.StringVar(value="output/backups")
        ttk.Entry(path_frame, textvariable=self.backup_path_var, width=50).pack(
            side=tk.LEFT, padx=10, fill=tk.X, expand=True)
        ttk.Button(path_frame, text="浏览...", command=self._browse_backup_path).pack(side=tk.RIGHT)
        
        # 备份选项
        options_inner_frame = ttk.Frame(settings_frame)
        options_inner_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.compress_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_inner_frame, text="压缩备份文件", 
                       variable=self.compress_var).pack(side=tk.LEFT)
        
        self.include_models_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_inner_frame, text="包含模型数据", 
                       variable=self.include_models_var).pack(side=tk.LEFT, padx=(20, 0))
        
        # 执行备份
        execute_frame = ttk.Frame(backup_tab)
        execute_frame.pack(fill=tk.X, padx=10, pady=20)
        
        ttk.Button(execute_frame, text="🔄 刷新会话列表", 
                  command=self._refresh_backup_sessions).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(execute_frame, text="💾 开始备份", 
                  command=self._start_backup, style="Accent.TButton").pack(side=tk.RIGHT)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(backup_tab, variable=self.progress_var,
                                          mode='determinate', length=400)
        self.progress_bar.pack(fill=tk.X, padx=10, pady=10)
        
        # 状态栏
        status_frame = ttk.Frame(backup_tab)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(
            side=tk.LEFT, padx=(5, 0))
        
        # 初始化会话列表
        self._refresh_backup_sessions()
    
    def _create_restore_tab(self):
        """创建恢复选项卡"""
        restore_tab = ttk.Frame(self.notebook)
        self.notebook.add(restore_tab, text="📥 恢复备份")
        
        # 备份文件选择
        file_frame = ttk.LabelFrame(restore_tab, text="选择备份文件")
        file_frame.pack(fill=tk.X, padx=10, pady=10)
        
        file_select_frame = ttk.Frame(file_frame)
        file_select_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(file_select_frame, text="备份文件:").pack(side=tk.LEFT)
        self.restore_file_var = tk.StringVar()
        ttk.Entry(file_select_frame, textvariable=self.restore_file_var, width=50).pack(
            side=tk.LEFT, padx=10, fill=tk.X, expand=True)
        ttk.Button(file_select_frame, text="浏览...", command=self._browse_restore_file).pack(side=tk.RIGHT)
        
        # 备份信息显示
        info_frame = ttk.LabelFrame(restore_tab, text="备份信息")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.backup_info_text = tk.Text(info_frame, wrap=tk.WORD, font=('Consolas', 10), height=10)
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.backup_info_text.yview)
        self.backup_info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.backup_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 恢复选项
        restore_options_frame = ttk.LabelFrame(restore_tab, text="恢复选项")
        restore_options_frame.pack(fill=tk.X, padx=10, pady=10)
        
        restore_inner_frame = ttk.Frame(restore_options_frame)
        restore_inner_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.overwrite_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(restore_inner_frame, text="覆盖现有会话", 
                       variable=self.overwrite_var).pack(side=tk.LEFT)
        
        self.restore_models_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(restore_inner_frame, text="恢复模型数据", 
                       variable=self.restore_models_var).pack(side=tk.LEFT, padx=(20, 0))
        
        # 执行恢复
        restore_execute_frame = ttk.Frame(restore_tab)
        restore_execute_frame.pack(fill=tk.X, padx=10, pady=20)
        
        ttk.Button(restore_execute_frame, text="🔍 分析备份文件", 
                  command=self._analyze_backup_file).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(restore_execute_frame, text="📥 开始恢复", 
                  command=self._start_restore, style="Accent.TButton").pack(side=tk.RIGHT)
    
    def _create_schedule_tab(self):
        """创建定时备份选项卡"""
        schedule_tab = ttk.Frame(self.notebook)
        self.notebook.add(schedule_tab, text="⏰ 定时备份")
        
        # 定时备份设置
        schedule_frame = ttk.LabelFrame(schedule_tab, text="定时备份设置")
        schedule_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 启用定时备份
        enable_frame = ttk.Frame(schedule_frame)
        enable_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.auto_backup_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(enable_frame, text="启用自动备份", 
                       variable=self.auto_backup_var, 
                       command=self._toggle_auto_backup).pack(side=tk.LEFT)
        
        # 备份频率
        frequency_frame = ttk.Frame(schedule_frame)
        frequency_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(frequency_frame, text="备份频率:").pack(side=tk.LEFT)
        self.backup_frequency_var = tk.StringVar(value="daily")
        frequency_combo = ttk.Combobox(frequency_frame, textvariable=self.backup_frequency_var,
                                     values=["hourly", "daily", "weekly", "monthly"],
                                     state="readonly", width=15)
        frequency_combo.pack(side=tk.LEFT, padx=10)
        
        # 备份时间
        time_frame = ttk.Frame(schedule_frame)
        time_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(time_frame, text="备份时间:").pack(side=tk.LEFT)
        self.backup_time_var = tk.StringVar(value="02:00")
        ttk.Entry(time_frame, textvariable=self.backup_time_var, width=10).pack(side=tk.LEFT, padx=10)
        
        # 保留策略
        retention_frame = ttk.LabelFrame(schedule_tab, text="备份保留策略")
        retention_frame.pack(fill=tk.X, padx=10, pady=10)
        
        retention_inner_frame = ttk.Frame(retention_frame)
        retention_inner_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(retention_inner_frame, text="保留备份数量:").pack(side=tk.LEFT)
        self.retention_count_var = tk.StringVar(value="7")
        ttk.Entry(retention_inner_frame, textvariable=self.retention_count_var, width=10).pack(side=tk.LEFT, padx=10)
        
        # 控制按钮
        control_frame = ttk.Frame(schedule_tab)
        control_frame.pack(fill=tk.X, padx=10, pady=20)
        
        ttk.Button(control_frame, text="💾 立即备份", 
                  command=self._immediate_backup).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="📋 查看备份历史", 
                  command=self._show_backup_history).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="🗑️ 清理旧备份", 
                  command=self._cleanup_old_backups).pack(side=tk.LEFT, padx=(0, 10))
    
    def _refresh_backup_sessions(self):
        """刷新备份会话列表"""
        try:
            # 清空现有列表
            for item in self.backup_sessions_tree.get_children():
                self.backup_sessions_tree.delete(item)

            # 获取会话列表
            sessions = self.session_manager.list_sessions()

            for session in sessions:
                # 估算会话大小
                size = self._estimate_session_size(session['session_id'])

                self.backup_sessions_tree.insert('', 'end', values=(
                    session['session_name'],
                    session['status'],
                    f"{size:.1f} MB"
                ))

            self.status_var.set(f"已加载 {len(sessions)} 个会话")

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(e, "刷新备份会话列表")
            self.status_var.set("刷新失败")

    def _estimate_session_size(self, session_id: str) -> float:
        """估算会话大小（MB）"""
        try:
            session = self.session_manager.load_session(session_id)
            if not session:
                return 0.0

            # 简单估算：基于数据项数量
            size = 0.0
            size += len(session.models) * 0.5  # 每个模型约0.5MB
            size += len(session.results) * 0.1  # 每个结果约0.1MB
            size += len(session.plots) * 0.2   # 每个图表约0.2MB
            size += len(session.configs) * 0.01 # 每个配置约0.01MB

            return max(size, 0.01)  # 最小0.01MB

        except:
            return 0.0

    def _browse_backup_path(self):
        """浏览备份路径"""
        path = filedialog.askdirectory(title="选择备份目录")
        if path:
            self.backup_path_var.set(path)

    def _start_backup(self):
        """开始备份"""
        backup_type = self.backup_type_var.get()
        backup_path = self.backup_path_var.get()

        if not backup_path:
            messagebox.showwarning("警告", "请选择备份路径")
            return

        # 获取要备份的会话
        if backup_type == "all":
            sessions = self.session_manager.list_sessions()
            session_ids = [s['session_id'] for s in sessions]
        else:
            selected_items = self.backup_sessions_tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请选择要备份的会话")
                return

            sessions = self.session_manager.list_sessions()
            session_ids = []
            for item in selected_items:
                values = self.backup_sessions_tree.item(item)['values']
                session_name = values[0]
                for session in sessions:
                    if session['session_name'] == session_name:
                        session_ids.append(session['session_id'])
                        break

        if not session_ids:
            messagebox.showwarning("警告", "没有找到要备份的会话")
            return

        # 在后台线程中执行备份
        threading.Thread(target=self._perform_backup,
                        args=(session_ids, backup_path), daemon=True).start()

    def _perform_backup(self, session_ids: List[str], backup_path: str):
        """执行备份操作"""
        try:
            self.status_var.set("正在创建备份...")
            self.progress_var.set(0)

            # 创建备份目录
            backup_dir = Path(backup_path)
            backup_dir.mkdir(parents=True, exist_ok=True)

            # 创建时间戳
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"session_backup_{timestamp}"

            if self.compress_var.get():
                backup_file = backup_dir / f"{backup_name}.zip"
                self._create_compressed_backup(session_ids, backup_file)
            else:
                backup_folder = backup_dir / backup_name
                self._create_folder_backup(session_ids, backup_folder)

            self.progress_var.set(100)
            self.status_var.set("备份完成")

            messagebox.showinfo("成功", f"备份已创建:\n{backup_file if self.compress_var.get() else backup_folder}")

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(e, "执行备份")
            self.status_var.set("备份失败")
            messagebox.showerror("错误", f"备份失败: {e}")
        finally:
            self.progress_var.set(0)

    def _create_compressed_backup(self, session_ids: List[str], backup_file: Path):
        """创建压缩备份"""
        with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            total_sessions = len(session_ids)

            # 创建备份元数据
            backup_metadata = {
                'backup_time': datetime.now().isoformat(),
                'session_count': total_sessions,
                'include_models': self.include_models_var.get(),
                'version': '1.0'
            }

            zipf.writestr('backup_metadata.json',
                         json.dumps(backup_metadata, indent=2, ensure_ascii=False))

            for i, session_id in enumerate(session_ids):
                try:
                    session = self.session_manager.load_session(session_id)
                    if session:
                        # 创建会话数据
                        session_data = {
                            'metadata': session.get_summary(),
                            'results': session.results,
                            'plots': session.plots if self.include_models_var.get() else {},
                            'configs': session.configs,
                            'data_info': session.data_info
                        }

                        # 如果包含模型数据
                        if self.include_models_var.get():
                            session_data['models'] = session.models

                        # 写入ZIP文件
                        session_json = json.dumps(session_data, indent=2, ensure_ascii=False)
                        zipf.writestr(f'sessions/{session.session_name}.json', session_json)

                    # 更新进度
                    progress = (i + 1) / total_sessions * 90  # 90%用于处理会话
                    self.progress_var.set(progress)

                except Exception as e:
                    print(f"备份会话 {session_id} 失败: {e}")

    def _create_folder_backup(self, session_ids: List[str], backup_folder: Path):
        """创建文件夹备份"""
        backup_folder.mkdir(parents=True, exist_ok=True)

        # 创建备份元数据
        backup_metadata = {
            'backup_time': datetime.now().isoformat(),
            'session_count': len(session_ids),
            'include_models': self.include_models_var.get(),
            'version': '1.0'
        }

        with open(backup_folder / 'backup_metadata.json', 'w', encoding='utf-8') as f:
            json.dump(backup_metadata, f, indent=2, ensure_ascii=False)

        # 创建会话目录
        sessions_dir = backup_folder / 'sessions'
        sessions_dir.mkdir(exist_ok=True)

        total_sessions = len(session_ids)

        for i, session_id in enumerate(session_ids):
            try:
                session = self.session_manager.load_session(session_id)
                if session:
                    # 创建会话数据
                    session_data = {
                        'metadata': session.get_summary(),
                        'results': session.results,
                        'plots': session.plots if self.include_models_var.get() else {},
                        'configs': session.configs,
                        'data_info': session.data_info
                    }

                    # 如果包含模型数据
                    if self.include_models_var.get():
                        session_data['models'] = session.models

                    # 保存会话文件
                    session_file = sessions_dir / f'{session.session_name}.json'
                    with open(session_file, 'w', encoding='utf-8') as f:
                        json.dump(session_data, f, indent=2, ensure_ascii=False)

                # 更新进度
                progress = (i + 1) / total_sessions * 90
                self.progress_var.set(progress)

            except Exception as e:
                print(f"备份会话 {session_id} 失败: {e}")

    def _browse_restore_file(self):
        """浏览恢复文件"""
        filename = filedialog.askopenfilename(
            title="选择备份文件",
            filetypes=[("ZIP文件", "*.zip"), ("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            self.restore_file_var.set(filename)

    def _analyze_backup_file(self):
        """分析备份文件"""
        backup_file = self.restore_file_var.get()
        if not backup_file:
            messagebox.showwarning("警告", "请选择备份文件")
            return

        try:
            backup_info = self._get_backup_info(backup_file)

            info_text = f"""备份文件分析结果
{'='*50}

文件路径: {backup_file}
文件大小: {os.path.getsize(backup_file) / (1024*1024):.2f} MB
备份时间: {backup_info.get('backup_time', '未知')}
会话数量: {backup_info.get('session_count', 0)}
包含模型: {'是' if backup_info.get('include_models', False) else '否'}
备份版本: {backup_info.get('version', '未知')}

会话列表:
"""

            for session_name in backup_info.get('sessions', []):
                info_text += f"  • {session_name}\n"

            if not backup_info.get('sessions'):
                info_text += "  (无会话信息)\n"

            self.backup_info_text.delete(1.0, tk.END)
            self.backup_info_text.insert(1.0, info_text)

            self.status_var.set("备份文件分析完成")

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(e, "分析备份文件")
            messagebox.showerror("错误", f"分析备份文件失败: {e}")

    def _get_backup_info(self, backup_file: str) -> Dict[str, Any]:
        """获取备份文件信息"""
        backup_path = Path(backup_file)

        if backup_path.suffix.lower() == '.zip':
            return self._analyze_zip_backup(backup_file)
        elif backup_path.suffix.lower() == '.json':
            return self._analyze_json_backup(backup_file)
        else:
            raise ValueError("不支持的备份文件格式")

    def _analyze_zip_backup(self, backup_file: str) -> Dict[str, Any]:
        """分析ZIP备份文件"""
        with zipfile.ZipFile(backup_file, 'r') as zipf:
            # 读取元数据
            try:
                metadata_content = zipf.read('backup_metadata.json').decode('utf-8')
                metadata = json.loads(metadata_content)
            except:
                metadata = {}

            # 获取会话列表
            sessions = []
            for file_info in zipf.filelist:
                if file_info.filename.startswith('sessions/') and file_info.filename.endswith('.json'):
                    session_name = Path(file_info.filename).stem
                    sessions.append(session_name)

            metadata['sessions'] = sessions
            return metadata

    def _analyze_json_backup(self, backup_file: str) -> Dict[str, Any]:
        """分析JSON备份文件"""
        with open(backup_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 如果是单个会话文件
        if 'metadata' in data:
            return {
                'backup_time': data.get('export_time', '未知'),
                'session_count': 1,
                'include_models': 'models' in data,
                'version': data.get('export_version', '1.0'),
                'sessions': [data['metadata'].get('session_name', '未知')]
            }

        # 如果是完整备份文件
        return data

    def _start_restore(self):
        """开始恢复"""
        backup_file = self.restore_file_var.get()
        if not backup_file:
            messagebox.showwarning("警告", "请选择备份文件")
            return

        if not os.path.exists(backup_file):
            messagebox.showerror("错误", "备份文件不存在")
            return

        # 确认恢复
        if not messagebox.askyesno("确认恢复", "确定要从备份文件恢复会话吗？\n\n此操作可能会覆盖现有数据！"):
            return

        # 在后台线程中执行恢复
        threading.Thread(target=self._perform_restore,
                        args=(backup_file,), daemon=True).start()

    def _perform_restore(self, backup_file: str):
        """执行恢复操作"""
        try:
            self.status_var.set("正在恢复备份...")
            self.progress_var.set(0)

            backup_path = Path(backup_file)

            if backup_path.suffix.lower() == '.zip':
                restored_count = self._restore_from_zip(backup_file)
            elif backup_path.suffix.lower() == '.json':
                restored_count = self._restore_from_json(backup_file)
            else:
                raise ValueError("不支持的备份文件格式")

            self.progress_var.set(100)
            self.status_var.set("恢复完成")

            messagebox.showinfo("成功", f"成功恢复 {restored_count} 个会话")

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(e, "执行恢复")
            self.status_var.set("恢复失败")
            messagebox.showerror("错误", f"恢复失败: {e}")
        finally:
            self.progress_var.set(0)

    def _restore_from_zip(self, backup_file: str) -> int:
        """从ZIP文件恢复"""
        restored_count = 0

        with zipfile.ZipFile(backup_file, 'r') as zipf:
            # 获取会话文件列表
            session_files = [f for f in zipf.namelist()
                           if f.startswith('sessions/') and f.endswith('.json')]

            total_files = len(session_files)

            for i, session_file in enumerate(session_files):
                try:
                    # 读取会话数据
                    session_content = zipf.read(session_file).decode('utf-8')
                    session_data = json.loads(session_content)

                    # 恢复会话
                    if self._restore_session_data(session_data):
                        restored_count += 1

                    # 更新进度
                    progress = (i + 1) / total_files * 90
                    self.progress_var.set(progress)

                except Exception as e:
                    print(f"恢复会话文件 {session_file} 失败: {e}")

        return restored_count

    def _restore_from_json(self, backup_file: str) -> int:
        """从JSON文件恢复"""
        with open(backup_file, 'r', encoding='utf-8') as f:
            session_data = json.load(f)

        if self._restore_session_data(session_data):
            return 1
        else:
            return 0

    def _restore_session_data(self, session_data: Dict[str, Any]) -> bool:
        """恢复单个会话数据"""
        try:
            metadata = session_data.get('metadata', {})
            session_name = metadata.get('session_name', 'Restored_Session')

            # 检查是否已存在
            existing_sessions = self.session_manager.list_sessions()
            existing_names = [s['session_name'] for s in existing_sessions]

            if session_name in existing_names and not self.overwrite_var.get():
                # 生成新名称
                import uuid
                session_name = f"{session_name}_restored_{str(uuid.uuid4())[:8]}"

            # 创建新会话
            new_session = self.session_manager.create_session(
                session_name,
                metadata.get('description', '') + "\n\n[从备份恢复]"
            )

            # 恢复结果数据
            if 'results' in session_data:
                for result_name, result_info in session_data['results'].items():
                    new_session.add_result(result_name, result_info.get('data', {}),
                                         result_info.get('type', 'restored'))

            # 恢复配置数据
            if 'configs' in session_data:
                for config_name, config_info in session_data['configs'].items():
                    new_session.add_config(config_name, config_info.get('data', {}))

            # 恢复模型数据（如果选择了）
            if self.restore_models_var.get() and 'models' in session_data:
                for model_name, model_info in session_data['models'].items():
                    new_session.add_model(model_name, model_info.get('data', {}),
                                        model_info.get('type', 'restored'))

            # 恢复图表数据
            if 'plots' in session_data:
                for plot_name, plot_info in session_data['plots'].items():
                    new_session.add_plot(plot_name, plot_info.get('data', {}),
                                       plot_info.get('type', 'restored'))

            # 恢复数据信息
            if 'data_info' in session_data:
                new_session.data_info = session_data['data_info']

            # 保存会话
            self.session_manager.save_session(new_session)

            return True

        except Exception as e:
            print(f"恢复会话数据失败: {e}")
            return False

    def get_frame(self):
        """获取主框架"""
        return self.frame
