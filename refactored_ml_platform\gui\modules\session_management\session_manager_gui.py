#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话管理GUI模块
提供训练会话的图形化管理界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
from datetime import datetime
from typing import Dict, List, Optional, Any
import threading
import json
import os
from pathlib import Path

try:
    from ...core.event_manager import get_event_manager
    from ....core.session_manager import get_session_manager, TrainingSession
    from ....utils.error_handler import get_error_handler
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))

    try:
        from gui.core.event_manager import get_event_manager
        from core.session_manager import get_session_manager, TrainingSession
        from utils.error_handler import get_error_handler
    except ImportError:
        # 如果仍然失败，创建简化版本
        get_event_manager = None
        get_session_manager = None
        TrainingSession = None
        get_error_handler = None


class SessionManagerGUI:
    """会话管理GUI"""

    def __init__(self, parent):
        """初始化会话管理GUI"""
        self.parent = parent
        try:
            self.session_manager = get_session_manager()
            self.event_manager = get_event_manager()
            self.error_handler = get_error_handler()
        except:
            # 如果导入失败，创建简化版本
            self.session_manager = None
            self.event_manager = None
            self.error_handler = None

        # GUI组件
        self.main_frame = None
        self.sessions_tree = None
        self.detail_text = None

        # 控制变量
        self.selected_session_id = None
        self.status_var = tk.StringVar(value="就绪")
        self.search_var = tk.StringVar()
        self.filter_var = tk.StringVar(value="all")

        # 创建界面
        if self.session_manager:
            self._create_interface()

            # 注册事件监听
            if self.event_manager:
                self._register_events()

            # 初始加载会话列表
            self._refresh_sessions()
        else:
            self._create_simple_interface()

    def _create_simple_interface(self):
        """创建简化界面（当会话管理器不可用时）"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 显示不可用消息
        message_frame = ttk.Frame(self.main_frame)
        message_frame.pack(expand=True, fill='both')

        ttk.Label(message_frame, text="会话管理功能暂时不可用",
                 font=('Arial', 16, 'bold')).pack(pady=20)

        ttk.Label(message_frame, text="原因：会话管理器模块导入失败\n\n" +
                                    "功能说明：\n" +
                                    "• 会话管理用于保存和恢复训练状态\n" +
                                    "• 可以管理多个训练项目\n" +
                                    "• 支持导入导出训练结果\n\n" +
                                    "请检查系统配置或联系开发者",
                 justify='left').pack(pady=10)

    def _check_session_manager(self) -> bool:
        """检查会话管理器是否可用"""
        if not self.session_manager:
            messagebox.showerror("错误", "会话管理器不可用，请检查系统配置")
            return False
        return True
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.frame = self.main_frame  # 保持兼容性
        
        # 标题
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(title_frame, text="训练会话管理", font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # 工具栏
        self._create_toolbar()

        # 搜索和过滤栏
        self._create_search_filter_bar()

        # 主要内容区域
        content_frame = ttk.Frame(self.frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 左侧：会话列表
        self._create_sessions_list(content_frame)
        
        # 右侧：会话详情
        self._create_session_details(content_frame)
        
        # 状态栏
        self._create_status_bar()
    
    def _create_toolbar(self):
        """创建工具栏"""
        toolbar_frame = ttk.Frame(self.frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 会话操作按钮
        ttk.Button(toolbar_frame, text="📝 新建会话", command=self._new_session).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="🔄 激活会话", command=self._activate_session).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="💾 保存会话", command=self._save_session).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="📂 加载会话", command=self._load_session).pack(side=tk.LEFT, padx=(0, 5))
        
        # 分隔符
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # 管理操作按钮
        ttk.Button(toolbar_frame, text="📊 导出报告", command=self._export_report).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="📤 导出会话", command=self._export_session).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="📥 导入会话", command=self._import_session).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="🗑️ 删除会话", command=self._delete_session).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="🔄 刷新列表", command=self._refresh_sessions).pack(side=tk.LEFT, padx=(0, 5))
        
        # 右侧：批量操作和统计信息
        ttk.Button(toolbar_frame, text="📦 批量操作", command=self._show_batch_operations).pack(side=tk.RIGHT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="📈 统计信息", command=self._show_statistics).pack(side=tk.RIGHT)

    def _create_search_filter_bar(self):
        """创建搜索和过滤栏"""
        search_frame = ttk.Frame(self.frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        # 搜索框
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self._on_search_change)

        # 过滤下拉框
        ttk.Label(search_frame, text="状态过滤:").pack(side=tk.LEFT, padx=(10, 5))
        filter_combo = ttk.Combobox(search_frame, textvariable=self.filter_var,
                                   values=["all", "created", "active", "completed", "archived"],
                                   state="readonly", width=15)
        filter_combo.pack(side=tk.LEFT, padx=(0, 10))
        filter_combo.bind('<<ComboboxSelected>>', self._on_filter_change)

        # 清除搜索按钮
        ttk.Button(search_frame, text="🗑️ 清除", command=self._clear_search).pack(side=tk.LEFT, padx=(10, 0))
    
    def _create_sessions_list(self, parent):
        """创建会话列表"""
        # 左侧框架
        left_frame = ttk.LabelFrame(parent, text="会话列表")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 会话列表（支持多选）
        columns = ('会话ID', '会话名称', '状态', '创建时间', '模型数', '结果数')
        self.sessions_tree = ttk.Treeview(left_frame, columns=columns, show='headings', height=15, selectmode='extended')
        
        # 设置列标题和宽度
        column_widths = {'会话ID': 150, '会话名称': 200, '状态': 80, '创建时间': 150, '模型数': 60, '结果数': 60}
        for col in columns:
            self.sessions_tree.heading(col, text=col)
            self.sessions_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)
        
        # 滚动条
        sessions_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.sessions_tree.yview)
        self.sessions_tree.configure(yscrollcommand=sessions_scrollbar.set)
        
        self.sessions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        sessions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 绑定选择事件
        self.sessions_tree.bind('<<TreeviewSelect>>', self._on_session_select)
        
        # 右键菜单
        self._create_context_menu()
    
    def _create_session_details(self, parent):
        """创建会话详情面板"""
        # 右侧框架
        right_frame = ttk.LabelFrame(parent, text="会话详情")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 详情文本框
        self.detail_text = tk.Text(right_frame, wrap=tk.WORD, font=('Consolas', 10))
        detail_scrollbar = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=self.detail_text.yview)
        self.detail_text.configure(yscrollcommand=detail_scrollbar.set)
        
        self.detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 初始显示
        self._show_welcome_message()
    
    def _create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(side=tk.LEFT, padx=(5, 0))
        
        # 当前活动会话显示
        if self.session_manager:
            current_session = self.session_manager.get_current_session()
            if current_session:
                current_text = f"当前会话: {current_session.session_name}"
            else:
                current_text = "当前会话: 无"
        else:
            current_text = "当前会话: 会话管理器不可用"
        
        self.current_session_var = tk.StringVar(value=current_text)
        ttk.Label(status_frame, textvariable=self.current_session_var, foreground="green").pack(side=tk.RIGHT)
    
    def _create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.sessions_tree, tearoff=0)
        self.context_menu.add_command(label="激活会话", command=self._activate_session)
        self.context_menu.add_command(label="重命名会话", command=self._rename_session)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="导出报告", command=self._export_report)
        self.context_menu.add_command(label="导出会话", command=self._export_session)
        self.context_menu.add_command(label="复制会话", command=self._copy_session)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除会话", command=self._delete_session)
        
        # 绑定右键事件
        self.sessions_tree.bind("<Button-3>", self._show_context_menu)
    
    def _register_events(self):
        """注册事件监听"""
        self.event_manager.subscribe('session_created', self._on_session_created)
        self.event_manager.subscribe('session_saved', self._on_session_saved)
        self.event_manager.subscribe('session_activated', self._on_session_activated)
        self.event_manager.subscribe('session_deleted', self._on_session_deleted)
    
    def _refresh_sessions(self):
        """刷新会话列表"""
        try:
            self.status_var.set("正在刷新会话列表...")

            # 清空现有列表
            for item in self.sessions_tree.get_children():
                self.sessions_tree.delete(item)

            # 获取会话列表
            if not self.session_manager:
                self.status_var.set("会话管理器不可用")
                return

            sessions = self.session_manager.list_sessions()

            # 应用搜索和过滤
            filtered_sessions = self._filter_sessions(sessions)

            # 添加到树形控件
            for session in filtered_sessions:
                session_id = session['session_id']
                session_name = session['session_name']
                status = session['status']
                created_time = datetime.fromisoformat(session['created_time']).strftime('%Y-%m-%d %H:%M')
                model_count = session.get('model_count', 0)
                result_count = session.get('result_count', 0)

                self.sessions_tree.insert('', 'end', values=(
                    session_id, session_name, status, created_time, model_count, result_count
                ))

            self.status_var.set(f"显示 {len(filtered_sessions)} / {len(sessions)} 个会话")

        except Exception as e:
            self.error_handler.handle_error(e, "刷新会话列表")
            self.status_var.set("刷新失败")

    def _filter_sessions(self, sessions: List[Dict]) -> List[Dict]:
        """过滤会话列表"""
        filtered = sessions

        # 应用状态过滤
        status_filter = self.filter_var.get()
        if status_filter != "all":
            filtered = [s for s in filtered if s.get('status', '') == status_filter]

        # 应用搜索过滤
        search_text = self.search_var.get().lower().strip()
        if search_text:
            filtered = [s for s in filtered
                       if (search_text in s.get('session_name', '').lower() or
                           search_text in s.get('session_id', '').lower() or
                           search_text in s.get('description', '').lower())]

        return filtered

    def _on_search_change(self, event=None):
        """搜索内容改变事件"""
        # 延迟刷新以避免频繁更新
        if hasattr(self, '_search_timer'):
            self.parent.after_cancel(self._search_timer)
        self._search_timer = self.parent.after(500, self._refresh_sessions)

    def _on_filter_change(self, event=None):
        """过滤条件改变事件"""
        self._refresh_sessions()

    def _clear_search(self):
        """清除搜索条件"""
        self.search_var.set("")
        self.filter_var.set("all")
        self._refresh_sessions()
    
    def _on_session_select(self, event):
        """会话选择事件处理"""
        selection = self.sessions_tree.selection()
        if selection:
            item = self.sessions_tree.item(selection[0])
            self.selected_session_id = item['values'][0]
            self._show_session_details(self.selected_session_id)
    
    def _show_session_details(self, session_id: str):
        """显示会话详情"""
        try:
            session = self.session_manager.load_session(session_id)
            if session:
                details = self._format_session_details(session)
                self.detail_text.delete(1.0, tk.END)
                self.detail_text.insert(1.0, details)
            else:
                self.detail_text.delete(1.0, tk.END)
                self.detail_text.insert(1.0, "无法加载会话详情")
                
        except Exception as e:
            self.error_handler.handle_error(e, "显示会话详情")
    
    def _format_session_details(self, session: TrainingSession) -> str:
        """格式化会话详情"""
        details = f"""会话详细信息
{'='*50}

基本信息:
  会话ID: {session.session_id}
  会话名称: {session.session_name}
  描述: {session.description}
  状态: {session.status}
  创建时间: {session.created_time.strftime('%Y-%m-%d %H:%M:%S')}
  修改时间: {session.modified_time.strftime('%Y-%m-%d %H:%M:%S')}

统计信息:
  模型数量: {len(session.models)}
  结果数量: {len(session.results)}
  图表数量: {len(session.plots)}
  配置数量: {len(session.configs)}

模型列表:
"""
        
        for model_name, model_info in session.models.items():
            details += f"  • {model_name} ({model_info.get('type', 'unknown')})\n"
        
        if not session.models:
            details += "  (无模型)\n"
        
        details += "\n结果列表:\n"
        for result_name, result_info in session.results.items():
            details += f"  • {result_name} ({result_info.get('type', 'unknown')})\n"
        
        if not session.results:
            details += "  (无结果)\n"
        
        details += "\n图表列表:\n"
        for plot_name, plot_info in session.plots.items():
            details += f"  • {plot_name} ({plot_info.get('type', 'unknown')})\n"
        
        if not session.plots:
            details += "  (无图表)\n"
        
        return details
    
    def _show_welcome_message(self):
        """显示欢迎消息"""
        welcome_text = """欢迎使用训练会话管理器！

功能说明:
• 新建会话: 创建新的训练会话
• 激活会话: 设置选中会话为当前活动会话
• 保存会话: 保存当前会话的所有数据
• 加载会话: 加载选中的会话数据
• 导出报告: 生成会话的详细报告
• 删除会话: 永久删除选中的会话

使用提示:
1. 在左侧列表中选择会话查看详情
2. 右键点击会话可显示更多操作选项
3. 当前活动会话会在状态栏显示
4. 训练过程中会自动保存到当前会话

开始使用前，请先创建一个新会话或激活现有会话。
"""
        self.detail_text.delete(1.0, tk.END)
        self.detail_text.insert(1.0, welcome_text)
    
    def _new_session(self):
        """新建会话"""
        if not self._check_session_manager():
            return

        # 获取会话名称
        session_name = simpledialog.askstring("新建会话", "请输入会话名称:")
        if not session_name:
            return

        # 获取会话描述
        description = simpledialog.askstring("新建会话", "请输入会话描述 (可选):", initialvalue="")
        if description is None:
            description = ""

        try:
            # 创建会话
            session = self.session_manager.create_session(session_name, description)
            self.status_var.set(f"已创建会话: {session.session_name}")
            messagebox.showinfo("成功", f"会话 '{session.session_name}' 创建成功并已激活")

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(e, "创建会话")
            messagebox.showerror("错误", f"创建会话失败: {e}")
    
    def _activate_session(self):
        """激活会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return
        
        try:
            success = self.session_manager.activate_session(self.selected_session_id)
            if success:
                self.status_var.set(f"已激活会话: {self.selected_session_id}")
                messagebox.showinfo("成功", "会话激活成功")
            else:
                messagebox.showerror("错误", "会话激活失败")
                
        except Exception as e:
            self.error_handler.handle_error(e, "激活会话")
            messagebox.showerror("错误", f"激活会话失败: {e}")
    
    def _save_session(self):
        """保存会话"""
        current_session = self.session_manager.get_current_session()
        if not current_session:
            messagebox.showwarning("警告", "没有活动的会话可保存")
            return
        
        try:
            success = self.session_manager.save_session()
            if success:
                self.status_var.set("会话保存成功")
                messagebox.showinfo("成功", "当前会话已保存")
            else:
                messagebox.showerror("错误", "会话保存失败")
                
        except Exception as e:
            self.error_handler.handle_error(e, "保存会话")
            messagebox.showerror("错误", f"保存会话失败: {e}")
    
    def _load_session(self):
        """加载会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return
        
        try:
            session = self.session_manager.load_session(self.selected_session_id)
            if session:
                self.status_var.set(f"已加载会话: {session.session_name}")
                messagebox.showinfo("成功", f"会话 '{session.session_name}' 加载成功")
            else:
                messagebox.showerror("错误", "会话加载失败")
                
        except Exception as e:
            self.error_handler.handle_error(e, "加载会话")
            messagebox.showerror("错误", f"加载会话失败: {e}")
    
    def _delete_session(self):
        """删除会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return
        
        # 确认删除
        result = messagebox.askyesno("确认删除", 
                                   f"确定要删除会话 '{self.selected_session_id}' 吗？\n\n此操作不可撤销！")
        if not result:
            return
        
        try:
            success = self.session_manager.delete_session(self.selected_session_id)
            if success:
                self.status_var.set("会话删除成功")
                messagebox.showinfo("成功", "会话已删除")
                self.selected_session_id = None
                self._show_welcome_message()
            else:
                messagebox.showerror("错误", "会话删除失败")
                
        except Exception as e:
            self.error_handler.handle_error(e, "删除会话")
            messagebox.showerror("错误", f"删除会话失败: {e}")
    
    def _export_report(self):
        """导出报告"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return

        try:
            # 导入报告生成器
            from ....utils.report_generator import get_report_generator

            # 获取会话数据
            session = self.session_manager.load_session(self.selected_session_id)
            if not session:
                messagebox.showerror("错误", "无法加载会话数据")
                return

            # 选择保存路径
            filename = filedialog.asksaveasfilename(
                title="导出会话报告",
                defaultextension=".html",
                filetypes=[("HTML文件", "*.html"), ("所有文件", "*.*")],
                initialvalue=f"session_report_{session.session_name}.html"
            )

            if not filename:
                return

            # 生成报告
            report_generator = get_report_generator()
            session_data = session.get_summary()

            # 添加详细信息
            session_data.update({
                'models': session.models,
                'results': session.results,
                'plots': session.plots,
                'configs': session.configs
            })

            report_generator.generate_session_report(session_data, filename)

            messagebox.showinfo("成功", f"会话报告已导出到:\n{filename}")
            self.status_var.set("报告导出成功")

        except ImportError:
            messagebox.showerror("错误", "报告生成器不可用")
        except Exception as e:
            self.error_handler.handle_error(e, "导出报告")
            messagebox.showerror("错误", f"导出报告失败: {e}")

    def _export_session(self):
        """导出会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return

        try:
            # 获取会话数据
            session = self.session_manager.load_session(self.selected_session_id)
            if not session:
                messagebox.showerror("错误", "无法加载会话数据")
                return

            # 选择保存路径
            filename = filedialog.asksaveasfilename(
                title="导出会话",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
                initialvalue=f"session_{session.session_name}.json"
            )

            if not filename:
                return

            # 导出会话数据
            session_data = {
                'metadata': session.get_summary(),
                'models': {k: {**v, 'data': str(v['data'])[:1000] + '...' if len(str(v['data'])) > 1000 else str(v['data'])}
                          for k, v in session.models.items()},
                'results': session.results,
                'plots': {k: {**v, 'data': str(v['data'])[:1000] + '...' if len(str(v['data'])) > 1000 else str(v['data'])}
                         for k, v in session.plots.items()},
                'configs': session.configs,
                'data_info': session.data_info,
                'export_time': datetime.now().isoformat(),
                'export_version': '1.0'
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("成功", f"会话已导出到:\n{filename}")
            self.status_var.set("会话导出成功")

        except Exception as e:
            self.error_handler.handle_error(e, "导出会话")
            messagebox.showerror("错误", f"导出会话失败: {e}")

    def _import_session(self):
        """导入会话"""
        try:
            # 选择导入文件
            filename = filedialog.askopenfilename(
                title="导入会话",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if not filename:
                return

            # 读取会话数据
            with open(filename, 'r', encoding='utf-8') as f:
                session_data = json.load(f)

            # 验证数据格式
            if 'metadata' not in session_data:
                messagebox.showerror("错误", "无效的会话文件格式")
                return

            metadata = session_data['metadata']

            # 检查会话是否已存在
            existing_sessions = self.session_manager.list_sessions()
            existing_ids = [s['session_id'] for s in existing_sessions]

            original_id = metadata['session_id']
            if original_id in existing_ids:
                # 生成新的会话ID
                import uuid
                new_id = f"imported_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
                metadata['session_id'] = new_id
                metadata['session_name'] = f"{metadata['session_name']}_imported"

            # 创建新会话
            new_session = self.session_manager.create_session(
                metadata['session_name'],
                metadata.get('description', '') + f"\n\n[从 {Path(filename).name} 导入]"
            )

            # 导入数据
            if 'results' in session_data:
                for result_name, result_info in session_data['results'].items():
                    new_session.add_result(result_name, result_info.get('data', {}),
                                         result_info.get('type', 'imported'))

            if 'configs' in session_data:
                for config_name, config_info in session_data['configs'].items():
                    new_session.add_config(config_name, config_info.get('data', {}))

            # 保存会话
            self.session_manager.save_session(new_session)

            messagebox.showinfo("成功", f"会话已成功导入:\n{new_session.session_name}")
            self.status_var.set("会话导入成功")
            self._refresh_sessions()

        except json.JSONDecodeError:
            messagebox.showerror("错误", "无效的JSON文件格式")
        except Exception as e:
            self.error_handler.handle_error(e, "导入会话")
            messagebox.showerror("错误", f"导入会话失败: {e}")
    
    def _rename_session(self):
        """重命名会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return
        
        # 获取新名称
        new_name = simpledialog.askstring("重命名会话", "请输入新的会话名称:")
        if not new_name:
            return
        
        try:
            session = self.session_manager.load_session(self.selected_session_id)
            if session:
                session.session_name = new_name
                session.modified_time = datetime.now()
                self.session_manager.save_session(session)
                self.status_var.set("会话重命名成功")
                messagebox.showinfo("成功", "会话重命名成功")
            else:
                messagebox.showerror("错误", "无法加载会话")
                
        except Exception as e:
            self.error_handler.handle_error(e, "重命名会话")
            messagebox.showerror("错误", f"重命名会话失败: {e}")
    
    def _copy_session(self):
        """复制会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return

        try:
            # 获取原会话
            original_session = self.session_manager.load_session(self.selected_session_id)
            if not original_session:
                messagebox.showerror("错误", "无法加载原会话")
                return

            # 获取新会话名称
            new_name = simpledialog.askstring(
                "复制会话",
                "请输入新会话名称:",
                initialvalue=f"{original_session.session_name}_copy"
            )

            if not new_name:
                return

            # 创建新会话
            new_session = self.session_manager.create_session(
                new_name,
                f"复制自: {original_session.session_name}\n{original_session.description}"
            )

            # 复制数据
            # 复制模型
            for model_name, model_info in original_session.models.items():
                new_session.add_model(
                    model_name,
                    model_info['data'],
                    model_info.get('type', 'single')
                )

            # 复制结果
            for result_name, result_info in original_session.results.items():
                new_session.add_result(
                    result_name,
                    result_info['data'],
                    result_info.get('type', 'training')
                )

            # 复制图表
            for plot_name, plot_info in original_session.plots.items():
                new_session.add_plot(
                    plot_name,
                    plot_info['data'],
                    plot_info.get('type', 'single_model')
                )

            # 复制配置
            for config_name, config_info in original_session.configs.items():
                new_session.add_config(config_name, config_info['data'])

            # 复制数据信息
            new_session.data_info = original_session.data_info.copy()

            # 保存新会话
            self.session_manager.save_session(new_session)

            messagebox.showinfo("成功", f"会话已复制为: {new_session.session_name}")
            self.status_var.set("会话复制成功")
            self._refresh_sessions()

        except Exception as e:
            self.error_handler.handle_error(e, "复制会话")
            messagebox.showerror("错误", f"复制会话失败: {e}")
    
    def _show_statistics(self):
        """显示统计信息"""
        try:
            sessions = self.session_manager.list_sessions()
            
            total_sessions = len(sessions)
            total_models = sum(s.get('model_count', 0) for s in sessions)
            total_results = sum(s.get('result_count', 0) for s in sessions)
            
            # 按状态统计
            status_counts = {}
            for session in sessions:
                status = session.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
            
            stats_text = f"""会话统计信息
{'='*30}

总体统计:
  总会话数: {total_sessions}
  总模型数: {total_models}
  总结果数: {total_results}

状态分布:
"""
            
            for status, count in status_counts.items():
                stats_text += f"  {status}: {count}\n"
            
            # 显示统计窗口
            stats_window = tk.Toplevel(self.parent)
            stats_window.title("统计信息")
            stats_window.geometry("400x300")
            
            text_widget = tk.Text(stats_window, wrap=tk.WORD, font=('Consolas', 10))
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(1.0, stats_text)
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            self.error_handler.handle_error(e, "显示统计信息")
            messagebox.showerror("错误", f"获取统计信息失败: {e}")

    def _show_batch_operations(self):
        """显示批量操作窗口"""
        batch_window = tk.Toplevel(self.parent)
        batch_window.title("批量操作")
        batch_window.geometry("500x400")
        batch_window.transient(self.parent)
        batch_window.grab_set()

        # 操作选择
        operation_frame = ttk.LabelFrame(batch_window, text="选择操作")
        operation_frame.pack(fill=tk.X, padx=10, pady=10)

        operation_var = tk.StringVar(value="delete")
        operations = [
            ("批量删除", "delete"),
            ("批量导出", "export"),
            ("批量归档", "archive"),
            ("批量激活", "activate")
        ]

        for i, (text, value) in enumerate(operations):
            ttk.Radiobutton(operation_frame, text=text, variable=operation_var,
                           value=value).grid(row=i//2, column=i%2, sticky=tk.W, padx=10, pady=5)

        # 会话选择
        selection_frame = ttk.LabelFrame(batch_window, text="选择会话")
        selection_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 会话列表
        batch_tree = ttk.Treeview(selection_frame, columns=('ID', '名称', '状态'),
                                 show='headings', height=10, selectmode='extended')

        for col in ['ID', '名称', '状态']:
            batch_tree.heading(col, text=col)
            batch_tree.column(col, width=150)

        # 填充会话数据
        sessions = self.session_manager.list_sessions()
        for session in sessions:
            batch_tree.insert('', 'end', values=(
                session['session_id'][:20] + '...' if len(session['session_id']) > 20 else session['session_id'],
                session['session_name'],
                session['status']
            ))

        batch_scrollbar = ttk.Scrollbar(selection_frame, orient=tk.VERTICAL, command=batch_tree.yview)
        batch_tree.configure(yscrollcommand=batch_scrollbar.set)

        batch_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        batch_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

        # 选择按钮
        select_frame = ttk.Frame(batch_window)
        select_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(select_frame, text="全选",
                  command=lambda: batch_tree.selection_set(batch_tree.get_children())).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(select_frame, text="取消全选",
                  command=lambda: batch_tree.selection_remove(batch_tree.get_children())).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(select_frame, text="反选",
                  command=lambda: self._invert_selection(batch_tree)).pack(side=tk.LEFT, padx=(0, 5))

        # 执行按钮
        button_frame = ttk.Frame(batch_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        def execute_batch_operation():
            selected_items = batch_tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请选择要操作的会话")
                return

            operation = operation_var.get()
            session_ids = []

            for item in selected_items:
                values = batch_tree.item(item)['values']
                # 找到完整的session_id
                for session in sessions:
                    if session['session_name'] == values[1]:
                        session_ids.append(session['session_id'])
                        break

            if not session_ids:
                messagebox.showerror("错误", "无法获取会话ID")
                return

            # 确认操作
            if not messagebox.askyesno("确认", f"确定要对 {len(session_ids)} 个会话执行{dict(operations)[operation]}操作吗？"):
                return

            try:
                self._execute_batch_operation(operation, session_ids)
                batch_window.destroy()
                self._refresh_sessions()
                messagebox.showinfo("成功", f"批量操作完成，处理了 {len(session_ids)} 个会话")
            except Exception as e:
                messagebox.showerror("错误", f"批量操作失败: {e}")

        ttk.Button(button_frame, text="执行操作", command=execute_batch_operation).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=batch_window.destroy).pack(side=tk.RIGHT)

    def _invert_selection(self, tree):
        """反选树形控件中的项目"""
        all_items = set(tree.get_children())
        selected_items = set(tree.selection())
        new_selection = all_items - selected_items

        tree.selection_remove(tree.get_children())
        tree.selection_add(list(new_selection))

    def _execute_batch_operation(self, operation: str, session_ids: List[str]):
        """执行批量操作"""
        success_count = 0
        error_count = 0

        for session_id in session_ids:
            try:
                if operation == "delete":
                    if self.session_manager.delete_session(session_id):
                        success_count += 1
                    else:
                        error_count += 1

                elif operation == "export":
                    # 导出到默认目录
                    export_dir = Path("output/batch_export")
                    export_dir.mkdir(parents=True, exist_ok=True)

                    session = self.session_manager.load_session(session_id)
                    if session:
                        export_file = export_dir / f"session_{session.session_name}.json"
                        session_data = {
                            'metadata': session.get_summary(),
                            'results': session.results,
                            'configs': session.configs,
                            'export_time': datetime.now().isoformat()
                        }

                        with open(export_file, 'w', encoding='utf-8') as f:
                            json.dump(session_data, f, indent=2, ensure_ascii=False)

                        success_count += 1
                    else:
                        error_count += 1

                elif operation == "archive":
                    session = self.session_manager.load_session(session_id)
                    if session:
                        session.status = "archived"
                        session.modified_time = datetime.now()
                        self.session_manager.save_session(session)
                        success_count += 1
                    else:
                        error_count += 1

                elif operation == "activate":
                    if self.session_manager.activate_session(session_id):
                        success_count += 1
                    else:
                        error_count += 1

            except Exception as e:
                self.logger.error(f"批量操作失败 {session_id}: {e}")
                error_count += 1

        if error_count > 0:
            messagebox.showwarning("部分失败", f"成功: {success_count}, 失败: {error_count}")

        self.status_var.set(f"批量操作完成: 成功 {success_count}, 失败 {error_count}")
    
    def _show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def _on_session_created(self, event_data):
        """会话创建事件处理"""
        self._refresh_sessions()
        self._update_current_session_display()
    
    def _on_session_saved(self, event_data):
        """会话保存事件处理"""
        self._refresh_sessions()
    
    def _on_session_activated(self, event_data):
        """会话激活事件处理"""
        self._update_current_session_display()
    
    def _on_session_deleted(self, event_data):
        """会话删除事件处理"""
        self._refresh_sessions()
        self._update_current_session_display()
    
    def _update_current_session_display(self):
        """更新当前会话显示"""
        current_session = self.session_manager.get_current_session()
        if current_session:
            current_text = f"当前会话: {current_session.session_name}"
        else:
            current_text = "当前会话: 无"
        
        self.current_session_var.set(current_text)
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
