"""
会话管理标签页
提供项目会话的创建、保存、加载和管理功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager


class SessionManagerTab(BaseGUI):
    """会话管理标签页"""
    
    def __init__(self, parent: tk.Widget):
        """初始化会话管理标签页"""
        self.current_session = None
        self.sessions_data = {}
        self.session_file_path = None
        # 会话存储目录与索引
        self.sessions_dir = os.path.join(os.getcwd(), 'sessions')
        os.makedirs(self.sessions_dir, exist_ok=True)
        self.sessions_index: Dict[str, str] = {}
        
        # 会话数据结构
        self.session_template = {
            'session_id': '',
            'name': '',
            'description': '',
            'created_time': '',
            'modified_time': '',
            'data_info': {},
            'models_info': {},
            'ensemble_info': {},
            'visualization_config': {},
            'settings': {}
        }
        
        super().__init__(parent)
        
        # 订阅各种事件以自动保存会话状态
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.DATA_LOADED, self._on_data_loaded)
        event_manager.subscribe(EventTypes.MODEL_TRAINED, self._on_models_trained)
        event_manager.subscribe('ensemble_training_completed', self._on_ensemble_completed)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='main')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建上下分割
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.VERTICAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 上方控制面板
        top_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(top_frame, weight=1)
        
        # 下方会话列表面板
        bottom_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(bottom_frame, weight=2)
        
        # 设置控制面板
        self._setup_control_panel(top_frame)
        
        # 设置会话列表面板
        self._setup_sessions_panel(bottom_frame)
        
        self.register_component('main_frame', self.main_frame)
        self.register_component('paned_window', paned_window)
    
    def _setup_control_panel(self, parent):
        """设置控制面板"""
        factory = get_component_factory()
        
        # 当前会话信息
        current_frame = factory.create_frame(parent, style='section')
        current_frame.pack(fill=tk.X, padx=5, pady=5)
        factory.create_label(current_frame, text="当前会话", style='title').pack(anchor=tk.W, padx=5, pady=(5, 0))
        
        # 会话名称
        name_frame = factory.create_frame(current_frame)
        name_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(name_frame, text="会话名称:").pack(side=tk.LEFT)
        self.session_name_var = tk.StringVar(value="新建会话")
        name_entry = factory.create_entry(name_frame, textvariable=self.session_name_var)
        name_entry.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        
        # 会话描述
        desc_frame = factory.create_frame(current_frame)
        desc_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(desc_frame, text="会话描述:").pack(anchor=tk.W)
        self.session_desc_text = factory.create_text(desc_frame, height=3, wrap=tk.WORD)
        desc_scrollbar = factory.create_scrollbar(desc_frame, orient=tk.VERTICAL)
        desc_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.session_desc_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.session_desc_text.configure(yscrollcommand=desc_scrollbar.set)
        desc_scrollbar.configure(command=self.session_desc_text.yview)
        
        # 会话操作按钮
        buttons_frame = factory.create_frame(parent)
        buttons_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # 第一行按钮
        buttons_row1 = factory.create_frame(buttons_frame)
        buttons_row1.pack(fill=tk.X, pady=(0, 5))
        
        new_btn = factory.create_button(
            buttons_row1,
            text="新建会话",
            command=self._new_session,
            style='primary'
        )
        new_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        save_btn = factory.create_button(
            buttons_row1,
            text="保存会话",
            command=self._save_session,
            style='secondary'
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        load_btn = factory.create_button(
            buttons_row1,
            text="加载会话",
            command=self._load_session,
            style='secondary'
        )
        load_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 第二行按钮
        buttons_row2 = factory.create_frame(buttons_frame)
        buttons_row2.pack(fill=tk.X)
        
        auto_save_btn = factory.create_button(
            buttons_row2,
            text="自动保存",
            command=self._toggle_auto_save,
            style='secondary'
        )
        auto_save_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        delete_btn = factory.create_button(
            buttons_row2,
            text="删除会话",
            command=self._delete_session,
            style='danger'
        )
        delete_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 自动保存状态
        self.auto_save_var = tk.BooleanVar(value=True)
        auto_save_check = factory.create_checkbutton(
            buttons_row2,
            text="启用自动保存",
            variable=self.auto_save_var
        )
        auto_save_check.pack(side=tk.RIGHT)
        
        # 状态标签
        self.status_label = factory.create_label(
            parent,
            text="准备就绪",
            style='info'
        )
        self.status_label.pack(padx=5, pady=5)
    
    def _setup_sessions_panel(self, parent):
        """设置会话列表面板"""
        factory = get_component_factory()
        
        # 会话列表
        sessions_frame = factory.create_frame(parent, style='section')
        sessions_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        factory.create_label(sessions_frame, text="会话列表", style='title').pack(anchor=tk.W, padx=5, pady=(5, 0))
        
        # 会话表格
        columns = ['会话名称', '创建时间', '修改时间', '数据集', '模型数量', '描述']
        self.sessions_tree = factory.create_treeview(
            sessions_frame,
            columns=columns,
            show='headings'
        )
        
        # 设置列
        column_widths = {'会话名称': 150, '创建时间': 120, '修改时间': 120, 
                        '数据集': 100, '模型数量': 80, '描述': 200}
        
        for col in columns:
            self.sessions_tree.heading(col, text=col)
            self.sessions_tree.column(col, width=column_widths.get(col, 100), anchor=tk.W)
        
        # 滚动条
        sessions_scrollbar = factory.create_scrollbar(sessions_frame, orient=tk.VERTICAL)
        sessions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.sessions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.sessions_tree.configure(yscrollcommand=sessions_scrollbar.set)
        sessions_scrollbar.configure(command=self.sessions_tree.yview)
        
        # 绑定双击事件
        self.sessions_tree.bind('<Double-1>', self._on_session_double_click)
        
        # 右键菜单
        self.context_menu = tk.Menu(self.sessions_tree, tearoff=0)
        self.context_menu.add_command(label="加载会话", command=self._load_selected_session)
        self.context_menu.add_command(label="重命名", command=self._rename_session)
        self.context_menu.add_command(label="复制会话", command=self._copy_session)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除会话", command=self._delete_selected_session)
        
        self.sessions_tree.bind('<Button-3>', self._show_context_menu)
        
        # 初始化会话列表
        self._load_sessions_list()
    
    def _new_session(self):
        """新建会话"""
        # 如果当前有未保存的会话，询问是否保存
        if self.current_session and self._has_unsaved_changes():
            result = messagebox.askyesnocancel("保存会话", "当前会话有未保存的更改，是否保存？")
            if result is None:  # 取消
                return
            elif result:  # 是
                self._save_session()
        
        # 创建新会话
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.current_session = self.session_template.copy()
        self.current_session.update({
            'session_id': session_id,
            'name': self.session_name_var.get(),
            'created_time': datetime.now().isoformat(),
            'modified_time': datetime.now().isoformat()
        })
        
        self.status_label.config(text=f"新建会话: {self.current_session['name']}")
        self.logger.info(f"新建会话: {session_id}")
    
    def _save_session(self):
        """保存会话"""
        if not self.current_session:
            self._new_session()
        
        # 更新会话信息
        self.current_session['name'] = self.session_name_var.get()
        self.current_session['description'] = self.session_desc_text.get(1.0, tk.END).strip()
        self.current_session['modified_time'] = datetime.now().isoformat()
        
        # 选择保存位置
        if not self.session_file_path:
            file_path = filedialog.asksaveasfilename(
                title="保存会话",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if not file_path:
                return
            self.session_file_path = file_path
        
        try:
            # 保存到文件
            with open(self.session_file_path, 'w', encoding='utf-8') as f:
                json.dump(self.current_session, f, indent=2, ensure_ascii=False)
            
            # 更新会话列表
            self._update_sessions_list()
            
            self.status_label.config(text=f"会话已保存: {self.current_session['name']}")
            self.logger.info(f"会话已保存: {self.session_file_path}")
            
        except Exception as e:
            messagebox.showerror("保存失败", f"保存会话时出错:\n{str(e)}")
            self.logger.error(f"保存会话失败: {e}")
    
    def _load_session(self):
        """加载会话"""
        file_path = filedialog.askopenfilename(
            title="加载会话",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            self.current_session = session_data
            self.session_file_path = file_path
            
            # 更新UI
            self.session_name_var.set(session_data.get('name', ''))
            self.session_desc_text.delete(1.0, tk.END)
            self.session_desc_text.insert(1.0, session_data.get('description', ''))
            
            # 发布会话加载事件
            event_manager = get_event_manager()
            event_manager.publish('session_loaded', {'session_data': session_data})
            
            self.status_label.config(text=f"会话已加载: {session_data.get('name', '')}")
            self.logger.info(f"会话已加载: {file_path}")
            
        except Exception as e:
            messagebox.showerror("加载失败", f"加载会话时出错:\n{str(e)}")
            self.logger.error(f"加载会话失败: {e}")
    
    def _delete_session(self):
        """删除当前会话"""
        if not self.current_session:
            messagebox.showinfo("提示", "没有当前会话可删除")
            return
        
        result = messagebox.askyesno("确认删除", f"确定要删除会话 '{self.current_session['name']}' 吗？")
        if result:
            if self.session_file_path and os.path.exists(self.session_file_path):
                try:
                    os.remove(self.session_file_path)
                    self.logger.info(f"会话文件已删除: {self.session_file_path}")
                except Exception as e:
                    self.logger.error(f"删除会话文件失败: {e}")
            
            self.current_session = None
            self.session_file_path = None
            self.session_name_var.set("新建会话")
            self.session_desc_text.delete(1.0, tk.END)
            
            self._update_sessions_list()
            self.status_label.config(text="会话已删除")
    
    def _toggle_auto_save(self):
        """切换自动保存状态"""
        auto_save_enabled = self.auto_save_var.get()
        status = "启用" if auto_save_enabled else "禁用"
        self.status_label.config(text=f"自动保存已{status}")
        self.logger.info(f"自动保存已{status}")
    
    def _load_sessions_list(self):
        """加载会话列表"""
        # 这里可以从配置文件或数据库加载会话列表
        # 目前使用示例数据
        try:
            # 清空现有项目与索引
            if hasattr(self, 'sessions_tree'):
                for item in self.sessions_tree.get_children():
                    self.sessions_tree.delete(item)
            self.sessions_index.clear()
            
            # 扫描会话目录下的 json 文件
            if not os.path.isdir(self.sessions_dir):
                os.makedirs(self.sessions_dir, exist_ok=True)
            for fname in os.listdir(self.sessions_dir):
                if not fname.lower().endswith('.json'):
                    continue
                fpath = os.path.join(self.sessions_dir, fname)
                try:
                    with open(fpath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    sid = data.get('session_id') or os.path.splitext(fname)[0]
                    values = (
                        data.get('name', sid),
                        (data.get('created_time', '') or '').split('T')[0],
                        (data.get('modified_time', '') or '').split('T')[0],
                        data.get('data_info', {}).get('dataset_name', ''),
                        len(data.get('models_info', {})),
                        (data.get('description', '')[:50] + '...') if len(data.get('description', '')) > 50 else data.get('description', '')
                    )
                    self.sessions_tree.insert('', tk.END, values=values, tags=(sid,))
                    self.sessions_index[sid] = fpath
                except Exception as e:
                    # 忽略损坏的会话文件但记录日志
                    self.logger.warning(f"读取会话文件失败 {fpath}: {e}")
        except Exception as e:
            self.logger.error(f"加载会话列表失败: {e}")
    
    def _update_sessions_list(self):
        """更新会话列表"""
        if not self.current_session:
            return
        
        # 清空现有项目
        for item in self.sessions_tree.get_children():
            self.sessions_tree.delete(item)
        
        # 添加当前会话
        session = self.current_session
        values = (
            session.get('name', ''),
            session.get('created_time', '').split('T')[0] if session.get('created_time') else '',
            session.get('modified_time', '').split('T')[0] if session.get('modified_time') else '',
            session.get('data_info', {}).get('dataset_name', ''),
            len(session.get('models_info', {})),
            session.get('description', '')[:50] + '...' if len(session.get('description', '')) > 50 else session.get('description', '')
        )
        
        self.sessions_tree.insert('', tk.END, values=values, tags=(session.get('session_id', ''),))
    
    def _on_session_double_click(self, event):
        """会话双击事件"""
        self._load_selected_session()
    
    def _load_selected_session(self):
        """加载选中的会话"""
        selection = self.sessions_tree.selection()
        if not selection:
            return
        # 取出第一个选中的条目tag中的session_id
        item_id = selection[0]
        tags = self.sessions_tree.item(item_id, 'tags')
        if not tags:
            return
        sid = tags[0]
        fpath = self.sessions_index.get(sid)
        if not fpath or not os.path.exists(fpath):
            messagebox.showerror("错误", "会话文件不存在或已被移除")
            return
        try:
            with open(fpath, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            self.current_session = session_data
            self.session_file_path = fpath
            # 更新UI
            self.session_name_var.set(session_data.get('name', ''))
            self.session_desc_text.delete(1.0, tk.END)
            self.session_desc_text.insert(1.0, session_data.get('description', ''))
            # 发布事件
            event_manager = get_event_manager()
            event_manager.publish('session_loaded', {'session_data': session_data})
            self.status_label.config(text=f"会话已加载: {session_data.get('name', '')}")
            self.logger.info(f"会话已加载: {fpath}")
        except Exception as e:
            messagebox.showerror("加载失败", f"加载会话时出错:\n{str(e)}")
            self.logger.error(f"加载选中会话失败: {e}")
    
    def _show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def _rename_session(self):
        """重命名会话"""
        selection = self.sessions_tree.selection()
        if not selection:
            return
        item_id = selection[0]
        tags = self.sessions_tree.item(item_id, 'tags')
        if not tags:
            return
        sid = tags[0]
        fpath = self.sessions_index.get(sid)
        if not fpath or not os.path.exists(fpath):
            messagebox.showerror("错误", "会话文件不存在或已被移除")
            return
        
        # 获取当前名称
        current_name = self.sessions_tree.item(item_id, 'values')[0]
        
        # 弹出输入对话框
        from tkinter import simpledialog
        new_name = simpledialog.askstring("重命名会话", "请输入新的会话名称:", initialvalue=current_name)
        if not new_name or new_name == current_name:
            return
        
        try:
            # 读取会话数据
            with open(fpath, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            # 更新名称和修改时间
            session_data['name'] = new_name
            session_data['modified_time'] = datetime.now().isoformat()
            
            # 保存回文件
            with open(fpath, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            
            # 如果是当前会话，更新UI
            if self.current_session and self.current_session.get('session_id') == sid:
                self.current_session['name'] = new_name
                self.session_name_var.set(new_name)
            
            # 刷新列表
            self._load_sessions_list()
            self.status_label.config(text=f"会话已重命名: {new_name}")
            self.logger.info(f"会话已重命名: {fpath} -> {new_name}")
            
        except Exception as e:
            messagebox.showerror("重命名失败", f"重命名会话时出错:\n{str(e)}")
            self.logger.error(f"重命名会话失败: {e}")
    
    def _copy_session(self):
        """复制会话"""
        selection = self.sessions_tree.selection()
        if not selection:
            return
        item_id = selection[0]
        tags = self.sessions_tree.item(item_id, 'tags')
        if not tags:
            return
        sid = tags[0]
        fpath = self.sessions_index.get(sid)
        if not fpath or not os.path.exists(fpath):
            messagebox.showerror("错误", "会话文件不存在或已被移除")
            return
        
        try:
            # 读取原会话数据
            with open(fpath, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            
            # 创建副本数据
            import copy
            copied_data = copy.deepcopy(original_data)
            
            # 生成新的会话ID和名称
            new_session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            original_name = copied_data.get('name', '未命名会话')
            copied_data.update({
                'session_id': new_session_id,
                'name': f"{original_name}_副本",
                'created_time': datetime.now().isoformat(),
                'modified_time': datetime.now().isoformat()
            })
            
            # 保存到新文件
            new_fpath = os.path.join(self.sessions_dir, f"{new_session_id}.json")
            with open(new_fpath, 'w', encoding='utf-8') as f:
                json.dump(copied_data, f, indent=2, ensure_ascii=False)
            
            # 刷新列表
            self._load_sessions_list()
            self.status_label.config(text=f"会话已复制: {copied_data['name']}")
            self.logger.info(f"会话已复制: {fpath} -> {new_fpath}")
            
        except Exception as e:
            messagebox.showerror("复制失败", f"复制会话时出错:\n{str(e)}")
            self.logger.error(f"复制会话失败: {e}")
    
    def _delete_selected_session(self):
        """删除选中的会话"""
        selection = self.sessions_tree.selection()
        if not selection:
            return
        item_id = selection[0]
        tags = self.sessions_tree.item(item_id, 'tags')
        if not tags:
            return
        sid = tags[0]
        fpath = self.sessions_index.get(sid)
        name = self.sessions_tree.item(item_id, 'values')[0]
        if not messagebox.askyesno("确认删除", f"确定要删除会话 '{name}' 吗？"):
            return
        try:
            if fpath and os.path.exists(fpath):
                os.remove(fpath)
            # 如果当前会话就是删除的会话，则清空当前会话
            if self.current_session and self.current_session.get('session_id') == sid:
                self.current_session = None
                self.session_file_path = None
                self.session_name_var.set("新建会话")
                self.session_desc_text.delete(1.0, tk.END)
            # 刷新列表
            self._load_sessions_list()
            self.status_label.config(text="会话已删除")
            self.logger.info(f"会话已删除: {fpath or sid}")
        except Exception as e:
            messagebox.showerror("删除失败", f"删除会话时出错:\n{str(e)}")
            self.logger.error(f"删除选中会话失败: {e}")
    
    def _has_unsaved_changes(self) -> bool:
        """检查是否有未保存的更改"""
        if not self.current_session:
            return False
        
        # 简单检查：比较当前UI状态与会话数据
        current_name = self.session_name_var.get()
        current_desc = self.session_desc_text.get(1.0, tk.END).strip()
        
        return (current_name != self.current_session.get('name', '') or
                current_desc != self.current_session.get('description', ''))
    
    def _on_data_loaded(self, event_data: Dict[str, Any]):
        """数据加载事件处理"""
        if self.current_session and self.auto_save_var.get():
            self.current_session['data_info'] = event_data
            self.current_session['modified_time'] = datetime.now().isoformat()
            self._auto_save()
    
    def _on_models_trained(self, event_data: Dict[str, Any]):
        """模型训练完成事件处理"""
        if self.current_session and self.auto_save_var.get():
            self.current_session['models_info'] = event_data.get('results', {})
            self.current_session['modified_time'] = datetime.now().isoformat()
            self._auto_save()
    
    def _on_ensemble_completed(self, event_data: Dict[str, Any]):
        """集成训练完成事件处理"""
        if self.current_session and self.auto_save_var.get():
            self.current_session['ensemble_info'] = event_data.get('results', {})
            self.current_session['modified_time'] = datetime.now().isoformat()
            self._auto_save()
    
    def _auto_save(self):
        """自动保存"""
        if self.session_file_path:
            try:
                with open(self.session_file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.current_session, f, indent=2, ensure_ascii=False)
                self.logger.info("会话已自动保存")
            except Exception as e:
                self.logger.error(f"自动保存失败: {e}")
    
    def get_current_session(self) -> Optional[Dict[str, Any]]:
        """获取当前会话"""
        return self.current_session.copy() if self.current_session else None
