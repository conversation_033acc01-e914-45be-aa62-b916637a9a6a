#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU设置GUI模块
提供GPU配置和监控的图形化界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Dict, List, Optional, Any

from ....utils.gpu_manager import get_gpu_manager
from ....core.event_manager import get_event_manager
from ....utils.error_handler import get_error_handler


class GPUSettingsGUI:
    """GPU设置GUI"""
    
    def __init__(self, parent):
        """初始化GPU设置GUI"""
        self.parent = parent
        self.gpu_manager = get_gpu_manager()
        self.event_manager = get_event_manager()
        self.error_handler = get_error_handler()
        
        # GUI组件
        self.frame = None
        
        # 控制变量
        self.use_gpu_var = tk.BooleanVar()
        self.auto_detect_var = tk.BooleanVar()
        self.fallback_cpu_var = tk.BooleanVar()
        self.device_type_var = tk.StringVar()
        self.memory_limit_var = tk.IntVar()
        self.status_var = tk.StringVar(value="就绪")
        
        # GPU设备变量
        self.gpu_device_vars = {}
        
        # 创建界面
        self._create_interface()
        
        # 加载当前配置
        self._load_current_config()
        
        # 刷新GPU信息
        self._refresh_gpu_info()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.frame = ttk.Frame(self.parent)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(title_frame, text="GPU设置", font=('Arial', 16, 'bold')).pack()
        ttk.Label(title_frame, text="配置GPU加速和设备管理", 
                 foreground="gray").pack()
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self._create_general_settings_tab()
        self._create_device_info_tab()
        self._create_performance_tab()
    
    def _create_general_settings_tab(self):
        """创建常规设置选项卡"""
        general_tab = ttk.Frame(self.notebook)
        self.notebook.add(general_tab, text="⚙️ 常规设置")
        
        # GPU启用设置
        gpu_frame = ttk.LabelFrame(general_tab, text="GPU加速设置")
        gpu_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # GPU开关
        gpu_switch_frame = ttk.Frame(gpu_frame)
        gpu_switch_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Checkbutton(gpu_switch_frame, text="启用GPU加速", 
                       variable=self.use_gpu_var,
                       command=self._on_gpu_toggle).pack(side=tk.LEFT)
        
        ttk.Button(gpu_switch_frame, text="🔍 检测GPU", 
                  command=self._detect_gpu).pack(side=tk.RIGHT)
        
        # 自动检测和回退设置
        auto_frame = ttk.Frame(gpu_frame)
        auto_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Checkbutton(auto_frame, text="自动检测GPU能力", 
                       variable=self.auto_detect_var).pack(side=tk.LEFT)
        
        ttk.Checkbutton(auto_frame, text="GPU不可用时回退到CPU", 
                       variable=self.fallback_cpu_var).pack(side=tk.RIGHT)
        
        # 设备类型选择
        device_frame = ttk.LabelFrame(general_tab, text="设备类型")
        device_frame.pack(fill=tk.X, padx=10, pady=10)
        
        device_options_frame = ttk.Frame(device_frame)
        device_options_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(device_options_frame, text="设备类型:").pack(side=tk.LEFT, padx=(0, 10))
        
        device_types = ["auto", "cuda", "opencl", "cpu"]
        self.device_combo = ttk.Combobox(device_options_frame, textvariable=self.device_type_var,
                                        values=device_types, state="readonly", width=15)
        self.device_combo.pack(side=tk.LEFT, padx=(0, 20))
        
        # 内存限制设置
        memory_frame = ttk.LabelFrame(general_tab, text="内存管理")
        memory_frame.pack(fill=tk.X, padx=10, pady=10)
        
        memory_options_frame = ttk.Frame(memory_frame)
        memory_options_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(memory_options_frame, text="GPU内存限制 (MB):").pack(side=tk.LEFT, padx=(0, 10))
        
        memory_spinbox = tk.Spinbox(memory_options_frame, textvariable=self.memory_limit_var,
                                   from_=0, to=32768, increment=512, width=10)
        memory_spinbox.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(memory_options_frame, text="(0 = 无限制)").pack(side=tk.LEFT)
        
        # GPU设备选择
        self.devices_frame = ttk.LabelFrame(general_tab, text="GPU设备选择")
        self.devices_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 操作按钮
        actions_frame = ttk.Frame(general_tab)
        actions_frame.pack(fill=tk.X, padx=10, pady=20)
        
        ttk.Button(actions_frame, text="💾 应用设置", 
                  command=self._apply_settings, style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(actions_frame, text="🔄 重置设置", 
                  command=self._reset_settings).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(actions_frame, text="🧪 测试GPU", 
                  command=self._test_gpu).pack(side=tk.LEFT, padx=(0, 10))
        
        # 状态栏
        status_frame = ttk.Frame(general_tab)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(
            side=tk.LEFT, padx=(5, 0))
    
    def _create_device_info_tab(self):
        """创建设备信息选项卡"""
        info_tab = ttk.Frame(self.notebook)
        self.notebook.add(info_tab, text="🖥️ 设备信息")
        
        # 设备信息显示
        info_frame = ttk.LabelFrame(info_tab, text="GPU设备信息")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 信息文本框
        self.info_text = tk.Text(info_frame, wrap=tk.WORD, font=('Consolas', 10))
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 刷新按钮
        refresh_frame = ttk.Frame(info_tab)
        refresh_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(refresh_frame, text="🔄 刷新信息", 
                  command=self._refresh_gpu_info).pack(side=tk.LEFT)
        
        ttk.Button(refresh_frame, text="📋 复制信息", 
                  command=self._copy_gpu_info).pack(side=tk.LEFT, padx=(10, 0))
    
    def _create_performance_tab(self):
        """创建性能监控选项卡"""
        perf_tab = ttk.Frame(self.notebook)
        self.notebook.add(perf_tab, text="📊 性能监控")
        
        # 性能监控区域
        monitor_frame = ttk.LabelFrame(perf_tab, text="GPU使用监控")
        monitor_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建性能表格
        columns = ('设备', 'GPU使用率', '内存使用', '温度', '功耗')
        self.perf_tree = ttk.Treeview(monitor_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.perf_tree.heading(col, text=col)
            self.perf_tree.column(col, width=100, anchor=tk.CENTER)
        
        perf_scrollbar = ttk.Scrollbar(monitor_frame, orient=tk.VERTICAL, command=self.perf_tree.yview)
        self.perf_tree.configure(yscrollcommand=perf_scrollbar.set)
        
        self.perf_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        perf_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 监控控制
        control_frame = ttk.Frame(perf_tab)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.monitor_var = tk.BooleanVar()
        ttk.Checkbutton(control_frame, text="启用实时监控", 
                       variable=self.monitor_var,
                       command=self._toggle_monitoring).pack(side=tk.LEFT)
        
        ttk.Button(control_frame, text="📊 刷新数据", 
                  command=self._refresh_performance).pack(side=tk.LEFT, padx=(20, 0))
    
    def _load_current_config(self):
        """加载当前配置"""
        try:
            config = self.gpu_manager.gpu_config
            
            self.use_gpu_var.set(config.get('use_gpu', False))
            self.auto_detect_var.set(config.get('auto_detect', True))
            self.fallback_cpu_var.set(config.get('fallback_to_cpu', True))
            self.device_type_var.set(config.get('device_type', 'auto'))
            self.memory_limit_var.set(config.get('memory_limit', 0) or 0)
            
        except Exception as e:
            self.error_handler.handle_error(e, "加载GPU配置")
    
    def _refresh_gpu_info(self):
        """刷新GPU信息"""
        try:
            # 在后台线程中获取GPU信息
            threading.Thread(target=self._update_gpu_info, daemon=True).start()
            
        except Exception as e:
            self.error_handler.handle_error(e, "刷新GPU信息")
    
    def _update_gpu_info(self):
        """更新GPU信息"""
        try:
            # 获取GPU信息
            gpu_info = self.gpu_manager.get_gpu_info()
            device_summary = self.gpu_manager.get_device_info_summary()
            
            # 更新信息显示
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, device_summary)
            
            # 更新设备选择界面
            self._update_device_selection(gpu_info)
            
            self.status_var.set("GPU信息已更新")
            
        except Exception as e:
            self.error_handler.handle_error(e, "更新GPU信息")
            self.status_var.set("GPU信息更新失败")
    
    def _update_device_selection(self, gpu_info: Dict[str, Any]):
        """更新设备选择界面"""
        # 清空现有设备选择
        for widget in self.devices_frame.winfo_children():
            widget.destroy()
        
        self.gpu_device_vars.clear()
        
        if gpu_info['gpu_count'] == 0:
            ttk.Label(self.devices_frame, text="未检测到GPU设备", 
                     foreground="red").pack(padx=10, pady=10)
            return
        
        # 创建设备选择复选框
        ttk.Label(self.devices_frame, text="选择要使用的GPU设备:").pack(anchor=tk.W, padx=10, pady=(10, 5))
        
        for device in gpu_info['gpu_devices']:
            device_id = device['id']
            device_name = device['name']
            memory_info = f"{device['memory_free']:.0f}MB / {device['memory_total']:.0f}MB"
            
            var = tk.BooleanVar()
            self.gpu_device_vars[device_id] = var
            
            # 默认选择第一个设备
            if device_id == 0:
                var.set(True)
            
            device_frame = ttk.Frame(self.devices_frame)
            device_frame.pack(fill=tk.X, padx=20, pady=2)
            
            ttk.Checkbutton(device_frame, text=f"GPU {device_id}: {device_name}", 
                           variable=var).pack(side=tk.LEFT)
            
            ttk.Label(device_frame, text=f"({memory_info})", 
                     foreground="gray").pack(side=tk.RIGHT)
    
    def _on_gpu_toggle(self):
        """GPU开关切换事件"""
        if self.use_gpu_var.get():
            self.status_var.set("GPU加速已启用")
        else:
            self.status_var.set("GPU加速已禁用")
    
    def _detect_gpu(self):
        """检测GPU"""
        self.status_var.set("正在检测GPU...")
        
        # 在后台线程中检测
        threading.Thread(target=self._perform_gpu_detection, daemon=True).start()
    
    def _perform_gpu_detection(self):
        """执行GPU检测"""
        try:
            # 重新检测GPU能力
            self.gpu_manager._detect_gpu_capabilities()
            
            # 刷新信息
            self._update_gpu_info()
            
            # 更新配置
            self._load_current_config()
            
            self.status_var.set("GPU检测完成")
            
        except Exception as e:
            self.error_handler.handle_error(e, "GPU检测")
            self.status_var.set("GPU检测失败")
    
    def _apply_settings(self):
        """应用设置"""
        try:
            # 获取选中的GPU设备
            selected_gpus = []
            for device_id, var in self.gpu_device_vars.items():
                if var.get():
                    selected_gpus.append(device_id)
            
            # 应用配置
            success = self.gpu_manager.configure_gpu(
                use_gpu=self.use_gpu_var.get(),
                gpu_ids=selected_gpus if selected_gpus else None,
                device_type=self.device_type_var.get(),
                memory_limit=self.memory_limit_var.get() if self.memory_limit_var.get() > 0 else None
            )
            
            if success:
                self.status_var.set("GPU设置已应用")
                messagebox.showinfo("成功", "GPU设置已成功应用")
                
                # 发布配置更新事件
                self.event_manager.publish('gpu_config_updated', {
                    'use_gpu': self.use_gpu_var.get(),
                    'gpu_ids': selected_gpus
                })
            else:
                messagebox.showerror("错误", "GPU设置应用失败")
                
        except Exception as e:
            self.error_handler.handle_error(e, "应用GPU设置")
            messagebox.showerror("错误", f"应用GPU设置失败: {e}")
    
    def _reset_settings(self):
        """重置设置"""
        try:
            # 重置为默认值
            self.use_gpu_var.set(False)
            self.auto_detect_var.set(True)
            self.fallback_cpu_var.set(True)
            self.device_type_var.set('auto')
            self.memory_limit_var.set(0)
            
            # 重置设备选择
            for var in self.gpu_device_vars.values():
                var.set(False)
            
            # 如果有设备，选择第一个
            if 0 in self.gpu_device_vars:
                self.gpu_device_vars[0].set(True)
            
            self.status_var.set("设置已重置")
            
        except Exception as e:
            self.error_handler.handle_error(e, "重置GPU设置")
    
    def _test_gpu(self):
        """测试GPU"""
        self.status_var.set("正在测试GPU...")
        
        # 在后台线程中测试
        threading.Thread(target=self._perform_gpu_test, daemon=True).start()
    
    def _perform_gpu_test(self):
        """执行GPU测试"""
        try:
            if not self.gpu_manager.is_gpu_available():
                messagebox.showwarning("警告", "GPU不可用，无法进行测试")
                self.status_var.set("GPU测试跳过")
                return
            
            # 简单的GPU测试
            test_results = []
            
            # 测试XGBoost GPU
            try:
                import xgboost as xgb
                import numpy as np
                
                X = np.random.random((1000, 10))
                y = np.random.randint(0, 2, 1000)
                
                params = self.gpu_manager.get_xgboost_params()
                if params:
                    model = xgb.XGBClassifier(**params, n_estimators=10)
                    model.fit(X, y)
                    test_results.append("XGBoost GPU: ✓")
                else:
                    test_results.append("XGBoost GPU: ✗")
            except Exception as e:
                test_results.append(f"XGBoost GPU: ✗ ({str(e)[:50]})")
            
            # 测试LightGBM GPU
            try:
                import lightgbm as lgb
                import numpy as np
                
                X = np.random.random((1000, 10))
                y = np.random.randint(0, 2, 1000)
                
                params = self.gpu_manager.get_lightgbm_params()
                if params:
                    train_data = lgb.Dataset(X, label=y)
                    lgb.train(params, train_data, num_boost_round=10, verbose=-1)
                    test_results.append("LightGBM GPU: ✓")
                else:
                    test_results.append("LightGBM GPU: ✗")
            except Exception as e:
                test_results.append(f"LightGBM GPU: ✗ ({str(e)[:50]})")
            
            # 显示测试结果
            result_text = "GPU测试结果:\n\n" + "\n".join(test_results)
            messagebox.showinfo("GPU测试结果", result_text)
            
            self.status_var.set("GPU测试完成")
            
        except Exception as e:
            self.error_handler.handle_error(e, "GPU测试")
            messagebox.showerror("错误", f"GPU测试失败: {e}")
            self.status_var.set("GPU测试失败")
    
    def _copy_gpu_info(self):
        """复制GPU信息到剪贴板"""
        try:
            info_text = self.info_text.get(1.0, tk.END)
            self.parent.clipboard_clear()
            self.parent.clipboard_append(info_text)
            self.status_var.set("GPU信息已复制到剪贴板")
            
        except Exception as e:
            self.error_handler.handle_error(e, "复制GPU信息")
    
    def _toggle_monitoring(self):
        """切换性能监控"""
        if self.monitor_var.get():
            self._start_monitoring()
        else:
            self._stop_monitoring()
    
    def _start_monitoring(self):
        """开始性能监控"""
        self.status_var.set("性能监控已启动")
        # 这里可以实现实时性能监控
    
    def _stop_monitoring(self):
        """停止性能监控"""
        self.status_var.set("性能监控已停止")
    
    def _refresh_performance(self):
        """刷新性能数据"""
        # 清空现有数据
        for item in self.perf_tree.get_children():
            self.perf_tree.delete(item)
        
        # 这里可以实现性能数据获取和显示
        # 由于需要特殊的GPU监控库，这里只显示占位符
        self.perf_tree.insert('', 'end', values=('GPU 0', '0%', '0MB/8GB', '0°C', '0W'))
        
        self.status_var.set("性能数据已刷新")
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
