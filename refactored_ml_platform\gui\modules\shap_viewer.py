#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHAP交互式查看器模块
提供完整的SHAP分析界面，包括摘要图、依赖图、力图、决策图、瀑布图等交互式功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import numpy as np
from pathlib import Path
import threading
from typing import List, Dict, Any, Optional, Tuple
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import logging

try:
    from ...utils.shap_analyzer import ShapAnalyzer
    from ...utils.error_handler import get_error_handler
    from ..components.progress_widget import ProgressWidget
    from ..core.event_manager import get_event_manager
except ImportError:
    # 处理相对导入问题
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent
    sys.path.insert(0, str(project_root))
    
    from utils.shap_analyzer import ShapAnalyzer
    from utils.error_handler import get_error_handler
    from gui.components.progress_widget import ProgressWidget
    from gui.core.event_manager import get_event_manager


class SHAPViewerModule:
    """SHAP交互式查看器模块"""
    
    def __init__(self, parent):
        """初始化SHAP查看器模块"""
        self.parent = parent
        self.logger = logging.getLogger(__name__)
        self.shap_analyzer = ShapAnalyzer()
        self.error_handler = get_error_handler()
        self.event_manager = get_event_manager()
        
        # 数据相关
        self.current_model = None
        self.current_X_test = None
        self.current_y_test = None
        self.model_name = ""
        self.feature_names = []
        
        # GUI组件
        self.main_frame = None
        self.canvas_frame = None
        self.current_figure = None
        self.canvas = None
        self.toolbar = None
        
        # 控制变量
        self.plot_type_var = tk.StringVar(value="摘要图")
        self.feature_idx_var = tk.IntVar(value=0)
        self.sample_idx_var = tk.IntVar(value=0)
        self.max_display_var = tk.IntVar(value=15)
        self.max_samples_var = tk.IntVar(value=100)
        self.interaction_idx_var = tk.IntVar(value=-1)
        
        # 图表类型配置
        self.plot_types = {
            "摘要图": "summary",
            "蜂群图": "beeswarm", 
            "依赖图": "dependence",
            "瀑布图": "waterfall",
            "力图": "force",
            "决策图": "decision",
            "部分依赖图": "partial_dependence"
        }
        
        # 分析状态
        self.is_analyzing = False
        self.analysis_completed = False
        
        self.logger.info("SHAP查看器模块初始化完成")
    
    def create_interface(self, parent_notebook):
        """创建SHAP查看器界面"""
        # 创建主选项卡
        self.main_frame = ttk.Frame(parent_notebook)
        parent_notebook.add(self.main_frame, text="🔍 SHAP分析")
        
        # 创建左右分割面板
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧控制面板
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)
        
        # 右侧结果显示面板
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=2)
        
        # 创建左侧控制界面
        self._create_control_panel(left_frame)
        
        # 创建右侧结果显示界面
        self._create_result_panel(right_frame)
        
        return self.main_frame
    
    def _create_control_panel(self, parent):
        """创建控制面板"""
        # 模型选择区域
        model_frame = ttk.LabelFrame(parent, text="🤖 模型选择")
        model_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 模型信息显示
        self.model_info_text = tk.Text(model_frame, height=4, wrap=tk.WORD, font=('Consolas', 9))
        model_scrollbar = ttk.Scrollbar(model_frame, orient=tk.VERTICAL, command=self.model_info_text.yview)
        self.model_info_text.configure(yscrollcommand=model_scrollbar.set)
        
        self.model_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        model_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 加载模型按钮
        ttk.Button(model_frame, text="🔄 加载当前模型", 
                  command=self._load_current_model).pack(fill=tk.X, padx=5, pady=5)
        
        # 图表类型选择区域
        chart_frame = ttk.LabelFrame(parent, text="📊 图表类型")
        chart_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 图表类型选择
        ttk.Label(chart_frame, text="图表类型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.plot_type_combo = ttk.Combobox(chart_frame, textvariable=self.plot_type_var,
                                          values=list(self.plot_types.keys()), 
                                          state="readonly", width=15)
        self.plot_type_combo.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
        self.plot_type_combo.bind('<<ComboboxSelected>>', self._on_plot_type_change)
        
        chart_frame.columnconfigure(1, weight=1)
        
        # 参数配置区域
        param_frame = ttk.LabelFrame(parent, text="⚙️ 参数配置")
        param_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 最大显示特征数
        ttk.Label(param_frame, text="最大显示特征数:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Spinbox(param_frame, from_=5, to=30, textvariable=self.max_display_var, width=10).grid(
            row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 最大样本数
        ttk.Label(param_frame, text="最大样本数:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Spinbox(param_frame, from_=50, to=500, textvariable=self.max_samples_var, width=10).grid(
            row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 特征索引（用于依赖图等）
        ttk.Label(param_frame, text="特征索引:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.feature_spinbox = ttk.Spinbox(param_frame, from_=0, to=0, textvariable=self.feature_idx_var, width=10)
        self.feature_spinbox.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 样本索引（用于瀑布图、力图等）
        ttk.Label(param_frame, text="样本索引:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.sample_spinbox = ttk.Spinbox(param_frame, from_=0, to=0, textvariable=self.sample_idx_var, width=10)
        self.sample_spinbox.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 交互特征索引（用于依赖图）
        ttk.Label(param_frame, text="交互特征索引:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.interaction_spinbox = ttk.Spinbox(param_frame, from_=-1, to=0, textvariable=self.interaction_idx_var, width=10)
        self.interaction_spinbox.grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 特征名称显示
        self.feature_listbox = tk.Listbox(param_frame, height=6)
        feature_scrollbar = ttk.Scrollbar(param_frame, orient=tk.VERTICAL, command=self.feature_listbox.yview)
        self.feature_listbox.configure(yscrollcommand=feature_scrollbar.set)
        
        ttk.Label(param_frame, text="特征列表:").grid(row=5, column=0, sticky=tk.NW, padx=5, pady=5)
        self.feature_listbox.grid(row=5, column=1, sticky="ew", padx=5, pady=5)
        feature_scrollbar.grid(row=5, column=2, sticky="ns", pady=5)
        
        self.feature_listbox.bind('<<ListboxSelect>>', self._on_feature_select)
        
        param_frame.columnconfigure(1, weight=1)
        
        # 分析按钮区域
        analysis_frame = ttk.LabelFrame(parent, text="🚀 执行分析")
        analysis_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(analysis_frame, text="🔍 开始SHAP分析", 
                  command=self._start_shap_analysis).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(analysis_frame, text="📈 生成当前图表", 
                  command=self._generate_current_plot).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(analysis_frame, text="💾 保存图表", 
                  command=self._save_current_chart).pack(fill=tk.X, padx=5, pady=2)
        
        # 进度条
        self.progress_widget = ProgressWidget(parent)
        self.progress_widget.pack(fill=tk.X, padx=5, pady=5)
    
    def _create_result_panel(self, parent):
        """创建结果显示面板"""
        # 图表显示区域
        chart_frame = ttk.LabelFrame(parent, text="📊 SHAP分析结果")
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 图表工具栏
        toolbar_frame = ttk.Frame(chart_frame)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar_frame, text="🔄 刷新", 
                  command=self._refresh_chart).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="🗑️ 清空", 
                  command=self._clear_chart).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="🔍 放大", 
                  command=self._zoom_in).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="🔍 缩小", 
                  command=self._zoom_out).pack(side=tk.LEFT, padx=2)
        
        # 图表显示区域
        self.canvas_frame = ttk.Frame(chart_frame)
        self.canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 初始提示
        self._show_initial_message()
    
    def _show_initial_message(self):
        """显示初始提示信息"""
        initial_label = ttk.Label(self.canvas_frame, 
                                text="请先加载模型并开始SHAP分析",
                                font=('Arial', 12))
        initial_label.pack(expand=True)
        self.initial_label = initial_label

    def _load_current_model(self):
        """加载当前模型"""
        try:
            # 从事件管理器获取当前模型数据
            model_data = self.event_manager.get_data('current_model')
            if not model_data:
                messagebox.showwarning("警告", "没有找到当前模型，请先在模型训练模块训练模型")
                return

            self.current_model = model_data.get('model')
            self.current_X_test = model_data.get('X_test')
            self.current_y_test = model_data.get('y_test')
            self.model_name = model_data.get('model_name', 'Unknown')

            if self.current_model is None or self.current_X_test is None:
                messagebox.showerror("错误", "模型数据不完整")
                return

            # 更新特征信息
            if hasattr(self.current_X_test, 'columns'):
                self.feature_names = list(self.current_X_test.columns)
            else:
                self.feature_names = [f'Feature_{i}' for i in range(self.current_X_test.shape[1])]

            # 更新界面
            self._update_model_info()
            self._update_feature_controls()

            self.logger.info(f"模型已加载: {self.model_name}")

        except Exception as e:
            self.logger.error(f"加载模型失败: {e}")
            messagebox.showerror("错误", f"加载模型失败: {e}")

    def _update_model_info(self):
        """更新模型信息显示"""
        info_text = f"""模型名称: {self.model_name}
模型类型: {type(self.current_model).__name__}
测试样本数: {len(self.current_X_test)}
特征数量: {len(self.feature_names)}
特征名称: {', '.join(self.feature_names[:5])}{'...' if len(self.feature_names) > 5 else ''}
"""

        self.model_info_text.delete(1.0, tk.END)
        self.model_info_text.insert(1.0, info_text)

    def _update_feature_controls(self):
        """更新特征相关控件"""
        if not self.feature_names:
            return

        # 更新特征索引范围
        max_idx = len(self.feature_names) - 1
        self.feature_spinbox.config(to=max_idx)
        self.interaction_spinbox.config(to=max_idx)

        # 更新样本索引范围
        if self.current_X_test is not None:
            max_sample_idx = len(self.current_X_test) - 1
            self.sample_spinbox.config(to=max_sample_idx)

        # 更新特征列表
        self.feature_listbox.delete(0, tk.END)
        for i, feature_name in enumerate(self.feature_names):
            self.feature_listbox.insert(tk.END, f"{i}: {feature_name}")

    def _on_plot_type_change(self, event=None):
        """图表类型改变事件"""
        plot_type = self.plot_type_var.get()
        self.logger.info(f"图表类型改变为: {plot_type}")

        # 根据图表类型启用/禁用相关控件
        if plot_type in ["依赖图", "部分依赖图"]:
            self.feature_spinbox.config(state="normal")
            self.interaction_spinbox.config(state="normal" if plot_type == "依赖图" else "disabled")
        elif plot_type in ["瀑布图", "力图"]:
            self.sample_spinbox.config(state="normal")
            self.feature_spinbox.config(state="disabled")
            self.interaction_spinbox.config(state="disabled")
        else:
            self.feature_spinbox.config(state="disabled")
            self.sample_spinbox.config(state="disabled")
            self.interaction_spinbox.config(state="disabled")

    def _on_feature_select(self, event=None):
        """特征选择事件"""
        selection = self.feature_listbox.curselection()
        if selection:
            feature_idx = selection[0]
            self.feature_idx_var.set(feature_idx)

    def _start_shap_analysis(self):
        """开始SHAP分析"""
        if self.current_model is None or self.current_X_test is None:
            messagebox.showwarning("警告", "请先加载模型")
            return

        if self.is_analyzing:
            messagebox.showinfo("提示", "SHAP分析正在进行中，请稍候")
            return

        def analysis_task():
            try:
                self.is_analyzing = True
                self.progress_widget.start("正在初始化SHAP解释器...")

                # 初始化SHAP解释器
                success = self.shap_analyzer.initialize_explainer(
                    self.current_model, self.current_X_test
                )

                if not success:
                    raise ValueError("SHAP解释器初始化失败")

                self.progress_widget.update("正在计算SHAP值...")

                # 计算SHAP值
                max_samples = self.max_samples_var.get()
                success = self.shap_analyzer.calculate_shap_values(
                    self.current_X_test, max_samples=max_samples
                )

                if not success:
                    raise ValueError("SHAP值计算失败")

                self.analysis_completed = True

                # 生成第一个图表
                self.parent.after(0, self._generate_current_plot)

                self.progress_widget.complete("SHAP分析完成")

            except Exception as e:
                self.logger.error(f"SHAP分析失败: {e}")
                self.progress_widget.error(f"分析失败: {e}")
                self.parent.after(0, lambda: messagebox.showerror("错误", f"SHAP分析失败: {e}"))
            finally:
                self.is_analyzing = False

        # 在后台线程中执行
        threading.Thread(target=analysis_task, daemon=True).start()

    def _generate_current_plot(self):
        """生成当前选择的图表"""
        if not self.analysis_completed:
            messagebox.showwarning("警告", "请先完成SHAP分析")
            return

        plot_type = self.plot_type_var.get()
        plot_method = self.plot_types.get(plot_type)

        if not plot_method:
            messagebox.showerror("错误", f"不支持的图表类型: {plot_type}")
            return

        try:
            # 清除之前的图表
            self._clear_chart()

            # 生成新图表
            fig = None

            if plot_method == "summary":
                fig = self.shap_analyzer.create_summary_plot(self.current_X_test)
            elif plot_method == "beeswarm":
                fig = self.shap_analyzer.create_beeswarm_plot(
                    self.current_X_test, max_display=self.max_display_var.get()
                )
            elif plot_method == "dependence":
                feature_idx = self.feature_idx_var.get()
                interaction_idx = self.interaction_idx_var.get()
                interaction_idx = interaction_idx if interaction_idx >= 0 else None
                fig = self.shap_analyzer.create_dependence_plot(
                    self.current_X_test, feature_idx, interaction_idx
                )
            elif plot_method == "waterfall":
                sample_idx = self.sample_idx_var.get()
                fig = self.shap_analyzer.create_waterfall_plot(self.current_X_test, sample_idx)
            elif plot_method == "force":
                sample_idx = self.sample_idx_var.get()
                fig = self.shap_analyzer.create_force_plot(self.current_X_test, sample_idx)
            elif plot_method == "decision":
                fig = self.shap_analyzer.create_decision_plot(
                    self.current_X_test, max_samples=self.max_samples_var.get()
                )
            elif plot_method == "partial_dependence":
                feature_idx = self.feature_idx_var.get()
                fig = self.shap_analyzer.create_partial_dependence_plot(self.current_X_test, feature_idx)

            if fig:
                self._display_chart(fig)
            else:
                messagebox.showwarning("警告", f"无法生成 {plot_type} 图表")

        except Exception as e:
            self.logger.error(f"生成图表失败: {e}")
            messagebox.showerror("错误", f"生成图表失败: {e}")

    def _display_chart(self, fig):
        """显示图表"""
        try:
            # 移除初始提示
            if hasattr(self, 'initial_label'):
                self.initial_label.destroy()
                delattr(self, 'initial_label')

            self.current_figure = fig

            # 在GUI中显示图表
            self.canvas = FigureCanvasTkAgg(fig, self.canvas_frame)
            self.canvas.draw()
            self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 添加工具栏
            self.toolbar = NavigationToolbar2Tk(self.canvas, self.canvas_frame)
            self.toolbar.update()

            self.logger.info("图表显示成功")

        except Exception as e:
            self.logger.error(f"显示图表失败: {e}")
            messagebox.showerror("错误", f"显示图表失败: {e}")

    def _save_current_chart(self):
        """保存当前图表"""
        if self.current_figure is None:
            messagebox.showwarning("警告", "没有可保存的图表")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存SHAP图表",
            defaultextension=".png",
            filetypes=[
                ("PNG files", "*.png"),
                ("PDF files", "*.pdf"),
                ("SVG files", "*.svg"),
                ("EPS files", "*.eps"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                self.current_figure.savefig(file_path, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"图表已保存到: {file_path}")
                self.logger.info(f"SHAP图表已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存图表失败: {e}")
                self.logger.error(f"保存图表失败: {e}")

    def _refresh_chart(self):
        """刷新图表"""
        if self.canvas:
            self.canvas.draw()

    def _clear_chart(self):
        """清空图表"""
        if self.canvas:
            self.canvas.get_tk_widget().destroy()
            self.canvas = None

        if self.toolbar:
            self.toolbar.destroy()
            self.toolbar = None

        if self.current_figure:
            plt.close(self.current_figure)
            self.current_figure = None

    def _zoom_in(self):
        """放大图表"""
        if self.toolbar:
            self.toolbar.zoom()

    def _zoom_out(self):
        """缩小图表"""
        if self.toolbar:
            self.toolbar.back()

    def set_model_data(self, model, X_test, y_test=None, model_name=""):
        """设置模型数据（外部调用接口）"""
        self.current_model = model
        self.current_X_test = X_test
        self.current_y_test = y_test
        self.model_name = model_name

        # 更新特征信息
        if hasattr(X_test, 'columns'):
            self.feature_names = list(X_test.columns)
        else:
            self.feature_names = [f'Feature_{i}' for i in range(X_test.shape[1])]

        # 更新界面
        self._update_model_info()
        self._update_feature_controls()

        self.logger.info(f"外部设置模型数据: {model_name}")

    def get_current_shap_values(self):
        """获取当前SHAP值"""
        return self.shap_analyzer.shap_values if self.analysis_completed else None

    def get_feature_importance(self):
        """获取特征重要性"""
        return self.shap_analyzer.get_feature_importance() if self.analysis_completed else None

    def export_shap_data(self, file_path: str):
        """导出SHAP数据"""
        if not self.analysis_completed:
            raise ValueError("请先完成SHAP分析")

        try:
            import pickle

            shap_data = {
                'model_name': self.model_name,
                'feature_names': self.feature_names,
                'shap_values': self.shap_analyzer.shap_values,
                'expected_value': self.shap_analyzer.explainer.expected_value if self.shap_analyzer.explainer else None,
                'X_test': self.current_X_test,
                'y_test': self.current_y_test
            }

            with open(file_path, 'wb') as f:
                pickle.dump(shap_data, f)

            self.logger.info(f"SHAP数据已导出到: {file_path}")

        except Exception as e:
            self.logger.error(f"导出SHAP数据失败: {e}")
            raise

    def import_shap_data(self, file_path: str):
        """导入SHAP数据"""
        try:
            import pickle

            with open(file_path, 'rb') as f:
                shap_data = pickle.load(f)

            self.model_name = shap_data.get('model_name', 'Imported')
            self.feature_names = shap_data.get('feature_names', [])
            self.current_X_test = shap_data.get('X_test')
            self.current_y_test = shap_data.get('y_test')

            # 设置SHAP分析器数据
            self.shap_analyzer.shap_values = shap_data.get('shap_values')
            if 'expected_value' in shap_data and shap_data['expected_value'] is not None:
                # 这里需要重建explainer，但为了简化，我们只设置基本数据
                pass

            self.analysis_completed = True

            # 更新界面
            self._update_model_info()
            self._update_feature_controls()

            self.logger.info(f"SHAP数据已从 {file_path} 导入")

        except Exception as e:
            self.logger.error(f"导入SHAP数据失败: {e}")
            raise
