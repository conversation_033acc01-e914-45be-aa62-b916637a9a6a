#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置生成器GUI模块
提供配置文件生成的图形化界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from pathlib import Path
from typing import Dict, List, Optional, Any

try:
    from ....tools.config_generator import get_config_generator
    from ....utils.error_handler import get_error_handler
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))

    try:
        from tools.config_generator import get_config_generator
        from utils.error_handler import get_error_handler
    except ImportError:
        # 如果仍然失败，创建简化版本
        get_config_generator = None
        get_error_handler = None


class ConfigGeneratorGUI:
    """配置生成器GUI"""
    
    def __init__(self, parent):
        """初始化配置生成器GUI"""
        self.parent = parent
        try:
            self.config_generator = get_config_generator()
            self.error_handler = get_error_handler()
        except:
            # 如果导入失败，创建简化版本
            self.config_generator = None
            self.error_handler = None

        # GUI组件
        self.main_frame = None
        self.frame = None
        
        # 控制变量
        self.project_name_var = tk.StringVar(value="ML_Project")
        self.data_path_var = tk.StringVar()
        self.output_path_var = tk.StringVar(value="config/project_config.json")
        self.template_type_var = tk.StringVar(value="basic")
        
        # 模型选择变量
        self.model_vars = {}
        
        # 创建界面
        if self.config_generator:
            self._create_interface()
        else:
            self._create_simple_interface()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.frame = self.main_frame  # 保持兼容性
        
        # 标题
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(title_frame, text="配置文件生成器", font=('Arial', 16, 'bold')).pack()
        ttk.Label(title_frame, text="快速生成机器学习项目配置文件", 
                 foreground="gray").pack()
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self._create_basic_config_tab()
        self._create_advanced_config_tab()
        self._create_template_tab()

    def _create_simple_interface(self):
        """创建简化界面（当配置生成器不可用时）"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 显示不可用消息
        message_frame = ttk.Frame(self.main_frame)
        message_frame.pack(expand=True, fill='both')

        ttk.Label(message_frame, text="配置生成器暂时不可用",
                 font=('Arial', 16, 'bold')).pack(pady=20)

        ttk.Label(message_frame, text="原因：配置生成器模块导入失败\n\n" +
                                    "功能说明：\n" +
                                    "• 快速生成项目配置文件\n" +
                                    "• 支持多种配置模板\n" +
                                    "• 模型参数自动配置\n" +
                                    "• JSON/YAML格式支持\n\n" +
                                    "请检查系统配置或联系开发者",
                 justify='left').pack(pady=10)
    
    def _create_basic_config_tab(self):
        """创建基础配置选项卡"""
        basic_tab = ttk.Frame(self.notebook)
        self.notebook.add(basic_tab, text="🔧 基础配置")
        
        # 项目信息
        project_frame = ttk.LabelFrame(basic_tab, text="项目信息")
        project_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 项目名称
        name_frame = ttk.Frame(project_frame)
        name_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(name_frame, text="项目名称:").pack(side=tk.LEFT)
        ttk.Entry(name_frame, textvariable=self.project_name_var, width=30).pack(
            side=tk.LEFT, padx=10, fill=tk.X, expand=True)
        
        # 数据路径
        data_frame = ttk.Frame(project_frame)
        data_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(data_frame, text="数据文件:").pack(side=tk.LEFT)
        ttk.Entry(data_frame, textvariable=self.data_path_var, width=40).pack(
            side=tk.LEFT, padx=10, fill=tk.X, expand=True)
        ttk.Button(data_frame, text="浏览...", command=self._browse_data_file).pack(side=tk.RIGHT)
        
        # 模型选择
        models_frame = ttk.LabelFrame(basic_tab, text="选择模型")
        models_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 获取支持的模型
        supported_models = self.config_generator.list_supported_models()
        
        # 创建模型复选框
        models_grid = ttk.Frame(models_frame)
        models_grid.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        for i, model in enumerate(supported_models):
            var = tk.BooleanVar()
            self.model_vars[model] = var
            
            # 默认选择一些常用模型
            if model in ['RandomForest', 'LogisticRegression', 'XGBoost']:
                var.set(True)
            
            ttk.Checkbutton(models_grid, text=model, variable=var).grid(
                row=i//3, column=i%3, sticky=tk.W, padx=10, pady=5)
        
        # 输出设置
        output_frame = ttk.LabelFrame(basic_tab, text="输出设置")
        output_frame.pack(fill=tk.X, padx=10, pady=10)
        
        output_path_frame = ttk.Frame(output_frame)
        output_path_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(output_path_frame, text="输出路径:").pack(side=tk.LEFT)
        ttk.Entry(output_path_frame, textvariable=self.output_path_var, width=40).pack(
            side=tk.LEFT, padx=10, fill=tk.X, expand=True)
        ttk.Button(output_path_frame, text="浏览...", command=self._browse_output_file).pack(side=tk.RIGHT)
        
        # 生成按钮
        button_frame = ttk.Frame(basic_tab)
        button_frame.pack(fill=tk.X, padx=10, pady=20)
        
        ttk.Button(button_frame, text="🔧 生成基础配置", 
                  command=self._generate_basic_config, style="Accent.TButton").pack(side=tk.RIGHT)
    
    def _create_advanced_config_tab(self):
        """创建高级配置选项卡"""
        advanced_tab = ttk.Frame(self.notebook)
        self.notebook.add(advanced_tab, text="⚙️ 高级配置")
        
        # 配置编辑器
        editor_frame = ttk.LabelFrame(advanced_tab, text="配置编辑器")
        editor_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 文本编辑器
        self.config_text = tk.Text(editor_frame, wrap=tk.WORD, font=('Consolas', 10))
        config_scrollbar = ttk.Scrollbar(editor_frame, orient=tk.VERTICAL, command=self.config_text.yview)
        self.config_text.configure(yscrollcommand=config_scrollbar.set)
        
        self.config_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        config_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 操作按钮
        advanced_button_frame = ttk.Frame(advanced_tab)
        advanced_button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(advanced_button_frame, text="📁 加载配置", 
                  command=self._load_config).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(advanced_button_frame, text="✅ 验证配置", 
                  command=self._validate_config).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(advanced_button_frame, text="💾 保存配置", 
                  command=self._save_advanced_config, style="Accent.TButton").pack(side=tk.RIGHT)
        
        # 加载默认配置
        self._load_default_config()
    
    def _create_template_tab(self):
        """创建模板选项卡"""
        template_tab = ttk.Frame(self.notebook)
        self.notebook.add(template_tab, text="📋 配置模板")
        
        # 模板选择
        template_selection_frame = ttk.LabelFrame(template_tab, text="选择模板")
        template_selection_frame.pack(fill=tk.X, padx=10, pady=10)
        
        template_frame = ttk.Frame(template_selection_frame)
        template_frame.pack(fill=tk.X, padx=10, pady=10)
        
        templates = [
            ("基础模板", "basic", "适合初学者的简单配置"),
            ("高级模板", "advanced", "包含更多配置选项"),
            ("集成学习模板", "ensemble", "专为集成学习设计")
        ]
        
        for i, (name, value, desc) in enumerate(templates):
            frame = ttk.Frame(template_frame)
            frame.pack(fill=tk.X, pady=5)
            
            ttk.Radiobutton(frame, text=name, variable=self.template_type_var, 
                           value=value).pack(side=tk.LEFT)
            ttk.Label(frame, text=f"- {desc}", foreground="gray").pack(side=tk.LEFT, padx=(10, 0))
        
        # 模板预览
        preview_frame = ttk.LabelFrame(template_tab, text="模板预览")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.template_preview_text = tk.Text(preview_frame, wrap=tk.WORD, font=('Consolas', 9), 
                                           state=tk.DISABLED)
        template_preview_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, 
                                                 command=self.template_preview_text.yview)
        self.template_preview_text.configure(yscrollcommand=template_preview_scrollbar.set)
        
        self.template_preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        template_preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 模板操作按钮
        template_button_frame = ttk.Frame(template_tab)
        template_button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(template_button_frame, text="👁️ 预览模板", 
                  command=self._preview_template).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(template_button_frame, text="📋 使用模板", 
                  command=self._use_template, style="Accent.TButton").pack(side=tk.RIGHT)
        
        # 绑定模板类型变化事件
        self.template_type_var.trace('w', lambda *args: self._preview_template())
        
        # 初始预览
        self._preview_template()
    
    def _browse_data_file(self):
        """浏览数据文件"""
        filename = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.data_path_var.set(filename)
    
    def _browse_output_file(self):
        """浏览输出文件"""
        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("YAML文件", "*.yaml"), ("所有文件", "*.*")]
        )
        if filename:
            self.output_path_var.set(filename)
    
    def _generate_basic_config(self):
        """生成基础配置"""
        try:
            # 获取输入参数
            project_name = self.project_name_var.get().strip()
            data_path = self.data_path_var.get().strip()
            output_path = self.output_path_var.get().strip()
            
            # 验证输入
            if not project_name:
                messagebox.showwarning("警告", "请输入项目名称")
                return
            
            if not data_path:
                messagebox.showwarning("警告", "请选择数据文件")
                return
            
            if not output_path:
                messagebox.showwarning("警告", "请指定输出路径")
                return
            
            # 获取选中的模型
            selected_models = [model for model, var in self.model_vars.items() if var.get()]
            
            if not selected_models:
                messagebox.showwarning("警告", "请至少选择一个模型")
                return
            
            # 生成配置
            config = self.config_generator.generate_basic_config(
                project_name=project_name,
                data_path=data_path,
                models=selected_models,
                output_path=output_path
            )
            
            messagebox.showinfo("成功", f"基础配置已生成并保存到:\n{output_path}")
            
        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(e, "生成基础配置")
            messagebox.showerror("错误", f"生成配置失败: {e}")
    
    def _load_default_config(self):
        """加载默认配置到编辑器"""
        try:
            template = self.config_generator.generate_template('advanced')
            config_json = json.dumps(template, indent=2, ensure_ascii=False)
            
            self.config_text.delete(1.0, tk.END)
            self.config_text.insert(1.0, config_json)
            
        except Exception as e:
            self.config_text.delete(1.0, tk.END)
            self.config_text.insert(1.0, "# 加载默认配置失败\n# 请手动输入配置")
    
    def _load_config(self):
        """加载配置文件"""
        filename = filedialog.askopenfilename(
            title="加载配置文件",
            filetypes=[("JSON文件", "*.json"), ("YAML文件", "*.yaml"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                config = self.config_generator.load_config(filename)
                if config:
                    config_json = json.dumps(config, indent=2, ensure_ascii=False)
                    self.config_text.delete(1.0, tk.END)
                    self.config_text.insert(1.0, config_json)
                    messagebox.showinfo("成功", "配置文件已加载")
                else:
                    messagebox.showerror("错误", "加载配置文件失败")
            except Exception as e:
                messagebox.showerror("错误", f"加载配置文件失败: {e}")
    
    def _validate_config(self):
        """验证配置"""
        try:
            config_text = self.config_text.get(1.0, tk.END).strip()
            if not config_text:
                messagebox.showwarning("警告", "配置内容为空")
                return
            
            # 解析JSON
            config = json.loads(config_text)
            
            # 验证配置
            errors = self.config_generator.validate_config(config)
            
            if errors:
                error_msg = "配置验证失败:\n" + "\n".join(f"• {error}" for error in errors)
                messagebox.showerror("验证失败", error_msg)
            else:
                messagebox.showinfo("验证成功", "配置文件格式正确，所有必需字段都已填写")
                
        except json.JSONDecodeError as e:
            messagebox.showerror("JSON错误", f"配置文件格式错误: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"验证配置失败: {e}")
    
    def _save_advanced_config(self):
        """保存高级配置"""
        try:
            config_text = self.config_text.get(1.0, tk.END).strip()
            if not config_text:
                messagebox.showwarning("警告", "配置内容为空")
                return
            
            # 解析JSON
            config = json.loads(config_text)
            
            # 选择保存路径
            filename = filedialog.asksaveasfilename(
                title="保存配置文件",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("YAML文件", "*.yaml"), ("所有文件", "*.*")]
            )
            
            if filename:
                success = self.config_generator.save_config(config, filename)
                if success:
                    messagebox.showinfo("成功", f"配置已保存到:\n{filename}")
                else:
                    messagebox.showerror("错误", "保存配置失败")
                    
        except json.JSONDecodeError as e:
            messagebox.showerror("JSON错误", f"配置文件格式错误: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def _preview_template(self):
        """预览模板"""
        try:
            template_type = self.template_type_var.get()
            template = self.config_generator.generate_template(template_type)
            template_json = json.dumps(template, indent=2, ensure_ascii=False)
            
            self.template_preview_text.config(state=tk.NORMAL)
            self.template_preview_text.delete(1.0, tk.END)
            self.template_preview_text.insert(1.0, template_json)
            self.template_preview_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.template_preview_text.config(state=tk.NORMAL)
            self.template_preview_text.delete(1.0, tk.END)
            self.template_preview_text.insert(1.0, f"预览模板失败: {e}")
            self.template_preview_text.config(state=tk.DISABLED)
    
    def _use_template(self):
        """使用模板"""
        try:
            template_type = self.template_type_var.get()
            template = self.config_generator.generate_template(template_type)
            template_json = json.dumps(template, indent=2, ensure_ascii=False)
            
            # 将模板内容复制到高级配置编辑器
            self.config_text.delete(1.0, tk.END)
            self.config_text.insert(1.0, template_json)
            
            # 切换到高级配置选项卡
            self.notebook.select(1)
            
            messagebox.showinfo("成功", f"已将{template_type}模板加载到高级配置编辑器")
            
        except Exception as e:
            messagebox.showerror("错误", f"使用模板失败: {e}")
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
