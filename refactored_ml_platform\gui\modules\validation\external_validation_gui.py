#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外部验证GUI模块
提供外部数据验证的图形化界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import numpy as np
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any

try:
    from ...core.event_manager import get_event_manager
    from algorithms.external_validation import ExternalValidator, run_external_validation
    from core.model_manager import get_model_manager
    from utils.plot_manager import get_plot_manager
    from utils.error_handler import get_error_handler
    from utils.data_loader import get_data_loader
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))

    from gui.core.event_manager import get_event_manager
    from algorithms.external_validation import ExternalValidator, run_external_validation
    from core.model_manager import get_model_manager
    from utils.plot_manager import get_plot_manager
    from utils.error_handler import get_error_handler
    from utils.data_loader import get_data_loader


class ExternalValidationGUI:
    """外部验证GUI"""
    
    def __init__(self, parent):
        """初始化外部验证GUI"""
        self.parent = parent
        self.event_manager = get_event_manager()
        self.model_manager = get_model_manager()
        self.plot_manager = get_plot_manager()
        self.error_handler = get_error_handler()
        self.data_loader = get_data_loader()
        
        # 数据存储
        self.available_models = {}
        self.validation_results = {}
        self.external_data = None
        
        # GUI组件
        self.frame = None
        
        # 控制变量
        self.model_var = tk.StringVar()
        self.data_path_var = tk.StringVar()
        self.target_col_var = tk.StringVar()
        self.status_var = tk.StringVar(value="就绪")
        
        # 创建界面
        self._create_interface()
        
        # 注册事件监听
        self._register_events()
        
        # 初始化数据
        self._refresh_models()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.frame = ttk.Frame(self.parent)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(title_frame, text="外部验证", font=('Arial', 16, 'bold')).pack()
        ttk.Label(title_frame, text="使用外部数据集验证已训练模型的性能", 
                 foreground="gray").pack()
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self._create_validation_config_tab()
        self._create_results_tab()
    
    def _create_validation_config_tab(self):
        """创建验证配置选项卡"""
        config_tab = ttk.Frame(self.notebook)
        self.notebook.add(config_tab, text="⚙️ 验证配置")
        
        # 模型选择
        model_frame = ttk.LabelFrame(config_tab, text="模型选择")
        model_frame.pack(fill=tk.X, padx=10, pady=10)
        
        model_config_frame = ttk.Frame(model_frame)
        model_config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(model_config_frame, text="选择模型:").pack(side=tk.LEFT, padx=(0, 10))
        self.model_combo = ttk.Combobox(model_config_frame, textvariable=self.model_var,
                                       state="readonly", width=25)
        self.model_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.model_combo.bind('<<ComboboxSelected>>', self._on_model_change)
        
        ttk.Button(model_config_frame, text="🔄 刷新模型", 
                  command=self._refresh_models).pack(side=tk.LEFT, padx=(10, 0))
        
        # 模型信息显示
        self.model_info_text = tk.Text(model_frame, height=4, wrap=tk.WORD, 
                                      font=('Consolas', 9), state=tk.DISABLED)
        self.model_info_text.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # 外部数据配置
        data_frame = ttk.LabelFrame(config_tab, text="外部数据配置")
        data_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 数据文件选择
        file_frame = ttk.Frame(data_frame)
        file_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(file_frame, text="数据文件:").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Entry(file_frame, textvariable=self.data_path_var, width=40).pack(
            side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)
        ttk.Button(file_frame, text="浏览...", command=self._browse_data_file).pack(side=tk.RIGHT)
        
        # 目标列配置
        target_frame = ttk.Frame(data_frame)
        target_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Label(target_frame, text="目标列:").pack(side=tk.LEFT, padx=(0, 10))
        self.target_combo = ttk.Combobox(target_frame, textvariable=self.target_col_var,
                                        width=20)
        self.target_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(target_frame, text="📊 预览数据", 
                  command=self._preview_data).pack(side=tk.LEFT, padx=(10, 0))
        
        # 数据预览
        preview_frame = ttk.LabelFrame(data_frame, text="数据预览")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建表格
        columns = ('列名', '类型', '示例值', '缺失值')
        self.data_tree = ttk.Treeview(preview_frame, columns=columns, show='headings', height=6)
        
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=100, anchor=tk.CENTER)
        
        data_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        self.data_tree.configure(yscrollcommand=data_scrollbar.set)
        
        self.data_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        data_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 验证控制
        control_frame = ttk.Frame(config_tab)
        control_frame.pack(fill=tk.X, padx=10, pady=20)
        
        ttk.Button(control_frame, text="🚀 开始验证", 
                  command=self._start_validation, style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="⏹️ 停止验证", 
                  command=self._stop_validation, state=tk.DISABLED).pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var,
                                          mode='determinate', length=200)
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 状态栏
        status_frame = ttk.Frame(config_tab)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(
            side=tk.LEFT, padx=(5, 0))
    
    def _create_results_tab(self):
        """创建结果选项卡"""
        results_tab = ttk.Frame(self.notebook)
        self.notebook.add(results_tab, text="📊 验证结果")
        
        # 结果摘要
        summary_frame = ttk.LabelFrame(results_tab, text="验证摘要")
        summary_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 性能指标网格
        self.metrics_frame = ttk.Frame(summary_frame)
        self.metrics_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 详细结果
        details_frame = ttk.LabelFrame(results_tab, text="详细结果")
        details_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建笔记本控件用于不同类型的结果
        self.results_notebook = ttk.Notebook(details_frame)
        self.results_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 性能指标选项卡
        self._create_metrics_tab()
        
        # 可视化选项卡
        self._create_visualization_tab()
        
        # 预测结果选项卡
        self._create_predictions_tab()
        
        # 操作按钮
        actions_frame = ttk.Frame(results_tab)
        actions_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(actions_frame, text="💾 保存结果", 
                  command=self._save_results).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(actions_frame, text="📊 生成报告", 
                  command=self._generate_report).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(actions_frame, text="📈 导出图表", 
                  command=self._export_charts).pack(side=tk.LEFT, padx=(0, 10))
    
    def _create_metrics_tab(self):
        """创建性能指标选项卡"""
        metrics_tab = ttk.Frame(self.results_notebook)
        self.results_notebook.add(metrics_tab, text="📊 性能指标")
        
        self.metrics_text = tk.Text(metrics_tab, wrap=tk.WORD, font=('Consolas', 10))
        metrics_scrollbar = ttk.Scrollbar(metrics_tab, orient=tk.VERTICAL, command=self.metrics_text.yview)
        self.metrics_text.configure(yscrollcommand=metrics_scrollbar.set)
        
        self.metrics_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        metrics_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def _create_visualization_tab(self):
        """创建可视化选项卡"""
        viz_tab = ttk.Frame(self.results_notebook)
        self.results_notebook.add(viz_tab, text="📈 可视化")
        
        # 图表类型选择
        chart_control_frame = ttk.Frame(viz_tab)
        chart_control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(chart_control_frame, text="图表类型:").pack(side=tk.LEFT, padx=(0, 10))
        
        self.chart_type_var = tk.StringVar(value="ROC曲线")
        chart_types = ["ROC曲线", "PR曲线", "混淆矩阵", "预测分布"]
        self.chart_combo = ttk.Combobox(chart_control_frame, textvariable=self.chart_type_var,
                                       values=chart_types, state="readonly", width=15)
        self.chart_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.chart_combo.bind('<<ComboboxSelected>>', self._on_chart_type_change)
        
        ttk.Button(chart_control_frame, text="🔄 刷新图表", 
                  command=self._refresh_chart).pack(side=tk.LEFT, padx=(10, 0))
        
        # 图表显示区域
        import matplotlib.pyplot as plt
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
        
        self.viz_fig, self.viz_ax = plt.subplots(figsize=(10, 6))
        self.viz_fig.patch.set_facecolor('white')
        
        self.viz_canvas = FigureCanvasTkAgg(self.viz_fig, viz_tab)
        self.viz_canvas.draw()
        self.viz_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 工具栏
        viz_toolbar_frame = ttk.Frame(viz_tab)
        viz_toolbar_frame.pack(fill=tk.X)
        self.viz_toolbar = NavigationToolbar2Tk(self.viz_canvas, viz_toolbar_frame)
        self.viz_toolbar.update()
        
        # 初始化空白图表
        self._show_empty_chart()
    
    def _create_predictions_tab(self):
        """创建预测结果选项卡"""
        pred_tab = ttk.Frame(self.results_notebook)
        self.results_notebook.add(pred_tab, text="🎯 预测结果")
        
        # 预测结果表格
        pred_columns = ('样本ID', '真实值', '预测值', '预测概率', '正确性')
        self.pred_tree = ttk.Treeview(pred_tab, columns=pred_columns, show='headings', height=15)
        
        for col in pred_columns:
            self.pred_tree.heading(col, text=col)
            self.pred_tree.column(col, width=100, anchor=tk.CENTER)
        
        pred_scrollbar = ttk.Scrollbar(pred_tab, orient=tk.VERTICAL, command=self.pred_tree.yview)
        self.pred_tree.configure(yscrollcommand=pred_scrollbar.set)
        
        self.pred_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        pred_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def _register_events(self):
        """注册事件监听"""
        self.event_manager.subscribe('model_trained', self._on_model_trained)
        self.event_manager.subscribe('validation_completed', self._on_validation_completed)
    
    def _on_model_trained(self, event_data):
        """模型训练完成事件处理"""
        self._refresh_models()
    
    def _on_validation_completed(self, event_data):
        """验证完成事件处理"""
        self.status_var.set("外部验证完成")
        self._display_validation_results(event_data.get('results'))
    
    def _refresh_models(self):
        """刷新模型列表"""
        try:
            # 获取所有可用模型
            all_models = set()
            
            # 从模型管理器获取
            manager_models = self.model_manager.list_models()
            for model_name in manager_models:
                model_data = self.model_manager.get_model_data(model_name)
                if model_data and 'model' in model_data:
                    self.available_models[model_name] = model_data
                    all_models.add(model_name)
            
            # 更新下拉框
            model_list = sorted(all_models)
            self.model_combo['values'] = model_list
            
            if model_list and not self.model_var.get():
                self.model_var.set(model_list[0])
                self._on_model_change()
            
            self.status_var.set(f"已加载 {len(model_list)} 个可验证模型")
            
        except Exception as e:
            self.error_handler.handle_error(e, "刷新模型列表")
    
    def _on_model_change(self, event=None):
        """模型选择改变事件处理"""
        model_name = self.model_var.get()
        if model_name and model_name in self.available_models:
            model_data = self.available_models[model_name]
            self._display_model_info(model_name, model_data)
    
    def _display_model_info(self, model_name: str, model_data: Dict[str, Any]):
        """显示模型信息"""
        self.model_info_text.config(state=tk.NORMAL)
        self.model_info_text.delete(1.0, tk.END)
        
        info_text = f"模型: {model_name}\n"
        info_text += f"类型: {model_data.get('model_type', '未知')}\n"
        info_text += f"训练时间: {model_data.get('training_time', 0):.2f} 秒\n"
        info_text += f"特征数量: {len(model_data.get('feature_names', []))}\n"
        
        self.model_info_text.insert(1.0, info_text)
        self.model_info_text.config(state=tk.DISABLED)
    
    def _browse_data_file(self):
        """浏览数据文件"""
        filename = filedialog.askopenfilename(
            title="选择外部验证数据文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.data_path_var.set(filename)
            self._preview_data()
    
    def _preview_data(self):
        """预览数据"""
        data_path = self.data_path_var.get()
        if not data_path:
            messagebox.showwarning("警告", "请先选择数据文件")
            return
        
        try:
            # 加载数据
            self.external_data = self.data_loader.load_data(data_path)
            
            # 清空现有预览
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)
            
            # 更新目标列选项
            columns = list(self.external_data.columns)
            self.target_combo['values'] = columns
            
            # 如果还没有选择目标列，尝试自动检测
            if not self.target_col_var.get():
                # 寻找可能的目标列名
                target_candidates = ['target', 'label', 'y', 'class', 'outcome']
                for candidate in target_candidates:
                    if candidate in columns:
                        self.target_col_var.set(candidate)
                        break
                else:
                    # 如果没有找到，选择最后一列
                    if columns:
                        self.target_col_var.set(columns[-1])
            
            # 显示数据预览
            for col in self.external_data.columns:
                col_data = self.external_data[col]
                col_type = str(col_data.dtype)
                sample_value = str(col_data.iloc[0]) if len(col_data) > 0 else "N/A"
                missing_count = col_data.isnull().sum()
                
                self.data_tree.insert('', 'end', values=(
                    col, col_type, sample_value, missing_count
                ))
            
            self.status_var.set(f"数据预览完成 - 形状: {self.external_data.shape}")
            
        except Exception as e:
            self.error_handler.handle_error(e, "预览数据")
            messagebox.showerror("错误", f"数据预览失败: {e}")
    
    def _start_validation(self):
        """开始验证"""
        # 检查必要参数
        model_name = self.model_var.get()
        data_path = self.data_path_var.get()
        target_col = self.target_col_var.get()
        
        if not model_name:
            messagebox.showwarning("警告", "请选择模型")
            return
        
        if not data_path:
            messagebox.showwarning("警告", "请选择数据文件")
            return
        
        if not target_col:
            messagebox.showwarning("警告", "请选择目标列")
            return
        
        if self.external_data is None:
            messagebox.showwarning("警告", "请先预览数据")
            return
        
        # 在后台线程中执行验证
        threading.Thread(target=self._perform_validation, 
                        args=(model_name, data_path, target_col), daemon=True).start()
    
    def _perform_validation(self, model_name: str, data_path: str, target_col: str):
        """执行验证"""
        try:
            self.status_var.set("正在进行外部验证...")
            self.progress_var.set(20)
            
            # 获取模型数据
            model_data = self.available_models[model_name]
            model = model_data['model']
            preprocessor = model_data.get('preprocessor')
            
            self.progress_var.set(40)
            
            # 创建外部验证器
            validator = ExternalValidator(model, preprocessor)
            
            self.progress_var.set(60)
            
            # 执行验证
            results = validator.validate(self.external_data, target_col)
            
            self.progress_var.set(80)
            
            # 保存结果
            self.validation_results[model_name] = results
            
            self.progress_var.set(100)
            
            # 显示结果
            self._display_validation_results(results)
            
            self.status_var.set("外部验证完成")
            
            # 发布事件
            self.event_manager.publish('validation_completed', {
                'model_name': model_name,
                'results': results
            })
            
            # 重置进度条
            self.parent.after(2000, lambda: self.progress_var.set(0))
            
        except Exception as e:
            self.error_handler.handle_error(e, "外部验证")
            self.status_var.set("外部验证失败")
            self.progress_var.set(0)
            messagebox.showerror("错误", f"外部验证失败: {e}")
    
    def _stop_validation(self):
        """停止验证"""
        # 这里可以添加停止验证的逻辑
        self.status_var.set("验证已停止")
    
    def _display_validation_results(self, results: Dict[str, Any]):
        """显示验证结果"""
        try:
            # 显示性能指标
            self._display_metrics(results)
            
            # 显示预测结果
            self._display_predictions(results)
            
            # 生成可视化
            self._refresh_chart()
            
            # 切换到结果选项卡
            self.notebook.select(1)
            
        except Exception as e:
            self.error_handler.handle_error(e, "显示验证结果")
    
    def _display_metrics(self, results: Dict[str, Any]):
        """显示性能指标"""
        metrics = results.get('metrics', {})
        
        metrics_text = "=== 外部验证性能指标 ===\n\n"
        
        metric_names = {
            'accuracy': '准确率',
            'auc': 'AUC',
            'f1_score': 'F1分数',
            'precision': '精确率',
            'recall': '召回率'
        }
        
        for key, name in metric_names.items():
            if key in metrics:
                metrics_text += f"{name}: {metrics[key]:.4f}\n"
        
        metrics_text += f"\n样本数量: {results.get('sample_count', 0)}\n"
        metrics_text += f"验证时间: {results.get('validation_time', 0):.2f} 秒\n"
        
        self.metrics_text.delete(1.0, tk.END)
        self.metrics_text.insert(1.0, metrics_text)
    
    def _display_predictions(self, results: Dict[str, Any]):
        """显示预测结果"""
        # 清空现有数据
        for item in self.pred_tree.get_children():
            self.pred_tree.delete(item)
        
        y_true = results.get('y_true', [])
        y_pred = results.get('y_pred', [])
        y_pred_proba = results.get('y_pred_proba', [])
        
        # 显示前100个预测结果
        max_display = min(100, len(y_true))
        
        for i in range(max_display):
            true_val = y_true[i] if i < len(y_true) else "N/A"
            pred_val = y_pred[i] if i < len(y_pred) else "N/A"
            pred_proba = y_pred_proba[i] if i < len(y_pred_proba) else "N/A"
            
            if isinstance(pred_proba, (list, np.ndarray)):
                pred_proba = pred_proba[1] if len(pred_proba) > 1 else pred_proba[0]
            
            correct = "✓" if true_val == pred_val else "✗"
            
            self.pred_tree.insert('', 'end', values=(
                i + 1, true_val, pred_val, f"{pred_proba:.4f}" if isinstance(pred_proba, (int, float)) else pred_proba, correct
            ))
    
    def _show_empty_chart(self):
        """显示空白图表"""
        self.viz_ax.clear()
        self.viz_ax.text(0.5, 0.5, '请完成外部验证后查看图表', 
                        ha='center', va='center', transform=self.viz_ax.transAxes,
                        fontsize=14, color='gray')
        self.viz_ax.set_xticks([])
        self.viz_ax.set_yticks([])
        self.viz_canvas.draw()
    
    def _on_chart_type_change(self, event=None):
        """图表类型改变事件处理"""
        self._refresh_chart()
    
    def _refresh_chart(self):
        """刷新图表"""
        model_name = self.model_var.get()
        if not model_name or model_name not in self.validation_results:
            self._show_empty_chart()
            return
        
        results = self.validation_results[model_name]
        chart_type = self.chart_type_var.get()
        
        try:
            self.viz_ax.clear()
            
            if chart_type == "ROC曲线":
                self._plot_roc_curve(results)
            elif chart_type == "PR曲线":
                self._plot_pr_curve(results)
            elif chart_type == "混淆矩阵":
                self._plot_confusion_matrix(results)
            elif chart_type == "预测分布":
                self._plot_prediction_distribution(results)
            
            self.viz_canvas.draw()
            
        except Exception as e:
            self.error_handler.handle_error(e, "刷新图表")
            self._show_empty_chart()
    
    def _plot_roc_curve(self, results: Dict[str, Any]):
        """绘制ROC曲线"""
        y_true = results.get('y_true')
        y_pred_proba = results.get('y_pred_proba')
        
        if y_true is not None and y_pred_proba is not None:
            from sklearn.metrics import roc_curve, auc
            
            fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
            roc_auc = auc(fpr, tpr)
            
            self.viz_ax.plot(fpr, tpr, color='darkorange', lw=2,
                           label=f'ROC曲线 (AUC = {roc_auc:.3f})')
            self.viz_ax.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
            
            self.viz_ax.set_xlim([0.0, 1.0])
            self.viz_ax.set_ylim([0.0, 1.05])
            self.viz_ax.set_xlabel('假正率')
            self.viz_ax.set_ylabel('真正率')
            self.viz_ax.set_title('外部验证 - ROC曲线')
            self.viz_ax.legend(loc="lower right")
            self.viz_ax.grid(True, alpha=0.3)
    
    def _plot_pr_curve(self, results: Dict[str, Any]):
        """绘制PR曲线"""
        y_true = results.get('y_true')
        y_pred_proba = results.get('y_pred_proba')
        
        if y_true is not None and y_pred_proba is not None:
            from sklearn.metrics import precision_recall_curve, average_precision_score
            
            precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
            avg_precision = average_precision_score(y_true, y_pred_proba)
            
            self.viz_ax.plot(recall, precision, color='darkorange', lw=2,
                           label=f'PR曲线 (AP = {avg_precision:.3f})')
            
            self.viz_ax.set_xlim([0.0, 1.0])
            self.viz_ax.set_ylim([0.0, 1.05])
            self.viz_ax.set_xlabel('召回率')
            self.viz_ax.set_ylabel('精确率')
            self.viz_ax.set_title('外部验证 - PR曲线')
            self.viz_ax.legend(loc="lower left")
            self.viz_ax.grid(True, alpha=0.3)
    
    def _plot_confusion_matrix(self, results: Dict[str, Any]):
        """绘制混淆矩阵"""
        y_true = results.get('y_true')
        y_pred = results.get('y_pred')
        
        if y_true is not None and y_pred is not None:
            from sklearn.metrics import confusion_matrix
            import seaborn as sns
            
            cm = confusion_matrix(y_true, y_pred)
            
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=self.viz_ax)
            self.viz_ax.set_xlabel('预测标签')
            self.viz_ax.set_ylabel('真实标签')
            self.viz_ax.set_title('外部验证 - 混淆矩阵')
    
    def _plot_prediction_distribution(self, results: Dict[str, Any]):
        """绘制预测分布"""
        y_pred_proba = results.get('y_pred_proba')
        
        if y_pred_proba is not None:
            self.viz_ax.hist(y_pred_proba, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
            self.viz_ax.set_xlabel('预测概率')
            self.viz_ax.set_ylabel('频次')
            self.viz_ax.set_title('外部验证 - 预测概率分布')
            self.viz_ax.grid(True, alpha=0.3)
    
    def _save_results(self):
        """保存结果"""
        model_name = self.model_var.get()
        if not model_name or model_name not in self.validation_results:
            messagebox.showwarning("警告", "没有可保存的验证结果")
            return
        
        filename = filedialog.asksaveasfilename(
            title="保存验证结果",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                results = self.validation_results[model_name]
                
                if filename.endswith('.json'):
                    import json
                    # 转换numpy数组为列表
                    save_results = {}
                    for key, value in results.items():
                        if isinstance(value, np.ndarray):
                            save_results[key] = value.tolist()
                        else:
                            save_results[key] = value
                    
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(save_results, f, indent=2, ensure_ascii=False)
                
                elif filename.endswith('.csv'):
                    # 保存预测结果为CSV
                    df = pd.DataFrame({
                        'y_true': results.get('y_true', []),
                        'y_pred': results.get('y_pred', []),
                        'y_pred_proba': results.get('y_pred_proba', [])
                    })
                    df.to_csv(filename, index=False)
                
                messagebox.showinfo("成功", f"验证结果已保存到: {filename}")
                
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
    
    def _generate_report(self):
        """生成报告"""
        messagebox.showinfo("提示", "报告生成功能待实现")
    
    def _export_charts(self):
        """导出图表"""
        filename = filedialog.asksaveasfilename(
            title="导出图表",
            defaultextension=".png",
            filetypes=[("PNG文件", "*.png"), ("PDF文件", "*.pdf"), ("SVG文件", "*.svg")]
        )
        
        if filename:
            try:
                self.viz_fig.savefig(filename, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"图表已导出到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
