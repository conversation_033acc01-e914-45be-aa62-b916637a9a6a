"""
对比可视化标签页
提供多模型对比可视化功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Any, Optional

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager
from ...components.chart_widgets import ChartWidget


class ComparisonVisualizationTab(BaseGUI):
    """对比可视化标签页"""
    
    def __init__(self, parent: tk.Widget):
        """初始化对比可视化标签页"""
        self.trained_models = {}
        self.selected_models = []
        self.current_comparison_type = "performance_comparison"
        
        # 对比类型配置
        self.comparison_types = {
            "performance_comparison": "性能对比",
            "roc_comparison": "ROC曲线对比",
            "feature_importance_comparison": "特征重要性对比",
            "learning_curve_comparison": "学习曲线对比"
        }
        
        super().__init__(parent)
        
        # 订阅模型训练完成事件
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.MODEL_TRAINED, self._on_models_trained)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='main')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分割
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧控制面板
        left_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(left_frame, weight=1)
        
        # 右侧对比面板
        right_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(right_frame, weight=3)
        
        # 设置控制面板
        self._setup_control_panel(left_frame)
        
        # 设置对比面板
        self._setup_comparison_panel(right_frame)
        
        self.register_component('main_frame', self.main_frame)
        self.register_component('paned_window', paned_window)
    
    def _setup_control_panel(self, parent):
        """设置控制面板"""
        factory = get_component_factory()
        
        # 模型选择
        model_frame = factory.create_labelframe(parent, text="模型选择", style='section')
        model_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 全选/取消全选按钮
        select_frame = factory.create_frame(model_frame)
        select_frame.pack(fill=tk.X, padx=5, pady=5)
        
        select_all_btn = factory.create_button(
            select_frame,
            text="全选",
            command=self._select_all_models,
            style='secondary'
        )
        select_all_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        deselect_all_btn = factory.create_button(
            select_frame,
            text="取消全选",
            command=self._deselect_all_models,
            style='secondary'
        )
        deselect_all_btn.pack(side=tk.LEFT)
        
        # 模型复选框
        self.model_vars = {}
        self.models_scroll_frame = factory.create_scrollable_frame(model_frame)
        self.models_scroll_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 对比类型选择
        comparison_type_frame = factory.create_labelframe(parent, text="对比类型", style='section')
        comparison_type_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.comparison_type_var = tk.StringVar(value="performance_comparison")
        for comp_type, display_name in self.comparison_types.items():
            radio = factory.create_radiobutton(
                comparison_type_frame,
                text=display_name,
                variable=self.comparison_type_var,
                value=comp_type,
                command=self._on_comparison_type_changed
            )
            radio.pack(anchor=tk.W, padx=5, pady=2)
        
        # 生成对比按钮
        self.generate_button = factory.create_button(
            parent,
            text="📊 生成对比",
            command=self._generate_comparison,
            style='primary'
        )
        self.generate_button.pack(fill=tk.X, padx=5, pady=10)
        self.generate_button.config(state=tk.DISABLED)
        
        # 状态标签
        self.status_label = factory.create_label(
            parent,
            text="请选择至少2个模型进行对比",
            style='info'
        )
        self.status_label.pack(padx=5, pady=5)
    
    def _setup_comparison_panel(self, parent):
        """设置对比面板"""
        factory = get_component_factory()
        
        # 对比结果标签页
        self.comparison_notebook = factory.create_notebook(parent)
        self.comparison_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 主对比图表标签页
        main_comparison_frame = factory.create_frame(self.comparison_notebook)
        self.comparison_notebook.add(main_comparison_frame, text="主对比图表")
        
        # 主对比图表容器
        self.main_comparison_widget = ChartWidget(main_comparison_frame, chart_type="comparison")
        
        # 统计分析标签页
        stats_frame = factory.create_frame(self.comparison_notebook)
        self.comparison_notebook.add(stats_frame, text="统计分析")
        
        # 统计分析文本
        self.stats_text = factory.create_text(
            stats_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        stats_scrollbar = factory.create_scrollbar(stats_frame, orient=tk.VERTICAL)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        stats_scrollbar.configure(command=self.stats_text.yview)
    
    def _on_models_trained(self, event_data: Dict[str, Any]):
        """模型训练完成事件处理"""
        self.trained_models = event_data.get('results', {})
        
        # 清空现有的模型选择控件
        for widget in self.models_scroll_frame.scrollable_frame.winfo_children():
            widget.destroy()
        self.model_vars = {}
        
        # 创建新的模型复选框
        factory = get_component_factory()
        for model_name in self.trained_models.keys():
            var = tk.BooleanVar()
            display_name = self._get_display_name(model_name)
            checkbox = factory.create_checkbox(
                self.models_scroll_frame.scrollable_frame,
                text=display_name,
                variable=var,
                command=self._on_model_selection_changed
            )
            checkbox.pack(anchor=tk.W, pady=2)
            self.model_vars[model_name] = var
        
        self.logger.info(f"接收到 {len(self.trained_models)} 个训练完成的模型")
        self.status_label.config(text=f"可对比 {len(self.trained_models)} 个模型")
    
    def _get_display_name(self, model_name: str) -> str:
        """获取模型显示名称"""
        display_names = {
            'DecisionTree': '决策树',
            'RandomForest': '随机森林',
            'XGBoost': 'XGBoost',
            'LightGBM': 'LightGBM',
            'CatBoost': 'CatBoost',
            'Logistic': '逻辑回归',
            'SVM': '支持向量机',
            'KNN': 'K近邻',
            'NaiveBayes': '朴素贝叶斯',
            'NeuralNet': '神经网络'
        }
        return display_names.get(model_name, model_name)
    
    def _select_all_models(self):
        """选择所有模型"""
        for var in self.model_vars.values():
            var.set(True)
        self._on_model_selection_changed()
    
    def _deselect_all_models(self):
        """取消选择所有模型"""
        for var in self.model_vars.values():
            var.set(False)
        self._on_model_selection_changed()
    
    def _on_model_selection_changed(self):
        """模型选择变化处理"""
        self.selected_models = [
            model for model, var in self.model_vars.items() if var.get()
        ]
        
        # 更新生成按钮状态
        if len(self.selected_models) >= 2:
            self.generate_button.config(state=tk.NORMAL)
            self.status_label.config(text=f"已选择 {len(self.selected_models)} 个模型")
        else:
            self.generate_button.config(state=tk.DISABLED)
            self.status_label.config(text="请至少选择2个模型进行对比")
    
    def _on_comparison_type_changed(self):
        """对比类型变化处理"""
        self.current_comparison_type = self.comparison_type_var.get()
        self.logger.info(f"对比类型变更为: {self.comparison_types[self.current_comparison_type]}")
    
    def _generate_comparison(self):
        """生成对比"""
        if len(self.selected_models) < 2:
            messagebox.showwarning("警告", "请至少选择2个模型进行对比")
            return
        
        try:
            comparison_config = self._build_comparison_config()
            
            # 生成主对比图表
            self.main_comparison_widget.load_data(comparison_config)
            
            # 更新统计分析
            self._update_statistical_analysis()
            
            self.logger.info(f"生成对比: {self.comparison_types[self.current_comparison_type]}")
            self.status_label.config(text="对比生成完成")
            
        except Exception as e:
            self.logger.error(f"生成对比失败: {e}")
            messagebox.showerror("生成失败", f"对比生成过程中出现错误:\n{e}")
    
    def _build_comparison_config(self) -> Dict[str, Any]:
        """构建对比配置"""
        config = {
            'comparison_type': self.current_comparison_type,
            'selected_models': self.selected_models,
            'models_data': {name: self.trained_models[name] for name in self.selected_models}
        }
        
        return config
    
    def _update_statistical_analysis(self):
        """更新统计分析"""
        if not self.selected_models:
            return
        
        analysis_text = f"""
模型对比统计分析
{'='*50}

参与对比的模型: {len(self.selected_models)} 个
对比类型: {self.comparison_types[self.current_comparison_type]}

模型性能概览:
"""
        
        for model_name in self.selected_models:
            model_result = self.trained_models[model_name]
            display_name = self._get_display_name(model_name)
            
            analysis_text += f"""
{display_name}:
  准确率: {model_result.get('accuracy', 0):.4f}
  精确率: {model_result.get('precision', 0):.4f}
  召回率: {model_result.get('recall', 0):.4f}
  F1分数: {model_result.get('f1_score', 0):.4f}
  AUC: {model_result.get('auc', 0):.4f}
  训练时间: {model_result.get('training_time', 0):.2f}秒
"""
        
        analysis_text += """

对比总结:
- 建议选择综合性能最佳的模型用于生产环境
- 可以考虑将多个优秀模型进行集成学习
- 注意平衡模型性能和训练时间的关系
        """
        
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, analysis_text.strip())
        self.stats_text.config(state=tk.DISABLED)
    
    def get_comparison_results(self) -> Dict[str, Any]:
        """获取对比结果"""
        return {
            'comparison_type': self.current_comparison_type,
            'selected_models': self.selected_models,
            'models_data': {name: self.trained_models[name] for name in self.selected_models}
        }
    
    def clear_comparisons(self):
        """清空对比结果"""
        self.main_comparison_widget.clear_chart()
        
        # 清空统计分析
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, "选择模型和对比类型后，统计分析将在这里显示...")
        self.stats_text.config(state=tk.DISABLED)
