"""
数据可视化标签页
提供数据探索和可视化功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager
from ...components.chart_widgets import ChartWidget


class DataVisualizationTab(BaseGUI):
    """数据可视化标签页"""
    
    def __init__(self, parent: tk.Widget):
        """初始化数据可视化标签页"""
        self.data = None
        self.numeric_columns = []
        self.categorical_columns = []
        self.current_chart_type = "histogram"
        
        # 图表类型配置
        self.chart_types = {
            "histogram": "直方图",
            "boxplot": "箱线图", 
            "scatter": "散点图",
            "correlation": "相关性热图",
            "distribution": "分布图",
            "pairplot": "配对图",
            "violin": "小提琴图",
            "bar": "柱状图"
        }
        
        super().__init__(parent)
        
        # 订阅数据更新事件
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.DATA_LOADED, self._on_data_loaded)
        event_manager.subscribe(EventTypes.DATA_PREPROCESSED, self._on_data_preprocessed)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='main')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分割
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧控制面板
        left_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(left_frame, weight=1)
        
        # 右侧图表面板
        right_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(right_frame, weight=3)
        
        # 设置控制面板
        self._setup_control_panel(left_frame)
        
        # 设置图表面板
        self._setup_chart_panel(right_frame)
        
        self.register_component('main_frame', self.main_frame)
        self.register_component('paned_window', paned_window)
    
    def _setup_control_panel(self, parent):
        """设置控制面板"""
        factory = get_component_factory()
        
        # 数据状态
        status_frame = factory.create_labelframe(parent, text="数据状态", style='section')
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.data_status_label = factory.create_label(
            status_frame,
            text="❌ 未加载数据",
            style='info'
        )
        self.data_status_label.pack(anchor=tk.W, padx=5, pady=5)
        
        # 图表类型选择
        chart_type_frame = factory.create_labelframe(parent, text="图表类型", style='section')
        chart_type_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.chart_type_var = tk.StringVar(value="histogram")
        for chart_type, display_name in self.chart_types.items():
            radio = factory.create_radiobutton(
                chart_type_frame,
                text=display_name,
                variable=self.chart_type_var,
                value=chart_type,
                command=self._on_chart_type_changed
            )
            radio.pack(anchor=tk.W, padx=5, pady=2)
        
        # 变量选择
        variables_frame = factory.create_labelframe(parent, text="变量选择", style='section')
        variables_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # X轴变量
        x_frame = factory.create_frame(variables_frame)
        x_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(x_frame, text="X轴变量:").pack(anchor=tk.W)
        self.x_var = tk.StringVar()
        self.x_combo = factory.create_combobox(
            x_frame,
            textvariable=self.x_var,
            state="readonly"
        )
        self.x_combo.pack(fill=tk.X, pady=(2, 0))
        self.x_combo.bind('<<ComboboxSelected>>', self._on_variable_changed)
        
        # Y轴变量
        y_frame = factory.create_frame(variables_frame)
        y_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(y_frame, text="Y轴变量:").pack(anchor=tk.W)
        self.y_var = tk.StringVar()
        self.y_combo = factory.create_combobox(
            y_frame,
            textvariable=self.y_var,
            state="readonly"
        )
        self.y_combo.pack(fill=tk.X, pady=(2, 0))
        self.y_combo.bind('<<ComboboxSelected>>', self._on_variable_changed)
        
        # 分组变量（可选）
        group_frame = factory.create_frame(variables_frame)
        group_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(group_frame, text="分组变量（可选）:").pack(anchor=tk.W)
        self.group_var = tk.StringVar()
        self.group_combo = factory.create_combobox(
            group_frame,
            textvariable=self.group_var,
            state="readonly"
        )
        self.group_combo.pack(fill=tk.X, pady=(2, 0))
        self.group_combo.bind('<<ComboboxSelected>>', self._on_variable_changed)
        
        # 图表选项
        options_frame = factory.create_labelframe(parent, text="图表选项", style='section')
        options_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 显示选项
        self.show_grid_var = tk.BooleanVar(value=True)
        grid_check = factory.create_checkbox(
            options_frame,
            text="显示网格",
            variable=self.show_grid_var,
            command=self._on_option_changed
        )
        grid_check.pack(anchor=tk.W, padx=5, pady=2)
        
        self.show_legend_var = tk.BooleanVar(value=True)
        legend_check = factory.create_checkbox(
            options_frame,
            text="显示图例",
            variable=self.show_legend_var,
            command=self._on_option_changed
        )
        legend_check.pack(anchor=tk.W, padx=5, pady=2)
        
        # 生成图表按钮
        self.generate_button = factory.create_button(
            parent,
            text="📊 生成图表",
            command=self._generate_chart,
            style='primary'
        )
        self.generate_button.pack(fill=tk.X, padx=5, pady=10)
        self.generate_button.config(state=tk.DISABLED)
        
        # 导出按钮
        export_frame = factory.create_frame(parent)
        export_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.export_button = factory.create_button(
            export_frame,
            text="💾 导出图表",
            command=self._export_chart,
            style='secondary'
        )
        self.export_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        self.export_button.config(state=tk.DISABLED)
        
        self.save_button = factory.create_button(
            export_frame,
            text="📁 保存配置",
            command=self._save_config,
            style='secondary'
        )
        self.save_button.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(2, 0))
        self.save_button.config(state=tk.DISABLED)
    
    def _setup_chart_panel(self, parent):
        """设置图表面板"""
        factory = get_component_factory()
        
        # 图表标签页
        self.chart_notebook = factory.create_notebook(parent)
        self.chart_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 主图表标签页
        main_chart_frame = factory.create_frame(self.chart_notebook)
        self.chart_notebook.add(main_chart_frame, text="主图表")
        
        # 图表容器
        self.main_chart_widget = ChartWidget(main_chart_frame, chart_type="data_visualization")
        
        # 统计信息标签页
        stats_frame = factory.create_frame(self.chart_notebook)
        self.chart_notebook.add(stats_frame, text="统计信息")
        
        # 统计信息文本
        self.stats_text = factory.create_text(
            stats_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        stats_scrollbar = factory.create_scrollbar(stats_frame, orient=tk.VERTICAL)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        stats_scrollbar.configure(command=self.stats_text.yview)
        
        # 数据预览标签页
        preview_frame = factory.create_frame(self.chart_notebook)
        self.chart_notebook.add(preview_frame, text="数据预览")
        
        # 数据预览表格
        self.preview_tree = factory.create_treeview(
            preview_frame,
            show='headings'
        )
        
        preview_scrollbar_v = factory.create_scrollbar(preview_frame, orient=tk.VERTICAL)
        preview_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        
        preview_scrollbar_h = factory.create_scrollbar(preview_frame, orient=tk.HORIZONTAL)
        preview_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.preview_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.preview_tree.configure(
            yscrollcommand=preview_scrollbar_v.set,
            xscrollcommand=preview_scrollbar_h.set
        )
        preview_scrollbar_v.configure(command=self.preview_tree.yview)
        preview_scrollbar_h.configure(command=self.preview_tree.xview)
    
    def _on_data_loaded(self, event_data: Dict[str, Any]):
        """数据加载事件处理"""
        self.data = event_data.get('data')
        if self.data is not None:
            self._analyze_data()
            self._update_variable_options()
            self._update_data_preview()
            self._update_statistics()
            
            self.data_status_label.config(
                text=f"✅ 已加载数据 ({self.data.shape[0]} 行, {self.data.shape[1]} 列)"
            )
            self.generate_button.config(state=tk.NORMAL)
            self.save_button.config(state=tk.NORMAL)
            
            self.logger.info(f"接收到数据: {self.data.shape}")
        else:
            self.data_status_label.config(text="❌ 数据加载失败")
    
    def _on_data_preprocessed(self, event_data: Dict[str, Any]):
        """数据预处理事件处理"""
        # 如果有预处理后的数据，也可以用于可视化
        preprocessed_data = event_data.get('preprocessed_data')
        if preprocessed_data is not None:
            self.logger.info("接收到预处理后的数据，可用于高级可视化")
    
    def _analyze_data(self):
        """分析数据类型"""
        if self.data is None:
            return
        
        self.numeric_columns = []
        self.categorical_columns = []
        
        for col in self.data.columns:
            if pd.api.types.is_numeric_dtype(self.data[col]):
                self.numeric_columns.append(col)
            else:
                self.categorical_columns.append(col)
        
        self.logger.info(f"数值列: {len(self.numeric_columns)}, 分类列: {len(self.categorical_columns)}")
    
    def _update_variable_options(self):
        """更新变量选择选项"""
        all_columns = list(self.data.columns)
        
        # 更新X轴变量选项
        self.x_combo['values'] = all_columns
        if all_columns:
            self.x_var.set(all_columns[0])
        
        # 更新Y轴变量选项
        self.y_combo['values'] = all_columns
        if len(all_columns) > 1:
            self.y_var.set(all_columns[1])
        
        # 更新分组变量选项
        group_options = [''] + self.categorical_columns
        self.group_combo['values'] = group_options
        self.group_var.set('')
    
    def _update_data_preview(self):
        """更新数据预览"""
        if self.data is None:
            return
        
        # 清空现有数据
        for item in self.preview_tree.get_children():
            self.preview_tree.delete(item)
        
        # 设置列
        columns = list(self.data.columns)
        self.preview_tree['columns'] = columns
        
        for col in columns:
            self.preview_tree.heading(col, text=col)
            self.preview_tree.column(col, width=100, anchor=tk.CENTER)
        
        # 添加数据（前100行）
        preview_data = self.data.head(100)
        for _, row in preview_data.iterrows():
            values = [str(row[col]) for col in columns]
            self.preview_tree.insert('', tk.END, values=values)
    
    def _update_statistics(self):
        """更新统计信息"""
        if self.data is None:
            return
        
        stats_text = f"""
数据统计信息
{'='*50}

基本信息:
- 总行数: {self.data.shape[0]:,}
- 总列数: {self.data.shape[1]:,}
- 数值列数: {len(self.numeric_columns)}
- 分类列数: {len(self.categorical_columns)}
- 内存使用: {self.data.memory_usage(deep=True).sum() / 1024**2:.2f} MB

缺失值统计:
"""
        
        missing_stats = self.data.isnull().sum()
        for col, missing_count in missing_stats.items():
            if missing_count > 0:
                missing_pct = (missing_count / len(self.data)) * 100
                stats_text += f"- {col}: {missing_count} ({missing_pct:.1f}%)\n"
        
        if missing_stats.sum() == 0:
            stats_text += "- 无缺失值\n"
        
        # 数值列统计
        if self.numeric_columns:
            stats_text += f"\n数值列统计:\n"
            numeric_desc = self.data[self.numeric_columns].describe()
            
            for col in self.numeric_columns:
                stats_text += f"\n{col}:\n"
                stats_text += f"  均值: {numeric_desc.loc['mean', col]:.4f}\n"
                stats_text += f"  标准差: {numeric_desc.loc['std', col]:.4f}\n"
                stats_text += f"  最小值: {numeric_desc.loc['min', col]:.4f}\n"
                stats_text += f"  最大值: {numeric_desc.loc['max', col]:.4f}\n"
        
        # 分类列统计
        if self.categorical_columns:
            stats_text += f"\n分类列统计:\n"
            
            for col in self.categorical_columns:
                unique_count = self.data[col].nunique()
                most_common = self.data[col].mode().iloc[0] if not self.data[col].empty else "N/A"
                stats_text += f"\n{col}:\n"
                stats_text += f"  唯一值数量: {unique_count}\n"
                stats_text += f"  最常见值: {most_common}\n"
        
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text.strip())
        self.stats_text.config(state=tk.DISABLED)
    
    def _on_chart_type_changed(self):
        """图表类型变化处理"""
        self.current_chart_type = self.chart_type_var.get()
        self._update_variable_requirements()
        self.logger.info(f"图表类型变更为: {self.chart_types[self.current_chart_type]}")
    
    def _update_variable_requirements(self):
        """根据图表类型更新变量要求"""
        chart_type = self.current_chart_type
        
        # 根据图表类型启用/禁用变量选择
        if chart_type in ['histogram', 'boxplot', 'violin']:
            # 单变量图表
            self.y_combo.config(state='disabled')
        elif chart_type in ['correlation', 'pairplot']:
            # 多变量图表
            self.x_combo.config(state='disabled')
            self.y_combo.config(state='disabled')
        else:
            # 双变量图表
            self.x_combo.config(state='readonly')
            self.y_combo.config(state='readonly')
    
    def _on_variable_changed(self, event=None):
        """变量选择变化处理"""
        self.logger.info(f"变量选择变更: X={self.x_var.get()}, Y={self.y_var.get()}, Group={self.group_var.get()}")
    
    def _on_option_changed(self):
        """选项变化处理"""
        self.logger.info("图表选项已更新")
    
    def _generate_chart(self):
        """生成图表"""
        if self.data is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
        
        try:
            chart_config = self._build_chart_config()
            
            # 这里应该调用实际的图表生成逻辑
            # 目前使用占位符
            self.main_chart_widget.load_data(chart_config)
            
            # 启用导出按钮
            self.export_button.config(state=tk.NORMAL)
            
            self.logger.info(f"生成图表: {self.chart_types[self.current_chart_type]}")
            
        except Exception as e:
            self.logger.error(f"生成图表失败: {e}")
            messagebox.showerror("生成失败", f"图表生成过程中出现错误:\n{e}")
    
    def _build_chart_config(self) -> Dict[str, Any]:
        """构建图表配置"""
        config = {
            'chart_type': self.current_chart_type,
            'data': self.data,
            'x_var': self.x_var.get(),
            'y_var': self.y_var.get(),
            'group_var': self.group_var.get(),
            'show_grid': self.show_grid_var.get(),
            'show_legend': self.show_legend_var.get(),
            'title': f"{self.chart_types[self.current_chart_type]} - {self.x_var.get()}"
        }
        
        # 根据图表类型添加特定配置
        if self.current_chart_type == 'histogram':
            config['bins'] = 30
        elif self.current_chart_type == 'scatter':
            config['alpha'] = 0.6
        
        return config
    
    def _export_chart(self):
        """导出图表"""
        try:
            from tkinter import filedialog
            
            file_path = filedialog.asksaveasfilename(
                title="导出图表",
                defaultextension=".png",
                filetypes=[
                    ("PNG files", "*.png"),
                    ("PDF files", "*.pdf"),
                    ("SVG files", "*.svg"),
                    ("All files", "*.*")
                ]
            )
            
            if file_path:
                # 这里应该实现实际的导出逻辑
                self.logger.info(f"图表导出到: {file_path}")
                messagebox.showinfo("导出成功", f"图表已导出到:\n{file_path}")
        
        except Exception as e:
            self.logger.error(f"导出图表失败: {e}")
            messagebox.showerror("导出失败", f"导出过程中出现错误:\n{e}")
    
    def _save_config(self):
        """保存图表配置"""
        try:
            from tkinter import filedialog
            import json
            
            file_path = filedialog.asksaveasfilename(
                title="保存图表配置",
                defaultextension=".json",
                filetypes=[
                    ("JSON files", "*.json"),
                    ("All files", "*.*")
                ]
            )
            
            if file_path:
                config = self._build_chart_config()
                # 移除不能序列化的数据
                config.pop('data', None)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                self.logger.info(f"配置保存到: {file_path}")
                messagebox.showinfo("保存成功", f"图表配置已保存到:\n{file_path}")
        
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            messagebox.showerror("保存失败", f"保存过程中出现错误:\n{e}")
    
    def load_config(self, config_path: str):
        """加载图表配置"""
        try:
            import json
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 应用配置
            self.chart_type_var.set(config.get('chart_type', 'histogram'))
            self.x_var.set(config.get('x_var', ''))
            self.y_var.set(config.get('y_var', ''))
            self.group_var.set(config.get('group_var', ''))
            self.show_grid_var.set(config.get('show_grid', True))
            self.show_legend_var.set(config.get('show_legend', True))
            
            self._on_chart_type_changed()
            
            self.logger.info(f"配置加载成功: {config_path}")
            messagebox.showinfo("加载成功", "图表配置已加载")
        
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            messagebox.showerror("加载失败", f"加载配置过程中出现错误:\n{e}")
    
    def get_current_chart_config(self) -> Dict[str, Any]:
        """获取当前图表配置"""
        return self._build_chart_config()
    
    def clear_charts(self):
        """清空图表"""
        self.main_chart_widget.clear_chart()
        self.export_button.config(state=tk.DISABLED)
