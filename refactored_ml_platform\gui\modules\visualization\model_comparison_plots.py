#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型比较可视化模块
提供多个模型的比较可视化功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import threading

try:
    from ...core.event_manager import get_event_manager
    from utils.plot_manager import get_plot_manager
    from core.model_manager import get_model_manager
    from utils.error_handler import get_error_handler
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))

    from gui.core.event_manager import get_event_manager
    from utils.plot_manager import get_plot_manager
    from core.model_manager import get_model_manager
    from utils.error_handler import get_error_handler


class ModelComparisonPlotsModule:
    """模型比较可视化模块"""
    
    def __init__(self, parent):
        """初始化模型比较可视化模块"""
        self.parent = parent
        self.plot_manager = get_plot_manager()
        self.event_manager = get_event_manager()
        self.model_manager = get_model_manager()
        self.error_handler = get_error_handler()
        
        # 数据存储
        self.model_results = {}
        self.selected_models = {}
        
        # GUI组件
        self.frame = None
        self.canvas = None
        self.toolbar = None
        self.fig = None
        self.ax = None
        
        # 控制变量
        self.plot_type_var = tk.StringVar(value="性能比较")
        self.metric_var = tk.StringVar(value="accuracy")
        self.status_var = tk.StringVar(value="就绪")
        
        # 创建界面
        self._create_interface()
        
        # 注册事件监听
        self._register_events()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.frame = ttk.Frame(self.parent)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self._create_model_selection_tab()
        self._create_comparison_tab()
    
    def _create_model_selection_tab(self):
        """创建模型选择选项卡"""
        selection_tab = ttk.Frame(self.notebook)
        self.notebook.add(selection_tab, text="🎯 模型选择")
        
        # 可用模型列表
        models_frame = ttk.LabelFrame(selection_tab, text="可用模型")
        models_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 模型列表框架
        list_frame = ttk.Frame(models_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧：可用模型列表
        left_frame = ttk.Frame(list_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        ttk.Label(left_frame, text="可用模型:").pack(anchor=tk.W)
        
        self.available_listbox = tk.Listbox(left_frame, selectmode=tk.MULTIPLE, height=15)
        available_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.available_listbox.yview)
        self.available_listbox.configure(yscrollcommand=available_scrollbar.set)
        
        self.available_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        available_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 中间：操作按钮
        middle_frame = ttk.Frame(list_frame)
        middle_frame.pack(side=tk.LEFT, padx=10)
        
        ttk.Button(middle_frame, text="➡️", command=self._add_selected_models).pack(pady=5)
        ttk.Button(middle_frame, text="⬅️", command=self._remove_selected_models).pack(pady=5)
        ttk.Button(middle_frame, text="➡️➡️", command=self._add_all_models).pack(pady=5)
        ttk.Button(middle_frame, text="⬅️⬅️", command=self._remove_all_models).pack(pady=5)
        
        # 右侧：选中模型列表
        right_frame = ttk.Frame(list_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        ttk.Label(right_frame, text="选中模型:").pack(anchor=tk.W)
        
        self.selected_listbox = tk.Listbox(right_frame, selectmode=tk.MULTIPLE, height=15)
        selected_scrollbar = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=self.selected_listbox.yview)
        self.selected_listbox.configure(yscrollcommand=selected_scrollbar.set)
        
        self.selected_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        selected_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 底部：操作按钮
        button_frame = ttk.Frame(models_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="🔄 刷新模型列表", command=self._refresh_models).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="📊 查看模型详情", command=self._show_model_summary).pack(side=tk.LEFT, padx=(0, 5))
        
        # 初始加载模型
        self._refresh_models()
    
    def _create_comparison_tab(self):
        """创建比较选项卡"""
        comparison_tab = ttk.Frame(self.notebook)
        self.notebook.add(comparison_tab, text="📈 模型比较")
        
        # 控制面板
        control_frame = ttk.LabelFrame(comparison_tab, text="比较设置")
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 第一行：图表类型和指标选择
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(row1, text="比较类型:").pack(side=tk.LEFT, padx=(0, 5))
        plot_types = ["性能比较", "多模型ROC", "多模型PR", "雷达图", "排名图"]
        self.plot_type_combo = ttk.Combobox(
            row1, textvariable=self.plot_type_var,
            values=plot_types, state="readonly", width=12
        )
        self.plot_type_combo.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(row1, text="比较指标:").pack(side=tk.LEFT, padx=(0, 5))
        metrics = ["accuracy", "auc", "f1_score", "precision", "recall"]
        self.metric_combo = ttk.Combobox(
            row1, textvariable=self.metric_var,
            values=metrics, state="readonly", width=10
        )
        self.metric_combo.pack(side=tk.LEFT, padx=(0, 20))
        
        # 第二行：操作按钮
        row2 = ttk.Frame(control_frame)
        row2.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(row2, text="📊 生成比较图", command=self._generate_comparison).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="💾 保存图表", command=self._save_plot).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="📋 导出数据", command=self._export_comparison_data).pack(side=tk.LEFT, padx=(0, 5))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            row2, variable=self.progress_var,
            mode='determinate', length=200
        )
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 图表显示区域
        chart_frame = ttk.LabelFrame(comparison_tab, text="比较图表")
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建matplotlib图表
        self.fig, self.ax = plt.subplots(figsize=(12, 8))
        self.fig.patch.set_facecolor('white')
        
        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 创建工具栏
        toolbar_frame = ttk.Frame(chart_frame)
        toolbar_frame.pack(fill=tk.X)
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        self.toolbar.update()
        
        # 状态栏
        status_frame = ttk.Frame(comparison_tab)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(side=tk.LEFT, padx=(5, 0))
        
        # 初始化空白图表
        self._show_empty_plot()
    
    def _register_events(self):
        """注册事件监听"""
        self.event_manager.subscribe('model_trained', self._on_model_trained)
        self.event_manager.subscribe('models_updated', self._on_models_updated)
    
    def _show_empty_plot(self):
        """显示空白图表"""
        self.ax.clear()
        self.ax.text(0.5, 0.5, '请选择至少2个模型进行比较\n然后点击"生成比较图"', 
                    ha='center', va='center', transform=self.ax.transAxes,
                    fontsize=14, color='gray')
        self.ax.set_xticks([])
        self.ax.set_yticks([])
        self.canvas.draw()
    
    def _on_model_trained(self, event_data):
        """模型训练完成事件处理"""
        model_name = event_data.get('model_name')
        result = event_data.get('result')
        
        if model_name and result:
            self.model_results[model_name] = result
            self._refresh_models()
            self.status_var.set(f"模型 {model_name} 已添加到可比较列表")
    
    def _on_models_updated(self, event_data):
        """模型更新事件处理"""
        self._refresh_models()
    
    def _refresh_models(self):
        """刷新模型列表"""
        try:
            # 清空列表
            self.available_listbox.delete(0, tk.END)
            
            # 获取所有可用模型
            all_models = set()
            
            # 从训练结果获取
            all_models.update(self.model_results.keys())
            
            # 从模型管理器获取
            manager_models = self.model_manager.list_models()
            for model_name in manager_models:
                result = self.model_manager.get_model_result(model_name)
                if result:
                    self.model_results[model_name] = result
                    all_models.add(model_name)
            
            # 添加到可用列表
            for model_name in sorted(all_models):
                self.available_listbox.insert(tk.END, model_name)
            
            self.status_var.set(f"已加载 {len(all_models)} 个可比较模型")
            
        except Exception as e:
            self.error_handler.handle_error(e, "刷新模型列表")
    
    def _add_selected_models(self):
        """添加选中的模型"""
        selected_indices = self.available_listbox.curselection()
        for index in selected_indices:
            model_name = self.available_listbox.get(index)
            if model_name not in [self.selected_listbox.get(i) for i in range(self.selected_listbox.size())]:
                self.selected_listbox.insert(tk.END, model_name)
        
        self._update_selection_status()
    
    def _remove_selected_models(self):
        """移除选中的模型"""
        selected_indices = list(self.selected_listbox.curselection())
        selected_indices.reverse()  # 从后往前删除
        for index in selected_indices:
            self.selected_listbox.delete(index)
        
        self._update_selection_status()
    
    def _add_all_models(self):
        """添加所有模型"""
        self.selected_listbox.delete(0, tk.END)
        for i in range(self.available_listbox.size()):
            model_name = self.available_listbox.get(i)
            self.selected_listbox.insert(tk.END, model_name)
        
        self._update_selection_status()
    
    def _remove_all_models(self):
        """移除所有模型"""
        self.selected_listbox.delete(0, tk.END)
        self._update_selection_status()
    
    def _update_selection_status(self):
        """更新选择状态"""
        selected_count = self.selected_listbox.size()
        self.status_var.set(f"已选择 {selected_count} 个模型进行比较")
    
    def _generate_comparison(self):
        """生成比较图表"""
        selected_models = [self.selected_listbox.get(i) for i in range(self.selected_listbox.size())]
        
        if len(selected_models) < 2:
            messagebox.showwarning("警告", "请至少选择2个模型进行比较")
            return
        
        plot_type = self.plot_type_var.get()
        if not plot_type:
            messagebox.showwarning("警告", "请选择比较类型")
            return
        
        # 在后台线程中生成比较图表
        threading.Thread(target=self._perform_comparison_generation, 
                        args=(selected_models, plot_type), daemon=True).start()
    
    def _perform_comparison_generation(self, selected_models: List[str], plot_type: str):
        """执行比较图表生成"""
        try:
            self.status_var.set("正在生成比较图表...")
            self.progress_var.set(20)
            
            # 收集模型数据
            comparison_data = {}
            for model_name in selected_models:
                if model_name in self.model_results:
                    comparison_data[model_name] = self.model_results[model_name]
                else:
                    result = self.model_manager.get_model_result(model_name)
                    if result:
                        comparison_data[model_name] = result
            
            if len(comparison_data) < 2:
                messagebox.showerror("错误", "无法获取足够的模型数据进行比较")
                return
            
            self.progress_var.set(50)
            
            # 根据比较类型生成图表
            if plot_type == "性能比较":
                self._generate_performance_comparison(comparison_data)
            elif plot_type == "多模型ROC":
                self._generate_multi_roc_comparison(comparison_data)
            elif plot_type == "多模型PR":
                self._generate_multi_pr_comparison(comparison_data)
            elif plot_type == "雷达图":
                self._generate_radar_chart(comparison_data)
            elif plot_type == "排名图":
                self._generate_ranking_chart(comparison_data)
            else:
                messagebox.showwarning("警告", f"不支持的比较类型: {plot_type}")
                return
            
            self.progress_var.set(100)
            self.status_var.set("比较图表生成完成")
            
            # 重置进度条
            self.parent.after(2000, lambda: self.progress_var.set(0))
            
        except Exception as e:
            self.error_handler.handle_error(e, "生成比较图表")
            self.status_var.set("比较图表生成失败")
            self.progress_var.set(0)
    
    def _generate_performance_comparison(self, comparison_data: Dict[str, Any]):
        """生成性能比较图"""
        metric = self.metric_var.get()
        
        # 提取指标数据
        results = {}
        for model_name, result in comparison_data.items():
            if metric in result:
                results[model_name] = {metric: result[metric]}
            elif 'metrics' in result and metric in result['metrics']:
                results[model_name] = {metric: result['metrics'][metric]}
            else:
                results[model_name] = {metric: 0.0}
        
        # 清空当前图表
        self.ax.clear()
        
        # 使用绘图管理器生成比较图
        fig = self.plot_manager.plot_model_comparison(results, metric)
        
        # 将生成的图表复制到当前画布
        self._copy_plot_to_canvas(fig)
        plt.close(fig)
    
    def _generate_multi_roc_comparison(self, comparison_data: Dict[str, Any]):
        """生成多模型ROC比较图"""
        # 收集ROC数据
        roc_data = {}
        for model_name, result in comparison_data.items():
            y_true = result.get('y_true') or result.get('y_test')
            y_pred_proba = result.get('y_pred_proba')
            
            if y_true is not None and y_pred_proba is not None:
                roc_data[model_name] = (y_true, y_pred_proba)
        
        if len(roc_data) < 2:
            messagebox.showerror("错误", "缺少ROC比较所需的数据")
            return
        
        self.ax.clear()
        
        fig = self.plot_manager.plot_multi_model_roc(roc_data)
        
        self._copy_plot_to_canvas(fig)
        plt.close(fig)
    
    def _generate_multi_pr_comparison(self, comparison_data: Dict[str, Any]):
        """生成多模型PR比较图"""
        self.ax.clear()
        
        # 绘制多个PR曲线
        for model_name, result in comparison_data.items():
            y_true = result.get('y_true') or result.get('y_test')
            y_pred_proba = result.get('y_pred_proba')
            
            if y_true is not None and y_pred_proba is not None:
                from sklearn.metrics import precision_recall_curve, average_precision_score
                
                precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
                avg_precision = average_precision_score(y_true, y_pred_proba)
                
                color = self.plot_manager.get_model_color(model_name)
                self.ax.plot(recall, precision, color=color, lw=2,
                           label=f'{model_name} (AP = {avg_precision:.3f})')
        
        self.ax.set_xlabel('召回率 (Recall)')
        self.ax.set_ylabel('精确率 (Precision)')
        self.ax.set_title('多模型PR曲线比较')
        self.ax.legend(loc='best')
        self.ax.grid(True, alpha=0.3)
        
        self.canvas.draw()
    
    def _generate_radar_chart(self, comparison_data: Dict[str, Any]):
        """生成雷达图"""
        # 这里实现雷达图的生成逻辑
        self.ax.clear()
        self.ax.text(0.5, 0.5, '雷达图功能开发中...', 
                    ha='center', va='center', transform=self.ax.transAxes,
                    fontsize=14, color='orange')
        self.canvas.draw()
    
    def _generate_ranking_chart(self, comparison_data: Dict[str, Any]):
        """生成排名图"""
        # 这里实现排名图的生成逻辑
        self.ax.clear()
        self.ax.text(0.5, 0.5, '排名图功能开发中...', 
                    ha='center', va='center', transform=self.ax.transAxes,
                    fontsize=14, color='orange')
        self.canvas.draw()
    
    def _copy_plot_to_canvas(self, source_fig):
        """将源图表复制到当前画布"""
        try:
            # 获取源图表的轴
            source_ax = source_fig.axes[0]
            
            # 复制图表内容到当前轴
            for line in source_ax.get_lines():
                self.ax.plot(line.get_xdata(), line.get_ydata(), 
                           color=line.get_color(), linewidth=line.get_linewidth(),
                           linestyle=line.get_linestyle(), label=line.get_label())
            
            # 复制条形图
            for patch in source_ax.patches:
                if hasattr(patch, 'get_height'):  # 条形图
                    self.ax.bar(patch.get_x(), patch.get_height(), 
                              width=patch.get_width(), color=patch.get_facecolor(),
                              alpha=patch.get_alpha())
            
            # 复制轴属性
            self.ax.set_xlabel(source_ax.get_xlabel())
            self.ax.set_ylabel(source_ax.get_ylabel())
            self.ax.set_title(source_ax.get_title())
            
            # 复制图例
            if source_ax.get_legend():
                self.ax.legend()
            
            # 复制网格
            self.ax.grid(True, alpha=0.3)
            
            # 刷新画布
            self.canvas.draw()
            
        except Exception as e:
            # 如果复制失败，显示简单的占位符
            self.ax.text(0.5, 0.5, f'比较图表已生成\n(显示可能不完整)', 
                        ha='center', va='center', transform=self.ax.transAxes,
                        fontsize=12, color='blue')
            self.canvas.draw()
    
    def _save_plot(self):
        """保存当前图表"""
        filename = filedialog.asksaveasfilename(
            title="保存比较图表",
            defaultextension=".png",
            filetypes=[("PNG文件", "*.png"), ("PDF文件", "*.pdf"), ("SVG文件", "*.svg"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                self.fig.savefig(filename, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"比较图表已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
    
    def _export_comparison_data(self):
        """导出比较数据"""
        selected_models = [self.selected_listbox.get(i) for i in range(self.selected_listbox.size())]
        
        if not selected_models:
            messagebox.showwarning("警告", "没有选择的模型")
            return
        
        filename = filedialog.asksaveasfilename(
            title="导出比较数据",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                # 收集比较数据
                comparison_df = pd.DataFrame()
                
                for model_name in selected_models:
                    if model_name in self.model_results:
                        result = self.model_results[model_name]
                        model_data = {
                            'Model': model_name,
                            'Accuracy': result.get('accuracy', 0),
                            'AUC': result.get('auc', 0),
                            'F1_Score': result.get('f1_score', 0),
                            'Precision': result.get('precision', 0),
                            'Recall': result.get('recall', 0),
                            'Training_Time': result.get('training_time', 0)
                        }
                        comparison_df = pd.concat([comparison_df, pd.DataFrame([model_data])], ignore_index=True)
                
                # 保存数据
                if filename.endswith('.csv'):
                    comparison_df.to_csv(filename, index=False)
                elif filename.endswith('.xlsx'):
                    comparison_df.to_excel(filename, index=False)
                
                messagebox.showinfo("成功", f"比较数据已导出到: {filename}")
                
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")
    
    def _show_model_summary(self):
        """显示模型摘要"""
        selected_models = [self.selected_listbox.get(i) for i in range(self.selected_listbox.size())]
        
        if not selected_models:
            messagebox.showwarning("警告", "请先选择模型")
            return
        
        # 创建摘要窗口
        summary_window = tk.Toplevel(self.parent)
        summary_window.title("模型比较摘要")
        summary_window.geometry("800x600")
        
        # 创建表格
        columns = ('模型', '准确率', 'AUC', 'F1分数', '精确率', '召回率', '训练时间')
        tree = ttk.Treeview(summary_window, columns=columns, show='headings', height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100, anchor=tk.CENTER)
        
        # 填充数据
        for model_name in selected_models:
            if model_name in self.model_results:
                result = self.model_results[model_name]
                tree.insert('', 'end', values=(
                    model_name,
                    f"{result.get('accuracy', 0):.4f}",
                    f"{result.get('auc', 0):.4f}",
                    f"{result.get('f1_score', 0):.4f}",
                    f"{result.get('precision', 0):.4f}",
                    f"{result.get('recall', 0):.4f}",
                    f"{result.get('training_time', 0):.2f}s"
                ))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(summary_window, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
