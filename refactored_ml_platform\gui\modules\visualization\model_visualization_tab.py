"""
模型可视化标签页
提供模型结果的可视化功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Any, Optional

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager
from ...components.chart_widgets import ChartWidget


class ModelVisualizationTab(BaseGUI):
    """模型可视化标签页"""
    
    def __init__(self, parent: tk.Widget):
        """初始化模型可视化标签页"""
        self.trained_models = {}
        self.selected_model = None
        self.current_visualization_type = "roc_curve"
        
        # 可视化类型配置
        self.visualization_types = {
            "roc_curve": "ROC曲线",
            "confusion_matrix": "混淆矩阵",
            "feature_importance": "特征重要性",
            "learning_curve": "学习曲线",
            "precision_recall": "精确率-召回率曲线",
            "calibration": "校准曲线",
            "shap_summary": "SHAP摘要图",
            "shap_waterfall": "SHAP瀑布图"
        }
        
        super().__init__(parent)
        
        # 订阅模型训练完成事件
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.MODEL_TRAINED, self._on_models_trained)
    
    def _setup_ui(self):
        """设置UI"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent, style='main')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分割
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧控制面板
        left_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(left_frame, weight=1)
        
        # 右侧可视化面板
        right_frame = factory.create_frame(paned_window, style='card')
        paned_window.add(right_frame, weight=3)
        
        # 设置控制面板
        self._setup_control_panel(left_frame)
        
        # 设置可视化面板
        self._setup_visualization_panel(right_frame)
        
        self.register_component('main_frame', self.main_frame)
        self.register_component('paned_window', paned_window)
    
    def _setup_control_panel(self, parent):
        """设置控制面板"""
        factory = get_component_factory()
        
        # 模型选择
        model_frame = factory.create_labelframe(parent, text="模型选择", style='section')
        model_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.model_listbox = tk.Listbox(model_frame, height=8)
        self.model_listbox.pack(fill=tk.X, padx=5, pady=5)
        self.model_listbox.bind('<<ListboxSelect>>', self._on_model_select)
        
        # 可视化类型选择
        viz_type_frame = factory.create_labelframe(parent, text="可视化类型", style='section')
        viz_type_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.viz_type_var = tk.StringVar(value="roc_curve")
        for viz_type, display_name in self.visualization_types.items():
            radio = factory.create_radiobutton(
                viz_type_frame,
                text=display_name,
                variable=self.viz_type_var,
                value=viz_type,
                command=self._on_visualization_type_changed
            )
            radio.pack(anchor=tk.W, padx=5, pady=2)
        
        # 可视化选项
        options_frame = factory.create_labelframe(parent, text="可视化选项", style='section')
        options_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 显示选项
        self.show_confidence_var = tk.BooleanVar(value=True)
        confidence_check = factory.create_checkbox(
            options_frame,
            text="显示置信区间",
            variable=self.show_confidence_var,
            command=self._on_option_changed
        )
        confidence_check.pack(anchor=tk.W, padx=5, pady=2)
        
        self.show_threshold_var = tk.BooleanVar(value=False)
        threshold_check = factory.create_checkbox(
            options_frame,
            text="显示阈值",
            variable=self.show_threshold_var,
            command=self._on_option_changed
        )
        threshold_check.pack(anchor=tk.W, padx=5, pady=2)
        
        self.normalize_var = tk.BooleanVar(value=False)
        normalize_check = factory.create_checkbox(
            options_frame,
            text="归一化显示",
            variable=self.normalize_var,
            command=self._on_option_changed
        )
        normalize_check.pack(anchor=tk.W, padx=5, pady=2)
        
        # 特征数量选择（用于特征重要性）
        feature_frame = factory.create_frame(options_frame)
        feature_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(feature_frame, text="显示特征数:").pack(side=tk.LEFT)
        self.n_features_var = tk.IntVar(value=20)
        feature_spinbox = factory.create_spinbox(
            feature_frame,
            from_=5,
            to=50,
            increment=5,
            textvariable=self.n_features_var,
            width=8,
            command=self._on_option_changed
        )
        feature_spinbox.pack(side=tk.RIGHT)
        
        # 生成可视化按钮
        self.generate_button = factory.create_button(
            parent,
            text="📊 生成可视化",
            command=self._generate_visualization,
            style='primary'
        )
        self.generate_button.pack(fill=tk.X, padx=5, pady=10)
        self.generate_button.config(state=tk.DISABLED)
        
        # 导出和保存按钮
        export_frame = factory.create_frame(parent)
        export_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.export_button = factory.create_button(
            export_frame,
            text="💾 导出",
            command=self._export_visualization,
            style='secondary'
        )
        self.export_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        self.export_button.config(state=tk.DISABLED)
        
        self.batch_export_button = factory.create_button(
            export_frame,
            text="📦 批量导出",
            command=self._batch_export,
            style='secondary'
        )
        self.batch_export_button.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(2, 0))
        self.batch_export_button.config(state=tk.DISABLED)
        
        # 状态标签
        self.status_label = factory.create_label(
            parent,
            text="请选择模型进行可视化",
            style='info'
        )
        self.status_label.pack(padx=5, pady=5)
    
    def _setup_visualization_panel(self, parent):
        """设置可视化面板"""
        factory = get_component_factory()
        
        # 可视化标签页
        self.viz_notebook = factory.create_notebook(parent)
        self.viz_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 主可视化标签页
        main_viz_frame = factory.create_frame(self.viz_notebook)
        self.viz_notebook.add(main_viz_frame, text="主图表")
        
        # 主图表容器
        self.main_chart_widget = ChartWidget(main_viz_frame, chart_type="model_visualization")
        
        # 对比可视化标签页
        compare_viz_frame = factory.create_frame(self.viz_notebook)
        self.viz_notebook.add(compare_viz_frame, text="对比图表")
        
        # 对比图表容器
        self.compare_chart_widget = ChartWidget(compare_viz_frame, chart_type="comparison")
        
        # 详细分析标签页
        analysis_frame = factory.create_frame(self.viz_notebook)
        self.viz_notebook.add(analysis_frame, text="详细分析")
        
        # 分析结果文本
        self.analysis_text = factory.create_text(
            analysis_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        analysis_scrollbar = factory.create_scrollbar(analysis_frame, orient=tk.VERTICAL)
        analysis_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.analysis_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.analysis_text.configure(yscrollcommand=analysis_scrollbar.set)
        analysis_scrollbar.configure(command=self.analysis_text.yview)
        
        # 交互式分析标签页
        interactive_frame = factory.create_frame(self.viz_notebook)
        self.viz_notebook.add(interactive_frame, text="交互式分析")
        
        # 交互式控件
        self._setup_interactive_analysis(interactive_frame)
    
    def _setup_interactive_analysis(self, parent):
        """设置交互式分析"""
        factory = get_component_factory()
        
        # 阈值调整
        threshold_frame = factory.create_labelframe(parent, text="阈值调整", style='section')
        threshold_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 阈值滑块
        factory.create_label(threshold_frame, text="分类阈值:").pack(anchor=tk.W, padx=5)
        
        self.threshold_var = tk.DoubleVar(value=0.5)
        self.threshold_scale = factory.create_scale(
            threshold_frame,
            from_=0.0,
            to=1.0,
            resolution=0.01,
            orient=tk.HORIZONTAL,
            variable=self.threshold_var,
            command=self._on_threshold_changed
        )
        self.threshold_scale.pack(fill=tk.X, padx=5, pady=5)
        
        # 阈值信息显示
        self.threshold_info_label = factory.create_label(
            threshold_frame,
            text="阈值: 0.50",
            style='info'
        )
        self.threshold_info_label.pack(padx=5, pady=2)
        
        # 性能指标显示
        metrics_frame = factory.create_labelframe(parent, text="实时性能指标", style='section')
        metrics_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 指标标签
        self.precision_label = factory.create_label(metrics_frame, text="精确率: --")
        self.precision_label.pack(anchor=tk.W, padx=5, pady=2)
        
        self.recall_label = factory.create_label(metrics_frame, text="召回率: --")
        self.recall_label.pack(anchor=tk.W, padx=5, pady=2)
        
        self.f1_label = factory.create_label(metrics_frame, text="F1分数: --")
        self.f1_label.pack(anchor=tk.W, padx=5, pady=2)
        
        # 样本分析
        sample_frame = factory.create_labelframe(parent, text="样本分析", style='section')
        sample_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 样本选择
        sample_select_frame = factory.create_frame(sample_frame)
        sample_select_frame.pack(fill=tk.X, padx=5, pady=5)
        
        factory.create_label(sample_select_frame, text="样本索引:").pack(side=tk.LEFT)
        self.sample_index_var = tk.IntVar(value=0)
        sample_spinbox = factory.create_spinbox(
            sample_select_frame,
            from_=0,
            to=100,
            increment=1,
            textvariable=self.sample_index_var,
            width=8,
            command=self._on_sample_changed
        )
        sample_spinbox.pack(side=tk.RIGHT)
        
        # 样本详情
        self.sample_details_text = factory.create_text(
            sample_frame,
            height=8,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        sample_scrollbar = factory.create_scrollbar(sample_frame, orient=tk.VERTICAL)
        sample_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.sample_details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.sample_details_text.configure(yscrollcommand=sample_scrollbar.set)
        sample_scrollbar.configure(command=self.sample_details_text.yview)
    
    def _on_models_trained(self, event_data: Dict[str, Any]):
        """模型训练完成事件处理"""
        self.trained_models = event_data.get('results', {})
        
        # 更新模型列表
        self.model_listbox.delete(0, tk.END)
        for model_name in self.trained_models.keys():
            display_name = self._get_display_name(model_name)
            self.model_listbox.insert(tk.END, display_name)
        
        self.batch_export_button.config(state=tk.NORMAL if self.trained_models else tk.DISABLED)
        
        self.logger.info(f"接收到 {len(self.trained_models)} 个训练完成的模型")
        self.status_label.config(text=f"可可视化 {len(self.trained_models)} 个模型")
    
    def _get_display_name(self, model_name: str) -> str:
        """获取模型显示名称"""
        display_names = {
            'DecisionTree': '决策树',
            'RandomForest': '随机森林',
            'XGBoost': 'XGBoost',
            'LightGBM': 'LightGBM',
            'CatBoost': 'CatBoost',
            'Logistic': '逻辑回归',
            'SVM': '支持向量机',
            'KNN': 'K近邻',
            'NaiveBayes': '朴素贝叶斯',
            'NeuralNet': '神经网络'
        }
        return display_names.get(model_name, model_name)
    
    def _on_model_select(self, event):
        """模型选择事件处理"""
        selection = self.model_listbox.curselection()
        if not selection:
            return
        
        index = selection[0]
        model_names = list(self.trained_models.keys())
        if index < len(model_names):
            self.selected_model = model_names[index]
            self.generate_button.config(state=tk.NORMAL)
            self.status_label.config(text=f"已选择模型: {self._get_display_name(self.selected_model)}")
            
            # 更新样本分析的范围
            if self.selected_model in self.trained_models:
                # 这里应该根据实际的测试集大小来设置
                # 目前使用模拟数据
                max_samples = 100  # 模拟值
                sample_spinbox = None
                # 找到样本选择控件并更新范围
                # 这里需要更复杂的控件查找逻辑
    
    def _on_visualization_type_changed(self):
        """可视化类型变化处理"""
        self.current_visualization_type = self.viz_type_var.get()
        self.logger.info(f"可视化类型变更为: {self.visualization_types[self.current_visualization_type]}")
        
        # 根据可视化类型启用/禁用相关选项
        self._update_option_availability()
    
    def _update_option_availability(self):
        """根据可视化类型更新选项可用性"""
        viz_type = self.current_visualization_type
        
        # 根据不同的可视化类型启用/禁用选项
        if viz_type == "feature_importance":
            # 特征重要性图需要特征数量选项
            pass
        elif viz_type in ["roc_curve", "precision_recall"]:
            # ROC和PR曲线需要置信区间选项
            pass
        elif viz_type == "confusion_matrix":
            # 混淆矩阵需要归一化选项
            pass
    
    def _on_option_changed(self):
        """选项变化处理"""
        self.logger.info("可视化选项已更新")
    
    def _on_threshold_changed(self, value):
        """阈值变化处理"""
        threshold = float(value)
        self.threshold_info_label.config(text=f"阈值: {threshold:.2f}")
        
        # 更新实时性能指标
        self._update_real_time_metrics(threshold)
    
    def _update_real_time_metrics(self, threshold: float):
        """更新实时性能指标"""
        if not self.selected_model or self.selected_model not in self.trained_models:
            return
        
        # 这里应该根据新阈值重新计算性能指标
        # 目前使用模拟数据
        import random
        
        precision = random.uniform(0.7, 0.95)
        recall = random.uniform(0.7, 0.95)
        f1 = 2 * (precision * recall) / (precision + recall)
        
        self.precision_label.config(text=f"精确率: {precision:.4f}")
        self.recall_label.config(text=f"召回率: {recall:.4f}")
        self.f1_label.config(text=f"F1分数: {f1:.4f}")
    
    def _on_sample_changed(self):
        """样本变化处理"""
        sample_index = self.sample_index_var.get()
        self._update_sample_details(sample_index)
    
    def _update_sample_details(self, sample_index: int):
        """更新样本详情"""
        if not self.selected_model or self.selected_model not in self.trained_models:
            return
        
        # 这里应该显示实际的样本详情
        # 目前使用模拟数据
        details = f"""
样本 #{sample_index} 详细信息
{'='*30}

特征值:
- 特征1: 0.234
- 特征2: 0.567
- 特征3: 0.891
- ...

预测结果:
- 预测类别: 正类
- 预测概率: 0.823
- 真实类别: 正类
- 预测正确: ✅

SHAP值:
- 特征1贡献: +0.12
- 特征2贡献: -0.05
- 特征3贡献: +0.31
- ...

解释:
该样本被正确分类为正类，主要由于特征3的强正向贡献。
        """
        
        self.sample_details_text.config(state=tk.NORMAL)
        self.sample_details_text.delete(1.0, tk.END)
        self.sample_details_text.insert(1.0, details.strip())
        self.sample_details_text.config(state=tk.DISABLED)
    
    def _generate_visualization(self):
        """生成可视化"""
        if not self.selected_model:
            messagebox.showwarning("警告", "请先选择模型")
            return
        
        try:
            viz_config = self._build_visualization_config()
            
            # 生成主图表
            self.main_chart_widget.load_data(viz_config)
            
            # 生成对比图表（如果适用）
            if self.current_visualization_type in ["roc_curve", "precision_recall"]:
                compare_config = self._build_comparison_config()
                self.compare_chart_widget.load_data(compare_config)
            
            # 更新详细分析
            self._update_detailed_analysis()
            
            # 启用导出按钮
            self.export_button.config(state=tk.NORMAL)
            
            self.logger.info(f"生成可视化: {self.visualization_types[self.current_visualization_type]}")
            self.status_label.config(text="可视化生成完成")
            
        except Exception as e:
            self.logger.error(f"生成可视化失败: {e}")
            messagebox.showerror("生成失败", f"可视化生成过程中出现错误:\n{e}")
    
    def _build_visualization_config(self) -> Dict[str, Any]:
        """构建可视化配置"""
        config = {
            'visualization_type': self.current_visualization_type,
            'model_name': self.selected_model,
            'model_data': self.trained_models[self.selected_model],
            'show_confidence': self.show_confidence_var.get(),
            'show_threshold': self.show_threshold_var.get(),
            'normalize': self.normalize_var.get(),
            'n_features': self.n_features_var.get(),
            'threshold': self.threshold_var.get()
        }
        
        return config
    
    def _build_comparison_config(self) -> Dict[str, Any]:
        """构建对比配置"""
        # 为对比图表准备配置
        config = {
            'comparison_type': 'multi_model',
            'models': self.trained_models,
            'visualization_type': self.current_visualization_type,
            'selected_model': self.selected_model
        }
        
        return config
    
    def _update_detailed_analysis(self):
        """更新详细分析"""
        if not self.selected_model or self.selected_model not in self.trained_models:
            return
        
        model_result = self.trained_models[self.selected_model]
        display_name = self._get_display_name(self.selected_model)
        viz_name = self.visualization_types[self.current_visualization_type]
        
        analysis = f"""
{display_name} - {viz_name} 详细分析
{'='*50}

模型性能概览:
- 准确率: {model_result.get('accuracy', 0):.4f}
- 精确率: {model_result.get('precision', 0):.4f}
- 召回率: {model_result.get('recall', 0):.4f}
- F1分数: {model_result.get('f1_score', 0):.4f}
- AUC: {model_result.get('auc', 0):.4f}

可视化分析:
"""
        
        if self.current_visualization_type == "roc_curve":
            analysis += """
ROC曲线分析:
- AUC值表示模型的整体分类能力
- 曲线越靠近左上角，性能越好
- 对角线表示随机分类器的性能
- 当前模型表现良好，具有较强的分类能力
"""
        elif self.current_visualization_type == "confusion_matrix":
            analysis += """
混淆矩阵分析:
- 对角线元素表示正确分类的样本数
- 非对角线元素表示错误分类的样本数
- 可以直观看出各类别的分类准确性
- 建议关注假阳性和假阴性的分布
"""
        elif self.current_visualization_type == "feature_importance":
            analysis += """
特征重要性分析:
- 显示各特征对模型预测的贡献度
- 重要性高的特征对模型决策影响更大
- 可用于特征选择和模型解释
- 建议保留重要性高的特征，考虑移除重要性低的特征
"""
        
        analysis += f"""

建议和改进方向:
- 如果性能不满意，可以尝试超参数调优
- 考虑特征工程来提升模型性能
- 可以尝试集成学习方法
- 注意检查数据质量和标签准确性

当前配置:
- 显示置信区间: {'是' if self.show_confidence_var.get() else '否'}
- 显示阈值: {'是' if self.show_threshold_var.get() else '否'}
- 归一化显示: {'是' if self.normalize_var.get() else '否'}
- 特征数量: {self.n_features_var.get()}
- 分类阈值: {self.threshold_var.get():.2f}
        """
        
        self.analysis_text.config(state=tk.NORMAL)
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(1.0, analysis.strip())
        self.analysis_text.config(state=tk.DISABLED)
    
    def _export_visualization(self):
        """导出可视化"""
        try:
            from tkinter import filedialog
            
            file_path = filedialog.asksaveasfilename(
                title="导出可视化",
                defaultextension=".png",
                filetypes=[
                    ("PNG files", "*.png"),
                    ("PDF files", "*.pdf"),
                    ("SVG files", "*.svg"),
                    ("All files", "*.*")
                ]
            )
            
            if file_path:
                # 这里应该实现实际的导出逻辑
                self.logger.info(f"可视化导出到: {file_path}")
                messagebox.showinfo("导出成功", f"可视化已导出到:\n{file_path}")
        
        except Exception as e:
            self.logger.error(f"导出可视化失败: {e}")
            messagebox.showerror("导出失败", f"导出过程中出现错误:\n{e}")
    
    def _batch_export(self):
        """批量导出所有模型的可视化"""
        if not self.trained_models:
            messagebox.showwarning("警告", "没有可导出的模型")
            return
        
        try:
            from tkinter import filedialog
            
            folder_path = filedialog.askdirectory(title="选择导出文件夹")
            
            if folder_path:
                # 这里应该实现批量导出逻辑
                exported_count = len(self.trained_models) * len(self.visualization_types)
                
                self.logger.info(f"批量导出到文件夹: {folder_path}")
                messagebox.showinfo(
                    "批量导出完成", 
                    f"已导出 {exported_count} 个可视化文件到:\n{folder_path}"
                )
        
        except Exception as e:
            self.logger.error(f"批量导出失败: {e}")
            messagebox.showerror("批量导出失败", f"批量导出过程中出现错误:\n{e}")
    
    def get_current_visualization_config(self) -> Dict[str, Any]:
        """获取当前可视化配置"""
        return self._build_visualization_config()
    
    def clear_visualizations(self):
        """清空可视化"""
        self.main_chart_widget.clear_chart()
        self.compare_chart_widget.clear_chart()
        self.export_button.config(state=tk.DISABLED)
        
        # 清空分析文本
        self.analysis_text.config(state=tk.NORMAL)
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(1.0, "选择模型和可视化类型后，详细分析将在这里显示...")
        self.analysis_text.config(state=tk.DISABLED)
