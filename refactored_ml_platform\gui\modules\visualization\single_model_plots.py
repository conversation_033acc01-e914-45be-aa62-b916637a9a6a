#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单模型可视化模块
提供单个模型的各种可视化功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import threading

try:
    from ...core.event_manager import get_event_manager
    from utils.plot_manager import get_plot_manager
    from core.model_manager import get_model_manager
    from utils.error_handler import get_error_handler
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))

    from gui.core.event_manager import get_event_manager
    from utils.plot_manager import get_plot_manager
    from core.model_manager import get_model_manager
    from utils.error_handler import get_error_handler


class SingleModelPlotsModule:
    """单模型可视化模块"""
    
    def __init__(self, parent):
        """初始化单模型可视化模块"""
        self.parent = parent
        self.plot_manager = get_plot_manager()
        self.event_manager = get_event_manager()
        self.model_manager = get_model_manager()
        self.error_handler = get_error_handler()
        
        # 数据存储
        self.current_model_results = {}
        self.current_model_name = None
        
        # GUI组件
        self.frame = None
        self.canvas = None
        self.toolbar = None
        self.fig = None
        self.ax = None
        
        # 控制变量
        self.model_var = tk.StringVar()
        self.plot_type_var = tk.StringVar(value="ROC曲线")
        self.status_var = tk.StringVar(value="就绪")
        
        # 创建界面
        self._create_interface()
        
        # 注册事件监听
        self._register_events()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.frame = ttk.Frame(self.parent)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 控制面板
        self._create_control_panel()
        
        # 图表显示区域
        self._create_chart_area()
        
        # 状态栏
        self._create_status_bar()
    
    def _create_control_panel(self):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(self.frame, text="可视化控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 第一行：模型选择和图表类型
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=tk.X, padx=10, pady=10)
        
        # 模型选择
        ttk.Label(row1, text="选择模型:").pack(side=tk.LEFT, padx=(0, 5))
        self.model_combo = ttk.Combobox(
            row1, textvariable=self.model_var,
            state="readonly", width=15
        )
        self.model_combo.pack(side=tk.LEFT, padx=(0, 20))
        self.model_combo.bind('<<ComboboxSelected>>', self._on_model_change)
        
        # 图表类型选择
        ttk.Label(row1, text="图表类型:").pack(side=tk.LEFT, padx=(0, 5))
        plot_types = ["ROC曲线", "PR曲线", "混淆矩阵", "学习曲线", "特征重要性"]
        self.plot_type_combo = ttk.Combobox(
            row1, textvariable=self.plot_type_var,
            values=plot_types, state="readonly", width=12
        )
        self.plot_type_combo.pack(side=tk.LEFT, padx=(0, 20))
        self.plot_type_combo.bind('<<ComboboxSelected>>', self._on_plot_type_change)
        
        # 第二行：操作按钮
        row2 = ttk.Frame(control_frame)
        row2.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(row2, text="📊 生成图表", command=self._generate_plot).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="💾 保存图表", command=self._save_plot).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="🔄 刷新模型", command=self._refresh_models).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="📋 查看详情", command=self._show_model_details).pack(side=tk.LEFT, padx=(0, 5))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            row2, variable=self.progress_var,
            mode='determinate', length=150
        )
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
    
    def _create_chart_area(self):
        """创建图表显示区域"""
        chart_frame = ttk.LabelFrame(self.frame, text="图表显示")
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建matplotlib图表
        self.fig, self.ax = plt.subplots(figsize=(10, 6))
        self.fig.patch.set_facecolor('white')
        
        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 创建工具栏
        toolbar_frame = ttk.Frame(chart_frame)
        toolbar_frame.pack(fill=tk.X)
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        self.toolbar.update()
        
        # 初始化空白图表
        self._show_empty_plot()
    
    def _create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.frame)
        status_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(side=tk.LEFT, padx=(5, 0))
    
    def _register_events(self):
        """注册事件监听"""
        self.event_manager.subscribe('model_trained', self._on_model_trained)
        self.event_manager.subscribe('models_updated', self._on_models_updated)
    
    def _show_empty_plot(self):
        """显示空白图表"""
        self.ax.clear()
        self.ax.text(0.5, 0.5, '请选择模型和图表类型\n然后点击"生成图表"', 
                    ha='center', va='center', transform=self.ax.transAxes,
                    fontsize=14, color='gray')
        self.ax.set_xticks([])
        self.ax.set_yticks([])
        self.canvas.draw()
    
    def _on_model_trained(self, event_data):
        """模型训练完成事件处理"""
        model_name = event_data.get('model_name')
        result = event_data.get('result')
        
        if model_name and result:
            self.current_model_results[model_name] = result
            self._refresh_models()
            self.status_var.set(f"模型 {model_name} 训练完成，可进行可视化")
    
    def _on_models_updated(self, event_data):
        """模型更新事件处理"""
        self._refresh_models()
    
    def _refresh_models(self):
        """刷新模型列表"""
        try:
            # 获取已训练的模型
            trained_models = list(self.current_model_results.keys())
            
            # 也可以从模型管理器获取
            available_models = self.model_manager.list_models()
            
            # 合并模型列表
            all_models = list(set(trained_models + available_models))
            
            # 更新下拉框
            self.model_combo['values'] = all_models
            
            if all_models and not self.model_var.get():
                self.model_var.set(all_models[0])
            
            self.status_var.set(f"已加载 {len(all_models)} 个模型")
            
        except Exception as e:
            self.error_handler.handle_error(e, "刷新模型列表")
    
    def _on_model_change(self, event=None):
        """模型选择改变事件处理"""
        self.current_model_name = self.model_var.get()
        if self.current_model_name:
            self.status_var.set(f"已选择模型: {self.current_model_name}")
    
    def _on_plot_type_change(self, event=None):
        """图表类型改变事件处理"""
        plot_type = self.plot_type_var.get()
        self.status_var.set(f"已选择图表类型: {plot_type}")
    
    def _generate_plot(self):
        """生成图表"""
        if not self.current_model_name:
            messagebox.showwarning("警告", "请先选择模型")
            return
        
        plot_type = self.plot_type_var.get()
        if not plot_type:
            messagebox.showwarning("警告", "请选择图表类型")
            return
        
        # 在后台线程中生成图表
        threading.Thread(target=self._perform_plot_generation, daemon=True).start()
    
    def _perform_plot_generation(self):
        """执行图表生成"""
        try:
            self.status_var.set("正在生成图表...")
            self.progress_var.set(20)
            
            # 获取模型结果
            if self.current_model_name in self.current_model_results:
                result = self.current_model_results[self.current_model_name]
            else:
                result = self.model_manager.get_model_result(self.current_model_name)
            
            if not result:
                messagebox.showerror("错误", f"无法获取模型 {self.current_model_name} 的结果")
                return
            
            self.progress_var.set(50)
            
            # 根据图表类型生成图表
            plot_type = self.plot_type_var.get()
            
            if plot_type == "ROC曲线":
                self._generate_roc_curve(result)
            elif plot_type == "PR曲线":
                self._generate_pr_curve(result)
            elif plot_type == "混淆矩阵":
                self._generate_confusion_matrix(result)
            elif plot_type == "学习曲线":
                self._generate_learning_curve(result)
            elif plot_type == "特征重要性":
                self._generate_feature_importance(result)
            else:
                messagebox.showwarning("警告", f"不支持的图表类型: {plot_type}")
                return
            
            self.progress_var.set(100)
            self.status_var.set("图表生成完成")
            
            # 重置进度条
            self.parent.after(2000, lambda: self.progress_var.set(0))
            
        except Exception as e:
            self.error_handler.handle_error(e, "生成图表")
            self.status_var.set("图表生成失败")
            self.progress_var.set(0)
    
    def _generate_roc_curve(self, result):
        """生成ROC曲线"""
        y_true = result.get('y_true') or result.get('y_test')
        y_pred_proba = result.get('y_pred_proba')
        
        if y_true is None or y_pred_proba is None:
            messagebox.showerror("错误", "缺少ROC曲线所需的数据")
            return
        
        # 清空当前图表
        self.ax.clear()
        
        # 使用绘图管理器生成ROC曲线
        fig = self.plot_manager.plot_roc_curve(
            y_true, y_pred_proba, self.current_model_name
        )
        
        # 将生成的图表复制到当前画布
        self._copy_plot_to_canvas(fig)
        plt.close(fig)  # 关闭临时图表
    
    def _generate_pr_curve(self, result):
        """生成PR曲线"""
        y_true = result.get('y_true') or result.get('y_test')
        y_pred_proba = result.get('y_pred_proba')
        
        if y_true is None or y_pred_proba is None:
            messagebox.showerror("错误", "缺少PR曲线所需的数据")
            return
        
        self.ax.clear()
        
        fig = self.plot_manager.plot_precision_recall_curve(
            y_true, y_pred_proba, self.current_model_name
        )
        
        self._copy_plot_to_canvas(fig)
        plt.close(fig)
    
    def _generate_confusion_matrix(self, result):
        """生成混淆矩阵"""
        y_true = result.get('y_true') or result.get('y_test')
        y_pred = result.get('y_pred')
        
        if y_true is None or y_pred is None:
            messagebox.showerror("错误", "缺少混淆矩阵所需的数据")
            return
        
        self.ax.clear()
        
        fig = self.plot_manager.plot_confusion_matrix(
            y_true, y_pred, self.current_model_name
        )
        
        self._copy_plot_to_canvas(fig)
        plt.close(fig)
    
    def _generate_learning_curve(self, result):
        """生成学习曲线"""
        model = result.get('model')
        X_test = result.get('X_test')
        y_test = result.get('y_test')
        
        if model is None or X_test is None or y_test is None:
            messagebox.showerror("错误", "缺少学习曲线所需的数据")
            return
        
        self.ax.clear()
        
        try:
            fig = self.plot_manager.plot_learning_curve(
                model, X_test, y_test, self.current_model_name
            )
            
            self._copy_plot_to_canvas(fig)
            plt.close(fig)
        except Exception as e:
            messagebox.showerror("错误", f"生成学习曲线失败: {e}")
    
    def _generate_feature_importance(self, result):
        """生成特征重要性图"""
        model = result.get('model')
        feature_names = result.get('feature_names')
        
        if model is None:
            messagebox.showerror("错误", "缺少模型对象")
            return
        
        # 获取特征重要性
        if hasattr(model, 'feature_importances_'):
            importances = model.feature_importances_
        elif hasattr(model, 'coef_'):
            importances = np.abs(model.coef_[0])
        else:
            messagebox.showerror("错误", "该模型不支持特征重要性分析")
            return
        
        if feature_names is None:
            feature_names = [f'Feature_{i}' for i in range(len(importances))]
        
        self.ax.clear()
        
        fig = self.plot_manager.plot_feature_importance(
            feature_names, importances, self.current_model_name
        )
        
        self._copy_plot_to_canvas(fig)
        plt.close(fig)
    
    def _copy_plot_to_canvas(self, source_fig):
        """将源图表复制到当前画布"""
        try:
            # 获取源图表的轴
            source_ax = source_fig.axes[0]
            
            # 复制图表内容到当前轴
            for line in source_ax.get_lines():
                self.ax.plot(line.get_xdata(), line.get_ydata(), 
                           color=line.get_color(), linewidth=line.get_linewidth(),
                           linestyle=line.get_linestyle(), label=line.get_label())
            
            # 复制轴属性
            self.ax.set_xlabel(source_ax.get_xlabel())
            self.ax.set_ylabel(source_ax.get_ylabel())
            self.ax.set_title(source_ax.get_title())
            
            # 复制图例
            if source_ax.get_legend():
                self.ax.legend()
            
            # 复制网格
            self.ax.grid(source_ax.get_xgridlines() or source_ax.get_ygridlines())
            
            # 刷新画布
            self.canvas.draw()
            
        except Exception as e:
            # 如果复制失败，显示简单的占位符
            self.ax.text(0.5, 0.5, f'图表已生成\n(显示可能不完整)', 
                        ha='center', va='center', transform=self.ax.transAxes,
                        fontsize=12, color='blue')
            self.canvas.draw()
    
    def _save_plot(self):
        """保存当前图表"""
        if not self.current_model_name:
            messagebox.showwarning("警告", "没有可保存的图表")
            return
        
        filename = filedialog.asksaveasfilename(
            title="保存图表",
            defaultextension=".png",
            filetypes=[("PNG文件", "*.png"), ("PDF文件", "*.pdf"), ("SVG文件", "*.svg"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                self.fig.savefig(filename, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"图表已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
    
    def _show_model_details(self):
        """显示模型详情"""
        if not self.current_model_name:
            messagebox.showwarning("警告", "请先选择模型")
            return
        
        # 获取模型结果
        if self.current_model_name in self.current_model_results:
            result = self.current_model_results[self.current_model_name]
        else:
            result = self.model_manager.get_model_result(self.current_model_name)
        
        if not result:
            messagebox.showerror("错误", f"无法获取模型 {self.current_model_name} 的详情")
            return
        
        # 创建详情窗口
        detail_window = tk.Toplevel(self.parent)
        detail_window.title(f"模型详情 - {self.current_model_name}")
        detail_window.geometry("600x400")
        
        # 创建文本框显示详细信息
        text_widget = tk.Text(detail_window, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(detail_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        # 格式化详情信息
        detail_text = f"模型: {self.current_model_name}\n"
        detail_text += "=" * 50 + "\n\n"
        
        for key, value in result.items():
            if key not in ['model', 'y_true', 'y_pred', 'y_pred_proba', 'X_test']:
                detail_text += f"{key}: {value}\n"
        
        text_widget.insert(1.0, detail_text)
        text_widget.config(state=tk.DISABLED)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
