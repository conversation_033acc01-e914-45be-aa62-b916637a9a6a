#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构后的多模型集成机器学习平台 - 主入口
基于模块化架构设计的新版本GUI应用
"""

import sys
import tkinter as tk
from pathlib import Path
import logging

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root.parent / 'code'))  # 原始代码路径

# 确保日志目录存在
from gui.core.event_manager import get_event_manager
from gui.core.config_manager import get_gui_config
from gui.core.component_factory import get_component_factory
from gui.layouts.main_layout import MainWindow


class RefactoredMLPlatform:
    """
    重构版机器学习平台主应用程序类
    负责应用程序的初始化、配置和生命周期管理
    """
    
    def __init__(self):
        """初始化应用程序"""
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 创建主窗口
        self.root = tk.Tk()
        
        # 设置项目路径
        self.project_path = project_root
        self.logger.info(f"项目路径: {self.project_path}")
        
        # 初始化核心组件
        self.event_manager = get_event_manager()
        self.config = get_gui_config()
        self.component_factory = get_component_factory()
        
        # 设置窗口
        self._setup_window()
        
        # 创建主界面
        self._create_main_interface()
        
        self.logger.info("重构版机器学习平台初始化完成")
    
    def _setup_window(self):
        """设置主窗口"""
        window_config = self.config.get_window_config()
        
        # 设置标题
        self.root.title(window_config.get('title', '多模型集成机器学习平台 - 重构版'))
        
        # 设置大小和位置
        size = window_config.get('size', [1400, 900])
        min_size = window_config.get('min_size', [1200, 800])
        
        self.root.geometry(f"{size[0]}x{size[1]}")
        self.root.minsize(min_size[0], min_size[1])
        
        # 居中显示
        if window_config.get('center_on_screen', True):
            self._center_window()
        
        # 设置图标（如果存在）
        try:
            icon_path = project_root / 'assets' / 'icon.ico'
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except Exception as e:
            self.logger.warning(f"无法加载图标: {e}")
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def _on_closing(self):
        """窗口关闭事件处理"""
        behavior_config = self.config.get_behavior_config()
        
        if behavior_config.get('confirm_on_exit', True):
            from tkinter import messagebox
            if not messagebox.askyesno("确认退出", "确定要退出应用程序吗？"):
                return
        
        # 保存配置
        if behavior_config.get('remember_window_state', True):
            self._save_window_state()
        
        self.logger.info("应用程序正在退出")
        self.root.destroy()
    
    def _create_main_interface(self):
        """创建主界面"""
        # 初始化主窗口
        main_window = MainWindow(self.root)
        
        self.logger.info("主界面初始化完成")
        tk.Label(log_frame, text="系统日志:", font=("Microsoft YaHei UI", 10, "bold")).pack(anchor=tk.W)
        
        self.log_text = tk.Text(log_frame, height=10, font=("Consolas", 9))
        scrollbar = tk.Scrollbar(log_frame, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加初始日志
        self._add_log("INFO", "重构版机器学习平台启动成功")
        self._add_log("INFO", "事件管理器已初始化")
        self._add_log("INFO", "配置管理器已加载")
        self._add_log("INFO", "组件工厂已准备就绪")
    
    def _test_event_system(self):
        """测试事件系统"""
        from gui.core.event_manager import EventTypes
        
        def test_callback(data):
            self._add_log("SUCCESS", f"事件系统测试成功: {data}")
        
        # 订阅测试事件
        self.event_manager.subscribe("test_event", test_callback)
        
        # 发布测试事件
        self.event_manager.publish("test_event", "Hello from Event System!")
        
        self._add_log("INFO", "事件系统测试已执行")
    
    def _add_log(self, level, message):
        """添加日志到界面"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
    
    def _save_window_state(self):
        """保存窗口状态"""
        try:
            geometry = self.root.geometry()
            width, height, x, y = map(int, geometry.replace('x', '+').replace('+', ' ').split())
            
            self.config.set('window.size', [width, height], save=False)
            self.config.set('window.position', [x, y], save=True)
        except Exception as e:
            self.logger.warning(f"保存窗口状态失败: {e}")
    
    def run(self):
        """启动应用"""
        try:
            self.logger.info("启动重构版机器学习平台")
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"应用运行时出现错误: {e}")
            raise


def main():
    """主函数"""
    try:
        # 确保日志目录存在
        log_dir = project_root / 'logs'
        log_dir.mkdir(exist_ok=True)
        
        # 创建并运行应用
        app = RefactoredMLPlatform()
        app.run()
        
    except Exception as e:
        logging.error(f"应用启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
