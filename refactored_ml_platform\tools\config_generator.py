#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置生成器
帮助用户快速生成和管理机器学习项目配置
"""

import json
import yaml
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
import logging

try:
    from ..utils.error_handler import get_error_handler, error_handler
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))
    from utils.error_handler import get_error_handler, error_handler


class ConfigGenerator:
    """配置生成器类"""
    
    def __init__(self):
        """初始化配置生成器"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        
        # 支持的模型类型
        self.supported_models = [
            'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
            'LogisticRegression', 'SVM', 'KNN', 'NaiveBayes',
            'DecisionTree', 'NeuralNet'
        ]
        
        # 默认配置模板
        self.default_config_template = {
            'project': {
                'name': 'ML_Project',
                'description': '机器学习项目',
                'version': '1.0.0',
                'created_time': None,
                'author': 'ML Platform User'
            },
            'data': {
                'train_path': None,
                'test_path': None,
                'validation_split': 0.2,
                'random_state': 42,
                'preprocessing': {
                    'normalize': True,
                    'handle_missing': 'drop',
                    'feature_selection': False
                }
            },
            'models': {
                'enabled_models': [],
                'hyperparameters': {},
                'cross_validation': {
                    'enabled': True,
                    'folds': 5,
                    'scoring': 'accuracy'
                }
            },
            'ensemble': {
                'enabled': False,
                'methods': ['voting', 'stacking'],
                'voting_strategy': 'soft',
                'meta_learner': 'LogisticRegression'
            },
            'evaluation': {
                'metrics': ['accuracy', 'precision', 'recall', 'f1_score', 'auc'],
                'generate_plots': True,
                'save_predictions': True
            },
            'output': {
                'save_models': True,
                'save_results': True,
                'output_dir': 'output',
                'report_format': 'html'
            }
        }
    
    @error_handler("生成基础配置")
    def generate_basic_config(self, project_name: str, data_path: str,
                            models: List[str], output_path: str = None) -> Dict[str, Any]:
        """
        生成基础配置
        
        Args:
            project_name: 项目名称
            data_path: 数据路径
            models: 模型列表
            output_path: 输出路径
            
        Returns:
            生成的配置字典
        """
        # 验证模型
        invalid_models = [m for m in models if m not in self.supported_models]
        if invalid_models:
            raise ValueError(f"不支持的模型: {invalid_models}")
        
        # 创建配置
        config = self.default_config_template.copy()
        
        # 更新项目信息
        config['project']['name'] = project_name
        config['project']['created_time'] = datetime.now().isoformat()
        
        # 更新数据配置
        config['data']['train_path'] = data_path
        
        # 更新模型配置
        config['models']['enabled_models'] = models
        config['models']['hyperparameters'] = self._get_default_hyperparameters(models)
        
        # 如果模型数量大于1，启用集成学习
        if len(models) > 1:
            config['ensemble']['enabled'] = True
        
        # 保存配置
        if output_path:
            self.save_config(config, output_path)
        
        return config
    
    def _get_default_hyperparameters(self, models: List[str]) -> Dict[str, Dict]:
        """获取默认超参数"""
        default_params = {
            'RandomForest': {
                'n_estimators': 100,
                'max_depth': None,
                'min_samples_split': 2,
                'min_samples_leaf': 1,
                'random_state': 42
            },
            'XGBoost': {
                'n_estimators': 100,
                'max_depth': 6,
                'learning_rate': 0.1,
                'subsample': 1.0,
                'random_state': 42
            },
            'LightGBM': {
                'n_estimators': 100,
                'max_depth': -1,
                'learning_rate': 0.1,
                'num_leaves': 31,
                'random_state': 42
            },
            'CatBoost': {
                'iterations': 100,
                'depth': 6,
                'learning_rate': 0.1,
                'random_state': 42,
                'verbose': False
            },
            'LogisticRegression': {
                'C': 1.0,
                'max_iter': 1000,
                'random_state': 42
            },
            'SVM': {
                'C': 1.0,
                'kernel': 'rbf',
                'gamma': 'scale',
                'random_state': 42
            },
            'KNN': {
                'n_neighbors': 5,
                'weights': 'uniform',
                'algorithm': 'auto'
            },
            'NaiveBayes': {},
            'DecisionTree': {
                'max_depth': None,
                'min_samples_split': 2,
                'min_samples_leaf': 1,
                'random_state': 42
            },
            'NeuralNet': {
                'hidden_layer_sizes': (100,),
                'activation': 'relu',
                'solver': 'adam',
                'max_iter': 200,
                'random_state': 42
            }
        }
        
        return {model: default_params.get(model, {}) for model in models}
    
    @error_handler("生成高级配置")
    def generate_advanced_config(self, **kwargs) -> Dict[str, Any]:
        """
        生成高级配置
        
        Args:
            **kwargs: 配置参数
            
        Returns:
            生成的配置字典
        """
        config = self.default_config_template.copy()
        
        # 递归更新配置
        self._deep_update(config, kwargs)
        
        # 设置创建时间
        if not config['project']['created_time']:
            config['project']['created_time'] = datetime.now().isoformat()
        
        return config
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    @error_handler("保存配置")
    def save_config(self, config: Dict[str, Any], output_path: str, 
                   format: str = 'auto') -> bool:
        """
        保存配置到文件
        
        Args:
            config: 配置字典
            output_path: 输出路径
            format: 文件格式 ('json', 'yaml', 'auto')
            
        Returns:
            是否保存成功
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 自动检测格式
        if format == 'auto':
            if output_path.suffix.lower() in ['.yml', '.yaml']:
                format = 'yaml'
            else:
                format = 'json'
        
        try:
            if format == 'yaml':
                with open(output_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
            else:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置已保存到: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            return False
    
    @error_handler("加载配置")
    def load_config(self, config_path: str) -> Optional[Dict[str, Any]]:
        """
        从文件加载配置
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置字典或None
        """
        config_path = Path(config_path)
        
        if not config_path.exists():
            self.logger.error(f"配置文件不存在: {config_path}")
            return None
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yml', '.yaml']:
                    config = yaml.safe_load(f)
                else:
                    config = json.load(f)
            
            self.logger.info(f"配置已加载: {config_path}")
            return config
            
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            return None
    
    @error_handler("验证配置")
    def validate_config(self, config: Dict[str, Any]) -> List[str]:
        """
        验证配置的有效性
        
        Args:
            config: 配置字典
            
        Returns:
            错误信息列表
        """
        errors = []
        
        # 检查必需字段
        required_fields = [
            'project.name',
            'data.train_path',
            'models.enabled_models'
        ]
        
        for field in required_fields:
            if not self._get_nested_value(config, field):
                errors.append(f"缺少必需字段: {field}")
        
        # 检查模型有效性
        enabled_models = config.get('models', {}).get('enabled_models', [])
        invalid_models = [m for m in enabled_models if m not in self.supported_models]
        if invalid_models:
            errors.append(f"不支持的模型: {invalid_models}")
        
        # 检查数据路径
        train_path = config.get('data', {}).get('train_path')
        if train_path and not Path(train_path).exists():
            errors.append(f"训练数据文件不存在: {train_path}")
        
        test_path = config.get('data', {}).get('test_path')
        if test_path and not Path(test_path).exists():
            errors.append(f"测试数据文件不存在: {test_path}")
        
        return errors
    
    def _get_nested_value(self, data: Dict, key_path: str):
        """获取嵌套字典的值"""
        keys = key_path.split('.')
        value = data
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        
        return value
    
    @error_handler("生成配置模板")
    def generate_template(self, template_type: str = 'basic') -> Dict[str, Any]:
        """
        生成配置模板
        
        Args:
            template_type: 模板类型 ('basic', 'advanced', 'ensemble')
            
        Returns:
            配置模板
        """
        if template_type == 'basic':
            return self._generate_basic_template()
        elif template_type == 'advanced':
            return self._generate_advanced_template()
        elif template_type == 'ensemble':
            return self._generate_ensemble_template()
        else:
            raise ValueError(f"不支持的模板类型: {template_type}")
    
    def _generate_basic_template(self) -> Dict[str, Any]:
        """生成基础模板"""
        template = self.default_config_template.copy()
        template['project']['name'] = 'Basic_ML_Project'
        template['models']['enabled_models'] = ['RandomForest', 'LogisticRegression']
        return template
    
    def _generate_advanced_template(self) -> Dict[str, Any]:
        """生成高级模板"""
        template = self.default_config_template.copy()
        template['project']['name'] = 'Advanced_ML_Project'
        template['models']['enabled_models'] = ['RandomForest', 'XGBoost', 'LightGBM']
        template['ensemble']['enabled'] = True
        template['data']['preprocessing']['feature_selection'] = True
        return template
    
    def _generate_ensemble_template(self) -> Dict[str, Any]:
        """生成集成学习模板"""
        template = self.default_config_template.copy()
        template['project']['name'] = 'Ensemble_ML_Project'
        template['models']['enabled_models'] = ['RandomForest', 'XGBoost', 'LightGBM', 'CatBoost']
        template['ensemble']['enabled'] = True
        template['ensemble']['methods'] = ['voting', 'stacking', 'bagging']
        return template
    
    def list_supported_models(self) -> List[str]:
        """列出支持的模型"""
        return self.supported_models.copy()
    
    def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """获取模型信息"""
        if model_name not in self.supported_models:
            return None
        
        model_info = {
            'name': model_name,
            'default_params': self._get_default_hyperparameters([model_name])[model_name],
            'description': self._get_model_description(model_name)
        }
        
        return model_info
    
    def _get_model_description(self, model_name: str) -> str:
        """获取模型描述"""
        descriptions = {
            'RandomForest': '随机森林 - 基于决策树的集成算法，具有良好的泛化能力',
            'XGBoost': 'XGBoost - 梯度提升算法，在结构化数据上表现优异',
            'LightGBM': 'LightGBM - 微软开发的梯度提升框架，训练速度快',
            'CatBoost': 'CatBoost - Yandex开发的梯度提升算法，对类别特征友好',
            'LogisticRegression': '逻辑回归 - 线性分类算法，可解释性强',
            'SVM': '支持向量机 - 基于间隔最大化的分类算法',
            'KNN': 'K近邻 - 基于实例的懒惰学习算法',
            'NaiveBayes': '朴素贝叶斯 - 基于贝叶斯定理的概率分类器',
            'DecisionTree': '决策树 - 基于树结构的分类算法，可解释性强',
            'NeuralNet': '神经网络 - 多层感知机，适合复杂非线性问题'
        }
        
        return descriptions.get(model_name, '暂无描述')


# 全局配置生成器实例
_config_generator = None

def get_config_generator() -> ConfigGenerator:
    """
    获取全局配置生成器实例
    
    Returns:
        配置生成器实例
    """
    global _config_generator
    if _config_generator is None:
        _config_generator = ConfigGenerator()
    return _config_generator
