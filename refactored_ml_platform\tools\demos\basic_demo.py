#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础功能演示
展示机器学习平台的基本使用方法
"""

import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
import logging

try:
    from ...core.model_manager import get_model_manager
    from ...core.session_manager import get_session_manager
    from ...utils.error_handler import get_error_handler, error_handler
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent
    sys.path.insert(0, str(project_root))
    from core.model_manager import get_model_manager
    from core.session_manager import get_session_manager
    from utils.error_handler import get_error_handler, error_handler


class BasicDemo:
    """基础功能演示类"""
    
    def __init__(self):
        """初始化演示"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        self.model_manager = get_model_manager()
        self.session_manager = get_session_manager()
        
        # 演示数据路径
        self.demo_data_dir = Path("output/demo_data")
        self.demo_data_dir.mkdir(parents=True, exist_ok=True)
    
    @error_handler("创建演示数据")
    def create_demo_data(self, n_samples: int = 1000, n_features: int = 20) -> str:
        """
        创建演示数据集
        
        Args:
            n_samples: 样本数量
            n_features: 特征数量
            
        Returns:
            数据文件路径
        """
        self.logger.info(f"创建演示数据集: {n_samples} 样本, {n_features} 特征")
        
        # 生成分类数据
        X, y = make_classification(
            n_samples=n_samples,
            n_features=n_features,
            n_informative=int(n_features * 0.7),
            n_redundant=int(n_features * 0.2),
            n_clusters_per_class=1,
            random_state=42
        )
        
        # 创建DataFrame
        feature_names = [f'feature_{i:02d}' for i in range(n_features)]
        df = pd.DataFrame(X, columns=feature_names)
        df['target'] = y
        
        # 保存数据
        data_path = self.demo_data_dir / f"demo_dataset_{n_samples}x{n_features}.csv"
        df.to_csv(data_path, index=False)
        
        self.logger.info(f"演示数据已保存到: {data_path}")
        self.logger.info(f"数据形状: {df.shape}")
        self.logger.info(f"类别分布: {np.bincount(y)}")
        
        return str(data_path)
    
    @error_handler("运行基础训练演示")
    def run_basic_training_demo(self) -> bool:
        """
        运行基础训练演示
        
        Returns:
            是否成功
        """
        self.logger.info("=" * 60)
        self.logger.info("基础训练演示")
        self.logger.info("=" * 60)
        
        try:
            # 1. 创建演示数据
            data_path = self.create_demo_data(n_samples=800, n_features=15)
            
            # 2. 加载数据
            df = pd.read_csv(data_path)
            X = df.drop('target', axis=1).values
            y = df['target'].values
            
            # 3. 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            self.logger.info(f"训练集大小: {X_train.shape}")
            self.logger.info(f"测试集大小: {X_test.shape}")
            
            # 4. 创建会话
            session = self.session_manager.create_session(
                "基础训练演示",
                "展示基本的模型训练和评估功能"
            )
            
            # 5. 训练多个模型
            models_to_train = ['RandomForest', 'LogisticRegression', 'SVM']
            
            for model_name in models_to_train:
                self.logger.info(f"\n训练模型: {model_name}")
                
                # 训练模型
                result = self.model_manager.train_model(
                    model_name, X_train, y_train, X_test, y_test
                )
                
                if result:
                    # 添加到会话
                    session.add_result(f"{model_name}_result", result, 'training')
                    
                    # 显示结果
                    self.logger.info(f"  准确率: {result.get('accuracy', 0):.4f}")
                    self.logger.info(f"  F1分数: {result.get('f1_score', 0):.4f}")
                    self.logger.info(f"  AUC: {result.get('auc', 0):.4f}")
                else:
                    self.logger.warning(f"  模型 {model_name} 训练失败")
            
            # 6. 保存会话
            self.session_manager.save_session(session)
            
            # 7. 显示会话摘要
            summary = session.get_summary()
            self.logger.info(f"\n会话摘要:")
            self.logger.info(f"  会话名称: {summary['session_name']}")
            self.logger.info(f"  模型数量: {summary.get('model_count', 0)}")
            self.logger.info(f"  结果数量: {summary.get('result_count', 0)}")
            
            self.logger.info("\n✅ 基础训练演示完成！")
            return True
            
        except Exception as e:
            self.logger.error(f"基础训练演示失败: {e}")
            return False
    
    @error_handler("运行模型比较演示")
    def run_model_comparison_demo(self) -> bool:
        """
        运行模型比较演示
        
        Returns:
            是否成功
        """
        self.logger.info("=" * 60)
        self.logger.info("模型比较演示")
        self.logger.info("=" * 60)
        
        try:
            # 1. 创建演示数据
            data_path = self.create_demo_data(n_samples=1200, n_features=25)
            
            # 2. 加载数据
            df = pd.read_csv(data_path)
            X = df.drop('target', axis=1).values
            y = df['target'].values
            
            # 3. 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.25, random_state=42, stratify=y
            )
            
            # 4. 创建会话
            session = self.session_manager.create_session(
                "模型比较演示",
                "比较不同模型在同一数据集上的性能"
            )
            
            # 5. 训练多个模型进行比较
            models_to_compare = [
                'RandomForest', 'XGBoost', 'LogisticRegression', 
                'SVM', 'KNN', 'NaiveBayes'
            ]
            
            results = {}
            
            for model_name in models_to_compare:
                self.logger.info(f"\n训练和评估模型: {model_name}")
                
                try:
                    # 训练模型
                    result = self.model_manager.train_model(
                        model_name, X_train, y_train, X_test, y_test
                    )
                    
                    if result:
                        results[model_name] = result
                        session.add_result(f"{model_name}_comparison", result, 'comparison')
                        
                        # 显示结果
                        self.logger.info(f"  准确率: {result.get('accuracy', 0):.4f}")
                        self.logger.info(f"  精确率: {result.get('precision', 0):.4f}")
                        self.logger.info(f"  召回率: {result.get('recall', 0):.4f}")
                        self.logger.info(f"  F1分数: {result.get('f1_score', 0):.4f}")
                        self.logger.info(f"  AUC: {result.get('auc', 0):.4f}")
                    else:
                        self.logger.warning(f"  模型 {model_name} 训练失败")
                        
                except Exception as e:
                    self.logger.warning(f"  模型 {model_name} 训练出错: {e}")
            
            # 6. 模型排名
            if results:
                self.logger.info("\n📊 模型性能排名 (按准确率):")
                sorted_results = sorted(results.items(), 
                                      key=lambda x: x[1].get('accuracy', 0), 
                                      reverse=True)
                
                for i, (model_name, result) in enumerate(sorted_results, 1):
                    accuracy = result.get('accuracy', 0)
                    auc = result.get('auc', 0)
                    self.logger.info(f"  {i}. {model_name}: 准确率={accuracy:.4f}, AUC={auc:.4f}")
                
                # 最佳模型
                best_model, best_result = sorted_results[0]
                self.logger.info(f"\n🏆 最佳模型: {best_model}")
                self.logger.info(f"   准确率: {best_result.get('accuracy', 0):.4f}")
                self.logger.info(f"   AUC: {best_result.get('auc', 0):.4f}")
            
            # 7. 保存会话
            self.session_manager.save_session(session)
            
            self.logger.info("\n✅ 模型比较演示完成！")
            return True
            
        except Exception as e:
            self.logger.error(f"模型比较演示失败: {e}")
            return False
    
    @error_handler("运行会话管理演示")
    def run_session_management_demo(self) -> bool:
        """
        运行会话管理演示
        
        Returns:
            是否成功
        """
        self.logger.info("=" * 60)
        self.logger.info("会话管理演示")
        self.logger.info("=" * 60)
        
        try:
            # 1. 创建多个演示会话
            sessions = []
            
            for i in range(3):
                session_name = f"演示会话_{i+1:02d}"
                session_desc = f"第{i+1}个演示会话，用于展示会话管理功能"
                
                session = self.session_manager.create_session(session_name, session_desc)
                
                # 添加一些模拟数据
                session.add_result(f'result_{i}', {
                    'accuracy': 0.8 + i * 0.05,
                    'f1_score': 0.75 + i * 0.04,
                    'model_name': f'Model_{i+1}'
                }, 'demo')
                
                session.add_config(f'config_{i}', {
                    'learning_rate': 0.01 * (i + 1),
                    'batch_size': 32 * (i + 1),
                    'epochs': 100 + i * 50
                })
                
                sessions.append(session)
                self.session_manager.save_session(session)
                
                self.logger.info(f"创建会话: {session_name}")
            
            # 2. 列出所有会话
            self.logger.info("\n📋 所有会话列表:")
            all_sessions = self.session_manager.list_sessions()
            
            for session_info in all_sessions:
                self.logger.info(f"  - {session_info['session_name']} "
                               f"({session_info['status']}) "
                               f"- {session_info['created_time']}")
            
            # 3. 加载和查看会话详情
            if sessions:
                demo_session = sessions[0]
                loaded_session = self.session_manager.load_session(demo_session.session_id)
                
                if loaded_session:
                    self.logger.info(f"\n🔍 会话详情: {loaded_session.session_name}")
                    summary = loaded_session.get_summary()
                    
                    for key, value in summary.items():
                        self.logger.info(f"  {key}: {value}")
            
            # 4. 会话操作演示
            if len(sessions) >= 2:
                # 激活会话
                session_to_activate = sessions[1]
                success = self.session_manager.activate_session(session_to_activate.session_id)
                if success:
                    self.logger.info(f"\n✅ 激活会话: {session_to_activate.session_name}")
                
                # 获取当前活动会话
                current_session = self.session_manager.get_current_session()
                if current_session:
                    self.logger.info(f"当前活动会话: {current_session.session_name}")
            
            self.logger.info("\n✅ 会话管理演示完成！")
            return True
            
        except Exception as e:
            self.logger.error(f"会话管理演示失败: {e}")
            return False
    
    @error_handler("运行完整演示")
    def run_complete_demo(self) -> bool:
        """
        运行完整演示
        
        Returns:
            是否成功
        """
        self.logger.info("🚀 开始完整功能演示")
        
        success_count = 0
        total_demos = 3
        
        # 运行各个演示
        demos = [
            ("基础训练演示", self.run_basic_training_demo),
            ("模型比较演示", self.run_model_comparison_demo),
            ("会话管理演示", self.run_session_management_demo)
        ]
        
        for demo_name, demo_func in demos:
            self.logger.info(f"\n{'='*20} {demo_name} {'='*20}")
            
            try:
                if demo_func():
                    success_count += 1
                    self.logger.info(f"✅ {demo_name} 成功")
                else:
                    self.logger.error(f"❌ {demo_name} 失败")
            except Exception as e:
                self.logger.error(f"❌ {demo_name} 异常: {e}")
        
        # 总结
        self.logger.info("\n" + "=" * 60)
        self.logger.info("演示总结")
        self.logger.info("=" * 60)
        self.logger.info(f"成功演示: {success_count}/{total_demos}")
        
        if success_count == total_demos:
            self.logger.info("🎉 所有演示都成功完成！")
            return True
        else:
            self.logger.warning(f"⚠️ 有 {total_demos - success_count} 个演示失败")
            return False
    
    def cleanup_demo_data(self):
        """清理演示数据"""
        try:
            import shutil
            if self.demo_data_dir.exists():
                shutil.rmtree(self.demo_data_dir)
                self.logger.info("演示数据已清理")
        except Exception as e:
            self.logger.warning(f"清理演示数据失败: {e}")


# 便捷函数
def run_basic_demo():
    """运行基础演示"""
    demo = BasicDemo()
    return demo.run_complete_demo()


if __name__ == "__main__":
    # 直接运行演示
    run_basic_demo()
