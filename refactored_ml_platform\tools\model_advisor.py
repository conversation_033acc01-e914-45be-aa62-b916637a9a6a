#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型选择顾问
为机器学习项目提供智能模型选择建议
"""

import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
from sklearn.model_selection import cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score
import warnings

try:
    from ..core.model_manager import get_model_manager
    from ..utils.error_handler import get_error_handler, error_handler
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))
    from core.model_manager import get_model_manager
    from utils.error_handler import get_error_handler, error_handler

warnings.filterwarnings('ignore')


class ModelSelectionAdvisor:
    """模型选择顾问类"""
    
    def __init__(self):
        """初始化模型选择顾问"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        self.model_manager = get_model_manager()
        
        # 模型特性描述
        self.model_characteristics = {
            'RandomForest': {
                'complexity': 'medium',
                'interpretability': 'medium',
                'overfitting_risk': 'low',
                'training_speed': 'medium',
                'prediction_speed': 'fast',
                'memory_usage': 'medium',
                'handles_missing': True,
                'handles_categorical': True,
                'suitable_for': ['tabular', 'mixed_types']
            },
            'XGBoost': {
                'complexity': 'high',
                'interpretability': 'low',
                'overfitting_risk': 'medium',
                'training_speed': 'medium',
                'prediction_speed': 'fast',
                'memory_usage': 'medium',
                'handles_missing': True,
                'handles_categorical': True,
                'suitable_for': ['tabular', 'structured']
            },
            'LightGBM': {
                'complexity': 'high',
                'interpretability': 'low',
                'overfitting_risk': 'medium',
                'training_speed': 'fast',
                'prediction_speed': 'fast',
                'memory_usage': 'low',
                'handles_missing': True,
                'handles_categorical': True,
                'suitable_for': ['tabular', 'large_datasets']
            },
            'CatBoost': {
                'complexity': 'high',
                'interpretability': 'low',
                'overfitting_risk': 'low',
                'training_speed': 'slow',
                'prediction_speed': 'fast',
                'memory_usage': 'medium',
                'handles_missing': True,
                'handles_categorical': True,
                'suitable_for': ['tabular', 'categorical_heavy']
            },
            'LogisticRegression': {
                'complexity': 'low',
                'interpretability': 'high',
                'overfitting_risk': 'low',
                'training_speed': 'fast',
                'prediction_speed': 'fast',
                'memory_usage': 'low',
                'handles_missing': False,
                'handles_categorical': False,
                'suitable_for': ['linear_separable', 'baseline']
            },
            'SVM': {
                'complexity': 'medium',
                'interpretability': 'low',
                'overfitting_risk': 'medium',
                'training_speed': 'slow',
                'prediction_speed': 'medium',
                'memory_usage': 'high',
                'handles_missing': False,
                'handles_categorical': False,
                'suitable_for': ['small_datasets', 'high_dimensional']
            },
            'KNN': {
                'complexity': 'low',
                'interpretability': 'medium',
                'overfitting_risk': 'medium',
                'training_speed': 'fast',
                'prediction_speed': 'slow',
                'memory_usage': 'high',
                'handles_missing': False,
                'handles_categorical': False,
                'suitable_for': ['small_datasets', 'local_patterns']
            },
            'NaiveBayes': {
                'complexity': 'low',
                'interpretability': 'high',
                'overfitting_risk': 'low',
                'training_speed': 'fast',
                'prediction_speed': 'fast',
                'memory_usage': 'low',
                'handles_missing': False,
                'handles_categorical': True,
                'suitable_for': ['text_classification', 'baseline']
            },
            'DecisionTree': {
                'complexity': 'low',
                'interpretability': 'high',
                'overfitting_risk': 'high',
                'training_speed': 'fast',
                'prediction_speed': 'fast',
                'memory_usage': 'low',
                'handles_missing': True,
                'handles_categorical': True,
                'suitable_for': ['interpretable_models', 'rule_extraction']
            },
            'NeuralNet': {
                'complexity': 'high',
                'interpretability': 'low',
                'overfitting_risk': 'high',
                'training_speed': 'slow',
                'prediction_speed': 'fast',
                'memory_usage': 'medium',
                'handles_missing': False,
                'handles_categorical': False,
                'suitable_for': ['complex_patterns', 'large_datasets']
            }
        }
    
    @error_handler("分析数据特征")
    def analyze_data_characteristics(self, data_path: str) -> Dict[str, Any]:
        """
        分析数据特征
        
        Args:
            data_path: 数据文件路径
            
        Returns:
            数据特征分析结果
        """
        self.logger.info(f"分析数据特征: {data_path}")
        
        try:
            # 加载数据
            if data_path.endswith('.csv'):
                df = pd.read_csv(data_path)
            elif data_path.endswith('.xlsx'):
                df = pd.read_excel(data_path)
            else:
                raise ValueError("不支持的文件格式")
            
            # 假设最后一列是标签
            X = df.iloc[:, :-1]
            y = df.iloc[:, -1]
            
            # 基本统计信息
            n_samples, n_features = X.shape
            n_classes = len(y.unique())
            
            # 数据类型分析
            numeric_features = X.select_dtypes(include=[np.number]).columns.tolist()
            categorical_features = X.select_dtypes(include=['object', 'category']).columns.tolist()
            
            # 缺失值分析
            missing_values = X.isnull().sum().sum()
            missing_ratio = missing_values / (n_samples * n_features)
            
            # 类别平衡分析
            class_counts = y.value_counts()
            class_balance_ratio = class_counts.min() / class_counts.max()
            
            # 特征相关性分析
            if len(numeric_features) > 1:
                correlation_matrix = X[numeric_features].corr()
                high_correlation_pairs = []
                for i in range(len(correlation_matrix.columns)):
                    for j in range(i+1, len(correlation_matrix.columns)):
                        corr_value = abs(correlation_matrix.iloc[i, j])
                        if corr_value > 0.8:
                            high_correlation_pairs.append((
                                correlation_matrix.columns[i],
                                correlation_matrix.columns[j],
                                corr_value
                            ))
            else:
                high_correlation_pairs = []
            
            # 数据复杂度评估
            complexity_score = self._calculate_data_complexity(X, y)
            
            analysis_result = {
                'basic_info': {
                    'n_samples': n_samples,
                    'n_features': n_features,
                    'n_classes': n_classes,
                    'problem_type': 'binary' if n_classes == 2 else 'multiclass'
                },
                'feature_info': {
                    'numeric_features': len(numeric_features),
                    'categorical_features': len(categorical_features),
                    'numeric_ratio': len(numeric_features) / n_features,
                    'categorical_ratio': len(categorical_features) / n_features
                },
                'data_quality': {
                    'missing_values': missing_values,
                    'missing_ratio': missing_ratio,
                    'has_missing': missing_values > 0
                },
                'class_distribution': {
                    'class_counts': class_counts.to_dict(),
                    'balance_ratio': class_balance_ratio,
                    'is_balanced': class_balance_ratio > 0.5
                },
                'feature_correlation': {
                    'high_correlation_pairs': high_correlation_pairs,
                    'has_multicollinearity': len(high_correlation_pairs) > 0
                },
                'complexity': {
                    'complexity_score': complexity_score,
                    'complexity_level': self._get_complexity_level(complexity_score)
                }
            }
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"数据分析失败: {e}")
            raise
    
    def _calculate_data_complexity(self, X: pd.DataFrame, y: pd.Series) -> float:
        """计算数据复杂度"""
        try:
            # 数值化处理
            X_numeric = pd.get_dummies(X, drop_first=True)
            
            # 标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X_numeric)
            
            # PCA分析
            pca = PCA()
            pca.fit(X_scaled)
            
            # 计算有效维度（95%方差）
            cumsum_variance = np.cumsum(pca.explained_variance_ratio_)
            effective_dims = np.argmax(cumsum_variance >= 0.95) + 1
            
            # 维度复杂度
            dim_complexity = min(effective_dims / len(X_numeric.columns), 1.0)
            
            # 样本稀疏度
            sparsity = min(len(X_numeric.columns) / len(X), 1.0)
            
            # 类别不平衡度
            class_counts = y.value_counts()
            imbalance = 1 - (class_counts.min() / class_counts.max())
            
            # 综合复杂度
            complexity = (dim_complexity * 0.4 + sparsity * 0.3 + imbalance * 0.3)
            
            return complexity
            
        except:
            return 0.5  # 默认中等复杂度
    
    def _get_complexity_level(self, complexity_score: float) -> str:
        """获取复杂度等级"""
        if complexity_score < 0.3:
            return 'low'
        elif complexity_score < 0.7:
            return 'medium'
        else:
            return 'high'
    
    @error_handler("推荐模型")
    def recommend_models(self, data_path: str, requirements: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        推荐适合的模型
        
        Args:
            data_path: 数据文件路径
            requirements: 用户需求
            
        Returns:
            模型推荐结果
        """
        # 分析数据特征
        data_analysis = self.analyze_data_characteristics(data_path)
        
        # 默认需求
        if requirements is None:
            requirements = {
                'priority': 'balanced',  # 'performance', 'speed', 'interpretability', 'balanced'
                'max_models': 5,
                'ensemble': True
            }
        
        # 基于数据特征和需求推荐模型
        recommendations = self._generate_model_recommendations(data_analysis, requirements)
        
        return {
            'data_analysis': data_analysis,
            'requirements': requirements,
            'recommendations': recommendations
        }
    
    def _generate_model_recommendations(self, data_analysis: Dict[str, Any], 
                                      requirements: Dict[str, Any]) -> Dict[str, Any]:
        """生成模型推荐"""
        basic_info = data_analysis['basic_info']
        feature_info = data_analysis['feature_info']
        data_quality = data_analysis['data_quality']
        complexity = data_analysis['complexity']
        
        # 计算每个模型的适合度分数
        model_scores = {}
        
        for model_name, characteristics in self.model_characteristics.items():
            score = 0
            reasons = []
            
            # 基于数据大小
            if basic_info['n_samples'] < 1000:
                if characteristics['training_speed'] == 'fast':
                    score += 0.2
                    reasons.append("适合小数据集")
            elif basic_info['n_samples'] > 10000:
                if model_name in ['LightGBM', 'XGBoost']:
                    score += 0.2
                    reasons.append("适合大数据集")
            
            # 基于特征类型
            if feature_info['categorical_ratio'] > 0.3:
                if characteristics['handles_categorical']:
                    score += 0.2
                    reasons.append("能处理类别特征")
            
            # 基于缺失值
            if data_quality['has_missing']:
                if characteristics['handles_missing']:
                    score += 0.15
                    reasons.append("能处理缺失值")
                else:
                    score -= 0.1
                    reasons.append("不能处理缺失值")
            
            # 基于复杂度
            complexity_level = complexity['complexity_level']
            if complexity_level == 'low' and characteristics['complexity'] == 'low':
                score += 0.15
                reasons.append("复杂度匹配")
            elif complexity_level == 'high' and characteristics['complexity'] == 'high':
                score += 0.15
                reasons.append("能处理复杂数据")
            
            # 基于用户需求
            priority = requirements.get('priority', 'balanced')
            if priority == 'performance':
                if model_name in ['XGBoost', 'LightGBM', 'CatBoost', 'RandomForest']:
                    score += 0.3
                    reasons.append("高性能模型")
            elif priority == 'speed':
                if characteristics['training_speed'] == 'fast':
                    score += 0.2
                    reasons.append("训练速度快")
                if characteristics['prediction_speed'] == 'fast':
                    score += 0.1
                    reasons.append("预测速度快")
            elif priority == 'interpretability':
                if characteristics['interpretability'] == 'high':
                    score += 0.3
                    reasons.append("可解释性强")
            
            model_scores[model_name] = {
                'score': score,
                'reasons': reasons,
                'characteristics': characteristics
            }
        
        # 排序并选择top模型
        sorted_models = sorted(model_scores.items(), key=lambda x: x[1]['score'], reverse=True)
        max_models = requirements.get('max_models', 5)
        top_models = sorted_models[:max_models]
        
        # 生成推荐结果
        recommended_models = []
        for model_name, model_info in top_models:
            recommended_models.append({
                'name': model_name,
                'score': model_info['score'],
                'reasons': model_info['reasons'],
                'characteristics': model_info['characteristics']
            })
        
        # 集成学习建议
        ensemble_recommendation = None
        if requirements.get('ensemble', False) and len(recommended_models) >= 2:
            ensemble_recommendation = self._recommend_ensemble_strategy(recommended_models, data_analysis)
        
        return {
            'recommended_models': recommended_models,
            'ensemble_recommendation': ensemble_recommendation,
            'summary': self._generate_recommendation_summary(recommended_models, data_analysis)
        }
    
    def _recommend_ensemble_strategy(self, models: List[Dict], data_analysis: Dict) -> Dict[str, Any]:
        """推荐集成策略"""
        n_samples = data_analysis['basic_info']['n_samples']
        complexity_level = data_analysis['complexity']['complexity_level']
        
        strategies = []
        
        # Voting
        strategies.append({
            'method': 'voting',
            'description': '投票集成，简单有效',
            'suitable_for': '所有情况',
            'priority': 0.8
        })
        
        # Stacking
        if n_samples > 1000:
            strategies.append({
                'method': 'stacking',
                'description': '堆叠集成，性能更好但复杂度高',
                'suitable_for': '大数据集',
                'priority': 0.9 if complexity_level == 'high' else 0.7
            })
        
        # Bagging
        strategies.append({
            'method': 'bagging',
            'description': '装袋集成，减少过拟合',
            'suitable_for': '高方差模型',
            'priority': 0.6
        })
        
        # 排序策略
        strategies.sort(key=lambda x: x['priority'], reverse=True)
        
        return {
            'recommended_strategy': strategies[0]['method'],
            'all_strategies': strategies,
            'meta_learner_suggestion': 'LogisticRegression' if strategies[0]['method'] == 'stacking' else None
        }
    
    def _generate_recommendation_summary(self, models: List[Dict], data_analysis: Dict) -> str:
        """生成推荐摘要"""
        basic_info = data_analysis['basic_info']
        complexity = data_analysis['complexity']
        
        summary_parts = []
        
        # 数据集描述
        summary_parts.append(f"数据集包含 {basic_info['n_samples']} 个样本，{basic_info['n_features']} 个特征")
        summary_parts.append(f"问题类型：{basic_info['problem_type']}分类")
        summary_parts.append(f"数据复杂度：{complexity['complexity_level']}")
        
        # 推荐模型
        if models:
            top_model = models[0]
            summary_parts.append(f"最推荐的模型是 {top_model['name']}（评分：{top_model['score']:.2f}）")
            
            if len(models) > 1:
                other_models = [m['name'] for m in models[1:3]]
                summary_parts.append(f"其他推荐模型：{', '.join(other_models)}")
        
        return "。".join(summary_parts) + "。"


# 全局模型选择顾问实例
_model_advisor = None

def get_model_advisor() -> ModelSelectionAdvisor:
    """
    获取全局模型选择顾问实例
    
    Returns:
        模型选择顾问实例
    """
    global _model_advisor
    if _model_advisor is None:
        _model_advisor = ModelSelectionAdvisor()
    return _model_advisor
