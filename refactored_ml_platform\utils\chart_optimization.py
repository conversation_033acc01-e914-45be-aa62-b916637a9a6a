#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表渲染优化配置模块
解决图表预览中的渲染混乱和性能问题
"""

import matplotlib
import matplotlib.pyplot as plt
import numpy as np
import warnings
from cycler import cycler
import logging
from typing import Dict, Any, Optional
import seaborn as sns

# 忽略matplotlib警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')


class ChartOptimizer:
    """图表渲染优化器"""
    
    def __init__(self):
        self.is_optimized = False
        self.original_settings = {}
        self.logger = logging.getLogger(__name__)
        
    def optimize_matplotlib(self):
        """优化matplotlib设置"""
        if self.is_optimized:
            return
            
        try:
            # 保存原始设置
            self.original_settings = {
                'backend': matplotlib.get_backend(),
                'interactive': matplotlib.is_interactive(),
                'rcParams': dict(plt.rcParams)
            }
            
            # 设置后端
            matplotlib.use('TkAgg', force=True)
            
            # 关闭交互模式以提高性能
            plt.ioff()
            
            # 优化渲染设置
            plt.rcParams.update({
                # 图形设置
                'figure.facecolor': 'white',
                'figure.edgecolor': 'none',
                'axes.facecolor': 'white',
                'axes.edgecolor': 'black',
                'axes.linewidth': 0.8,
                'axes.grid': True,
                'axes.grid.alpha': 0.3,
                'grid.linewidth': 0.5,
                'grid.alpha': 0.3,
                
                # 字体设置
                'font.family': ['Microsoft YaHei', 'SimHei', 'DejaVu Sans'],
                'font.size': 10,
                'axes.titlesize': 12,
                'axes.labelsize': 10,
                'xtick.labelsize': 9,
                'ytick.labelsize': 9,
                'legend.fontsize': 9,
                
                # 颜色设置
                'axes.prop_cycle': cycler('color', [
                    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728',
                    '#9467bd', '#8c564b', '#e377c2', '#7f7f7f',
                    '#bcbd22', '#17becf'
                ]),
                
                # 性能优化
                'path.simplify': True,
                'path.simplify_threshold': 0.1,
                'agg.path.chunksize': 10000,
                
                # 输出质量
                'figure.dpi': 100,
                'savefig.dpi': 150,
                'savefig.bbox': 'tight',
                'savefig.pad_inches': 0.1,
                
                # 内存优化
                'figure.max_open_warning': 20,
                
                # 中文支持
                'axes.unicode_minus': False,
            })
            
            # 设置seaborn样式
            try:
                sns.set_style("whitegrid")
                sns.set_palette("husl")
            except ImportError:
                pass
            
            self.is_optimized = True
            self.logger.info("matplotlib渲染优化已启用")
            
        except Exception as e:
            self.logger.error(f"matplotlib优化失败: {e}")
    
    def optimize_for_gui(self):
        """针对GUI环境的特殊优化"""
        if not self.is_optimized:
            self.optimize_matplotlib()
        
        try:
            # GUI特定设置
            plt.rcParams.update({
                'figure.figsize': (8, 6),
                'figure.autolayout': True,
                'toolbar': 'None',  # 禁用工具栏
                'keymap.fullscreen': [],  # 禁用全屏快捷键
                'keymap.home': [],
                'keymap.back': [],
                'keymap.forward': [],
                'keymap.pan': [],
                'keymap.zoom': [],
                'keymap.save': [],
                'keymap.quit': [],
                'keymap.grid': [],
                'keymap.yscale': [],
                'keymap.xscale': [],
            })
            
            self.logger.info("GUI渲染优化已启用")
            
        except Exception as e:
            self.logger.error(f"GUI优化失败: {e}")
    
    def optimize_for_shap(self):
        """针对SHAP图表的优化"""
        if not self.is_optimized:
            self.optimize_matplotlib()
        
        try:
            # SHAP特定设置
            plt.rcParams.update({
                'figure.figsize': (10, 6),
                'axes.titlesize': 14,
                'axes.labelsize': 11,
                'xtick.labelsize': 10,
                'ytick.labelsize': 10,
                'legend.fontsize': 10,
                'figure.constrained_layout.use': True,
            })
            
            self.logger.info("SHAP渲染优化已启用")
            
        except Exception as e:
            self.logger.error(f"SHAP优化失败: {e}")
    
    def create_optimized_figure(self, figsize=(8, 6), **kwargs) -> tuple:
        """创建优化的图形和轴"""
        try:
            # 确保优化已启用
            if not self.is_optimized:
                self.optimize_matplotlib()
            
            # 创建图形
            fig, ax = plt.subplots(figsize=figsize, **kwargs)
            
            # 应用优化设置
            fig.patch.set_facecolor('white')
            ax.set_facecolor('white')
            
            return fig, ax
            
        except Exception as e:
            self.logger.error(f"创建优化图形失败: {e}")
            return plt.subplots(figsize=figsize, **kwargs)
    
    def apply_chinese_font(self):
        """应用中文字体支持"""
        try:
            # 尝试设置中文字体
            chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
            
            for font in chinese_fonts:
                try:
                    plt.rcParams['font.sans-serif'] = [font] + plt.rcParams['font.sans-serif']
                    plt.rcParams['axes.unicode_minus'] = False
                    
                    # 测试字体是否可用
                    fig, ax = plt.subplots(figsize=(1, 1))
                    ax.text(0.5, 0.5, '测试中文', fontsize=12)
                    plt.close(fig)
                    
                    self.logger.info(f"中文字体设置成功: {font}")
                    break
                    
                except Exception:
                    continue
            else:
                self.logger.warning("未找到可用的中文字体")
                
        except Exception as e:
            self.logger.error(f"中文字体设置失败: {e}")
    
    def clear_figure_cache(self):
        """清理图形缓存"""
        try:
            plt.close('all')
            # 强制垃圾回收
            import gc
            gc.collect()
            self.logger.info("图形缓存已清理")
        except Exception as e:
            self.logger.error(f"清理图形缓存失败: {e}")
    
    def restore_settings(self):
        """恢复原始设置"""
        if not self.is_optimized or not self.original_settings:
            return
        
        try:
            # 恢复rcParams
            plt.rcParams.update(self.original_settings['rcParams'])
            
            # 恢复交互模式
            if self.original_settings['interactive']:
                plt.ion()
            else:
                plt.ioff()
            
            # 恢复后端
            matplotlib.use(self.original_settings['backend'])
            
            self.is_optimized = False
            self.logger.info("matplotlib设置已恢复")
            
        except Exception as e:
            self.logger.error(f"恢复设置失败: {e}")
    
    def get_optimization_status(self) -> Dict[str, Any]:
        """获取优化状态信息"""
        return {
            'is_optimized': self.is_optimized,
            'backend': matplotlib.get_backend(),
            'interactive': matplotlib.is_interactive(),
            'figure_count': len(plt.get_fignums()),
            'dpi': plt.rcParams['figure.dpi'],
            'font_family': plt.rcParams['font.family']
        }


# 全局优化器实例
_global_optimizer: Optional[ChartOptimizer] = None


def get_chart_optimizer() -> ChartOptimizer:
    """获取全局图表优化器实例"""
    global _global_optimizer
    if _global_optimizer is None:
        _global_optimizer = ChartOptimizer()
    return _global_optimizer


def optimize_charts_for_gui():
    """为GUI环境优化图表渲染"""
    optimizer = get_chart_optimizer()
    optimizer.optimize_for_gui()


def optimize_charts_for_shap():
    """为SHAP图表优化渲染"""
    optimizer = get_chart_optimizer()
    optimizer.optimize_for_shap()


def create_optimized_figure(figsize=(8, 6), **kwargs):
    """创建优化的图形"""
    optimizer = get_chart_optimizer()
    return optimizer.create_optimized_figure(figsize, **kwargs)


def clear_chart_cache():
    """清理图表缓存"""
    optimizer = get_chart_optimizer()
    optimizer.clear_figure_cache()
