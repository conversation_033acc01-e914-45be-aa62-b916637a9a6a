#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据探索工具
提供数据探索和统计分析功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path

from .error_handler import get_error_handler, error_handler


class DataExplorer:
    """数据探索器"""
    
    def __init__(self):
        """初始化数据探索器"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        
        # 设置matplotlib字体（避免中文字体问题）
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置seaborn样式
        sns.set_style("whitegrid")
        sns.set_palette("husl")
    
    @error_handler("数据基本信息分析")
    def analyze_basic_info(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析数据基本信息
        
        Args:
            df: 数据框
            
        Returns:
            基本信息字典
        """
        info = {
            'shape': df.shape,
            'columns': list(df.columns),
            'dtypes': df.dtypes.to_dict(),
            'memory_usage': df.memory_usage(deep=True).sum() / 1024**2,  # MB
            'missing_values': df.isnull().sum().to_dict(),
            'missing_percentage': (df.isnull().sum() / len(df) * 100).to_dict(),
            'duplicates': df.duplicated().sum(),
            'numeric_columns': df.select_dtypes(include=[np.number]).columns.tolist(),
            'categorical_columns': df.select_dtypes(include=['object', 'category']).columns.tolist()
        }
        
        self.logger.info(f"数据基本信息分析完成，形状: {info['shape']}")
        return info
    
    @error_handler("描述性统计分析")
    def analyze_descriptive_stats(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        分析描述性统计
        
        Args:
            df: 数据框
            
        Returns:
            描述性统计结果
        """
        results = {}
        
        # 数值变量描述性统计
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            results['numeric'] = df[numeric_cols].describe()
            
            # 添加偏度和峰度
            skewness = df[numeric_cols].skew()
            kurtosis = df[numeric_cols].kurtosis()
            
            results['numeric'].loc['skewness'] = skewness
            results['numeric'].loc['kurtosis'] = kurtosis
        
        # 分类变量描述性统计
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        if len(categorical_cols) > 0:
            cat_stats = []
            for col in categorical_cols:
                stats_dict = {
                    'column': col,
                    'unique_count': df[col].nunique(),
                    'most_frequent': df[col].mode().iloc[0] if not df[col].mode().empty else None,
                    'most_frequent_count': df[col].value_counts().iloc[0] if len(df[col].value_counts()) > 0 else 0,
                    'missing_count': df[col].isnull().sum()
                }
                cat_stats.append(stats_dict)
            
            results['categorical'] = pd.DataFrame(cat_stats)
        
        self.logger.info("描述性统计分析完成")
        return results
    
    @error_handler("相关性分析")
    def analyze_correlation(self, df: pd.DataFrame, method: str = 'pearson') -> pd.DataFrame:
        """
        分析变量间相关性
        
        Args:
            df: 数据框
            method: 相关性方法 ('pearson', 'spearman', 'kendall')
            
        Returns:
            相关性矩阵
        """
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) < 2:
            self.logger.warning("数值变量少于2个，无法进行相关性分析")
            return pd.DataFrame()
        
        correlation_matrix = df[numeric_cols].corr(method=method)
        
        self.logger.info(f"相关性分析完成，方法: {method}")
        return correlation_matrix
    
    @error_handler("分组概率分析")
    def analyze_group_probabilities(self, df: pd.DataFrame, target_col: str, 
                                  group_cols: List[str], bins: int = 5) -> Dict[str, pd.DataFrame]:
        """
        分析分组概率
        
        Args:
            df: 数据框
            target_col: 目标变量列名
            group_cols: 分组变量列名列表
            bins: 连续变量分箱数量
            
        Returns:
            分组概率分析结果
        """
        results = {}
        
        for col in group_cols:
            if col not in df.columns:
                continue
            
            try:
                # 处理连续变量
                if df[col].dtype in ['int64', 'float64']:
                    # 分箱处理
                    df_temp = df.copy()
                    df_temp[f'{col}_binned'] = pd.cut(df_temp[col], bins=bins, duplicates='drop')
                    group_col = f'{col}_binned'
                else:
                    df_temp = df.copy()
                    group_col = col
                
                # 计算分组概率
                crosstab = pd.crosstab(df_temp[group_col], df_temp[target_col], normalize='index')
                
                # 添加计数信息
                counts = pd.crosstab(df_temp[group_col], df_temp[target_col])
                
                # 合并概率和计数
                result_df = pd.DataFrame()
                for target_val in crosstab.columns:
                    result_df[f'prob_{target_val}'] = crosstab[target_val]
                    result_df[f'count_{target_val}'] = counts[target_val]
                
                result_df['total_count'] = counts.sum(axis=1)
                
                results[col] = result_df
                
            except Exception as e:
                self.logger.warning(f"分析变量 {col} 的分组概率时出错: {e}")
                continue
        
        self.logger.info(f"分组概率分析完成，分析了 {len(results)} 个变量")
        return results
    
    @error_handler("卡方检验")
    def perform_chi_square_test(self, df: pd.DataFrame, col1: str, col2: str) -> Dict[str, Any]:
        """
        执行卡方检验
        
        Args:
            df: 数据框
            col1: 第一个变量
            col2: 第二个变量
            
        Returns:
            卡方检验结果
        """
        # 创建交叉表
        crosstab = pd.crosstab(df[col1], df[col2])
        
        # 执行卡方检验
        chi2, p_value, dof, expected = chi2_contingency(crosstab)
        
        # 计算Cramer's V
        n = crosstab.sum().sum()
        cramers_v = np.sqrt(chi2 / (n * (min(crosstab.shape) - 1)))
        
        result = {
            'chi2_statistic': chi2,
            'p_value': p_value,
            'degrees_of_freedom': dof,
            'cramers_v': cramers_v,
            'crosstab': crosstab,
            'expected_frequencies': pd.DataFrame(expected, 
                                               index=crosstab.index, 
                                               columns=crosstab.columns)
        }
        
        self.logger.info(f"卡方检验完成: {col1} vs {col2}, p-value: {p_value:.4f}")
        return result
    
    @error_handler("创建相关性热力图")
    def create_correlation_heatmap(self, correlation_matrix: pd.DataFrame, 
                                 save_path: Optional[str] = None) -> plt.Figure:
        """
        创建相关性热力图
        
        Args:
            correlation_matrix: 相关性矩阵
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 创建热力图
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', 
                   center=0, square=True, linewidths=0.5, ax=ax)
        
        plt.title('Feature Correlation Matrix', fontsize=16, pad=20)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"相关性热力图已保存到: {save_path}")
        
        return fig
    
    @error_handler("创建分布图")
    def create_distribution_plot(self, df: pd.DataFrame, column: str, 
                               save_path: Optional[str] = None) -> plt.Figure:
        """
        创建变量分布图
        
        Args:
            df: 数据框
            column: 列名
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 直方图
        axes[0, 0].hist(df[column].dropna(), bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title(f'{column} - 直方图')
        axes[0, 0].set_xlabel(column)
        axes[0, 0].set_ylabel('频数')
        
        # 箱线图
        axes[0, 1].boxplot(df[column].dropna())
        axes[0, 1].set_title(f'{column} - 箱线图')
        axes[0, 1].set_ylabel(column)
        
        # Q-Q图
        stats.probplot(df[column].dropna(), dist="norm", plot=axes[1, 0])
        axes[1, 0].set_title(f'{column} - Q-Q图')
        
        # 密度图
        df[column].dropna().plot.density(ax=axes[1, 1], color='orange')
        axes[1, 1].set_title(f'{column} - 密度图')
        axes[1, 1].set_xlabel(column)
        axes[1, 1].set_ylabel('密度')
        
        plt.suptitle(f'{column} 分布分析', fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"分布图已保存到: {save_path}")
        
        return fig
    
    @error_handler("创建分组概率图")
    def create_group_probability_plot(self, group_prob_result: pd.DataFrame, 
                                    variable_name: str, target_name: str,
                                    save_path: Optional[str] = None) -> plt.Figure:
        """
        创建分组概率图
        
        Args:
            group_prob_result: 分组概率结果
            variable_name: 变量名
            target_name: 目标变量名
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 概率图
        prob_cols = [col for col in group_prob_result.columns if col.startswith('prob_')]
        group_prob_result[prob_cols].plot(kind='bar', ax=ax1, width=0.8)
        ax1.set_title(f'{variable_name} 各组的 {target_name} 概率分布')
        ax1.set_xlabel(variable_name)
        ax1.set_ylabel('概率')
        ax1.legend(title=target_name)
        ax1.tick_params(axis='x', rotation=45)
        
        # 计数图
        count_cols = [col for col in group_prob_result.columns if col.startswith('count_')]
        group_prob_result[count_cols].plot(kind='bar', ax=ax2, width=0.8)
        ax2.set_title(f'{variable_name} 各组的 {target_name} 计数分布')
        ax2.set_xlabel(variable_name)
        ax2.set_ylabel('计数')
        ax2.legend(title=target_name)
        ax2.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"分组概率图已保存到: {save_path}")
        
        return fig

    @error_handler("分组概率分析（高级）")
    def create_binned_probability_analysis(self, df: pd.DataFrame,
                                         continuous_vars: List[str],
                                         target_var: str,
                                         n_bins: int = 5,
                                         binning_method: str = 'qcut',
                                         min_bin_size: int = 20) -> Dict[str, Any]:
        """
        创建分组概率分析（复刻原项目功能）

        Args:
            df: 数据框
            continuous_vars: 连续变量列表
            target_var: 目标变量名
            n_bins: 分箱数量
            binning_method: 分箱方法 ('qcut', 'cut', 'tree')
            min_bin_size: 最小分箱样本数

        Returns:
            分析结果字典
        """
        results = {}

        for var in continuous_vars:
            if var not in df.columns or var == target_var:
                continue

            try:
                var_result = self._analyze_single_variable(
                    df, var, target_var, n_bins, binning_method, min_bin_size
                )

                if var_result is not None:
                    results[var] = var_result

            except Exception as e:
                self.logger.warning(f"分析变量 {var} 时出错: {e}")
                continue

        self.logger.info(f"分组概率分析完成，成功分析 {len(results)} 个变量")
        return results

    def _analyze_single_variable(self, df: pd.DataFrame, var: str, target_var: str,
                               n_bins: int, binning_method: str, min_bin_size: int) -> Optional[Dict[str, Any]]:
        """分析单个变量的分组概率"""
        try:
            # 移除缺失值
            clean_df = df[[var, target_var]].dropna()

            if len(clean_df) < min_bin_size:
                self.logger.warning(f"变量 {var} 有效样本数不足 {min_bin_size}")
                return None

            # 执行分箱
            if binning_method == 'qcut':
                try:
                    bins = pd.qcut(clean_df[var], q=n_bins, duplicates='drop')
                except ValueError:
                    # 如果qcut失败，回退到cut
                    bins = pd.cut(clean_df[var], bins=n_bins, duplicates='drop')
            elif binning_method == 'cut':
                bins = pd.cut(clean_df[var], bins=n_bins, duplicates='drop')
            elif binning_method == 'tree':
                # 使用决策树进行分箱
                from sklearn.tree import DecisionTreeClassifier
                dt = DecisionTreeClassifier(max_leaf_nodes=n_bins, random_state=42)
                dt.fit(clean_df[[var]], clean_df[target_var])
                thresholds = sorted(dt.tree_.threshold[dt.tree_.threshold != -2])
                if len(thresholds) > 0:
                    bins = pd.cut(clean_df[var], bins=[-np.inf] + thresholds + [np.inf])
                else:
                    bins = pd.cut(clean_df[var], bins=n_bins)
            else:
                bins = pd.qcut(clean_df[var], q=n_bins, duplicates='drop')

            # 计算每组的统计信息
            grouped = clean_df.groupby(bins, observed=False)

            results = []
            for group_name, group_data in grouped:
                if len(group_data) < 5:  # 跳过样本数太少的组
                    continue

                # 计算概率和置信区间
                target_counts = group_data[target_var].value_counts()
                total_count = len(group_data)

                # 计算正类概率
                positive_count = target_counts.get(1, 0)
                probability = positive_count / total_count

                # 计算95%置信区间
                if total_count > 0:
                    se = np.sqrt(probability * (1 - probability) / total_count)
                    ci_lower = max(0, probability - 1.96 * se)
                    ci_upper = min(1, probability + 1.96 * se)
                else:
                    ci_lower = ci_upper = probability

                # 获取区间信息
                if hasattr(group_name, 'left') and hasattr(group_name, 'right'):
                    interval_left = group_name.left
                    interval_right = group_name.right
                    interval_str = f"({interval_left:.2f}, {interval_right:.2f}]"
                else:
                    interval_str = str(group_name)
                    interval_left = interval_right = None

                results.append({
                    'group': group_name,
                    'interval_str': interval_str,
                    'interval_left': interval_left,
                    'interval_right': interval_right,
                    'count': total_count,
                    'positive_count': positive_count,
                    'negative_count': total_count - positive_count,
                    'probability': probability,
                    'ci_lower': ci_lower,
                    'ci_upper': ci_upper,
                    'mean_value': group_data[var].mean(),
                    'std_value': group_data[var].std()
                })

            if not results:
                return None

            return {
                'variable': var,
                'target': target_var,
                'binning_method': binning_method,
                'n_bins': len(results),
                'total_samples': len(clean_df),
                'results': results
            }

        except Exception as e:
            self.logger.error(f"分析变量 {var} 时出错: {e}")
            return None

    @error_handler("创建分组概率柱状图")
    def create_probability_bar_chart(self, analysis_result: Dict[str, Any],
                                   save_path: Optional[str] = None) -> plt.Figure:
        """创建分组概率柱状图"""
        if not analysis_result or 'results' not in analysis_result:
            return None

        results = analysis_result['results']
        var_name = analysis_result['variable']
        target_name = analysis_result['target']

        fig, ax = plt.subplots(figsize=(12, 8))

        # 准备数据
        groups = [r['interval_str'] for r in results]
        probabilities = [r['probability'] for r in results]
        ci_lower = [r['ci_lower'] for r in results]
        ci_upper = [r['ci_upper'] for r in results]
        counts = [r['count'] for r in results]

        # 创建柱状图
        bars = ax.bar(range(len(groups)), probabilities,
                     color='#2E86AB', alpha=0.7, edgecolor='black', linewidth=1)

        # 添加误差线（置信区间）
        yerr_lower = [max(0, p - ci_l) for p, ci_l in zip(probabilities, ci_lower)]
        yerr_upper = [max(0, ci_u - p) for p, ci_u in zip(ci_upper, probabilities)]
        ax.errorbar(range(len(groups)), probabilities,
                   yerr=[yerr_lower, yerr_upper],
                   fmt='none', color='black', capsize=5, capthick=2)

        # 在柱子上添加数值标签
        for i, (bar, prob, count) in enumerate(zip(bars, probabilities, counts)):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{prob:.3f}\n(n={count})',
                   ha='center', va='bottom', fontsize=10, fontweight='bold')

        # 设置标签和标题（使用英文避免字体问题）
        ax.set_xlabel(f'{var_name} (Binned)', fontsize=12, fontweight='bold')
        ax.set_ylabel(f'Probability of {target_name}=1', fontsize=12, fontweight='bold')
        ax.set_title(f'Probability Analysis: {var_name} vs {target_name}',
                    fontsize=14, fontweight='bold', pad=20)

        # 设置x轴标签
        ax.set_xticks(range(len(groups)))
        ax.set_xticklabels(groups, rotation=45, ha='right')

        # 设置y轴范围
        ax.set_ylim(0, 1.1)

        # 添加网格
        ax.grid(True, alpha=0.3, axis='y')

        # 美化图表
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"概率柱状图已保存到: {save_path}")

        return fig

    @error_handler("创建分组概率折线图")
    def create_probability_line_chart(self, analysis_result: Dict[str, Any],
                                    save_path: Optional[str] = None) -> plt.Figure:
        """创建分组概率折线图"""
        if not analysis_result or 'results' not in analysis_result:
            return None

        results = analysis_result['results']
        var_name = analysis_result['variable']
        target_name = analysis_result['target']

        fig, ax = plt.subplots(figsize=(12, 8))

        # 准备数据
        x_values = [r['mean_value'] for r in results]
        probabilities = [r['probability'] for r in results]
        ci_lower = [r['ci_lower'] for r in results]
        ci_upper = [r['ci_upper'] for r in results]

        # 创建折线图
        line = ax.plot(x_values, probabilities, 'o-',
                      color='#2E86AB', linewidth=2, markersize=8,
                      markerfacecolor='white', markeredgecolor='#2E86AB',
                      markeredgewidth=2, label=f'{target_name}=1 Probability')

        # 添加置信区间
        ax.fill_between(x_values, ci_lower, ci_upper,
                       alpha=0.3, color='#2E86AB', label='95% CI')

        # 添加数据点标签
        for x, y, count in zip(x_values, probabilities, [r['count'] for r in results]):
            ax.annotate(f'{y:.3f}\n(n={count})',
                       (x, y), textcoords="offset points",
                       xytext=(0,10), ha='center', fontsize=9)

        # 设置标签和标题
        ax.set_xlabel(f'{var_name} (Mean Value)', fontsize=12, fontweight='bold')
        ax.set_ylabel(f'Probability of {target_name}=1', fontsize=12, fontweight='bold')
        ax.set_title(f'Probability Trend: {var_name} vs {target_name}',
                    fontsize=14, fontweight='bold', pad=20)

        # 设置y轴范围
        ax.set_ylim(0, 1.1)

        # 添加图例
        ax.legend(loc='best', frameon=True, fancybox=True, shadow=True)

        # 添加网格
        ax.grid(True, alpha=0.3)

        # 美化图表
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"概率折线图已保存到: {save_path}")

        return fig


# 全局实例
_data_explorer = None

def get_data_explorer() -> DataExplorer:
    """获取数据探索器实例"""
    global _data_explorer
    if _data_explorer is None:
        _data_explorer = DataExplorer()
    return _data_explorer
