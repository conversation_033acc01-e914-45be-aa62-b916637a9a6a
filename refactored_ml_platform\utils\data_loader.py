#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据加载器模块
提供统一的数据加载和预处理功能
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Optional, Dict, Any, Tuple, List

try:
    import chardet
    HAS_CHARDET = True
except ImportError:
    HAS_CHARDET = False

from .error_handler import get_error_handler


class DataLoader:
    """数据加载器类"""
    
    def __init__(self):
        """初始化数据加载器"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        self.supported_formats = ['.csv', '.xlsx', '.xls', '.json']
    
    def load_data(self, file_path: str, **kwargs) -> Optional[pd.DataFrame]:
        """
        加载数据文件
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
            
        Returns:
            DataFrame或None
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            suffix = file_path.suffix.lower()
            
            if suffix not in self.supported_formats:
                raise ValueError(f"不支持的文件格式: {suffix}")
            
            if suffix == '.csv':
                return self._load_csv(file_path, **kwargs)
            elif suffix in ['.xlsx', '.xls']:
                return self._load_excel(file_path, **kwargs)
            elif suffix == '.json':
                return self._load_json(file_path, **kwargs)
            
        except Exception as e:
            self.error_handler.handle_error(e, f"加载数据文件 {file_path}")
            return None
    
    def _load_csv(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """加载CSV文件"""
        # 自动检测编码
        encoding = self._detect_encoding(file_path)
        
        # 默认参数
        default_params = {
            'encoding': encoding,
            'low_memory': False
        }
        default_params.update(kwargs)
        
        try:
            df = pd.read_csv(file_path, **default_params)
            self.logger.info(f"成功加载CSV文件: {file_path}, 形状: {df.shape}")
            return df
        except UnicodeDecodeError:
            # 尝试其他编码
            for enc in ['utf-8', 'gbk', 'gb2312', 'latin-1']:
                try:
                    default_params['encoding'] = enc
                    df = pd.read_csv(file_path, **default_params)
                    self.logger.info(f"使用编码 {enc} 成功加载CSV文件: {file_path}")
                    return df
                except:
                    continue
            raise
    
    def _load_excel(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """加载Excel文件"""
        df = pd.read_excel(file_path, **kwargs)
        self.logger.info(f"成功加载Excel文件: {file_path}, 形状: {df.shape}")
        return df
    
    def _load_json(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """加载JSON文件"""
        df = pd.read_json(file_path, **kwargs)
        self.logger.info(f"成功加载JSON文件: {file_path}, 形状: {df.shape}")
        return df
    
    def _detect_encoding(self, file_path: Path) -> str:
        """检测文件编码"""
        if not HAS_CHARDET:
            return 'utf-8'

        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # 读取前10KB
                result = chardet.detect(raw_data)
                encoding = result['encoding']
                confidence = result['confidence']

                if confidence > 0.7:
                    self.logger.debug(f"检测到编码: {encoding} (置信度: {confidence:.2f})")
                    return encoding
                else:
                    self.logger.warning(f"编码检测置信度较低: {confidence:.2f}, 使用默认编码")
                    return 'utf-8'
        except Exception as e:
            self.logger.warning(f"编码检测失败: {e}, 使用默认编码")
            return 'utf-8'
    
    def validate_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        验证数据质量
        
        Args:
            df: 数据框
            
        Returns:
            验证结果字典
        """
        try:
            result = {
                'shape': df.shape,
                'columns': df.columns.tolist(),
                'dtypes': df.dtypes.to_dict(),
                'missing_values': df.isnull().sum().to_dict(),
                'duplicate_rows': df.duplicated().sum(),
                'memory_usage': df.memory_usage(deep=True).sum(),
                'numeric_columns': df.select_dtypes(include=[np.number]).columns.tolist(),
                'categorical_columns': df.select_dtypes(include=['object']).columns.tolist()
            }
            
            # 计算缺失值比例
            total_cells = df.shape[0] * df.shape[1]
            missing_cells = df.isnull().sum().sum()
            result['missing_percentage'] = (missing_cells / total_cells) * 100
            
            self.logger.info(f"数据验证完成: {df.shape[0]}行 {df.shape[1]}列")
            return result
            
        except Exception as e:
            self.error_handler.handle_error(e, "数据验证")
            return {}
    
    def preprocess_data(self, df: pd.DataFrame, 
                       drop_duplicates: bool = True,
                       handle_missing: str = 'drop',
                       numeric_only: bool = False) -> pd.DataFrame:
        """
        预处理数据
        
        Args:
            df: 原始数据框
            drop_duplicates: 是否删除重复行
            handle_missing: 缺失值处理方式 ('drop', 'fill', 'none')
            numeric_only: 是否只保留数值列
            
        Returns:
            预处理后的数据框
        """
        try:
            processed_df = df.copy()
            
            # 删除重复行
            if drop_duplicates:
                before_count = len(processed_df)
                processed_df = processed_df.drop_duplicates()
                after_count = len(processed_df)
                if before_count != after_count:
                    self.logger.info(f"删除了 {before_count - after_count} 行重复数据")
            
            # 处理缺失值
            if handle_missing == 'drop':
                before_count = len(processed_df)
                processed_df = processed_df.dropna()
                after_count = len(processed_df)
                if before_count != after_count:
                    self.logger.info(f"删除了 {before_count - after_count} 行包含缺失值的数据")
            elif handle_missing == 'fill':
                # 数值列用均值填充，分类列用众数填充
                for col in processed_df.columns:
                    if processed_df[col].dtype in ['int64', 'float64']:
                        processed_df[col].fillna(processed_df[col].mean(), inplace=True)
                    else:
                        processed_df[col].fillna(processed_df[col].mode().iloc[0] if not processed_df[col].mode().empty else 'Unknown', inplace=True)
                self.logger.info("已填充缺失值")
            
            # 只保留数值列
            if numeric_only:
                numeric_cols = processed_df.select_dtypes(include=[np.number]).columns
                processed_df = processed_df[numeric_cols]
                self.logger.info(f"只保留数值列，剩余 {len(numeric_cols)} 列")
            
            self.logger.info(f"数据预处理完成，最终形状: {processed_df.shape}")
            return processed_df
            
        except Exception as e:
            self.error_handler.handle_error(e, "数据预处理")
            return df


# 全局数据加载器实例
_global_data_loader = None


def get_data_loader() -> DataLoader:
    """获取全局数据加载器实例"""
    global _global_data_loader
    if _global_data_loader is None:
        _global_data_loader = DataLoader()
    return _global_data_loader
