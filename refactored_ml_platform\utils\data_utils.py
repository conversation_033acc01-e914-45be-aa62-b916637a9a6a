#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据工具模块
提供数据处理相关的工具函数
"""

import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Tuple, Union


class DataUtils:
    """数据工具类"""
    
    @staticmethod
    def detect_data_types(df: pd.DataFrame) -> Dict[str, str]:
        """
        检测数据类型
        
        Args:
            df: DataFrame
            
        Returns:
            列名到数据类型的映射
        """
        type_mapping = {}
        
        for col in df.columns:
            if df[col].dtype in ['int64', 'float64']:
                type_mapping[col] = 'numeric'
            elif df[col].dtype == 'object':
                # 检查是否可以转换为数值
                try:
                    pd.to_numeric(df[col], errors='raise')
                    type_mapping[col] = 'numeric'
                except:
                    type_mapping[col] = 'categorical'
            elif df[col].dtype == 'bool':
                type_mapping[col] = 'boolean'
            else:
                type_mapping[col] = 'other'
        
        return type_mapping
    
    @staticmethod
    def get_missing_info(df: pd.DataFrame) -> Dict[str, Any]:
        """
        获取缺失值信息
        
        Args:
            df: DataFrame
            
        Returns:
            缺失值信息字典
        """
        missing_count = df.isnull().sum()
        missing_percent = (missing_count / len(df)) * 100
        
        return {
            'missing_count': missing_count.to_dict(),
            'missing_percent': missing_percent.to_dict(),
            'total_missing': missing_count.sum(),
            'columns_with_missing': missing_count[missing_count > 0].index.tolist()
        }
    
    @staticmethod
    def get_basic_stats(df: pd.DataFrame) -> Dict[str, Any]:
        """
        获取基本统计信息
        
        Args:
            df: DataFrame
            
        Returns:
            统计信息字典
        """
        stats = {
            'shape': df.shape,
            'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024,
            'duplicated_rows': df.duplicated().sum(),
            'numeric_columns': df.select_dtypes(include=[np.number]).columns.tolist(),
            'categorical_columns': df.select_dtypes(include=['object']).columns.tolist(),
            'boolean_columns': df.select_dtypes(include=['bool']).columns.tolist()
        }
        
        # 数值列统计
        if stats['numeric_columns']:
            numeric_stats = df[stats['numeric_columns']].describe()
            stats['numeric_stats'] = numeric_stats.to_dict()
        
        # 分类列统计
        if stats['categorical_columns']:
            categorical_stats = {}
            for col in stats['categorical_columns']:
                categorical_stats[col] = {
                    'unique_count': df[col].nunique(),
                    'top_values': df[col].value_counts().head(5).to_dict()
                }
            stats['categorical_stats'] = categorical_stats
        
        return stats
    
    @staticmethod
    def detect_outliers(series: pd.Series, method: str = 'iqr') -> List[int]:
        """
        检测异常值
        
        Args:
            series: 数据序列
            method: 检测方法 ('iqr', 'zscore')
            
        Returns:
            异常值索引列表
        """
        if method == 'iqr':
            Q1 = series.quantile(0.25)
            Q3 = series.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            outliers = series[(series < lower_bound) | (series > upper_bound)].index.tolist()
        
        elif method == 'zscore':
            z_scores = np.abs((series - series.mean()) / series.std())
            outliers = series[z_scores > 3].index.tolist()
        
        else:
            raise ValueError(f"不支持的异常值检测方法: {method}")
        
        return outliers
    
    @staticmethod
    def suggest_target_column(df: pd.DataFrame) -> Optional[str]:
        """
        建议目标列
        
        Args:
            df: DataFrame
            
        Returns:
            建议的目标列名
        """
        # 常见的目标列名
        common_targets = ['label', 'target', 'y', 'class', 'outcome', 'result']
        
        for target in common_targets:
            if target in df.columns:
                return target
        
        # 如果没有找到，检查是否有二分类的列
        for col in df.columns:
            if df[col].nunique() == 2:
                return col
        
        # 返回最后一列
        return df.columns[-1] if len(df.columns) > 0 else None
    
    @staticmethod
    def encode_categorical(df: pd.DataFrame, 
                          columns: Optional[List[str]] = None,
                          method: str = 'label') -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        编码分类变量
        
        Args:
            df: DataFrame
            columns: 要编码的列，None表示所有分类列
            method: 编码方法 ('label', 'onehot')
            
        Returns:
            编码后的DataFrame和编码器字典
        """
        df_encoded = df.copy()
        encoders = {}
        
        if columns is None:
            columns = df.select_dtypes(include=['object']).columns.tolist()
        
        if method == 'label':
            from sklearn.preprocessing import LabelEncoder
            for col in columns:
                if col in df.columns:
                    encoder = LabelEncoder()
                    df_encoded[col] = encoder.fit_transform(df[col].astype(str))
                    encoders[col] = encoder
        
        elif method == 'onehot':
            df_encoded = pd.get_dummies(df_encoded, columns=columns, prefix=columns)
            encoders['method'] = 'onehot'
            encoders['columns'] = columns
        
        return df_encoded, encoders
    
    @staticmethod
    def normalize_data(df: pd.DataFrame, 
                      columns: Optional[List[str]] = None,
                      method: str = 'standard') -> Tuple[pd.DataFrame, Any]:
        """
        标准化数据
        
        Args:
            df: DataFrame
            columns: 要标准化的列，None表示所有数值列
            method: 标准化方法 ('standard', 'minmax', 'robust')
            
        Returns:
            标准化后的DataFrame和标准化器
        """
        df_normalized = df.copy()
        
        if columns is None:
            columns = df.select_dtypes(include=[np.number]).columns.tolist()
        
        if method == 'standard':
            from sklearn.preprocessing import StandardScaler
            scaler = StandardScaler()
        elif method == 'minmax':
            from sklearn.preprocessing import MinMaxScaler
            scaler = MinMaxScaler()
        elif method == 'robust':
            from sklearn.preprocessing import RobustScaler
            scaler = RobustScaler()
        else:
            raise ValueError(f"不支持的标准化方法: {method}")
        
        df_normalized[columns] = scaler.fit_transform(df[columns])
        
        return df_normalized, scaler
    
    @staticmethod
    def split_features_target(df: pd.DataFrame, 
                             target_column: str) -> Tuple[pd.DataFrame, pd.Series]:
        """
        分离特征和目标变量
        
        Args:
            df: DataFrame
            target_column: 目标列名
            
        Returns:
            特征DataFrame和目标Series
        """
        if target_column not in df.columns:
            raise ValueError(f"目标列 '{target_column}' 不存在")
        
        X = df.drop(columns=[target_column])
        y = df[target_column]
        
        return X, y
    
    @staticmethod
    def balance_dataset(X: pd.DataFrame, y: pd.Series, 
                       method: str = 'oversample') -> Tuple[pd.DataFrame, pd.Series]:
        """
        平衡数据集
        
        Args:
            X: 特征数据
            y: 目标数据
            method: 平衡方法 ('oversample', 'undersample')
            
        Returns:
            平衡后的特征和目标数据
        """
        try:
            if method == 'oversample':
                from imblearn.over_sampling import RandomOverSampler
                sampler = RandomOverSampler(random_state=42)
            elif method == 'undersample':
                from imblearn.under_sampling import RandomUnderSampler
                sampler = RandomUnderSampler(random_state=42)
            else:
                raise ValueError(f"不支持的平衡方法: {method}")
            
            X_balanced, y_balanced = sampler.fit_resample(X, y)
            return pd.DataFrame(X_balanced, columns=X.columns), pd.Series(y_balanced)
        
        except ImportError:
            # 如果没有安装imbalanced-learn，使用简单的重采样
            from sklearn.utils import resample
            
            # 获取类别分布
            class_counts = y.value_counts()
            
            if method == 'oversample':
                # 过采样到最大类别的数量
                max_count = class_counts.max()
                balanced_dfs = []
                
                for class_label in class_counts.index:
                    class_data = X[y == class_label]
                    class_target = y[y == class_label]
                    
                    if len(class_data) < max_count:
                        # 重采样
                        resampled_data = resample(class_data, 
                                                replace=True, 
                                                n_samples=max_count,
                                                random_state=42)
                        resampled_target = pd.Series([class_label] * max_count)
                    else:
                        resampled_data = class_data
                        resampled_target = class_target
                    
                    balanced_dfs.append((resampled_data, resampled_target))
                
                # 合并所有类别
                X_balanced = pd.concat([df[0] for df in balanced_dfs], ignore_index=True)
                y_balanced = pd.concat([df[1] for df in balanced_dfs], ignore_index=True)
                
            else:  # undersample
                # 欠采样到最小类别的数量
                min_count = class_counts.min()
                balanced_dfs = []
                
                for class_label in class_counts.index:
                    class_data = X[y == class_label]
                    class_target = y[y == class_label]
                    
                    if len(class_data) > min_count:
                        # 重采样
                        resampled_data = resample(class_data, 
                                                replace=False, 
                                                n_samples=min_count,
                                                random_state=42)
                        resampled_target = pd.Series([class_label] * min_count)
                    else:
                        resampled_data = class_data
                        resampled_target = class_target
                    
                    balanced_dfs.append((resampled_data, resampled_target))
                
                # 合并所有类别
                X_balanced = pd.concat([df[0] for df in balanced_dfs], ignore_index=True)
                y_balanced = pd.concat([df[1] for df in balanced_dfs], ignore_index=True)
            
            return X_balanced, y_balanced
