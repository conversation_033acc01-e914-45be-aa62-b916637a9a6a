#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeLong检验实现
用于比较两个或多个ROC曲线的统计显著性差异
"""

import numpy as np
import pandas as pd
from scipy import stats
from sklearn.metrics import roc_auc_score, roc_curve
from itertools import combinations
import logging
from typing import Dict, List, Tuple, Optional, Any
import warnings

from .error_handler import get_error_handler, error_handler

# 忽略统计计算中的警告
warnings.filterwarnings("ignore", category=RuntimeWarning)


class DeLongTest:
    """DeLong检验类"""
    
    def __init__(self):
        """初始化DeLong检验"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
    
    @error_handler("计算DeLong方差")
    def _compute_delong_variance(self, y_true: np.ndarray, y_scores: np.ndarray) -> float:
        """
        计算DeLong方差
        
        Args:
            y_true: 真实标签
            y_scores: 预测分数
            
        Returns:
            DeLong方差
        """
        # 确保输入为numpy数组
        y_true = np.asarray(y_true)
        y_scores = np.asarray(y_scores)
        
        # 获取正负样本索引
        pos_indices = np.where(y_true == 1)[0]
        neg_indices = np.where(y_true == 0)[0]
        
        n_pos = len(pos_indices)
        n_neg = len(neg_indices)
        
        if n_pos == 0 or n_neg == 0:
            return 0.0
        
        # 计算V10和V01
        v10 = self._compute_v10(y_scores, pos_indices, neg_indices)
        v01 = self._compute_v01(y_scores, pos_indices, neg_indices)
        
        # 计算方差
        variance = (v10 / n_pos) + (v01 / n_neg)
        
        return max(variance, 1e-10)  # 避免除零错误
    
    def _compute_v10(self, y_scores: np.ndarray, pos_indices: np.ndarray, neg_indices: np.ndarray) -> float:
        """计算V10统计量"""
        n_pos = len(pos_indices)
        n_neg = len(neg_indices)
        
        if n_pos <= 1:
            return 0.0
        
        # 计算正样本间的比较
        pos_scores = y_scores[pos_indices]
        neg_scores = y_scores[neg_indices]
        
        # 对每个正样本，计算其与所有负样本的比较结果
        comparisons = []
        for pos_score in pos_scores:
            # 计算该正样本胜过的负样本比例
            wins = np.sum(pos_score > neg_scores)
            ties = np.sum(pos_score == neg_scores)
            psi = (wins + 0.5 * ties) / n_neg
            comparisons.append(psi)
        
        comparisons = np.array(comparisons)
        
        # 计算方差
        mean_psi = np.mean(comparisons)
        v10 = np.sum((comparisons - mean_psi) ** 2) / (n_pos - 1)
        
        return v10
    
    def _compute_v01(self, y_scores: np.ndarray, pos_indices: np.ndarray, neg_indices: np.ndarray) -> float:
        """计算V01统计量"""
        n_pos = len(pos_indices)
        n_neg = len(neg_indices)
        
        if n_neg <= 1:
            return 0.0
        
        # 计算负样本间的比较
        pos_scores = y_scores[pos_indices]
        neg_scores = y_scores[neg_indices]
        
        # 对每个负样本，计算其被正样本胜过的比例
        comparisons = []
        for neg_score in neg_scores:
            # 计算胜过该负样本的正样本比例
            wins = np.sum(pos_scores > neg_score)
            ties = np.sum(pos_scores == neg_score)
            psi = (wins + 0.5 * ties) / n_pos
            comparisons.append(psi)
        
        comparisons = np.array(comparisons)
        
        # 计算方差
        mean_psi = np.mean(comparisons)
        v01 = np.sum((comparisons - mean_psi) ** 2) / (n_neg - 1)
        
        return v01
    
    @error_handler("执行DeLong检验")
    def delong_roc_test(self, y_true: np.ndarray, y_scores1: np.ndarray, 
                       y_scores2: np.ndarray) -> Tuple[float, float, float, float]:
        """
        执行DeLong检验比较两个ROC曲线
        
        Args:
            y_true: 真实标签
            y_scores1: 模型1的预测分数
            y_scores2: 模型2的预测分数
            
        Returns:
            tuple: (z_score, p_value, auc1, auc2)
        """
        try:
            # 确保输入为numpy数组
            y_true = np.asarray(y_true)
            y_scores1 = np.asarray(y_scores1)
            y_scores2 = np.asarray(y_scores2)
            
            # 检查输入有效性
            if len(y_true) != len(y_scores1) or len(y_true) != len(y_scores2):
                raise ValueError("输入数组长度不一致")
            
            if len(np.unique(y_true)) != 2:
                raise ValueError("标签必须是二分类")
            
            # 计算AUC
            auc1 = roc_auc_score(y_true, y_scores1)
            auc2 = roc_auc_score(y_true, y_scores2)
            
            # 计算方差
            var1 = self._compute_delong_variance(y_true, y_scores1)
            var2 = self._compute_delong_variance(y_true, y_scores2)
            
            # 计算协方差（简化处理）
            covar = self._compute_covariance(y_true, y_scores1, y_scores2)
            
            # 计算标准误差
            se = np.sqrt(var1 + var2 - 2 * covar)
            
            if se == 0 or np.isnan(se):
                return 0.0, 1.0, auc1, auc2
            
            # 计算z分数
            z_score = (auc1 - auc2) / se
            
            # 计算p值（双尾检验）
            p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
            
            return z_score, p_value, auc1, auc2
            
        except Exception as e:
            self.logger.error(f"DeLong检验计算失败: {e}")
            return 0.0, 1.0, 0.0, 0.0
    
    def _compute_covariance(self, y_true: np.ndarray, y_scores1: np.ndarray, 
                          y_scores2: np.ndarray) -> float:
        """
        计算两个模型预测分数的协方差
        
        Args:
            y_true: 真实标签
            y_scores1: 模型1的预测分数
            y_scores2: 模型2的预测分数
            
        Returns:
            协方差值
        """
        try:
            # 获取正负样本索引
            pos_indices = np.where(y_true == 1)[0]
            neg_indices = np.where(y_true == 0)[0]
            
            n_pos = len(pos_indices)
            n_neg = len(neg_indices)
            
            if n_pos == 0 or n_neg == 0:
                return 0.0
            
            # 计算每个正样本对两个模型的psi值
            psi1_pos = []
            psi2_pos = []
            
            for pos_idx in pos_indices:
                pos_score1 = y_scores1[pos_idx]
                pos_score2 = y_scores2[pos_idx]
                
                # 计算psi1
                wins1 = np.sum(pos_score1 > y_scores1[neg_indices])
                ties1 = np.sum(pos_score1 == y_scores1[neg_indices])
                psi1 = (wins1 + 0.5 * ties1) / n_neg
                psi1_pos.append(psi1)
                
                # 计算psi2
                wins2 = np.sum(pos_score2 > y_scores2[neg_indices])
                ties2 = np.sum(pos_score2 == y_scores2[neg_indices])
                psi2 = (wins2 + 0.5 * ties2) / n_neg
                psi2_pos.append(psi2)
            
            # 计算每个负样本对两个模型的psi值
            psi1_neg = []
            psi2_neg = []
            
            for neg_idx in neg_indices:
                neg_score1 = y_scores1[neg_idx]
                neg_score2 = y_scores2[neg_idx]
                
                # 计算psi1
                wins1 = np.sum(y_scores1[pos_indices] > neg_score1)
                ties1 = np.sum(y_scores1[pos_indices] == neg_score1)
                psi1 = (wins1 + 0.5 * ties1) / n_pos
                psi1_neg.append(psi1)
                
                # 计算psi2
                wins2 = np.sum(y_scores2[pos_indices] > neg_score2)
                ties2 = np.sum(y_scores2[pos_indices] == neg_score2)
                psi2 = (wins2 + 0.5 * ties2) / n_pos
                psi2_neg.append(psi2)
            
            # 计算协方差
            psi1_pos = np.array(psi1_pos)
            psi2_pos = np.array(psi2_pos)
            psi1_neg = np.array(psi1_neg)
            psi2_neg = np.array(psi2_neg)
            
            # 正样本协方差
            if len(psi1_pos) > 1:
                cov_pos = np.cov(psi1_pos, psi2_pos)[0, 1] / n_pos
            else:
                cov_pos = 0.0
            
            # 负样本协方差
            if len(psi1_neg) > 1:
                cov_neg = np.cov(psi1_neg, psi2_neg)[0, 1] / n_neg
            else:
                cov_neg = 0.0
            
            return cov_pos + cov_neg
            
        except Exception as e:
            self.logger.warning(f"协方差计算失败，使用默认值0: {e}")
            return 0.0
    
    @error_handler("执行多模型DeLong比较")
    def compare_multiple_models(self, model_data: Dict[str, Dict[str, Any]], 
                              alpha: float = 0.05) -> Dict[str, Any]:
        """
        对多个模型进行两两DeLong检验比较
        
        Args:
            model_data: 模型数据字典 {model_name: {'y_true': ..., 'y_scores': ...}}
            alpha: 显著性水平
            
        Returns:
            比较结果字典
        """
        self.logger.info(f"开始DeLong检验，比较{len(model_data)}个模型")
        
        model_names = list(model_data.keys())
        results = {
            'pairwise_results': [],
            'summary': {
                'total_comparisons': 0,
                'significant_comparisons': 0,
                'alpha': alpha,
                'model_names': model_names
            },
            'comparison_matrix': None,
            'p_value_matrix': None
        }
        
        # 创建比较矩阵
        n_models = len(model_names)
        comparison_matrix = np.zeros((n_models, n_models))
        p_value_matrix = np.ones((n_models, n_models))
        
        # 两两比较
        for i, model1 in enumerate(model_names):
            for j, model2 in enumerate(model_names):
                if i >= j:
                    continue
                
                try:
                    data1 = model_data[model1]
                    data2 = model_data[model2]
                    
                    # 确保使用相同的测试集
                    y_true1 = data1['y_true']
                    y_true2 = data2['y_true']
                    
                    if not np.array_equal(y_true1, y_true2):
                        self.logger.warning(f"模型{model1}和{model2}的测试集不同，跳过比较")
                        continue
                    
                    y_scores1 = data1['y_scores']
                    y_scores2 = data2['y_scores']
                    
                    # 执行DeLong检验
                    z_score, p_value, auc1, auc2 = self.delong_roc_test(y_true1, y_scores1, y_scores2)
                    
                    is_significant = p_value < alpha
                    
                    comparison_result = {
                        'model1': model1,
                        'model2': model2,
                        'auc1': auc1,
                        'auc2': auc2,
                        'auc_diff': auc1 - auc2,
                        'z_score': z_score,
                        'p_value': p_value,
                        'is_significant': is_significant,
                        'alpha': alpha,
                        'better_model': model1 if auc1 > auc2 else model2
                    }
                    
                    results['pairwise_results'].append(comparison_result)
                    results['summary']['total_comparisons'] += 1
                    
                    if is_significant:
                        results['summary']['significant_comparisons'] += 1
                    
                    # 填充矩阵
                    comparison_matrix[i, j] = z_score
                    comparison_matrix[j, i] = -z_score
                    p_value_matrix[i, j] = p_value
                    p_value_matrix[j, i] = p_value
                    
                    self.logger.info(f"比较 {model1} vs {model2}: "
                                   f"AUC差异={auc1-auc2:.4f}, p={p_value:.4f}, "
                                   f"显著={'是' if is_significant else '否'}")
                    
                except Exception as e:
                    self.logger.error(f"比较模型{model1}和{model2}时出错: {e}")
                    continue
        
        # 保存矩阵
        results['comparison_matrix'] = comparison_matrix
        results['p_value_matrix'] = p_value_matrix
        
        self.logger.info(f"DeLong检验完成，共进行{results['summary']['total_comparisons']}次比较，"
                        f"其中{results['summary']['significant_comparisons']}次有显著差异")
        
        return results
    
    @error_handler("保存DeLong检验结果")
    def save_results(self, results: Dict[str, Any], save_path: str):
        """
        保存DeLong检验结果
        
        Args:
            results: 检验结果
            save_path: 保存路径
        """
        import json
        
        # 转换numpy数组为列表
        save_results = results.copy()
        if 'comparison_matrix' in save_results and save_results['comparison_matrix'] is not None:
            save_results['comparison_matrix'] = save_results['comparison_matrix'].tolist()
        if 'p_value_matrix' in save_results and save_results['p_value_matrix'] is not None:
            save_results['p_value_matrix'] = save_results['p_value_matrix'].tolist()
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(save_results, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"DeLong检验结果已保存到: {save_path}")
    
    def get_summary_report(self, results: Dict[str, Any]) -> str:
        """
        生成DeLong检验摘要报告
        
        Args:
            results: 检验结果
            
        Returns:
            摘要报告文本
        """
        summary = results['summary']
        pairwise_results = results['pairwise_results']
        
        report = f"""
=== DeLong检验结果摘要 ===

基本信息:
- 比较模型数量: {len(summary['model_names'])}
- 总比较次数: {summary['total_comparisons']}
- 显著差异比较: {summary['significant_comparisons']}
- 显著性水平: {summary['alpha']}

模型列表:
{', '.join(summary['model_names'])}

详细比较结果:
"""
        
        for result in pairwise_results:
            significance = "显著" if result['is_significant'] else "不显著"
            report += f"""
{result['model1']} vs {result['model2']}:
  - AUC差异: {result['auc_diff']:.4f} ({result['auc1']:.4f} - {result['auc2']:.4f})
  - Z分数: {result['z_score']:.4f}
  - P值: {result['p_value']:.4f}
  - 差异显著性: {significance}
  - 更优模型: {result['better_model']}
"""
        
        return report


# 全局DeLong检验实例
_delong_test = None

def get_delong_test() -> DeLongTest:
    """
    获取全局DeLong检验实例
    
    Returns:
        DeLong检验实例
    """
    global _delong_test
    if _delong_test is None:
        _delong_test = DeLongTest()
    return _delong_test


# 便捷函数
def perform_delong_comparison(model_data: Dict[str, Dict[str, Any]], 
                            alpha: float = 0.05) -> Dict[str, Any]:
    """
    执行DeLong比较的便捷函数
    
    Args:
        model_data: 模型数据字典
        alpha: 显著性水平
        
    Returns:
        比较结果
    """
    delong_test = get_delong_test()
    return delong_test.compare_multiple_models(model_data, alpha)


def save_delong_results(results: Dict[str, Any], save_path: str):
    """
    保存DeLong结果的便捷函数
    
    Args:
        results: 检验结果
        save_path: 保存路径
    """
    delong_test = get_delong_test()
    delong_test.save_results(results, save_path)
