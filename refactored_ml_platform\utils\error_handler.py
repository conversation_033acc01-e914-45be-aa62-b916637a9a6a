#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一错误处理模块
提供统一的异常处理和用户友好的错误提示
"""

import logging
import traceback
from typing import Optional, Callable, Any
from functools import wraps


class MLPlatformError(Exception):
    """机器学习平台基础异常类"""
    pass


class DataLoadError(MLPlatformError):
    """数据加载异常"""
    pass


class ModelTrainingError(MLPlatformError):
    """模型训练异常"""
    pass


class ConfigurationError(MLPlatformError):
    """配置异常"""
    pass


class ValidationError(MLPlatformError):
    """数据验证异常"""
    pass


class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def handle_error(self, error: Exception, context: str = "", 
                    show_user_message: bool = True) -> str:
        """
        处理异常
        
        Args:
            error: 异常对象
            context: 错误上下文
            show_user_message: 是否显示用户友好消息
            
        Returns:
            用户友好的错误消息
        """
        # 记录详细错误信息
        error_msg = f"错误上下文: {context}\n异常类型: {type(error).__name__}\n异常消息: {str(error)}"
        self.logger.error(error_msg)
        self.logger.debug(traceback.format_exc())
        
        # 生成用户友好的错误消息
        user_message = self._get_user_friendly_message(error, context)
        
        if show_user_message:
            self.logger.info(f"用户消息: {user_message}")
        
        return user_message
    
    def _get_user_friendly_message(self, error: Exception, context: str) -> str:
        """生成用户友好的错误消息"""
        error_type = type(error).__name__
        
        # 根据异常类型生成相应的用户消息
        if isinstance(error, DataLoadError):
            return f"数据加载失败: {str(error)}"
        elif isinstance(error, ModelTrainingError):
            return f"模型训练出错: {str(error)}"
        elif isinstance(error, ConfigurationError):
            return f"配置错误: {str(error)}"
        elif isinstance(error, ValidationError):
            return f"数据验证失败: {str(error)}"
        elif isinstance(error, FileNotFoundError):
            return f"文件未找到: {str(error)}"
        elif isinstance(error, PermissionError):
            return f"权限不足: {str(error)}"
        elif isinstance(error, MemoryError):
            return "内存不足，请尝试使用较小的数据集"
        elif isinstance(error, ImportError):
            return f"缺少必要的依赖包: {str(error)}"
        else:
            return f"发生未知错误: {str(error)}"


def error_handler(context: str = "", reraise: bool = False):
    """
    错误处理装饰器
    
    Args:
        context: 错误上下文描述
        reraise: 是否重新抛出异常
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                handler = ErrorHandler()
                user_msg = handler.handle_error(e, context or func.__name__)
                
                if reraise:
                    raise
                else:
                    # 返回错误信息而不是抛出异常
                    return {"error": True, "message": user_msg}
        
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default_return=None, **kwargs) -> Any:
    """
    安全执行函数，捕获所有异常
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        default_return: 异常时的默认返回值
        **kwargs: 函数关键字参数
        
    Returns:
        函数执行结果或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        handler = ErrorHandler()
        handler.handle_error(e, f"执行函数 {func.__name__}")
        return default_return


# 全局错误处理器实例
_global_error_handler = None


def get_error_handler() -> ErrorHandler:
    """获取全局错误处理器实例"""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = ErrorHandler()
    return _global_error_handler
