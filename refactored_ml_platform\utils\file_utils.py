#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件工具模块
提供文件操作相关的工具函数
"""

import os
import json
import pickle
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union


class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def ensure_dir(path: Union[str, Path]) -> Path:
        """
        确保目录存在，如果不存在则创建
        
        Args:
            path: 目录路径
            
        Returns:
            Path对象
        """
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @staticmethod
    def get_file_size_mb(file_path: Union[str, Path]) -> float:
        """
        获取文件大小（MB）
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件大小（MB）
        """
        file_path = Path(file_path)
        if file_path.exists():
            return file_path.stat().st_size / (1024 * 1024)
        return 0.0
    
    @staticmethod
    def is_file_supported(file_path: Union[str, Path], 
                         supported_extensions: List[str]) -> bool:
        """
        检查文件是否为支持的格式
        
        Args:
            file_path: 文件路径
            supported_extensions: 支持的扩展名列表
            
        Returns:
            是否支持
        """
        file_path = Path(file_path)
        return file_path.suffix.lower().lstrip('.') in [
            ext.lower().lstrip('.') for ext in supported_extensions
        ]
    
    @staticmethod
    def save_json(data: Dict[str, Any], file_path: Union[str, Path], 
                  indent: int = 2) -> bool:
        """
        保存数据为JSON文件
        
        Args:
            data: 要保存的数据
            file_path: 文件路径
            indent: 缩进
            
        Returns:
            是否成功
        """
        try:
            file_path = Path(file_path)
            FileUtils.ensure_dir(file_path.parent)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=indent, ensure_ascii=False)
            return True
        except Exception as e:
            logging.error(f"保存JSON文件失败: {e}")
            return False
    
    @staticmethod
    def load_json(file_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """
        加载JSON文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            加载的数据，失败返回None
        """
        try:
            file_path = Path(file_path)
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logging.error(f"加载JSON文件失败: {e}")
        return None
    
    @staticmethod
    def save_pickle(obj: Any, file_path: Union[str, Path]) -> bool:
        """
        保存对象为pickle文件
        
        Args:
            obj: 要保存的对象
            file_path: 文件路径
            
        Returns:
            是否成功
        """
        try:
            file_path = Path(file_path)
            FileUtils.ensure_dir(file_path.parent)
            
            with open(file_path, 'wb') as f:
                pickle.dump(obj, f)
            return True
        except Exception as e:
            logging.error(f"保存pickle文件失败: {e}")
            return False
    
    @staticmethod
    def load_pickle(file_path: Union[str, Path]) -> Any:
        """
        加载pickle文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            加载的对象，失败返回None
        """
        try:
            file_path = Path(file_path)
            if file_path.exists():
                with open(file_path, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logging.error(f"加载pickle文件失败: {e}")
        return None
    
    @staticmethod
    def get_project_root() -> Path:
        """
        获取项目根目录
        
        Returns:
            项目根目录路径
        """
        return Path(__file__).parent.parent
    
    @staticmethod
    def list_files(directory: Union[str, Path], 
                   pattern: str = "*", 
                   recursive: bool = False) -> List[Path]:
        """
        列出目录中的文件
        
        Args:
            directory: 目录路径
            pattern: 文件模式
            recursive: 是否递归
            
        Returns:
            文件路径列表
        """
        directory = Path(directory)
        if not directory.exists():
            return []
        
        if recursive:
            return list(directory.rglob(pattern))
        else:
            return list(directory.glob(pattern))
    
    @staticmethod
    def clean_filename(filename: str) -> str:
        """
        清理文件名，移除非法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename.strip()
    
    @staticmethod
    def backup_file(file_path: Union[str, Path], 
                   backup_suffix: str = '.bak') -> Optional[Path]:
        """
        备份文件
        
        Args:
            file_path: 文件路径
            backup_suffix: 备份后缀
            
        Returns:
            备份文件路径，失败返回None
        """
        try:
            file_path = Path(file_path)
            if file_path.exists():
                backup_path = file_path.with_suffix(file_path.suffix + backup_suffix)
                backup_path.write_bytes(file_path.read_bytes())
                return backup_path
        except Exception as e:
            logging.error(f"备份文件失败: {e}")
        return None
