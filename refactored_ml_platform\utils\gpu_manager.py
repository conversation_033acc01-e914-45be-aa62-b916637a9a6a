#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU管理器
提供GPU检测、配置和加速功能
"""

import os
import logging
import platform
from typing import Dict, List, Optional, Any, Tuple
import warnings

from .error_handler import get_error_handler, error_handler

# 忽略GPU相关警告
warnings.filterwarnings("ignore", category=UserWarning, module="xgboost")
warnings.filterwarnings("ignore", category=UserWarning, module="lightgbm")


class GPUManager:
    """GPU管理器"""
    
    def __init__(self):
        """初始化GPU管理器"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        
        # GPU配置
        self.gpu_config = {
            'use_gpu': False,
            'gpu_ids': [0],
            'precision': 'float32',
            'auto_detect': True,
            'fallback_to_cpu': True,
            'memory_limit': None,
            'device_type': 'auto'  # auto, cuda, opencl, cpu
        }
        
        # GPU信息缓存
        self._gpu_info_cache = None
        self._cuda_available = None
        self._opencl_available = None
        
        # 自动检测GPU
        if self.gpu_config['auto_detect']:
            self._detect_gpu_capabilities()
    
    @error_handler("检测GPU能力")
    def _detect_gpu_capabilities(self):
        """检测GPU能力"""
        self.logger.info("开始检测GPU能力...")
        
        # 检测CUDA
        self._cuda_available = self._check_cuda_availability()
        
        # 检测OpenCL
        self._opencl_available = self._check_opencl_availability()
        
        # 检测各个库的GPU支持
        gpu_support = {
            'xgboost': self._check_xgboost_gpu(),
            'lightgbm': self._check_lightgbm_gpu(),
            'catboost': self._check_catboost_gpu(),
            'tensorflow': self._check_tensorflow_gpu(),
            'pytorch': self._check_pytorch_gpu()
        }
        
        # 更新配置
        if any(gpu_support.values()):
            self.gpu_config['use_gpu'] = True
            self.logger.info("检测到GPU支持，启用GPU加速")
        else:
            self.gpu_config['use_gpu'] = False
            self.logger.info("未检测到GPU支持，使用CPU模式")
        
        # 记录检测结果
        self.logger.info(f"CUDA可用: {self._cuda_available}")
        self.logger.info(f"OpenCL可用: {self._opencl_available}")
        for lib, support in gpu_support.items():
            self.logger.info(f"{lib} GPU支持: {support}")
    
    def _check_cuda_availability(self) -> bool:
        """检查CUDA可用性"""
        try:
            # 检查NVIDIA-SMI
            import subprocess
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return True
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            pass
        
        # 检查CUDA环境变量
        cuda_path = os.environ.get('CUDA_PATH') or os.environ.get('CUDA_HOME')
        if cuda_path and os.path.exists(cuda_path):
            return True
        
        return False
    
    def _check_opencl_availability(self) -> bool:
        """检查OpenCL可用性"""
        try:
            import pyopencl as cl
            platforms = cl.get_platforms()
            return len(platforms) > 0
        except ImportError:
            return False
        except Exception:
            return False
    
    def _check_xgboost_gpu(self) -> bool:
        """检查XGBoost GPU支持"""
        try:
            import xgboost as xgb
            
            # 检查是否编译了GPU支持
            if hasattr(xgb, 'XGBClassifier'):
                # 尝试创建GPU参数的分类器
                try:
                    clf = xgb.XGBClassifier(tree_method='gpu_hist', gpu_id=0, n_estimators=1)
                    return True
                except Exception:
                    pass
            
            return False
        except ImportError:
            return False
    
    def _check_lightgbm_gpu(self) -> bool:
        """检查LightGBM GPU支持"""
        try:
            import lightgbm as lgb
            
            # 检查是否编译了GPU支持
            try:
                # 尝试创建GPU参数的模型
                train_data = lgb.Dataset([[1, 2], [3, 4]], label=[0, 1])
                params = {'objective': 'binary', 'device': 'gpu', 'gpu_platform_id': 0, 'gpu_device_id': 0}
                lgb.train(params, train_data, num_boost_round=1, verbose=-1)
                return True
            except Exception:
                pass
            
            return False
        except ImportError:
            return False
    
    def _check_catboost_gpu(self) -> bool:
        """检查CatBoost GPU支持"""
        try:
            import catboost as cb
            
            # CatBoost通常支持GPU，检查是否可以创建GPU模型
            try:
                clf = cb.CatBoostClassifier(iterations=1, task_type='GPU', devices='0', verbose=False)
                return True
            except Exception:
                pass
            
            return False
        except ImportError:
            return False
    
    def _check_tensorflow_gpu(self) -> bool:
        """检查TensorFlow GPU支持"""
        try:
            import tensorflow as tf
            return len(tf.config.list_physical_devices('GPU')) > 0
        except ImportError:
            return False
        except Exception:
            return False
    
    def _check_pytorch_gpu(self) -> bool:
        """检查PyTorch GPU支持"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
        except Exception:
            return False
    
    @error_handler("获取GPU信息")
    def get_gpu_info(self) -> Dict[str, Any]:
        """获取GPU信息"""
        if self._gpu_info_cache is not None:
            return self._gpu_info_cache
        
        gpu_info = {
            'cuda_available': self._cuda_available,
            'opencl_available': self._opencl_available,
            'gpu_count': 0,
            'gpu_devices': [],
            'memory_info': {},
            'driver_version': None,
            'cuda_version': None
        }
        
        # 获取NVIDIA GPU信息
        if self._cuda_available:
            try:
                import subprocess
                result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total,memory.used,driver_version', 
                                       '--format=csv,noheader,nounits'], 
                                      capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for i, line in enumerate(lines):
                        parts = line.split(', ')
                        if len(parts) >= 4:
                            gpu_info['gpu_devices'].append({
                                'id': i,
                                'name': parts[0],
                                'memory_total': int(parts[1]),
                                'memory_used': int(parts[2]),
                                'memory_free': int(parts[1]) - int(parts[2])
                            })
                            gpu_info['driver_version'] = parts[3]
                    
                    gpu_info['gpu_count'] = len(gpu_info['gpu_devices'])
                
            except Exception as e:
                self.logger.warning(f"获取NVIDIA GPU信息失败: {e}")
        
        # 获取CUDA版本
        try:
            import subprocess
            result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'release' in line:
                        gpu_info['cuda_version'] = line.split('release')[1].split(',')[0].strip()
                        break
        except Exception:
            pass
        
        self._gpu_info_cache = gpu_info
        return gpu_info
    
    def configure_gpu(self, use_gpu: bool = None, gpu_ids: List[int] = None, 
                     device_type: str = None, memory_limit: int = None) -> bool:
        """
        配置GPU设置
        
        Args:
            use_gpu: 是否使用GPU
            gpu_ids: GPU设备ID列表
            device_type: 设备类型 (auto, cuda, opencl, cpu)
            memory_limit: 内存限制 (MB)
            
        Returns:
            配置是否成功
        """
        try:
            if use_gpu is not None:
                self.gpu_config['use_gpu'] = use_gpu
            
            if gpu_ids is not None:
                self.gpu_config['gpu_ids'] = gpu_ids
            
            if device_type is not None:
                self.gpu_config['device_type'] = device_type
            
            if memory_limit is not None:
                self.gpu_config['memory_limit'] = memory_limit
            
            # 验证配置
            if self.gpu_config['use_gpu']:
                gpu_info = self.get_gpu_info()
                if gpu_info['gpu_count'] == 0:
                    self.logger.warning("启用GPU但未检测到GPU设备，回退到CPU模式")
                    if self.gpu_config['fallback_to_cpu']:
                        self.gpu_config['use_gpu'] = False
                        return True
                    else:
                        return False
            
            self.logger.info(f"GPU配置更新: {self.gpu_config}")
            return True
            
        except Exception as e:
            self.logger.error(f"GPU配置失败: {e}")
            return False
    
    def get_xgboost_params(self) -> Dict[str, Any]:
        """获取XGBoost GPU参数"""
        params = {}
        
        if self.gpu_config['use_gpu'] and self._check_xgboost_gpu():
            params.update({
                'tree_method': 'gpu_hist',
                'gpu_id': self.gpu_config['gpu_ids'][0] if self.gpu_config['gpu_ids'] else 0,
                'predictor': 'gpu_predictor'
            })
            
            if self.gpu_config['memory_limit']:
                # XGBoost GPU内存限制（以字节为单位）
                params['max_bin'] = min(256, self.gpu_config['memory_limit'] // 100)
        
        return params
    
    def get_lightgbm_params(self) -> Dict[str, Any]:
        """获取LightGBM GPU参数"""
        params = {}
        
        if self.gpu_config['use_gpu'] and self._check_lightgbm_gpu():
            params.update({
                'device': 'gpu',
                'gpu_platform_id': 0,
                'gpu_device_id': self.gpu_config['gpu_ids'][0] if self.gpu_config['gpu_ids'] else 0
            })
            
            if self.gpu_config['memory_limit']:
                # LightGBM GPU内存限制
                params['gpu_use_dp'] = False  # 使用单精度以节省内存
        
        return params
    
    def get_catboost_params(self) -> Dict[str, Any]:
        """获取CatBoost GPU参数"""
        params = {}
        
        if self.gpu_config['use_gpu'] and self._check_catboost_gpu():
            params.update({
                'task_type': 'GPU',
                'devices': str(self.gpu_config['gpu_ids'][0]) if self.gpu_config['gpu_ids'] else '0'
            })
            
            if self.gpu_config['memory_limit']:
                # CatBoost GPU内存限制
                params['gpu_ram_part'] = min(0.95, self.gpu_config['memory_limit'] / 1024 / 8)  # 假设8GB显存
        
        return params
    
    def setup_tensorflow_gpu(self):
        """设置TensorFlow GPU"""
        if not self.gpu_config['use_gpu']:
            return
        
        try:
            import tensorflow as tf
            
            # 获取GPU设备
            gpus = tf.config.list_physical_devices('GPU')
            if gpus:
                try:
                    # 设置内存增长
                    for gpu in gpus:
                        tf.config.experimental.set_memory_growth(gpu, True)
                    
                    # 设置可见GPU
                    if self.gpu_config['gpu_ids']:
                        visible_gpus = [gpus[i] for i in self.gpu_config['gpu_ids'] if i < len(gpus)]
                        tf.config.set_visible_devices(visible_gpus, 'GPU')
                    
                    # 设置内存限制
                    if self.gpu_config['memory_limit']:
                        tf.config.experimental.set_memory_limit(
                            gpus[0], self.gpu_config['memory_limit']
                        )
                    
                    self.logger.info(f"TensorFlow GPU配置完成，可用GPU: {len(gpus)}")
                    
                except RuntimeError as e:
                    self.logger.error(f"TensorFlow GPU配置失败: {e}")
        
        except ImportError:
            self.logger.warning("TensorFlow未安装，跳过GPU配置")
    
    def setup_pytorch_gpu(self):
        """设置PyTorch GPU"""
        if not self.gpu_config['use_gpu']:
            return
        
        try:
            import torch
            
            if torch.cuda.is_available():
                # 设置默认GPU设备
                if self.gpu_config['gpu_ids']:
                    torch.cuda.set_device(self.gpu_config['gpu_ids'][0])
                
                # 设置可见GPU
                if self.gpu_config['gpu_ids']:
                    os.environ['CUDA_VISIBLE_DEVICES'] = ','.join(map(str, self.gpu_config['gpu_ids']))
                
                self.logger.info(f"PyTorch GPU配置完成，可用GPU: {torch.cuda.device_count()}")
        
        except ImportError:
            self.logger.warning("PyTorch未安装，跳过GPU配置")
    
    def get_device_info_summary(self) -> str:
        """获取设备信息摘要"""
        gpu_info = self.get_gpu_info()
        
        summary = f"""
=== GPU设备信息 ===
CUDA可用: {'是' if gpu_info['cuda_available'] else '否'}
OpenCL可用: {'是' if gpu_info['opencl_available'] else '否'}
GPU数量: {gpu_info['gpu_count']}
驱动版本: {gpu_info['driver_version'] or '未知'}
CUDA版本: {gpu_info['cuda_version'] or '未知'}

GPU设备列表:
"""
        
        for device in gpu_info['gpu_devices']:
            summary += f"  GPU {device['id']}: {device['name']}\n"
            summary += f"    显存: {device['memory_free']:.0f}MB / {device['memory_total']:.0f}MB 可用\n"
        
        if not gpu_info['gpu_devices']:
            summary += "  无可用GPU设备\n"
        
        summary += f"\n当前配置: {'GPU模式' if self.gpu_config['use_gpu'] else 'CPU模式'}"
        
        return summary
    
    def is_gpu_available(self) -> bool:
        """检查GPU是否可用"""
        return self.gpu_config['use_gpu'] and self.get_gpu_info()['gpu_count'] > 0


# 全局GPU管理器实例
_gpu_manager = None

def get_gpu_manager() -> GPUManager:
    """
    获取全局GPU管理器实例
    
    Returns:
        GPU管理器实例
    """
    global _gpu_manager
    if _gpu_manager is None:
        _gpu_manager = GPUManager()
    return _gpu_manager


# 便捷函数
def configure_gpu_for_training(enable_gpu: bool = True, gpu_ids: List[int] = None):
    """
    为训练配置GPU的便捷函数
    
    Args:
        enable_gpu: 是否启用GPU
        gpu_ids: GPU设备ID列表
    """
    gpu_manager = get_gpu_manager()
    gpu_manager.configure_gpu(use_gpu=enable_gpu, gpu_ids=gpu_ids)
    
    # 设置各个深度学习框架
    gpu_manager.setup_tensorflow_gpu()
    gpu_manager.setup_pytorch_gpu()


def get_gpu_params_for_model(model_name: str) -> Dict[str, Any]:
    """
    获取特定模型的GPU参数
    
    Args:
        model_name: 模型名称
        
    Returns:
        GPU参数字典
    """
    gpu_manager = get_gpu_manager()
    
    if model_name.lower() in ['xgboost', 'xgb']:
        return gpu_manager.get_xgboost_params()
    elif model_name.lower() in ['lightgbm', 'lgb']:
        return gpu_manager.get_lightgbm_params()
    elif model_name.lower() in ['catboost', 'cat']:
        return gpu_manager.get_catboost_params()
    else:
        return {}
