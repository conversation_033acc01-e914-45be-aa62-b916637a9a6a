#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化模块
提供内存管理、计算优化和性能监控功能
"""

import gc
import psutil
import time
import logging
from typing import Dict, Any, Optional, Callable
from functools import wraps
import pandas as pd
import numpy as np


class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.memory_threshold = 0.8  # 内存使用阈值
        self.performance_stats = {}
        
    def monitor_memory(self) -> Dict[str, Any]:
        """监控内存使用情况"""
        memory = psutil.virtual_memory()
        
        memory_info = {
            'total_gb': round(memory.total / (1024**3), 2),
            'available_gb': round(memory.available / (1024**3), 2),
            'used_gb': round(memory.used / (1024**3), 2),
            'percent': memory.percent,
            'is_high': memory.percent > (self.memory_threshold * 100)
        }
        
        if memory_info['is_high']:
            self.logger.warning(f"内存使用率较高: {memory_info['percent']:.1f}%")
        
        return memory_info
    
    def optimize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame内存使用"""
        if df is None or df.empty:
            return df
        
        original_memory = df.memory_usage(deep=True).sum()
        optimized_df = df.copy()
        
        # 优化数值类型
        for col in optimized_df.select_dtypes(include=['int64']).columns:
            col_min = optimized_df[col].min()
            col_max = optimized_df[col].max()
            
            if col_min >= -128 and col_max <= 127:
                optimized_df[col] = optimized_df[col].astype('int8')
            elif col_min >= -32768 and col_max <= 32767:
                optimized_df[col] = optimized_df[col].astype('int16')
            elif col_min >= -2147483648 and col_max <= 2147483647:
                optimized_df[col] = optimized_df[col].astype('int32')
        
        for col in optimized_df.select_dtypes(include=['float64']).columns:
            optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='float')
        
        # 优化字符串类型
        for col in optimized_df.select_dtypes(include=['object']).columns:
            if optimized_df[col].dtype == 'object':
                try:
                    optimized_df[col] = optimized_df[col].astype('category')
                except:
                    pass
        
        optimized_memory = optimized_df.memory_usage(deep=True).sum()
        reduction = (1 - optimized_memory / original_memory) * 100
        
        self.logger.info(f"DataFrame内存优化完成，减少 {reduction:.1f}%")
        
        return optimized_df
    
    def clear_memory(self):
        """清理内存"""
        gc.collect()
        self.logger.info("内存清理完成")
    
    def performance_monitor(self, func_name: str = ""):
        """性能监控装饰器"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                start_memory = self.monitor_memory()
                
                try:
                    result = func(*args, **kwargs)
                    
                    end_time = time.time()
                    end_memory = self.monitor_memory()
                    
                    execution_time = end_time - start_time
                    memory_change = end_memory['used_gb'] - start_memory['used_gb']
                    
                    # 记录性能统计
                    stats = {
                        'execution_time': execution_time,
                        'memory_change_gb': memory_change,
                        'start_memory_percent': start_memory['percent'],
                        'end_memory_percent': end_memory['percent']
                    }
                    
                    self.performance_stats[func_name or func.__name__] = stats
                    
                    if execution_time > 5:  # 超过5秒的操作记录警告
                        self.logger.warning(f"函数 {func.__name__} 执行时间较长: {execution_time:.2f}秒")
                    
                    if abs(memory_change) > 0.1:  # 内存变化超过100MB
                        self.logger.info(f"函数 {func.__name__} 内存变化: {memory_change:+.2f}GB")
                    
                    return result
                    
                except Exception as e:
                    self.logger.error(f"函数 {func.__name__} 执行出错: {e}")
                    raise
            
            return wrapper
        return decorator
    
    def get_performance_report(self) -> str:
        """获取性能报告"""
        if not self.performance_stats:
            return "暂无性能统计数据"
        
        report = "性能监控报告\n" + "="*40 + "\n\n"
        
        for func_name, stats in self.performance_stats.items():
            report += f"函数: {func_name}\n"
            report += f"  执行时间: {stats['execution_time']:.3f}秒\n"
            report += f"  内存变化: {stats['memory_change_gb']:+.3f}GB\n"
            report += f"  内存使用: {stats['start_memory_percent']:.1f}% → {stats['end_memory_percent']:.1f}%\n\n"
        
        # 系统资源概况
        memory_info = self.monitor_memory()
        report += "当前系统状态\n" + "-"*20 + "\n"
        report += f"总内存: {memory_info['total_gb']:.1f}GB\n"
        report += f"可用内存: {memory_info['available_gb']:.1f}GB\n"
        report += f"已用内存: {memory_info['used_gb']:.1f}GB ({memory_info['percent']:.1f}%)\n"
        
        return report
    
    def optimize_large_dataset(self, df: pd.DataFrame, chunk_size: int = 10000) -> pd.DataFrame:
        """优化大数据集处理"""
        if len(df) <= chunk_size:
            return self.optimize_dataframe(df)
        
        self.logger.info(f"处理大数据集，分块大小: {chunk_size}")
        
        optimized_chunks = []
        for i in range(0, len(df), chunk_size):
            chunk = df.iloc[i:i+chunk_size]
            optimized_chunk = self.optimize_dataframe(chunk)
            optimized_chunks.append(optimized_chunk)
            
            # 定期清理内存
            if i % (chunk_size * 5) == 0:
                self.clear_memory()
        
        result = pd.concat(optimized_chunks, ignore_index=True)
        self.clear_memory()
        
        return result
    
    def suggest_optimizations(self, df: pd.DataFrame) -> Dict[str, str]:
        """建议优化方案"""
        suggestions = {}
        
        if df is None or df.empty:
            return suggestions
        
        # 检查数据大小
        memory_mb = df.memory_usage(deep=True).sum() / (1024**2)
        if memory_mb > 100:
            suggestions['memory'] = f"数据集较大({memory_mb:.1f}MB)，建议进行内存优化"
        
        # 检查数据类型
        if len(df.select_dtypes(include=['object']).columns) > 0:
            suggestions['dtypes'] = "存在object类型列，建议转换为category类型以节省内存"
        
        # 检查重复数据
        duplicates = df.duplicated().sum()
        if duplicates > 0:
            suggestions['duplicates'] = f"发现{duplicates}行重复数据，建议清理"
        
        # 检查缺失值
        missing_percent = (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100
        if missing_percent > 10:
            suggestions['missing'] = f"缺失值比例较高({missing_percent:.1f}%)，建议处理"
        
        return suggestions


# 全局性能优化器实例
_global_optimizer = None


def get_performance_optimizer() -> PerformanceOptimizer:
    """获取全局性能优化器实例"""
    global _global_optimizer
    if _global_optimizer is None:
        _global_optimizer = PerformanceOptimizer()
    return _global_optimizer


def performance_monitor(func_name: str = ""):
    """性能监控装饰器的便捷函数"""
    optimizer = get_performance_optimizer()
    return optimizer.performance_monitor(func_name)
