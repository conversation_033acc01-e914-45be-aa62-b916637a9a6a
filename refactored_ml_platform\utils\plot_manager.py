#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绘图管理器
提供统一的绘图接口和样式管理
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
import numpy as np
import pandas as pd
from sklearn.metrics import (
    roc_curve, auc, precision_recall_curve, average_precision_score,
    confusion_matrix, classification_report
)
from sklearn.model_selection import learning_curve
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import warnings

from .error_handler import get_error_handler, error_handler

# 忽略matplotlib警告
warnings.filterwarnings("ignore", category=UserWarning, module="matplotlib")


class PlotManager:
    """绘图管理器"""
    
    def __init__(self):
        """初始化绘图管理器"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        
        # 设置绘图样式
        self._setup_plot_style()
        
        # 颜色配置
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#F18F01',
            'warning': '#C73E1D',
            'info': '#6C5CE7',
            'light': '#DDD6FE',
            'dark': '#2D3748'
        }
        
        # 模型颜色映射
        self.model_colors = {
            'DecisionTree': '#FF6B6B',
            'RandomForest': '#4ECDC4',
            'XGBoost': '#45B7D1',
            'LightGBM': '#96CEB4',
            'CatBoost': '#FFEAA7',
            'Logistic': '#DDA0DD',
            'SVM': '#98D8C8',
            'KNN': '#F7DC6F',
            'NaiveBayes': '#BB8FCE',
            'NeuralNet': '#85C1E9'
        }
    
    def _setup_plot_style(self):
        """设置绘图样式"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置seaborn样式
        sns.set_style("whitegrid")
        sns.set_palette("husl")
        
        # 设置matplotlib参数
        plt.rcParams['figure.figsize'] = (10, 8)
        plt.rcParams['figure.dpi'] = 100
        plt.rcParams['savefig.dpi'] = 300
        plt.rcParams['savefig.bbox'] = 'tight'
        plt.rcParams['axes.titlesize'] = 14
        plt.rcParams['axes.labelsize'] = 12
        plt.rcParams['xtick.labelsize'] = 10
        plt.rcParams['ytick.labelsize'] = 10
        plt.rcParams['legend.fontsize'] = 10
    
    @error_handler("绘制ROC曲线")
    def plot_roc_curve(self, y_true, y_pred_proba, model_name: str = None, 
                      save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制ROC曲线
        
        Args:
            y_true: 真实标签
            y_pred_proba: 预测概率
            model_name: 模型名称
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 计算ROC曲线
        fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
        roc_auc = auc(fpr, tpr)
        
        # 绘制ROC曲线
        color = self.model_colors.get(model_name, self.colors['primary'])
        ax.plot(fpr, tpr, color=color, lw=2, 
               label=f'{model_name or "Model"} (AUC = {roc_auc:.3f})')
        
        # 绘制对角线
        ax.plot([0, 1], [0, 1], color='gray', lw=1, linestyle='--', alpha=0.8)
        
        # 设置图表属性
        ax.set_xlim([0.0, 1.0])
        ax.set_ylim([0.0, 1.05])
        ax.set_xlabel('假正率 (False Positive Rate)')
        ax.set_ylabel('真正率 (True Positive Rate)')
        ax.set_title(f'ROC曲线 - {model_name or "Model"}')
        ax.legend(loc="lower right")
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"ROC曲线已保存到: {save_path}")
        
        return fig
    
    @error_handler("绘制PR曲线")
    def plot_precision_recall_curve(self, y_true, y_pred_proba, model_name: str = None,
                                   save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制精确率-召回率曲线
        
        Args:
            y_true: 真实标签
            y_pred_proba: 预测概率
            model_name: 模型名称
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 计算PR曲线
        precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
        avg_precision = average_precision_score(y_true, y_pred_proba)
        
        # 绘制PR曲线
        color = self.model_colors.get(model_name, self.colors['primary'])
        ax.plot(recall, precision, color=color, lw=2,
               label=f'{model_name or "Model"} (AP = {avg_precision:.3f})')
        
        # 绘制基线
        baseline = np.sum(y_true) / len(y_true)
        ax.axhline(y=baseline, color='gray', linestyle='--', alpha=0.8,
                  label=f'基线 (AP = {baseline:.3f})')
        
        # 设置图表属性
        ax.set_xlim([0.0, 1.0])
        ax.set_ylim([0.0, 1.05])
        ax.set_xlabel('召回率 (Recall)')
        ax.set_ylabel('精确率 (Precision)')
        ax.set_title(f'精确率-召回率曲线 - {model_name or "Model"}')
        ax.legend(loc="lower left")
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"PR曲线已保存到: {save_path}")
        
        return fig
    
    @error_handler("绘制混淆矩阵")
    def plot_confusion_matrix(self, y_true, y_pred, model_name: str = None,
                            save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制混淆矩阵热力图
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签
            model_name: 模型名称
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 计算混淆矩阵
        cm = confusion_matrix(y_true, y_pred)
        
        # 绘制热力图
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax,
                   cbar_kws={'label': '样本数量'})
        
        # 设置图表属性
        ax.set_xlabel('预测标签')
        ax.set_ylabel('真实标签')
        ax.set_title(f'混淆矩阵 - {model_name or "Model"}')
        
        # 设置标签
        unique_labels = sorted(list(set(y_true) | set(y_pred)))
        ax.set_xticklabels(unique_labels)
        ax.set_yticklabels(unique_labels)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"混淆矩阵已保存到: {save_path}")
        
        return fig
    
    @error_handler("绘制学习曲线")
    def plot_learning_curve(self, estimator, X, y, model_name: str = None,
                          cv: int = 5, save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制学习曲线
        
        Args:
            estimator: 估计器
            X: 特征数据
            y: 目标变量
            model_name: 模型名称
            cv: 交叉验证折数
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 计算学习曲线
        train_sizes, train_scores, val_scores = learning_curve(
            estimator, X, y, cv=cv, n_jobs=-1,
            train_sizes=np.linspace(0.1, 1.0, 10),
            scoring='accuracy'
        )
        
        # 计算均值和标准差
        train_mean = np.mean(train_scores, axis=1)
        train_std = np.std(train_scores, axis=1)
        val_mean = np.mean(val_scores, axis=1)
        val_std = np.std(val_scores, axis=1)
        
        # 绘制学习曲线
        color = self.model_colors.get(model_name, self.colors['primary'])
        
        ax.plot(train_sizes, train_mean, 'o-', color=color, label='训练分数')
        ax.fill_between(train_sizes, train_mean - train_std, train_mean + train_std,
                       alpha=0.1, color=color)
        
        ax.plot(train_sizes, val_mean, 'o-', color=self.colors['secondary'], label='验证分数')
        ax.fill_between(train_sizes, val_mean - val_std, val_mean + val_std,
                       alpha=0.1, color=self.colors['secondary'])
        
        # 设置图表属性
        ax.set_xlabel('训练样本数量')
        ax.set_ylabel('准确率')
        ax.set_title(f'学习曲线 - {model_name or "Model"}')
        ax.legend(loc='best')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"学习曲线已保存到: {save_path}")
        
        return fig
    
    @error_handler("绘制特征重要性")
    def plot_feature_importance(self, feature_names: List[str], importances: np.ndarray,
                              model_name: str = None, top_k: int = 20,
                              save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制特征重要性图
        
        Args:
            feature_names: 特征名称列表
            importances: 特征重要性数组
            model_name: 模型名称
            top_k: 显示前k个重要特征
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 排序特征重要性
        indices = np.argsort(importances)[::-1][:top_k]
        sorted_features = [feature_names[i] for i in indices]
        sorted_importances = importances[indices]
        
        # 绘制条形图
        color = self.model_colors.get(model_name, self.colors['primary'])
        bars = ax.barh(range(len(sorted_features)), sorted_importances, color=color, alpha=0.8)
        
        # 设置图表属性
        ax.set_yticks(range(len(sorted_features)))
        ax.set_yticklabels(sorted_features)
        ax.set_xlabel('特征重要性')
        ax.set_title(f'特征重要性 - {model_name or "Model"} (Top {top_k})')
        ax.grid(True, alpha=0.3, axis='x')
        
        # 反转y轴，使最重要的特征在顶部
        ax.invert_yaxis()
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax.text(width + 0.001, bar.get_y() + bar.get_height()/2,
                   f'{width:.3f}', ha='left', va='center', fontsize=9)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"特征重要性图已保存到: {save_path}")
        
        return fig
    
    @error_handler("绘制模型比较图")
    def plot_model_comparison(self, results: Dict[str, Dict[str, float]], 
                            metric: str = 'accuracy', save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制模型比较图
        
        Args:
            results: 模型结果字典 {model_name: {metric: value}}
            metric: 比较的指标
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 提取数据
        model_names = list(results.keys())
        values = [results[model][metric] for model in model_names]
        
        # 获取颜色
        colors = [self.model_colors.get(model, self.colors['primary']) for model in model_names]
        
        # 绘制条形图
        bars = ax.bar(model_names, values, color=colors, alpha=0.8)
        
        # 设置图表属性
        ax.set_ylabel(metric.capitalize())
        ax.set_title(f'模型性能比较 - {metric.capitalize()}')
        ax.grid(True, alpha=0.3, axis='y')
        
        # 旋转x轴标签
        plt.xticks(rotation=45, ha='right')
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                   f'{value:.3f}', ha='center', va='bottom', fontsize=10)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"模型比较图已保存到: {save_path}")
        
        return fig
    
    @error_handler("绘制多模型ROC曲线")
    def plot_multi_model_roc(self, model_results: Dict[str, Tuple[np.ndarray, np.ndarray]],
                           save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制多模型ROC曲线对比
        
        Args:
            model_results: 模型结果字典 {model_name: (y_true, y_pred_proba)}
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 绘制每个模型的ROC曲线
        for model_name, (y_true, y_pred_proba) in model_results.items():
            fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
            roc_auc = auc(fpr, tpr)
            
            color = self.model_colors.get(model_name, self.colors['primary'])
            ax.plot(fpr, tpr, color=color, lw=2,
                   label=f'{model_name} (AUC = {roc_auc:.3f})')
        
        # 绘制对角线
        ax.plot([0, 1], [0, 1], color='gray', lw=1, linestyle='--', alpha=0.8)
        
        # 设置图表属性
        ax.set_xlim([0.0, 1.0])
        ax.set_ylim([0.0, 1.05])
        ax.set_xlabel('假正率 (False Positive Rate)')
        ax.set_ylabel('真正率 (True Positive Rate)')
        ax.set_title('多模型ROC曲线对比')
        ax.legend(loc="lower right")
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"多模型ROC曲线已保存到: {save_path}")
        
        return fig
    
    def get_model_color(self, model_name: str) -> str:
        """获取模型对应的颜色"""
        return self.model_colors.get(model_name, self.colors['primary'])
    
    def save_figure(self, fig: plt.Figure, save_path: str, dpi: int = 300):
        """
        保存图形
        
        Args:
            fig: matplotlib图形对象
            save_path: 保存路径
            dpi: 分辨率
        """
        try:
            fig.savefig(save_path, dpi=dpi, bbox_inches='tight')
            self.logger.info(f"图形已保存到: {save_path}")
        except Exception as e:
            self.logger.error(f"保存图形失败: {e}")
            raise


# 全局绘图管理器实例
_plot_manager = None

def get_plot_manager() -> PlotManager:
    """
    获取全局绘图管理器实例
    
    Returns:
        绘图管理器实例
    """
    global _plot_manager
    if _plot_manager is None:
        _plot_manager = PlotManager()
    return _plot_manager
