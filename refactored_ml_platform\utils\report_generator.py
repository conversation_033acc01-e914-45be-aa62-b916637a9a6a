#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能报告生成器
生成详细的模型性能分析报告
"""

import os
import json
import base64
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from io import BytesIO

from .plot_manager import get_plot_manager
from .error_handler import get_error_handler, error_handler


class ReportGenerator:
    """性能报告生成器"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        self.plot_manager = get_plot_manager()
        
        # 报告模板
        self.html_template = self._load_html_template()
    
    def _load_html_template(self) -> str:
        """加载HTML报告模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机器学习模型性能报告</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header .subtitle {
            color: #666;
            font-size: 1.2em;
            margin-top: 10px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-left: 4px solid #2E86AB;
            padding-left: 15px;
            margin-bottom: 20px;
        }
        .section h3 {
            color: #A23B72;
            margin-bottom: 15px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .metric-card h4 {
            margin: 0 0 10px 0;
            font-size: 1.1em;
        }
        .metric-card .value {
            font-size: 2em;
            font-weight: bold;
            margin: 0;
        }
        .chart-container {
            text-align: center;
            margin: 30px 0;
        }
        .chart-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .summary-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }
        .model-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .model-card {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .model-card h4 {
            color: #2E86AB;
            margin-top: 0;
        }
        .best-model {
            border: 2px solid #F18F01;
            background: linear-gradient(135deg, #fff7e6 0%, #ffe6cc 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        {content}
    </div>
</body>
</html>
        """
    
    @error_handler("生成单模型报告")
    def generate_single_model_report(self, model_name: str, model_result: Dict[str, Any],
                                   save_path: Optional[str] = None) -> str:
        """
        生成单模型性能报告
        
        Args:
            model_name: 模型名称
            model_result: 模型结果
            save_path: 保存路径
            
        Returns:
            生成的HTML报告内容
        """
        # 生成报告内容
        content = self._generate_single_model_content(model_name, model_result)
        
        # 填充模板
        html_content = self.html_template.format(content=content)
        
        # 保存报告
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            self.logger.info(f"单模型报告已保存到: {save_path}")
        
        return html_content
    
    @error_handler("生成模型比较报告")
    def generate_comparison_report(self, model_results: Dict[str, Dict[str, Any]],
                                 save_path: Optional[str] = None) -> str:
        """
        生成模型比较报告
        
        Args:
            model_results: 模型结果字典
            save_path: 保存路径
            
        Returns:
            生成的HTML报告内容
        """
        # 生成报告内容
        content = self._generate_comparison_content(model_results)
        
        # 填充模板
        html_content = self.html_template.format(content=content)
        
        # 保存报告
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            self.logger.info(f"模型比较报告已保存到: {save_path}")
        
        return html_content
    
    def _generate_single_model_content(self, model_name: str, model_result: Dict[str, Any]) -> str:
        """生成单模型报告内容"""
        # 报告头部
        header = f"""
        <div class="header">
            <h1>机器学习模型性能报告</h1>
            <div class="subtitle">模型: {model_name}</div>
            <div class="subtitle">生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
        </div>
        """
        
        # 性能指标摘要
        metrics_section = self._generate_metrics_section(model_result)
        
        # 图表部分
        charts_section = self._generate_charts_section(model_name, model_result)
        
        # 详细分析
        analysis_section = self._generate_analysis_section(model_result)
        
        # 模型信息
        model_info_section = self._generate_model_info_section(model_result)
        
        # 页脚
        footer = f"""
        <div class="footer">
            <p>报告由机器学习平台自动生成 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        """
        
        return header + metrics_section + charts_section + analysis_section + model_info_section + footer
    
    def _generate_comparison_content(self, model_results: Dict[str, Dict[str, Any]]) -> str:
        """生成模型比较报告内容"""
        # 报告头部
        header = f"""
        <div class="header">
            <h1>机器学习模型比较报告</h1>
            <div class="subtitle">比较模型数量: {len(model_results)}</div>
            <div class="subtitle">生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
        </div>
        """
        
        # 模型比较摘要
        summary_section = self._generate_comparison_summary(model_results)
        
        # 性能比较表格
        comparison_table = self._generate_comparison_table(model_results)
        
        # 比较图表
        comparison_charts = self._generate_comparison_charts(model_results)
        
        # 最佳模型推荐
        recommendation_section = self._generate_recommendation_section(model_results)
        
        # 页脚
        footer = f"""
        <div class="footer">
            <p>报告由机器学习平台自动生成 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        """
        
        return header + summary_section + comparison_table + comparison_charts + recommendation_section + footer
    
    def _generate_metrics_section(self, model_result: Dict[str, Any]) -> str:
        """生成性能指标部分"""
        metrics = model_result.get('metrics', {})
        
        # 如果没有metrics字段，从结果中直接提取
        if not metrics:
            metrics = {
                'accuracy': model_result.get('accuracy', 0),
                'auc': model_result.get('auc', 0),
                'f1_score': model_result.get('f1_score', 0),
                'precision': model_result.get('precision', 0),
                'recall': model_result.get('recall', 0)
            }
        
        metrics_html = """
        <div class="section">
            <h2>📊 性能指标</h2>
            <div class="metrics-grid">
        """
        
        metric_names = {
            'accuracy': '准确率',
            'auc': 'AUC',
            'f1_score': 'F1分数',
            'precision': '精确率',
            'recall': '召回率'
        }
        
        for key, name in metric_names.items():
            value = metrics.get(key, 0)
            metrics_html += f"""
                <div class="metric-card">
                    <h4>{name}</h4>
                    <p class="value">{value:.4f}</p>
                </div>
            """
        
        metrics_html += """
            </div>
        </div>
        """
        
        return metrics_html
    
    def _generate_charts_section(self, model_name: str, model_result: Dict[str, Any]) -> str:
        """生成图表部分"""
        charts_html = """
        <div class="section">
            <h2>📈 可视化分析</h2>
        """
        
        # 生成ROC曲线
        y_true = model_result.get('y_true') or model_result.get('y_test')
        y_pred_proba = model_result.get('y_pred_proba')

        if (y_true is not None and len(y_true) > 0 and
            y_pred_proba is not None and len(y_pred_proba) > 0):
            try:
                # 生成ROC曲线
                fig = self.plot_manager.plot_roc_curve(y_true, y_pred_proba, model_name)
                roc_img = self._fig_to_base64(fig)
                plt.close(fig)
                
                charts_html += f"""
                <div class="chart-container">
                    <h3>ROC曲线</h3>
                    <img src="data:image/png;base64,{roc_img}" alt="ROC曲线">
                </div>
                """
            except Exception as e:
                self.logger.warning(f"生成ROC曲线失败: {e}")
        
        # 生成混淆矩阵
        y_pred = model_result.get('y_pred')
        if (y_true is not None and len(y_true) > 0 and
            y_pred is not None and len(y_pred) > 0):
            try:
                fig = self.plot_manager.plot_confusion_matrix(y_true, y_pred, model_name)
                cm_img = self._fig_to_base64(fig)
                plt.close(fig)
                
                charts_html += f"""
                <div class="chart-container">
                    <h3>混淆矩阵</h3>
                    <img src="data:image/png;base64,{cm_img}" alt="混淆矩阵">
                </div>
                """
            except Exception as e:
                self.logger.warning(f"生成混淆矩阵失败: {e}")
        
        charts_html += "</div>"
        return charts_html
    
    def _generate_analysis_section(self, model_result: Dict[str, Any]) -> str:
        """生成详细分析部分"""
        training_time = model_result.get('training_time', 0)
        
        analysis_html = f"""
        <div class="section">
            <h2>🔍 详细分析</h2>
            <div class="summary-box">
                <h3>训练信息</h3>
                <p><strong>训练时间:</strong> {training_time:.2f} 秒</p>
                <p><strong>数据集大小:</strong> {model_result.get('data_size', '未知')}</p>
                <p><strong>特征数量:</strong> {len(model_result.get('feature_names', []))}</p>
            </div>
        </div>
        """
        
        return analysis_html
    
    def _generate_model_info_section(self, model_result: Dict[str, Any]) -> str:
        """生成模型信息部分"""
        model_info_html = """
        <div class="section">
            <h2>ℹ️ 模型信息</h2>
            <div class="table-container">
                <table>
                    <tr><th>属性</th><th>值</th></tr>
        """
        
        # 添加模型信息
        info_items = [
            ('模型类型', model_result.get('model_name', '未知')),
            ('训练时间', f"{model_result.get('training_time', 0):.2f} 秒"),
            ('特征数量', len(model_result.get('feature_names', []))),
            ('参数数量', model_result.get('param_count', '未知'))
        ]
        
        for key, value in info_items:
            model_info_html += f"<tr><td>{key}</td><td>{value}</td></tr>"
        
        model_info_html += """
                </table>
            </div>
        </div>
        """
        
        return model_info_html
    
    def _generate_comparison_summary(self, model_results: Dict[str, Dict[str, Any]]) -> str:
        """生成比较摘要"""
        # 找出最佳模型
        best_model = max(model_results.keys(), 
                        key=lambda x: model_results[x].get('accuracy', 0))
        
        summary_html = f"""
        <div class="section">
            <h2>📋 比较摘要</h2>
            <div class="summary-box">
                <h3>最佳模型: {best_model}</h3>
                <p>基于准确率评估，{best_model} 表现最佳</p>
                <p>准确率: {model_results[best_model].get('accuracy', 0):.4f}</p>
            </div>
        </div>
        """
        
        return summary_html
    
    def _generate_comparison_table(self, model_results: Dict[str, Dict[str, Any]]) -> str:
        """生成比较表格"""
        table_html = """
        <div class="section">
            <h2>📊 性能比较表</h2>
            <div class="table-container">
                <table>
                    <tr>
                        <th>模型</th>
                        <th>准确率</th>
                        <th>AUC</th>
                        <th>F1分数</th>
                        <th>精确率</th>
                        <th>召回率</th>
                        <th>训练时间(秒)</th>
                    </tr>
        """
        
        for model_name, result in model_results.items():
            metrics = result.get('metrics', {})
            if not metrics:
                metrics = {
                    'accuracy': result.get('accuracy', 0),
                    'auc': result.get('auc', 0),
                    'f1_score': result.get('f1_score', 0),
                    'precision': result.get('precision', 0),
                    'recall': result.get('recall', 0)
                }
            
            table_html += f"""
                <tr>
                    <td><strong>{model_name}</strong></td>
                    <td>{metrics.get('accuracy', 0):.4f}</td>
                    <td>{metrics.get('auc', 0):.4f}</td>
                    <td>{metrics.get('f1_score', 0):.4f}</td>
                    <td>{metrics.get('precision', 0):.4f}</td>
                    <td>{metrics.get('recall', 0):.4f}</td>
                    <td>{result.get('training_time', 0):.2f}</td>
                </tr>
            """
        
        table_html += """
                </table>
            </div>
        </div>
        """
        
        return table_html
    
    def _generate_comparison_charts(self, model_results: Dict[str, Dict[str, Any]]) -> str:
        """生成比较图表"""
        charts_html = """
        <div class="section">
            <h2>📈 比较图表</h2>
        """
        
        try:
            # 生成性能比较图
            comparison_data = {}
            for model_name, result in model_results.items():
                comparison_data[model_name] = {'accuracy': result.get('accuracy', 0)}
            
            fig = self.plot_manager.plot_model_comparison(comparison_data, 'accuracy')
            comparison_img = self._fig_to_base64(fig)
            plt.close(fig)
            
            charts_html += f"""
            <div class="chart-container">
                <h3>模型准确率比较</h3>
                <img src="data:image/png;base64,{comparison_img}" alt="模型比较图">
            </div>
            """
        except Exception as e:
            self.logger.warning(f"生成比较图表失败: {e}")
        
        charts_html += "</div>"
        return charts_html
    
    def _generate_recommendation_section(self, model_results: Dict[str, Dict[str, Any]]) -> str:
        """生成推荐部分"""
        # 基于不同指标找出最佳模型
        best_accuracy = max(model_results.keys(), key=lambda x: model_results[x].get('accuracy', 0))
        best_auc = max(model_results.keys(), key=lambda x: model_results[x].get('auc', 0))
        
        recommendation_html = f"""
        <div class="section">
            <h2>🎯 模型推荐</h2>
            <div class="model-comparison">
                <div class="model-card best-model">
                    <h4>🏆 最佳准确率模型</h4>
                    <p><strong>{best_accuracy}</strong></p>
                    <p>准确率: {model_results[best_accuracy].get('accuracy', 0):.4f}</p>
                </div>
                <div class="model-card">
                    <h4>📊 最佳AUC模型</h4>
                    <p><strong>{best_auc}</strong></p>
                    <p>AUC: {model_results[best_auc].get('auc', 0):.4f}</p>
                </div>
            </div>
        </div>
        """
        
        return recommendation_html
    
    def _fig_to_base64(self, fig) -> str:
        """将matplotlib图形转换为base64字符串"""
        buffer = BytesIO()
        fig.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        buffer.close()
        return image_base64

    @error_handler("生成会话报告")
    def generate_session_report(self, session_data: Dict[str, Any],
                              save_path: Optional[str] = None) -> str:
        """
        生成会话报告

        Args:
            session_data: 会话数据
            save_path: 保存路径

        Returns:
            生成的HTML报告内容
        """
        # 生成会话报告内容
        content = self._generate_session_content(session_data)

        # 填充模板
        html_content = self.html_template.format(content=content)

        # 保存报告
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            self.logger.info(f"会话报告已保存到: {save_path}")

        return html_content

    @error_handler("生成训练报告")
    def generate_training_report(self, training_data: Dict[str, Any],
                               save_path: Optional[str] = None) -> str:
        """
        生成训练报告

        Args:
            training_data: 训练数据
            save_path: 保存路径

        Returns:
            生成的HTML报告内容
        """
        # 生成训练报告内容
        content = self._generate_training_content(training_data)

        # 填充模板
        html_content = self.html_template.format(content=content)

        # 保存报告
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            self.logger.info(f"训练报告已保存到: {save_path}")

        return html_content

    @error_handler("生成验证报告")
    def generate_validation_report(self, validation_data: Dict[str, Any],
                                 save_path: Optional[str] = None) -> str:
        """
        生成验证报告

        Args:
            validation_data: 验证数据
            save_path: 保存路径

        Returns:
            生成的HTML报告内容
        """
        # 生成验证报告内容
        content = self._generate_validation_content(validation_data)

        # 填充模板
        html_content = self.html_template.format(content=content)

        # 保存报告
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            self.logger.info(f"验证报告已保存到: {save_path}")

        return html_content

    @error_handler("生成综合报告")
    def generate_comprehensive_report(self, project_data: Dict[str, Any],
                                    save_path: Optional[str] = None) -> str:
        """
        生成综合项目报告

        Args:
            project_data: 项目数据
            save_path: 保存路径

        Returns:
            生成的HTML报告内容
        """
        # 生成综合报告内容
        content = self._generate_comprehensive_content(project_data)

        # 填充模板
        html_content = self.html_template.format(content=content)

        # 保存报告
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            self.logger.info(f"综合报告已保存到: {save_path}")

        return html_content

    @error_handler("自动生成报告")
    def auto_generate_reports(self, data: Dict[str, Any], output_dir: str) -> List[str]:
        """
        自动生成所有类型的报告

        Args:
            data: 包含所有数据的字典
            output_dir: 输出目录

        Returns:
            生成的报告文件路径列表
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        generated_reports = []
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        try:
            # 生成模型比较报告
            if 'model_results' in data and len(data['model_results']) > 1:
                report_path = output_dir / f"model_comparison_{timestamp}.html"
                self.generate_comparison_report(data['model_results'], str(report_path))
                generated_reports.append(str(report_path))

            # 生成单模型报告
            if 'model_results' in data:
                for model_name, model_result in data['model_results'].items():
                    report_path = output_dir / f"single_model_{model_name}_{timestamp}.html"
                    self.generate_single_model_report(model_name, model_result, str(report_path))
                    generated_reports.append(str(report_path))

            # 生成会话报告
            if 'session_data' in data:
                report_path = output_dir / f"session_report_{timestamp}.html"
                self.generate_session_report(data['session_data'], str(report_path))
                generated_reports.append(str(report_path))

            # 生成训练报告
            if 'training_data' in data:
                report_path = output_dir / f"training_report_{timestamp}.html"
                self.generate_training_report(data['training_data'], str(report_path))
                generated_reports.append(str(report_path))

            # 生成验证报告
            if 'validation_data' in data:
                report_path = output_dir / f"validation_report_{timestamp}.html"
                self.generate_validation_report(data['validation_data'], str(report_path))
                generated_reports.append(str(report_path))

            # 生成综合报告
            if len(generated_reports) > 0:
                comprehensive_path = output_dir / f"comprehensive_report_{timestamp}.html"
                self.generate_comprehensive_report(data, str(comprehensive_path))
                generated_reports.append(str(comprehensive_path))

            self.logger.info(f"自动生成了 {len(generated_reports)} 个报告")
            return generated_reports

        except Exception as e:
            self.logger.error(f"自动生成报告失败: {e}")
            raise

    def _generate_session_content(self, session_data: Dict[str, Any]) -> str:
        """生成会话报告内容"""
        session_name = session_data.get('session_name', '未知会话')

        # 报告头部
        header = f"""
        <div class="header">
            <h1>训练会话报告</h1>
            <div class="subtitle">会话: {session_name}</div>
            <div class="subtitle">生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
        </div>
        """

        # 会话摘要
        summary_section = f"""
        <div class="section">
            <h2>📋 会话摘要</h2>
            <div class="summary-box">
                <p><strong>会话ID:</strong> {session_data.get('session_id', '未知')}</p>
                <p><strong>创建时间:</strong> {session_data.get('created_time', '未知')}</p>
                <p><strong>模型数量:</strong> {session_data.get('model_count', 0)}</p>
                <p><strong>结果数量:</strong> {session_data.get('result_count', 0)}</p>
            </div>
        </div>
        """

        # 页脚
        footer = f"""
        <div class="footer">
            <p>报告由机器学习平台自动生成 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        """

        return header + summary_section + footer

    def _generate_training_content(self, training_data: Dict[str, Any]) -> str:
        """生成训练报告内容"""
        # 报告头部
        header = f"""
        <div class="header">
            <h1>模型训练报告</h1>
            <div class="subtitle">训练任务: {training_data.get('task_name', '未知任务')}</div>
            <div class="subtitle">生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
        </div>
        """

        # 训练摘要
        summary_section = f"""
        <div class="section">
            <h2>📋 训练摘要</h2>
            <div class="summary-box">
                <p><strong>训练开始时间:</strong> {training_data.get('start_time', '未知')}</p>
                <p><strong>训练结束时间:</strong> {training_data.get('end_time', '未知')}</p>
                <p><strong>总训练时间:</strong> {training_data.get('total_time', 0):.2f} 秒</p>
                <p><strong>训练模型数量:</strong> {training_data.get('model_count', 0)}</p>
                <p><strong>数据集大小:</strong> {training_data.get('dataset_size', '未知')}</p>
            </div>
        </div>
        """

        # 训练参数
        params_section = self._generate_training_params_section(training_data)

        # 训练过程
        process_section = self._generate_training_process_section(training_data)

        # 页脚
        footer = f"""
        <div class="footer">
            <p>报告由机器学习平台自动生成 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        """

        return header + summary_section + params_section + process_section + footer

    def _generate_validation_content(self, validation_data: Dict[str, Any]) -> str:
        """生成验证报告内容"""
        # 报告头部
        header = f"""
        <div class="header">
            <h1>模型验证报告</h1>
            <div class="subtitle">验证任务: {validation_data.get('task_name', '未知任务')}</div>
            <div class="subtitle">生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
        </div>
        """

        # 验证摘要
        summary_section = f"""
        <div class="section">
            <h2>📋 验证摘要</h2>
            <div class="summary-box">
                <p><strong>验证类型:</strong> {validation_data.get('validation_type', '未知')}</p>
                <p><strong>验证数据集:</strong> {validation_data.get('validation_dataset', '未知')}</p>
                <p><strong>验证样本数:</strong> {validation_data.get('sample_count', 0)}</p>
                <p><strong>验证时间:</strong> {validation_data.get('validation_time', 0):.2f} 秒</p>
            </div>
        </div>
        """

        # 验证结果
        results_section = self._generate_validation_results_section(validation_data)

        # 页脚
        footer = f"""
        <div class="footer">
            <p>报告由机器学习平台自动生成 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        """

        return header + summary_section + results_section + footer

    def _generate_comprehensive_content(self, project_data: Dict[str, Any]) -> str:
        """生成综合报告内容"""
        # 报告头部
        header = f"""
        <div class="header">
            <h1>机器学习项目综合报告</h1>
            <div class="subtitle">项目: {project_data.get('project_name', '未知项目')}</div>
            <div class="subtitle">生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
        </div>
        """

        # 项目概览
        overview_section = self._generate_project_overview_section(project_data)

        # 模型性能总结
        performance_section = self._generate_performance_summary_section(project_data)

        # 最佳实践建议
        recommendations_section = self._generate_recommendations_section(project_data)

        # 页脚
        footer = f"""
        <div class="footer">
            <p>报告由机器学习平台自动生成 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        """

        return header + overview_section + performance_section + recommendations_section + footer

    def _generate_training_params_section(self, training_data: Dict[str, Any]) -> str:
        """生成训练参数部分"""
        params = training_data.get('parameters', {})

        params_html = """
        <div class="section">
            <h2>⚙️ 训练参数</h2>
            <div class="table-container">
                <table>
                    <tr><th>参数名</th><th>参数值</th></tr>
        """

        for param_name, param_value in params.items():
            params_html += f"<tr><td>{param_name}</td><td>{param_value}</td></tr>"

        params_html += """
                </table>
            </div>
        </div>
        """

        return params_html

    def _generate_training_process_section(self, training_data: Dict[str, Any]) -> str:
        """生成训练过程部分"""
        process_html = """
        <div class="section">
            <h2>🔄 训练过程</h2>
        """

        # 训练日志
        logs = training_data.get('training_logs', [])
        if logs:
            process_html += """
            <div class="table-container">
                <table>
                    <tr><th>时间</th><th>事件</th><th>详情</th></tr>
            """

            for log in logs[-10:]:  # 显示最后10条日志
                process_html += f"""
                <tr>
                    <td>{log.get('timestamp', '')}</td>
                    <td>{log.get('event', '')}</td>
                    <td>{log.get('details', '')}</td>
                </tr>
                """

            process_html += """
                </table>
            </div>
            """

        process_html += "</div>"
        return process_html

    def _generate_validation_results_section(self, validation_data: Dict[str, Any]) -> str:
        """生成验证结果部分"""
        results = validation_data.get('results', {})

        results_html = """
        <div class="section">
            <h2>📊 验证结果</h2>
            <div class="metrics-grid">
        """

        metric_names = {
            'accuracy': '准确率',
            'precision': '精确率',
            'recall': '召回率',
            'f1_score': 'F1分数',
            'auc': 'AUC'
        }

        for key, name in metric_names.items():
            value = results.get(key, 0)
            results_html += f"""
                <div class="metric-card">
                    <h4>{name}</h4>
                    <p class="value">{value:.4f}</p>
                </div>
            """

        results_html += """
            </div>
        </div>
        """

        return results_html


    def _generate_project_overview_section(self, project_data: Dict[str, Any]) -> str:
        """生成项目概览部分"""
        overview_html = f"""
        <div class="section">
            <h2>📋 项目概览</h2>
            <div class="summary-box">
                <p><strong>项目名称:</strong> {project_data.get('project_name', '未知项目')}</p>
                <p><strong>创建时间:</strong> {project_data.get('created_time', '未知')}</p>
                <p><strong>数据集:</strong> {project_data.get('dataset_name', '未知')}</p>
                <p><strong>问题类型:</strong> {project_data.get('problem_type', '未知')}</p>
                <p><strong>训练模型数:</strong> {len(project_data.get('model_results', {}))}</p>
                <p><strong>总训练时间:</strong> {project_data.get('total_training_time', 0):.2f} 秒</p>
            </div>
        </div>
        """
        return overview_html

    def _generate_performance_summary_section(self, project_data: Dict[str, Any]) -> str:
        """生成性能总结部分"""
        model_results = project_data.get('model_results', {})

        if not model_results:
            return """
            <div class="section">
                <h2>📊 性能总结</h2>
                <p>暂无模型结果数据</p>
            </div>
            """

        # 找出最佳模型
        best_model = max(model_results.keys(),
                        key=lambda x: model_results[x].get('accuracy', 0))

        performance_html = f"""
        <div class="section">
            <h2>📊 性能总结</h2>
            <div class="model-comparison">
                <div class="model-card best-model">
                    <h4>🏆 最佳模型</h4>
                    <p><strong>{best_model}</strong></p>
                    <p>准确率: {model_results[best_model].get('accuracy', 0):.4f}</p>
                    <p>AUC: {model_results[best_model].get('auc', 0):.4f}</p>
                </div>
        """

        # 添加其他模型的简要信息
        other_models = [name for name in model_results.keys() if name != best_model]
        for model_name in other_models[:3]:  # 最多显示3个其他模型
            result = model_results[model_name]
            performance_html += f"""
                <div class="model-card">
                    <h4>{model_name}</h4>
                    <p>准确率: {result.get('accuracy', 0):.4f}</p>
                    <p>AUC: {result.get('auc', 0):.4f}</p>
                </div>
            """

        performance_html += """
            </div>
        </div>
        """

        return performance_html

    def _generate_recommendations_section(self, project_data: Dict[str, Any]) -> str:
        """生成建议部分"""
        model_results = project_data.get('model_results', {})

        recommendations = []

        if model_results:
            # 基于结果生成建议
            accuracies = [result.get('accuracy', 0) for result in model_results.values()]
            avg_accuracy = sum(accuracies) / len(accuracies)

            if avg_accuracy < 0.7:
                recommendations.append("模型整体准确率较低，建议检查数据质量和特征工程")
            elif avg_accuracy > 0.9:
                recommendations.append("模型性能优秀，可以考虑部署到生产环境")

            # 检查模型多样性
            if len(model_results) > 1:
                accuracy_std = np.std(accuracies)
                if accuracy_std < 0.01:
                    recommendations.append("模型间性能差异较小，可以考虑集成学习")
                else:
                    recommendations.append("模型间性能差异较大，建议分析差异原因")

        # 默认建议
        if not recommendations:
            recommendations = [
                "建议进行更多的特征工程和数据预处理",
                "可以尝试不同的超参数调优策略",
                "考虑使用交叉验证来评估模型稳定性"
            ]

        recommendations_html = """
        <div class="section">
            <h2>💡 建议与改进</h2>
            <div class="summary-box">
                <h3>优化建议</h3>
                <ul>
        """

        for rec in recommendations:
            recommendations_html += f"<li>{rec}</li>"

        recommendations_html += """
                </ul>
            </div>
        </div>
        """

        return recommendations_html


# 全局报告生成器实例
_report_generator = None

def get_report_generator() -> ReportGenerator:
    """
    获取全局报告生成器实例

    Returns:
        报告生成器实例
    """
    global _report_generator
    if _report_generator is None:
        _report_generator = ReportGenerator()
    return _report_generator
