#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化报告调度器
定期生成和发送报告
"""

import os
import time
import threading
import schedule
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
import logging

from .report_generator import get_report_generator
from .error_handler import get_error_handler, error_handler


class ReportScheduler:
    """自动化报告调度器"""
    
    def __init__(self):
        """初始化调度器"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        self.report_generator = get_report_generator()
        
        # 调度状态
        self.is_running = False
        self.scheduler_thread = None
        
        # 任务列表
        self.scheduled_tasks = {}
        
        # 默认输出目录
        self.default_output_dir = Path("output/reports/scheduled")
        self.default_output_dir.mkdir(parents=True, exist_ok=True)
    
    @error_handler("启动调度器")
    def start(self):
        """启动调度器"""
        if self.is_running:
            self.logger.warning("调度器已在运行")
            return
        
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        self.logger.info("报告调度器已启动")
    
    def stop(self):
        """停止调度器"""
        self.is_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        self.logger.info("报告调度器已停止")
    
    def _run_scheduler(self):
        """运行调度器"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                self.error_handler.handle_error(e, "调度器运行")
                time.sleep(60)
    
    @error_handler("添加每日报告任务")
    def schedule_daily_report(self, time_str: str, data_provider: Callable,
                            report_types: List[str] = None, 
                            output_dir: Optional[str] = None) -> str:
        """
        添加每日报告任务
        
        Args:
            time_str: 时间字符串，格式如 "09:00"
            data_provider: 数据提供函数
            report_types: 报告类型列表
            output_dir: 输出目录
            
        Returns:
            任务ID
        """
        if report_types is None:
            report_types = ["comprehensive_report"]
        
        if output_dir is None:
            output_dir = str(self.default_output_dir)
        
        task_id = f"daily_{time_str.replace(':', '')}"
        
        def job():
            self._generate_scheduled_reports(data_provider, report_types, output_dir, "daily")
        
        schedule.every().day.at(time_str).do(job)
        
        self.scheduled_tasks[task_id] = {
            'type': 'daily',
            'time': time_str,
            'report_types': report_types,
            'output_dir': output_dir,
            'data_provider': data_provider
        }
        
        self.logger.info(f"已添加每日报告任务: {task_id}")
        return task_id
    
    @error_handler("添加每周报告任务")
    def schedule_weekly_report(self, day: str, time_str: str, data_provider: Callable,
                             report_types: List[str] = None,
                             output_dir: Optional[str] = None) -> str:
        """
        添加每周报告任务
        
        Args:
            day: 星期几，如 "monday"
            time_str: 时间字符串
            data_provider: 数据提供函数
            report_types: 报告类型列表
            output_dir: 输出目录
            
        Returns:
            任务ID
        """
        if report_types is None:
            report_types = ["comprehensive_report", "model_comparison"]
        
        if output_dir is None:
            output_dir = str(self.default_output_dir)
        
        task_id = f"weekly_{day}_{time_str.replace(':', '')}"
        
        def job():
            self._generate_scheduled_reports(data_provider, report_types, output_dir, "weekly")
        
        getattr(schedule.every(), day.lower()).at(time_str).do(job)
        
        self.scheduled_tasks[task_id] = {
            'type': 'weekly',
            'day': day,
            'time': time_str,
            'report_types': report_types,
            'output_dir': output_dir,
            'data_provider': data_provider
        }
        
        self.logger.info(f"已添加每周报告任务: {task_id}")
        return task_id
    
    @error_handler("添加训练完成报告任务")
    def schedule_training_completion_report(self, data_provider: Callable,
                                          output_dir: Optional[str] = None) -> str:
        """
        添加训练完成后自动生成报告的任务
        
        Args:
            data_provider: 数据提供函数
            output_dir: 输出目录
            
        Returns:
            任务ID
        """
        if output_dir is None:
            output_dir = str(self.default_output_dir)
        
        task_id = f"training_completion_{int(time.time())}"
        
        self.scheduled_tasks[task_id] = {
            'type': 'training_completion',
            'report_types': ['training_report', 'single_model'],
            'output_dir': output_dir,
            'data_provider': data_provider
        }
        
        self.logger.info(f"已添加训练完成报告任务: {task_id}")
        return task_id
    
    def trigger_training_completion_report(self, task_id: str):
        """触发训练完成报告"""
        if task_id not in self.scheduled_tasks:
            self.logger.warning(f"未找到任务: {task_id}")
            return
        
        task = self.scheduled_tasks[task_id]
        if task['type'] != 'training_completion':
            self.logger.warning(f"任务类型不匹配: {task_id}")
            return
        
        # 在后台线程中生成报告
        threading.Thread(
            target=self._generate_scheduled_reports,
            args=(task['data_provider'], task['report_types'], 
                  task['output_dir'], 'training_completion'),
            daemon=True
        ).start()
    
    def _generate_scheduled_reports(self, data_provider: Callable, 
                                  report_types: List[str], 
                                  output_dir: str, 
                                  schedule_type: str):
        """生成调度报告"""
        try:
            self.logger.info(f"开始生成 {schedule_type} 调度报告")
            
            # 获取数据
            data = data_provider()
            if not data:
                self.logger.warning("数据提供函数返回空数据")
                return
            
            # 创建输出目录
            output_path = Path(output_dir) / schedule_type / datetime.now().strftime('%Y%m%d')
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 生成报告
            generated_reports = []
            
            for report_type in report_types:
                try:
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    report_file = output_path / f"{report_type}_{timestamp}.html"
                    
                    if report_type == "single_model" and 'model_results' in data:
                        for model_name, model_result in data['model_results'].items():
                            model_file = output_path / f"single_model_{model_name}_{timestamp}.html"
                            self.report_generator.generate_single_model_report(
                                model_name, model_result, str(model_file)
                            )
                            generated_reports.append(str(model_file))
                    
                    elif report_type == "model_comparison" and 'model_results' in data:
                        if len(data['model_results']) > 1:
                            self.report_generator.generate_comparison_report(
                                data['model_results'], str(report_file)
                            )
                            generated_reports.append(str(report_file))
                    
                    elif report_type == "comprehensive_report":
                        self.report_generator.generate_comprehensive_report(
                            data, str(report_file)
                        )
                        generated_reports.append(str(report_file))
                    
                    elif report_type == "training_report" and 'training_data' in data:
                        self.report_generator.generate_training_report(
                            data['training_data'], str(report_file)
                        )
                        generated_reports.append(str(report_file))
                    
                    elif report_type == "validation_report" and 'validation_data' in data:
                        self.report_generator.generate_validation_report(
                            data['validation_data'], str(report_file)
                        )
                        generated_reports.append(str(report_file))
                    
                    elif report_type == "session_report" and 'session_data' in data:
                        self.report_generator.generate_session_report(
                            data['session_data'], str(report_file)
                        )
                        generated_reports.append(str(report_file))
                
                except Exception as e:
                    self.logger.error(f"生成 {report_type} 报告失败: {e}")
            
            self.logger.info(f"成功生成 {len(generated_reports)} 个调度报告")
            
            # 可以在这里添加邮件发送或其他通知功能
            self._notify_report_generated(generated_reports, schedule_type)
            
        except Exception as e:
            self.error_handler.handle_error(e, f"生成{schedule_type}调度报告")
    
    def _notify_report_generated(self, report_files: List[str], schedule_type: str):
        """通知报告已生成"""
        # 这里可以实现邮件发送、消息推送等功能
        self.logger.info(f"{schedule_type} 报告生成完成，共 {len(report_files)} 个文件")
    
    def remove_task(self, task_id: str):
        """移除任务"""
        if task_id in self.scheduled_tasks:
            del self.scheduled_tasks[task_id]
            # 清除schedule中的任务需要重新设计schedule的使用方式
            self.logger.info(f"已移除任务: {task_id}")
        else:
            self.logger.warning(f"未找到任务: {task_id}")
    
    def list_tasks(self) -> Dict[str, Dict]:
        """列出所有任务"""
        return self.scheduled_tasks.copy()
    
    def get_task_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        return {
            'is_running': self.is_running,
            'task_count': len(self.scheduled_tasks),
            'next_run': schedule.next_run() if schedule.jobs else None,
            'tasks': list(self.scheduled_tasks.keys())
        }


# 全局调度器实例
_report_scheduler = None

def get_report_scheduler() -> ReportScheduler:
    """
    获取全局报告调度器实例
    
    Returns:
        报告调度器实例
    """
    global _report_scheduler
    if _report_scheduler is None:
        _report_scheduler = ReportScheduler()
    return _report_scheduler
