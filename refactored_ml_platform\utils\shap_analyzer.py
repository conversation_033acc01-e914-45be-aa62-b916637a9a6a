#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHAP可解释性分析模块
提供模型可解释性分析功能
"""

import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Optional, Dict, Any, List, Tuple
import warnings

# 尝试导入SHAP，如果失败则提供替代方案
try:
    import shap
    HAS_SHAP = True
except ImportError:
    HAS_SHAP = False
    warnings.warn("SHAP未安装，将使用简化的特征重要性分析")

from .error_handler import error_handler, get_error_handler


class ShapAnalyzer:
    """SHAP分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        self.explainer = None
        self.shap_values = None
        self.feature_names = None
        
    @error_handler("SHAP分析器初始化")
    def initialize_explainer(self, model, X_train: pd.DataFrame, model_type: str = "auto") -> bool:
        """
        初始化SHAP解释器
        
        Args:
            model: 训练好的模型
            X_train: 训练数据
            model_type: 模型类型 ("tree", "linear", "kernel", "auto")
            
        Returns:
            是否初始化成功
        """
        if not HAS_SHAP:
            self.logger.warning("SHAP未安装，无法进行详细的可解释性分析")
            return False
        
        try:
            self.feature_names = list(X_train.columns)
            
            # 根据模型类型选择合适的解释器
            if model_type == "auto":
                model_type = self._detect_model_type(model)
            
            if model_type == "tree":
                self.explainer = shap.TreeExplainer(model)
            elif model_type == "linear":
                self.explainer = shap.LinearExplainer(model, X_train)
            elif model_type == "kernel":
                # 对于复杂模型使用KernelExplainer（较慢但通用）
                background = shap.sample(X_train, min(100, len(X_train)))
                self.explainer = shap.KernelExplainer(model.predict, background)
            else:
                # 默认使用Explainer（自动选择）
                self.explainer = shap.Explainer(model, X_train)
            
            self.logger.info(f"SHAP解释器初始化成功，类型: {model_type}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, "初始化SHAP解释器")
            return False
    
    def _detect_model_type(self, model) -> str:
        """自动检测模型类型"""
        model_name = type(model).__name__.lower()
        
        if any(tree_type in model_name for tree_type in ['tree', 'forest', 'xgb', 'lgb', 'catboost']):
            return "tree"
        elif any(linear_type in model_name for linear_type in ['linear', 'logistic', 'ridge', 'lasso']):
            return "linear"
        else:
            return "kernel"
    
    @error_handler("计算SHAP值")
    def calculate_shap_values(self, X_test: pd.DataFrame, max_samples: int = 100) -> bool:
        """
        计算SHAP值
        
        Args:
            X_test: 测试数据
            max_samples: 最大样本数（避免计算时间过长）
            
        Returns:
            是否计算成功
        """
        if not HAS_SHAP or self.explainer is None:
            return False
        
        try:
            # 限制样本数量以提高计算速度
            if len(X_test) > max_samples:
                X_sample = X_test.sample(n=max_samples, random_state=42)
                self.logger.info(f"为提高计算速度，随机采样 {max_samples} 个样本进行SHAP分析")
            else:
                X_sample = X_test
            
            # 计算SHAP值
            self.shap_values = self.explainer.shap_values(X_sample)
            
            # 如果是多分类问题，取第一个类别的SHAP值
            if isinstance(self.shap_values, list):
                self.shap_values = self.shap_values[0]
            
            self.logger.info(f"SHAP值计算完成，样本数: {len(X_sample)}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, "计算SHAP值")
            return False
    
    @error_handler("生成SHAP摘要图")
    def create_summary_plot(self, X_test: pd.DataFrame, save_path: Optional[str] = None) -> Optional[plt.Figure]:
        """
        创建SHAP摘要图
        
        Args:
            X_test: 测试数据
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        if not HAS_SHAP or self.shap_values is None:
            return self._create_fallback_importance_plot(X_test)
        
        try:
            plt.figure(figsize=(10, 6))
            shap.summary_plot(self.shap_values, X_test, feature_names=self.feature_names, show=False)
            
            fig = plt.gcf()
            plt.title("SHAP特征重要性摘要图", fontsize=14, pad=20)
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"SHAP摘要图已保存到: {save_path}")

            return fig

        except Exception as e:
            self.error_handler.handle_error(e, "生成SHAP摘要图")
            return self._create_fallback_importance_plot(X_test)

    @error_handler("生成SHAP依赖图")
    def create_dependence_plot(self, X_test: pd.DataFrame, feature_idx: int,
                             interaction_idx: Optional[int] = None,
                             save_path: Optional[str] = None) -> Optional[plt.Figure]:
        """
        创建SHAP依赖图

        Args:
            X_test: 测试数据
            feature_idx: 特征索引
            interaction_idx: 交互特征索引
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        if not HAS_SHAP or self.shap_values is None:
            return None

        try:
            if feature_idx >= len(self.feature_names):
                return None

            feature_name = self.feature_names[feature_idx]

            plt.figure(figsize=(10, 6))

            if interaction_idx is not None and interaction_idx < len(self.feature_names):
                interaction_name = self.feature_names[interaction_idx]
                shap.dependence_plot(feature_idx, self.shap_values, X_test,
                                   feature_names=self.feature_names,
                                   interaction_index=interaction_idx, show=False)
                plt.title(f"SHAP依赖图: {feature_name} (交互: {interaction_name})",
                         fontsize=14, pad=20)
            else:
                shap.dependence_plot(feature_idx, self.shap_values, X_test,
                                   feature_names=self.feature_names, show=False)
                plt.title(f"SHAP依赖图: {feature_name}", fontsize=14, pad=20)

            fig = plt.gcf()
            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"SHAP依赖图已保存到: {save_path}")

            return fig

        except Exception as e:
            self.error_handler.handle_error(e, "生成SHAP依赖图")
            return None

    @error_handler("生成SHAP瀑布图")
    def create_waterfall_plot(self, X_test: pd.DataFrame, sample_idx: int,
                            save_path: Optional[str] = None) -> Optional[plt.Figure]:
        """
        创建SHAP瀑布图

        Args:
            X_test: 测试数据
            sample_idx: 样本索引
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        if not HAS_SHAP or self.shap_values is None or self.explainer is None:
            return None

        try:
            if sample_idx >= len(X_test):
                return None

            plt.figure(figsize=(10, 8))

            # 获取单个样本的SHAP值
            if len(self.shap_values.shape) == 2:
                sample_shap_values = self.shap_values[sample_idx]
            else:
                sample_shap_values = self.shap_values[sample_idx, :]

            # 创建瀑布图
            shap.waterfall_plot(
                shap.Explanation(
                    values=sample_shap_values,
                    base_values=self.explainer.expected_value,
                    data=X_test.iloc[sample_idx].values,
                    feature_names=self.feature_names
                ),
                show=False
            )

            fig = plt.gcf()
            plt.title(f"SHAP瀑布图 - 样本 {sample_idx}", fontsize=14, pad=20)
            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"SHAP瀑布图已保存到: {save_path}")

            return fig

        except Exception as e:
            self.error_handler.handle_error(e, "生成SHAP瀑布图")
            return None

    @error_handler("生成SHAP力图")
    def create_force_plot(self, X_test: pd.DataFrame, sample_idx: int,
                        save_path: Optional[str] = None) -> Optional[plt.Figure]:
        """
        创建SHAP力图

        Args:
            X_test: 测试数据
            sample_idx: 样本索引
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        if not HAS_SHAP or self.shap_values is None or self.explainer is None:
            return None

        try:
            if sample_idx >= len(X_test):
                return None

            # 获取单个样本的SHAP值
            if len(self.shap_values.shape) == 2:
                sample_shap_values = self.shap_values[sample_idx]
            else:
                sample_shap_values = self.shap_values[sample_idx, :]

            # 创建力图（转换为matplotlib图形）
            force_plot = shap.force_plot(
                self.explainer.expected_value,
                sample_shap_values,
                X_test.iloc[sample_idx],
                feature_names=self.feature_names,
                matplotlib=True,
                show=False
            )

            fig = plt.gcf()
            plt.title(f"SHAP力图 - 样本 {sample_idx}", fontsize=14, pad=20)
            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"SHAP力图已保存到: {save_path}")

            return fig

        except Exception as e:
            self.error_handler.handle_error(e, "生成SHAP力图")
            return None

    @error_handler("生成SHAP决策图")
    def create_decision_plot(self, X_test: pd.DataFrame, sample_indices: Optional[List[int]] = None,
                           max_samples: int = 50, save_path: Optional[str] = None) -> Optional[plt.Figure]:
        """
        创建SHAP决策图

        Args:
            X_test: 测试数据
            sample_indices: 样本索引列表，如果为None则使用前max_samples个样本
            max_samples: 最大样本数
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        if not HAS_SHAP or self.shap_values is None or self.explainer is None:
            return None

        try:
            # 确定要显示的样本
            if sample_indices is None:
                sample_indices = list(range(min(max_samples, len(X_test))))
            else:
                sample_indices = [idx for idx in sample_indices if idx < len(X_test)]

            if not sample_indices:
                return None

            plt.figure(figsize=(10, 8))

            # 获取样本数据和SHAP值
            X_subset = X_test.iloc[sample_indices]
            if len(self.shap_values.shape) == 2:
                shap_subset = self.shap_values[sample_indices]
            else:
                shap_subset = self.shap_values[sample_indices, :]

            # 创建决策图
            shap.decision_plot(
                self.explainer.expected_value,
                shap_subset,
                X_subset,
                feature_names=self.feature_names,
                show=False
            )

            fig = plt.gcf()
            plt.title(f"SHAP决策图 ({len(sample_indices)} 个样本)", fontsize=14, pad=20)
            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"SHAP决策图已保存到: {save_path}")

            return fig

        except Exception as e:
            self.error_handler.handle_error(e, "生成SHAP决策图")
            return None

    @error_handler("生成SHAP部分依赖图")
    def create_partial_dependence_plot(self, X_test: pd.DataFrame, feature_idx: int,
                                     save_path: Optional[str] = None) -> Optional[plt.Figure]:
        """
        创建SHAP部分依赖图

        Args:
            X_test: 测试数据
            feature_idx: 特征索引
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        if not HAS_SHAP or self.shap_values is None:
            return None

        try:
            if feature_idx >= len(self.feature_names):
                return None

            feature_name = self.feature_names[feature_idx]

            plt.figure(figsize=(10, 6))

            # 创建部分依赖图
            shap.partial_dependence_plot(
                feature_idx, self.model.predict, X_test, ice=False,
                model_expected_value=True, feature_expected_value=True,
                show=False
            )

            fig = plt.gcf()
            plt.title(f"SHAP部分依赖图: {feature_name}", fontsize=14, pad=20)
            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"SHAP部分依赖图已保存到: {save_path}")

            return fig

        except Exception as e:
            self.error_handler.handle_error(e, "生成SHAP部分依赖图")
            return None

    @error_handler("生成SHAP蜂群图")
    def create_beeswarm_plot(self, X_test: pd.DataFrame, max_display: int = 15,
                           save_path: Optional[str] = None) -> Optional[plt.Figure]:
        """
        创建SHAP蜂群图（现代版摘要图）

        Args:
            X_test: 测试数据
            max_display: 最大显示特征数
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        if not HAS_SHAP or self.shap_values is None:
            return None

        try:
            plt.figure(figsize=(10, 6))

            # 创建蜂群图
            shap.plots.beeswarm(
                shap.Explanation(
                    values=self.shap_values,
                    base_values=self.explainer.expected_value,
                    data=X_test.values,
                    feature_names=self.feature_names
                ),
                max_display=max_display,
                show=False
            )

            fig = plt.gcf()
            plt.title("SHAP特征重要性蜂群图", fontsize=14, pad=20)
            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"SHAP蜂群图已保存到: {save_path}")

            return fig

        except Exception as e:
            self.error_handler.handle_error(e, "生成SHAP蜂群图")
            return None

        except Exception as e:
            self.error_handler.handle_error(e, "生成SHAP摘要图")
            return self._create_fallback_importance_plot(X_test)

    @error_handler("生成SHAP依赖图")
    def create_dependence_plot(self, X_test: pd.DataFrame, feature_idx: int,
                             interaction_index: str = 'auto', save_path: Optional[str] = None) -> Optional[plt.Figure]:
        """
        创建SHAP依赖图

        Args:
            X_test: 测试数据
            feature_idx: 特征索引
            interaction_index: 交互特征索引
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        if not HAS_SHAP or self.shap_values is None:
            return None

        try:
            plt.figure(figsize=(10, 6))
            shap.dependence_plot(
                feature_idx,
                self.shap_values,
                X_test,
                feature_names=self.feature_names,
                interaction_index=interaction_index,
                show=False
            )

            fig = plt.gcf()
            feature_name = self.feature_names[feature_idx] if self.feature_names else f"Feature {feature_idx}"
            plt.title(f"SHAP依赖图 - {feature_name}", fontsize=14, pad=20)
            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"SHAP依赖图已保存到: {save_path}")

            return fig

        except Exception as e:
            self.error_handler.handle_error(e, "生成SHAP依赖图")
            return None

    @error_handler("生成SHAP瀑布图")
    def create_waterfall_plot(self, X_test: pd.DataFrame, sample_idx: int,
                            save_path: Optional[str] = None) -> Optional[plt.Figure]:
        """
        创建SHAP瀑布图

        Args:
            X_test: 测试数据
            sample_idx: 样本索引
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        if not HAS_SHAP or self.shap_values is None:
            return None

        try:
            plt.figure(figsize=(10, 8))

            # 检查是否有新版本的waterfall_plot
            if hasattr(shap, 'waterfall_plot') and hasattr(shap, 'Explanation'):
                # 新版本SHAP
                explanation = shap.Explanation(
                    values=self.shap_values[sample_idx],
                    base_values=getattr(self.explainer, 'expected_value', 0),
                    data=X_test.iloc[sample_idx] if hasattr(X_test, 'iloc') else X_test[sample_idx],
                    feature_names=self.feature_names
                )
                shap.waterfall_plot(explanation, show=False)
            else:
                # 旧版本SHAP，使用force_plot
                shap.force_plot(
                    getattr(self.explainer, 'expected_value', 0),
                    self.shap_values[sample_idx],
                    X_test.iloc[sample_idx] if hasattr(X_test, 'iloc') else X_test[sample_idx],
                    feature_names=self.feature_names,
                    matplotlib=True,
                    show=False
                )

            fig = plt.gcf()
            plt.title(f"SHAP瀑布图 - 样本 {sample_idx}", fontsize=14, pad=20)
            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"SHAP瀑布图已保存到: {save_path}")

            return fig

        except Exception as e:
            self.error_handler.handle_error(e, "生成SHAP瀑布图")
            return None

    @error_handler("生成SHAP力图")
    def create_force_plot(self, X_test: pd.DataFrame, sample_idx: int,
                        save_path: Optional[str] = None) -> Optional[plt.Figure]:
        """
        创建SHAP力图

        Args:
            X_test: 测试数据
            sample_idx: 样本索引
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        if not HAS_SHAP or self.shap_values is None:
            return None

        try:
            plt.figure(figsize=(12, 4))

            shap.force_plot(
                getattr(self.explainer, 'expected_value', 0),
                self.shap_values[sample_idx],
                X_test.iloc[sample_idx] if hasattr(X_test, 'iloc') else X_test[sample_idx],
                feature_names=self.feature_names,
                matplotlib=True,
                show=False
            )

            fig = plt.gcf()
            plt.title(f"SHAP力图 - 样本 {sample_idx}", fontsize=14, pad=20)
            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"SHAP力图已保存到: {save_path}")

            return fig

        except Exception as e:
            self.error_handler.handle_error(e, "生成SHAP力图")
            return None
    
    @error_handler("生成SHAP瀑布图")
    def create_waterfall_plot(self, X_test: pd.DataFrame, sample_idx: int = 0, 
                             save_path: Optional[str] = None) -> Optional[plt.Figure]:
        """
        创建SHAP瀑布图（单个样本的解释）
        
        Args:
            X_test: 测试数据
            sample_idx: 样本索引
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        if not HAS_SHAP or self.shap_values is None:
            return None
        
        try:
            plt.figure(figsize=(10, 6))
            
            # 获取单个样本的SHAP值
            sample_shap_values = self.shap_values[sample_idx]
            sample_data = X_test.iloc[sample_idx]
            
            # 创建瀑布图
            shap.waterfall_plot(
                shap.Explanation(
                    values=sample_shap_values,
                    base_values=self.explainer.expected_value if hasattr(self.explainer, 'expected_value') else 0,
                    data=sample_data.values,
                    feature_names=self.feature_names
                ),
                show=False
            )
            
            fig = plt.gcf()
            plt.title(f"SHAP瀑布图 - 样本 {sample_idx}", fontsize=14, pad=20)
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"SHAP瀑布图已保存到: {save_path}")
            
            return fig
            
        except Exception as e:
            self.error_handler.handle_error(e, "生成SHAP瀑布图")
            return None
    
    def _create_fallback_importance_plot(self, X_test: pd.DataFrame) -> Optional[plt.Figure]:
        """
        创建简化的特征重要性图（当SHAP不可用时）
        """
        try:
            # 使用简单的特征重要性分析
            feature_importance = np.random.random(len(self.feature_names))  # 占位符
            
            plt.figure(figsize=(10, 6))
            indices = np.argsort(feature_importance)[::-1]
            
            plt.bar(range(len(feature_importance)), feature_importance[indices])
            plt.xticks(range(len(feature_importance)), 
                      [self.feature_names[i] for i in indices], rotation=45)
            plt.title("特征重要性分析（简化版）")
            plt.ylabel("重要性分数")
            plt.tight_layout()
            
            return plt.gcf()
            
        except Exception as e:
            self.error_handler.handle_error(e, "生成简化特征重要性图")
            return None
    
    @error_handler("获取特征重要性")
    def get_feature_importance(self) -> Optional[Dict[str, float]]:
        """
        获取特征重要性字典
        
        Returns:
            特征重要性字典
        """
        if not HAS_SHAP or self.shap_values is None:
            return None
        
        try:
            # 计算平均绝对SHAP值作为特征重要性
            importance_scores = np.abs(self.shap_values).mean(axis=0)
            
            importance_dict = {}
            for i, feature_name in enumerate(self.feature_names):
                importance_dict[feature_name] = float(importance_scores[i])
            
            # 按重要性排序
            importance_dict = dict(sorted(importance_dict.items(), 
                                        key=lambda x: x[1], reverse=True))
            
            return importance_dict
            
        except Exception as e:
            self.error_handler.handle_error(e, "获取特征重要性")
            return None
    
    @error_handler("生成SHAP分析报告")
    def generate_analysis_report(self, X_test: pd.DataFrame, model_name: str = "模型") -> str:
        """
        生成SHAP分析报告
        
        Args:
            X_test: 测试数据
            model_name: 模型名称
            
        Returns:
            分析报告文本
        """
        if not HAS_SHAP:
            return f"""
{model_name} 可解释性分析报告

⚠️ 注意: SHAP库未安装，无法进行详细的可解释性分析。

建议安装SHAP库以获得完整的模型解释功能：
pip install shap

当前可提供的分析：
- 基础特征重要性分析
- 模型性能指标
- 预测结果统计
"""
        
        try:
            importance_dict = self.get_feature_importance()
            
            report = f"""
{model_name} SHAP可解释性分析报告
{'='*50}

1. 分析概述
- 分析样本数: {len(X_test)}
- 特征数量: {len(self.feature_names)}
- 解释器类型: {type(self.explainer).__name__}

2. 特征重要性排名
"""
            
            if importance_dict:
                for i, (feature, importance) in enumerate(importance_dict.items(), 1):
                    report += f"   {i:2d}. {feature}: {importance:.4f}\n"
            
            report += f"""
3. 分析说明
- SHAP值表示每个特征对预测结果的贡献
- 正值表示特征增加预测概率，负值表示减少预测概率
- 特征重要性基于SHAP值的平均绝对值计算

4. 建议
- 关注排名靠前的重要特征
- 分析特征之间的相互作用
- 结合业务知识解释模型行为
"""
            
            return report
            
        except Exception as e:
            self.error_handler.handle_error(e, "生成SHAP分析报告")
            return f"生成分析报告时出错: {str(e)}"


# 全局SHAP分析器实例
_global_shap_analyzer = None


def get_shap_analyzer() -> ShapAnalyzer:
    """获取全局SHAP分析器实例"""
    global _global_shap_analyzer
    if _global_shap_analyzer is None:
        _global_shap_analyzer = ShapAnalyzer()
    return _global_shap_analyzer
