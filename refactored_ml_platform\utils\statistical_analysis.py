#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计分析工具集
提供各种统计分析方法
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.stats import chi2_contingency, fisher_exact, mannw<PERSON><PERSON><PERSON>, wilcoxon
from sklearn.metrics import confusion_matrix, classification_report
from sklearn.model_selection import permutation_test_score
import logging
from typing import Dict, List, Tuple, Optional, Any, Union
import warnings

from .error_handler import get_error_handler, error_handler

# 忽略统计计算中的警告
warnings.filterwarnings("ignore", category=RuntimeWarning)


class StatisticalAnalysis:
    """统计分析工具类"""
    
    def __init__(self):
        """初始化统计分析工具"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
    
    @error_handler("执行McNemar检验")
    def mcnemar_test(self, y_true: np.ndarray, y_pred1: np.ndarray, 
                    y_pred2: np.ndarray) -> Dict[str, Any]:
        """
        执行McNemar检验比较两个分类器的性能
        
        Args:
            y_true: 真实标签
            y_pred1: 分类器1的预测结果
            y_pred2: 分类器2的预测结果
            
        Returns:
            McNemar检验结果
        """
        # 创建列联表
        correct1 = (y_pred1 == y_true)
        correct2 = (y_pred2 == y_true)
        
        # 2x2列联表
        # [both_correct, model1_correct_model2_wrong]
        # [model1_wrong_model2_correct, both_wrong]
        both_correct = np.sum(correct1 & correct2)
        model1_correct_model2_wrong = np.sum(correct1 & ~correct2)
        model1_wrong_model2_correct = np.sum(~correct1 & correct2)
        both_wrong = np.sum(~correct1 & ~correct2)
        
        contingency_table = np.array([
            [both_correct, model1_correct_model2_wrong],
            [model1_wrong_model2_correct, both_wrong]
        ])
        
        # 执行McNemar检验
        # 使用不连续性校正
        b = model1_correct_model2_wrong
        c = model1_wrong_model2_correct
        
        if b + c == 0:
            statistic = 0
            p_value = 1.0
        else:
            # 使用连续性校正的McNemar检验
            statistic = (abs(b - c) - 1) ** 2 / (b + c)
            p_value = 1 - stats.chi2.cdf(statistic, df=1)
        
        # 计算准确率
        acc1 = np.mean(correct1)
        acc2 = np.mean(correct2)
        
        return {
            'statistic': statistic,
            'p_value': p_value,
            'contingency_table': contingency_table,
            'accuracy_1': acc1,
            'accuracy_2': acc2,
            'accuracy_diff': acc1 - acc2,
            'is_significant': p_value < 0.05,
            'interpretation': self._interpret_mcnemar_result(p_value, acc1, acc2)
        }
    
    def _interpret_mcnemar_result(self, p_value: float, acc1: float, acc2: float) -> str:
        """解释McNemar检验结果"""
        if p_value < 0.001:
            significance = "极显著"
        elif p_value < 0.01:
            significance = "高度显著"
        elif p_value < 0.05:
            significance = "显著"
        else:
            significance = "不显著"
        
        better_model = "模型1" if acc1 > acc2 else "模型2" if acc2 > acc1 else "相当"
        
        return f"两个模型的性能差异{significance}（p={p_value:.4f}），{better_model}表现更好"
    
    @error_handler("执行Cochran's Q检验")
    def cochran_q_test(self, y_true: np.ndarray, *y_preds: np.ndarray) -> Dict[str, Any]:
        """
        执行Cochran's Q检验比较多个分类器的性能
        
        Args:
            y_true: 真实标签
            *y_preds: 多个分类器的预测结果
            
        Returns:
            Cochran's Q检验结果
        """
        if len(y_preds) < 3:
            raise ValueError("Cochran's Q检验至少需要3个分类器")
        
        n_samples = len(y_true)
        n_models = len(y_preds)
        
        # 创建正确性矩阵
        correct_matrix = np.zeros((n_samples, n_models))
        for i, y_pred in enumerate(y_preds):
            correct_matrix[:, i] = (y_pred == y_true).astype(int)
        
        # 计算统计量
        # Q = (k-1) * [k * sum(Cj^2) - (sum(Cj))^2] / [k * sum(Ri) - sum(Ri^2)]
        k = n_models
        
        # 每个模型的正确预测数
        Cj = np.sum(correct_matrix, axis=0)
        
        # 每个样本被正确预测的模型数
        Ri = np.sum(correct_matrix, axis=1)
        
        numerator = k * np.sum(Cj ** 2) - (np.sum(Cj)) ** 2
        denominator = k * np.sum(Ri) - np.sum(Ri ** 2)
        
        if denominator == 0:
            q_statistic = 0
            p_value = 1.0
        else:
            q_statistic = (k - 1) * numerator / denominator
            # 自由度为 k-1
            p_value = 1 - stats.chi2.cdf(q_statistic, df=k-1)
        
        # 计算每个模型的准确率
        accuracies = Cj / n_samples
        
        return {
            'q_statistic': q_statistic,
            'p_value': p_value,
            'degrees_of_freedom': k - 1,
            'accuracies': accuracies,
            'is_significant': p_value < 0.05,
            'interpretation': self._interpret_cochran_q_result(p_value, k)
        }
    
    def _interpret_cochran_q_result(self, p_value: float, k: int) -> str:
        """解释Cochran's Q检验结果"""
        if p_value < 0.05:
            return f"在{k}个模型中存在显著的性能差异（p={p_value:.4f}）"
        else:
            return f"{k}个模型的性能无显著差异（p={p_value:.4f}）"
    
    @error_handler("执行Friedman检验")
    def friedman_test(self, performance_matrix: np.ndarray) -> Dict[str, Any]:
        """
        执行Friedman检验比较多个算法在多个数据集上的性能
        
        Args:
            performance_matrix: 性能矩阵，行为数据集，列为算法
            
        Returns:
            Friedman检验结果
        """
        n_datasets, n_algorithms = performance_matrix.shape
        
        if n_algorithms < 3:
            raise ValueError("Friedman检验至少需要3个算法")
        
        # 对每个数据集的算法性能进行排名
        ranks = np.zeros_like(performance_matrix)
        for i in range(n_datasets):
            # 降序排名（性能越高排名越靠前）
            ranks[i] = stats.rankdata(-performance_matrix[i])
        
        # 计算平均排名
        mean_ranks = np.mean(ranks, axis=0)
        
        # 计算Friedman统计量
        # χ²_F = 12N / [k(k+1)] * [∑Rj² - k(k+1)²/4]
        k = n_algorithms
        N = n_datasets
        
        sum_ranks_squared = np.sum(mean_ranks ** 2)
        friedman_statistic = (12 * N / (k * (k + 1))) * (sum_ranks_squared - k * (k + 1) ** 2 / 4)
        
        # 自由度为 k-1
        p_value = 1 - stats.chi2.cdf(friedman_statistic, df=k-1)
        
        # 计算临界差异（Critical Difference）
        # CD = q_α * sqrt(k(k+1)/(6N))
        # 这里使用Nemenyi后续检验的临界值
        q_alpha = stats.studentized_range.ppf(0.95, k, np.inf) / np.sqrt(2)
        critical_difference = q_alpha * np.sqrt(k * (k + 1) / (6 * N))
        
        return {
            'friedman_statistic': friedman_statistic,
            'p_value': p_value,
            'degrees_of_freedom': k - 1,
            'mean_ranks': mean_ranks,
            'critical_difference': critical_difference,
            'is_significant': p_value < 0.05,
            'interpretation': self._interpret_friedman_result(p_value, k, N)
        }
    
    def _interpret_friedman_result(self, p_value: float, k: int, N: int) -> str:
        """解释Friedman检验结果"""
        if p_value < 0.05:
            return f"在{N}个数据集上，{k}个算法的性能存在显著差异（p={p_value:.4f}）"
        else:
            return f"在{N}个数据集上，{k}个算法的性能无显著差异（p={p_value:.4f}）"
    
    @error_handler("执行Wilcoxon符号秩检验")
    def wilcoxon_signed_rank_test(self, performance1: np.ndarray, 
                                 performance2: np.ndarray) -> Dict[str, Any]:
        """
        执行Wilcoxon符号秩检验比较两个算法的性能
        
        Args:
            performance1: 算法1的性能数据
            performance2: 算法2的性能数据
            
        Returns:
            Wilcoxon检验结果
        """
        # 计算差异
        differences = performance1 - performance2
        
        # 执行Wilcoxon符号秩检验
        statistic, p_value = wilcoxon(differences, alternative='two-sided')
        
        # 计算效应大小（r = Z / sqrt(N)）
        n = len(differences)
        z_score = stats.norm.ppf(1 - p_value/2)  # 近似Z分数
        effect_size = z_score / np.sqrt(n)
        
        return {
            'statistic': statistic,
            'p_value': p_value,
            'z_score': z_score,
            'effect_size': effect_size,
            'mean_difference': np.mean(differences),
            'median_difference': np.median(differences),
            'is_significant': p_value < 0.05,
            'interpretation': self._interpret_wilcoxon_result(p_value, np.mean(differences))
        }
    
    def _interpret_wilcoxon_result(self, p_value: float, mean_diff: float) -> str:
        """解释Wilcoxon检验结果"""
        significance = "显著" if p_value < 0.05 else "不显著"
        better = "算法1" if mean_diff > 0 else "算法2" if mean_diff < 0 else "相当"
        
        return f"两个算法的性能差异{significance}（p={p_value:.4f}），{better}表现更好"
    
    @error_handler("执行置换检验")
    def permutation_test(self, estimator, X: np.ndarray, y: np.ndarray, 
                        cv: int = 5, n_permutations: int = 1000,
                        scoring: str = 'accuracy') -> Dict[str, Any]:
        """
        执行置换检验评估模型的统计显著性
        
        Args:
            estimator: 机器学习模型
            X: 特征数据
            y: 标签数据
            cv: 交叉验证折数
            n_permutations: 置换次数
            scoring: 评分方法
            
        Returns:
            置换检验结果
        """
        # 执行置换检验
        score, permutation_scores, p_value = permutation_test_score(
            estimator, X, y, cv=cv, n_permutations=n_permutations, 
            scoring=scoring, random_state=42
        )
        
        return {
            'true_score': score,
            'permutation_scores': permutation_scores,
            'p_value': p_value,
            'mean_permutation_score': np.mean(permutation_scores),
            'std_permutation_score': np.std(permutation_scores),
            'is_significant': p_value < 0.05,
            'interpretation': self._interpret_permutation_result(score, p_value, np.mean(permutation_scores))
        }
    
    def _interpret_permutation_result(self, true_score: float, p_value: float, 
                                    mean_perm_score: float) -> str:
        """解释置换检验结果"""
        if p_value < 0.05:
            return f"模型性能显著优于随机（真实分数={true_score:.4f}, p={p_value:.4f}）"
        else:
            return f"模型性能与随机无显著差异（真实分数={true_score:.4f}, p={p_value:.4f}）"
    
    @error_handler("计算置信区间")
    def bootstrap_confidence_interval(self, data: np.ndarray, statistic_func: callable,
                                    confidence_level: float = 0.95, 
                                    n_bootstrap: int = 1000) -> Dict[str, Any]:
        """
        使用Bootstrap方法计算统计量的置信区间
        
        Args:
            data: 数据
            statistic_func: 统计量计算函数
            confidence_level: 置信水平
            n_bootstrap: Bootstrap采样次数
            
        Returns:
            置信区间结果
        """
        n = len(data)
        bootstrap_stats = []
        
        # Bootstrap采样
        for _ in range(n_bootstrap):
            bootstrap_sample = np.random.choice(data, size=n, replace=True)
            bootstrap_stat = statistic_func(bootstrap_sample)
            bootstrap_stats.append(bootstrap_stat)
        
        bootstrap_stats = np.array(bootstrap_stats)
        
        # 计算置信区间
        alpha = 1 - confidence_level
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100
        
        ci_lower = np.percentile(bootstrap_stats, lower_percentile)
        ci_upper = np.percentile(bootstrap_stats, upper_percentile)
        
        # 原始统计量
        original_stat = statistic_func(data)
        
        return {
            'original_statistic': original_stat,
            'bootstrap_statistics': bootstrap_stats,
            'confidence_interval': (ci_lower, ci_upper),
            'confidence_level': confidence_level,
            'mean_bootstrap': np.mean(bootstrap_stats),
            'std_bootstrap': np.std(bootstrap_stats),
            'interpretation': f"{confidence_level*100:.0f}%置信区间: [{ci_lower:.4f}, {ci_upper:.4f}]"
        }


# 全局统计分析实例
_statistical_analysis = None

def get_statistical_analysis() -> StatisticalAnalysis:
    """
    获取全局统计分析实例
    
    Returns:
        统计分析实例
    """
    global _statistical_analysis
    if _statistical_analysis is None:
        _statistical_analysis = StatisticalAnalysis()
    return _statistical_analysis
