#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线程安全的GUI更新工具模块

提供线程安全的GUI更新函数，避免"main thread is not in main loop"错误
"""

import tkinter as tk
from tkinter import messagebox
import threading
from functools import wraps
from typing import Callable, Any, Optional
import logging


class ThreadSafeGUI:
    """线程安全的GUI更新工具类"""
    
    def __init__(self, root_widget: tk.Widget):
        """
        初始化线程安全GUI工具
        
        Args:
            root_widget: 主窗口或根控件
        """
        self.root = root_widget
        self._is_destroyed = False
        self.logger = logging.getLogger(__name__)
        
    def safe_call(self, func: Callable, *args, **kwargs):
        """
        线程安全地调用GUI函数
        
        Args:
            func: 要调用的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
        """
        def _call():
            try:
                if not self._is_destroyed:
                    func(*args, **kwargs)
            except tk.TclError:
                # GUI已销毁，忽略错误
                self._is_destroyed = True
            except Exception as e:
                self.logger.error(f"GUI更新时发生错误: {e}")
        
        try:
            if threading.current_thread() == threading.main_thread():
                # 如果已经在主线程中，直接调用
                _call()
            else:
                # 在后台线程中，使用after调度到主线程
                self.root.after(0, _call)
        except Exception as e:
            self.logger.error(f"线程安全调用失败: {e}")
    
    def safe_update_progress(self, progress_widget, value: float, message: str = ""):
        """
        线程安全地更新进度条
        
        Args:
            progress_widget: 进度条组件
            value: 进度值 (0-100)
            message: 进度消息
        """
        def update():
            try:
                if hasattr(progress_widget, 'set_progress'):
                    progress_widget.set_progress(value, message)
                elif hasattr(progress_widget, 'set'):
                    progress_widget.set(value)
                elif hasattr(progress_widget, 'config'):
                    progress_widget.config(value=value)
            except Exception as e:
                self.logger.error(f"更新进度条失败: {e}")
        
        self.safe_call(update)
    
    def safe_log_message(self, log_func: Callable, message: str):
        """
        线程安全地记录日志消息
        
        Args:
            log_func: 日志记录函数
            message: 日志消息
        """
        self.safe_call(log_func, message)
    
    def safe_update_status(self, status_widget, message: str):
        """
        线程安全地更新状态显示
        
        Args:
            status_widget: 状态显示组件
            message: 状态消息
        """
        def update():
            try:
                if hasattr(status_widget, 'set'):
                    status_widget.set(message)
                elif hasattr(status_widget, 'config'):
                    status_widget.config(text=message)
            except Exception as e:
                self.logger.error(f"更新状态失败: {e}")
        
        self.safe_call(update)
    
    def safe_show_message(self, title: str, message: str, msg_type: str = "info"):
        """
        线程安全地显示消息框
        
        Args:
            title: 消息框标题
            message: 消息内容
            msg_type: 消息类型 ('info', 'warning', 'error')
        """
        def show():
            try:
                if msg_type == "info":
                    messagebox.showinfo(title, message)
                elif msg_type == "warning":
                    messagebox.showwarning(title, message)
                elif msg_type == "error":
                    messagebox.showerror(title, message)
            except Exception as e:
                self.logger.error(f"显示消息框失败: {e}")
        
        self.safe_call(show)
    
    def safe_update_widget(self, widget, **kwargs):
        """
        线程安全地更新组件属性
        
        Args:
            widget: 要更新的组件
            **kwargs: 要更新的属性
        """
        def update():
            try:
                widget.config(**kwargs)
            except Exception as e:
                self.logger.error(f"更新组件失败: {e}")
        
        self.safe_call(update)
    
    def safe_insert_text(self, text_widget, position: str, content: str):
        """
        线程安全地向文本组件插入内容
        
        Args:
            text_widget: 文本组件
            position: 插入位置
            content: 插入内容
        """
        def insert():
            try:
                text_widget.insert(position, content)
                text_widget.see(tk.END)  # 滚动到末尾
            except Exception as e:
                self.logger.error(f"插入文本失败: {e}")
        
        self.safe_call(insert)
    
    def safe_clear_widget(self, widget):
        """
        线程安全地清空组件内容
        
        Args:
            widget: 要清空的组件
        """
        def clear():
            try:
                if hasattr(widget, 'delete'):
                    if hasattr(widget, 'get_children'):  # Treeview
                        for item in widget.get_children():
                            widget.delete(item)
                    else:  # Text widget
                        widget.delete(1.0, tk.END)
                elif hasattr(widget, 'clear'):
                    widget.clear()
            except Exception as e:
                self.logger.error(f"清空组件失败: {e}")
        
        self.safe_call(clear)
    
    def safe_enable_widget(self, widget, enabled: bool = True):
        """
        线程安全地启用/禁用组件
        
        Args:
            widget: 要操作的组件
            enabled: 是否启用
        """
        state = tk.NORMAL if enabled else tk.DISABLED
        self.safe_update_widget(widget, state=state)
    
    def destroy(self):
        """销毁工具"""
        self._is_destroyed = True


def thread_safe_gui_update(root_widget: tk.Widget):
    """
    装饰器：使函数的GUI更新操作线程安全
    
    Args:
        root_widget: 根组件
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            gui_updater = ThreadSafeGUI(root_widget)
            return gui_updater.safe_call(func, *args, **kwargs)
        return wrapper
    return decorator


# 全局GUI更新器实例
_global_gui_updater: Optional[ThreadSafeGUI] = None


def get_global_gui_updater() -> Optional[ThreadSafeGUI]:
    """获取全局GUI更新器实例"""
    return _global_gui_updater


def set_global_gui_updater(root_widget: tk.Widget):
    """设置全局GUI更新器实例"""
    global _global_gui_updater
    _global_gui_updater = ThreadSafeGUI(root_widget)


def safe_gui_call(func: Callable, *args, **kwargs):
    """
    使用全局GUI更新器进行线程安全调用
    
    Args:
        func: 要调用的函数
        *args: 函数参数
        **kwargs: 函数关键字参数
    """
    if _global_gui_updater:
        _global_gui_updater.safe_call(func, *args, **kwargs)
    else:
        # 如果没有全局更新器，直接调用（可能不安全）
        func(*args, **kwargs)
