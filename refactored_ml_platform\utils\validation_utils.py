#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证工具模块
提供数据验证和质量检查相关的工具函数
"""

import pandas as pd
import numpy as np
from typing import Any, Dict, List, Optional, Tuple, Union


class ValidationUtils:
    """验证工具类"""
    
    @staticmethod
    def validate_dataframe(df: pd.DataFrame) -> Dict[str, Any]:
        """
        验证DataFrame的基本质量
        
        Args:
            df: 要验证的DataFrame
            
        Returns:
            验证结果字典
        """
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'info': {}
        }
        
        # 基本信息
        validation_results['info']['shape'] = df.shape
        validation_results['info']['columns'] = df.columns.tolist()
        validation_results['info']['dtypes'] = df.dtypes.to_dict()
        
        # 检查是否为空
        if df.empty:
            validation_results['is_valid'] = False
            validation_results['errors'].append("DataFrame为空")
            return validation_results
        
        # 检查缺失值
        missing_info = ValidationUtils.check_missing_values(df)
        validation_results['info']['missing_values'] = missing_info
        
        # 如果缺失值过多，添加警告
        for col, pct in missing_info['missing_percentage'].items():
            if pct > 50:
                validation_results['warnings'].append(f"列 '{col}' 缺失值过多 ({pct:.1f}%)")
            elif pct > 80:
                validation_results['errors'].append(f"列 '{col}' 缺失值严重过多 ({pct:.1f}%)")
                validation_results['is_valid'] = False
        
        # 检查重复行
        duplicated_count = df.duplicated().sum()
        validation_results['info']['duplicated_rows'] = duplicated_count
        if duplicated_count > 0:
            duplicate_pct = (duplicated_count / len(df)) * 100
            if duplicate_pct > 10:
                validation_results['warnings'].append(f"重复行过多 ({duplicate_pct:.1f}%)")
        
        # 检查数据类型一致性
        type_issues = ValidationUtils.check_data_types(df)
        if type_issues:
            validation_results['warnings'].extend(type_issues)
        
        # 检查异常值
        outlier_info = ValidationUtils.check_outliers(df)
        validation_results['info']['outliers'] = outlier_info
        
        return validation_results
    
    @staticmethod
    def check_missing_values(df: pd.DataFrame) -> Dict[str, Any]:
        """
        检查缺失值
        
        Args:
            df: DataFrame
            
        Returns:
            缺失值信息
        """
        missing_count = df.isnull().sum()
        missing_percentage = (missing_count / len(df)) * 100
        
        return {
            'missing_count': missing_count.to_dict(),
            'missing_percentage': missing_percentage.to_dict(),
            'total_missing': missing_count.sum(),
            'columns_with_missing': missing_count[missing_count > 0].index.tolist()
        }
    
    @staticmethod
    def check_data_types(df: pd.DataFrame) -> List[str]:
        """
        检查数据类型问题
        
        Args:
            df: DataFrame
            
        Returns:
            问题列表
        """
        issues = []
        
        for col in df.columns:
            # 检查是否有混合类型
            if df[col].dtype == 'object':
                # 尝试转换为数值
                try:
                    pd.to_numeric(df[col], errors='raise')
                    issues.append(f"列 '{col}' 可能应该是数值类型")
                except:
                    # 检查是否有数值混在字符串中
                    numeric_count = 0
                    for val in df[col].dropna():
                        try:
                            float(str(val))
                            numeric_count += 1
                        except:
                            pass
                    
                    if numeric_count > 0 and numeric_count < len(df[col].dropna()):
                        issues.append(f"列 '{col}' 包含混合类型数据")
        
        return issues
    
    @staticmethod
    def check_outliers(df: pd.DataFrame, method: str = 'iqr') -> Dict[str, Any]:
        """
        检查异常值
        
        Args:
            df: DataFrame
            method: 检测方法
            
        Returns:
            异常值信息
        """
        outlier_info = {}
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            if method == 'iqr':
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)][col]
            
            elif method == 'zscore':
                z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
                outliers = df[z_scores > 3][col]
            
            else:
                continue
            
            outlier_info[col] = {
                'count': len(outliers),
                'percentage': (len(outliers) / len(df)) * 100,
                'values': outliers.tolist()[:10]  # 只保存前10个
            }
        
        return outlier_info
    
    @staticmethod
    def validate_target_column(df: pd.DataFrame, target_col: str) -> Dict[str, Any]:
        """
        验证目标列
        
        Args:
            df: DataFrame
            target_col: 目标列名
            
        Returns:
            验证结果
        """
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'info': {}
        }
        
        if target_col not in df.columns:
            result['is_valid'] = False
            result['errors'].append(f"目标列 '{target_col}' 不存在")
            return result
        
        target_series = df[target_col]
        
        # 基本信息
        result['info']['unique_values'] = target_series.nunique()
        result['info']['value_counts'] = target_series.value_counts().to_dict()
        result['info']['missing_count'] = target_series.isnull().sum()
        
        # 检查缺失值
        if target_series.isnull().sum() > 0:
            result['warnings'].append(f"目标列包含 {target_series.isnull().sum()} 个缺失值")
        
        # 检查类别分布
        if target_series.nunique() <= 10:  # 分类问题
            value_counts = target_series.value_counts()
            min_count = value_counts.min()
            max_count = value_counts.max()
            
            # 检查类别不平衡
            if max_count / min_count > 10:
                result['warnings'].append("目标变量存在严重的类别不平衡")
            elif max_count / min_count > 3:
                result['warnings'].append("目标变量存在类别不平衡")
            
            # 检查是否有类别样本过少
            for value, count in value_counts.items():
                if count < 5:
                    result['warnings'].append(f"类别 '{value}' 样本过少 ({count})")
        
        return result
    
    @staticmethod
    def validate_feature_columns(df: pd.DataFrame, 
                                exclude_cols: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        验证特征列
        
        Args:
            df: DataFrame
            exclude_cols: 要排除的列
            
        Returns:
            验证结果
        """
        if exclude_cols is None:
            exclude_cols = []
        
        feature_cols = [col for col in df.columns if col not in exclude_cols]
        
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'info': {
                'feature_count': len(feature_cols),
                'numeric_features': [],
                'categorical_features': [],
                'constant_features': [],
                'high_cardinality_features': []
            }
        }
        
        for col in feature_cols:
            # 分类特征和数值特征
            if df[col].dtype in ['int64', 'float64']:
                result['info']['numeric_features'].append(col)
            else:
                result['info']['categorical_features'].append(col)
            
            # 检查常数特征
            if df[col].nunique() <= 1:
                result['info']['constant_features'].append(col)
                result['warnings'].append(f"特征 '{col}' 是常数特征")
            
            # 检查高基数分类特征
            if df[col].dtype == 'object' and df[col].nunique() > 50:
                result['info']['high_cardinality_features'].append(col)
                result['warnings'].append(f"分类特征 '{col}' 基数过高 ({df[col].nunique()})")
        
        # 检查特征数量
        if len(feature_cols) == 0:
            result['is_valid'] = False
            result['errors'].append("没有可用的特征列")
        elif len(feature_cols) > len(df):
            result['warnings'].append("特征数量超过样本数量，可能存在过拟合风险")
        
        return result
    
    @staticmethod
    def validate_data_split(X_train: pd.DataFrame, X_test: pd.DataFrame,
                           y_train: pd.Series, y_test: pd.Series) -> Dict[str, Any]:
        """
        验证数据分割
        
        Args:
            X_train: 训练特征
            X_test: 测试特征
            y_train: 训练标签
            y_test: 测试标签
            
        Returns:
            验证结果
        """
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'info': {}
        }
        
        # 基本信息
        result['info']['train_size'] = len(X_train)
        result['info']['test_size'] = len(X_test)
        result['info']['test_ratio'] = len(X_test) / (len(X_train) + len(X_test))
        
        # 检查数据形状一致性
        if X_train.shape[1] != X_test.shape[1]:
            result['is_valid'] = False
            result['errors'].append("训练集和测试集特征数量不一致")
        
        if len(X_train) != len(y_train):
            result['is_valid'] = False
            result['errors'].append("训练特征和标签数量不一致")
        
        if len(X_test) != len(y_test):
            result['is_valid'] = False
            result['errors'].append("测试特征和标签数量不一致")
        
        # 检查列名一致性
        if not X_train.columns.equals(X_test.columns):
            result['warnings'].append("训练集和测试集列名不一致")
        
        # 检查测试集比例
        test_ratio = result['info']['test_ratio']
        if test_ratio < 0.1:
            result['warnings'].append(f"测试集比例过小 ({test_ratio:.1%})")
        elif test_ratio > 0.5:
            result['warnings'].append(f"测试集比例过大 ({test_ratio:.1%})")
        
        # 检查类别分布
        if hasattr(y_train, 'value_counts') and hasattr(y_test, 'value_counts'):
            train_dist = y_train.value_counts(normalize=True).sort_index()
            test_dist = y_test.value_counts(normalize=True).sort_index()
            
            # 检查类别是否一致
            if not train_dist.index.equals(test_dist.index):
                result['warnings'].append("训练集和测试集包含不同的类别")
            else:
                # 检查分布差异
                max_diff = abs(train_dist - test_dist).max()
                if max_diff > 0.1:
                    result['warnings'].append(f"训练集和测试集类别分布差异较大 (最大差异: {max_diff:.1%})")
        
        return result
